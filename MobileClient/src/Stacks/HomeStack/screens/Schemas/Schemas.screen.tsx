import React, { useCallback, useState } from 'react';
import { View, RefreshControl } from 'react-native';
import { useTheme } from '../../../../Common/Theme/hooks/useTheme';
import { createSchemaStyles } from './Schemas.styles';
import { useSchemaData } from './Schemas.hooks';
import { SchemaItem, SchemasProps } from './Schemas.types';
import Animated from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import MenuCards from '../../components/MenuCards';
// React Native Paper Components
import {
  Appbar,
  Card,
  Text,
  // Searchbar,
  Surface,
  TouchableRipple,
  Button,
  IconButton,
  Avatar,
  useTheme as usePaperTheme,
} from 'react-native-paper';
import SearchBar from '../../../../Components/SearchInput';
import SafeContainerView from '../../../../Components/SafeContainer/index';
import SkeletonLoader from '../../components/SkeletonLoader';
import EmptyState from '../../components/EmptyState';
import MyHeader from '../../../../Components/UI/Appbar';
import AppHeader from '../../../../Components/AppHeader';
import { scale } from 'react-native-size-matters';
const SchemasScreen: React.FC<SchemasProps> = ({ navigation, route }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const theme = useTheme();
  const paperTheme = usePaperTheme();
  const { colors, spacing, fontFamily, fontSize, shadow, borderRadius } = theme;
  const styles = createSchemaStyles(
    colors,
    spacing,
    fontFamily,
    fontSize,
    shadow,
    borderRadius,
    paperTheme
  );
  const { workspace, isBackAction } = route.params;
  const insets = useSafeAreaInsets();
  const { schemas, loading, error, refreshing, onRefresh, isFavorite, toggleFavorite } =
    useSchemaData(workspace.id);

  // Handle back button press
  const handleBackPress = () => {
    if (navigation.goBack) {
      navigation.goBack();
    }
  };

  const keyExtractor = useCallback((item: SchemaItem) => item.id, []);

  const handleSchemaPress = useCallback(
    (schema: SchemaItem) => {
      navigation.navigate('Solutions', { schema, workspace });
    },
    [navigation, workspace]
  );

  if (error) {
    return (
      <SafeContainerView backgroundColor={paperTheme.colors.background}>
        <Surface style={[styles.container, { flex: 1 }]} elevation={0}>
          <MyHeader title={'Schemas'} handleBack={handleBackPress} isBackAction={isBackAction} />
          <Surface style={styles.errorContainer} elevation={1}>
            <Text style={[styles.errorText, { color: paperTheme.colors.error }]}>{error}</Text>
            <Button
              mode="contained"
              onPress={onRefresh}
              style={{ marginTop: spacing.m }}
              buttonColor={paperTheme.colors.primary}
            >
              Retry
            </Button>
          </Surface>
        </Surface>
      </SafeContainerView>
    );
  }

  if (loading && schemas.length === 0) {
    return (
      <SafeContainerView backgroundColor={paperTheme.colors.background}>
        <Surface style={[styles.container, { flex: 1 }]} elevation={0}>
          <MyHeader title={'Schemas'} handleBack={handleBackPress} isBackAction={isBackAction} />
          <View style={{ padding: spacing.m }}>
            <SkeletonLoader count={3} />
          </View>
        </Surface>
      </SafeContainerView>
    );
  }

  return (
    <SafeContainerView backgroundColor={paperTheme.colors.background}>
      {/* <Appbar.Header
          mode="small"
          // style={{backgroundColor: paperTheme.colors.primary, marginBottom: 0}}
          >
          <Appbar.BackAction
            onPress={handleBackPress}
            // color={paperTheme.colors.onPrimary}
          />
          <Appbar.Content
            title="Schemas"
            titleStyle={{
              // color: paperTheme.colors.onPrimary,
              // fontFamily: fontFamily.semiBold,
            }}
          />
        </Appbar.Header> */}
      <AppHeader
        title={'Schemas'}
        handleBack={handleBackPress}
        hideBackAction={isBackAction}
        showDrawer={isBackAction}
      />
      <Surface style={[styles.container, { flex: 1 }]} elevation={0}>
        <View style={{ marginHorizontal: scale(10) }}>
          <SearchBar
            placeholder="Search Schemas"
            onChangeText={(text) => setSearchQuery(text)}
            value={searchQuery}
            style={styles.searchBar}
            inputStyle={{ fontFamily: fontFamily.regular }}
            theme={paperTheme}
            mode="bar"
            onSubmitEditing={() => console.log('Search submitted:', searchQuery)}
          />
        </View>

        <Animated.FlatList
          data={schemas.filter((schema) =>
            schema.schemaName.toLowerCase().includes(searchQuery.toLowerCase())
          )}
          keyExtractor={keyExtractor}
          contentContainerStyle={[
            styles.contentContainer,
            { paddingBottom: insets.bottom + spacing.xl8 },
          ]}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            !loading ? (
              <Surface style={styles.emptyStateContainer}>
                <EmptyState onRefresh={onRefresh} data={'Schemas'} />
              </Surface>
            ) : null
          }
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[paperTheme.colors.primary]}
              tintColor={paperTheme.colors.primary}
              titleColor={paperTheme.colors.primary}
            />
          }
          bounces={false}
          renderItem={({ item }) => (
            <Animated.View>
              {/* <Card
                style={[
                  styles.schemaCard,
                  {
                    borderBottomWidth: 3,
                    borderBottomColor: paperTheme.colors.secondary,
                  },
                ]}
                mode="outlined"
                // elevation={2}
                theme={{
                  ...paperTheme,
                  colors: {
                    ...paperTheme.colors,
                    surface: paperTheme.colors.elevation.level1,
                  },
                }}>
                <TouchableRipple
                  onPress={() => handleSchemaPress(item)}
                  style={{borderRadius: 12}}>
                  <>
                    <Card.Title
                      title={item.schemaName}
                      subtitle={item.id}
                      titleStyle={[
                        styles.schemaName,
                        {color: paperTheme.colors.onSurface},
                      ]}
                      subtitleStyle={[
                        styles.schemaId,
                        {color: paperTheme.colors.onSurfaceVariant},
                      ]}
                      left={() => (
                        <View style={styles.iconContainer}>
                          {item.schemaLogo ? (
                            <Avatar.Image
                              source={{uri: item.schemaLogo}}
                              size={48}
                              style={{
                                backgroundColor:
                                  paperTheme.colors.surfaceVariant,
                              }}
                            />
                          ) : (
                            <Avatar.Icon
                              icon="briefcase-outline"
                              size={48}
                              color={paperTheme.colors.primary}
                              style={{
                                backgroundColor:
                                  paperTheme.colors.surfaceVariant,
                              }}
                            />
                          )}
                        </View>
                      )}
                    />
                    <Card.Actions
                      style={[
                        styles.cardFooter,
                        {borderTopColor: paperTheme.colors.outlineVariant},
                      ]}>
                      <Surface
                        style={[
                          styles.statusBadge,
                          {
                            backgroundColor:
                              item.statusName === 'Active'
                                ? paperTheme.colors.primaryContainer
                                : paperTheme.colors.errorContainer,
                          },
                        ]}
                        elevation={0}
                        theme={paperTheme}>
                        <Text
                          style={[
                            styles.statusText,
                            {
                              color:
                                item.statusName === 'Active'
                                  ? paperTheme.colors.onPrimaryContainer
                                  : paperTheme.colors.onErrorContainer,
                            },
                          ]}>
                          {item.statusName}
                        </Text>
                      </Surface>

                      <IconButton
                        icon={isFavorite(item.id) ? 'star' : 'star-outline'}
                        iconColor={
                          isFavorite(item.id)
                            ? paperTheme.colors.primary
                            : paperTheme.colors.outline
                        }
                        size={24}
                        onPress={() => toggleFavorite(item.id)}
                      />
                    </Card.Actions>
                  </>
                </TouchableRipple>
              </Card> */}

              <MenuCards
                item={item}
                title={item?.schemaLabel || item.schemaName}
                subTitle={item.schemaRoleName}
                statusText={item.statusName}
                onCardPress={handleSchemaPress}
                isFavorite={() => isFavorite(item.id)}
                toggleFavorite={() => toggleFavorite(item.id)}
              />
            </Animated.View>
          )}
        />
      </Surface>
    </SafeContainerView>
  );
};

export default SchemasScreen;
