import React, { useCallback, useState } from 'react';
import { View, RefreshControl, Image } from 'react-native';
import { NavigationProp, useNavigation, useRoute } from '@react-navigation/native';
import Animated, { useAnimatedScrollHandler } from 'react-native-reanimated';

// Custom hooks
import { useSolutionAppsData } from './SolutionApps.hooks';
import { useTheme } from '../../../../Common/Theme/hooks/useTheme';

// React Native Paper Components
import {
  Appbar,
  Card,
  Text,
  Searchbar,
  Surface,
  TouchableRipple,
  Button,
  IconButton,
  Avatar,
  useTheme as usePaperTheme,
  Snackbar,
} from 'react-native-paper';

// Components
import SafeContainerView from '../../../../Components/SafeContainer/index';
import SkeletonLoader from '../../components/SkeletonLoader';
import EmptyState from '../../components/EmptyState';

// Styles and types
import { createStyles } from './SolutionApps.styles';
import { SolutionAppItem } from './SolutionApps.types';
import { RootStackParamList } from '../../../../Common/Routes/StackTypes';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { RightDrawer } from '../../components/RightDrawer';
import AsyncStorage from '@react-native-async-storage/async-storage';
import MyHeader from '../../../../Components/UI/Appbar';
import AppHeader from '../../../../Components/AppHeader';
import SearchBar from '../../../../Components/SearchInput';
interface SolutionProps {
  navigation: NavigationProp<RootStackParamList>;
  route: any;
}
import { Dimensions } from 'react-native';
import { useDispatch } from 'react-redux';
import { setAppData } from '../../../../State/Slices/InitialNavigationSlices/AppData';
import { scale } from 'react-native-size-matters';
import FastImage from 'react-native-fast-image';

const SCREEN_WIDTH = Dimensions.get('window').width;
const cardColumnPadding = 16;
const cardSpacing = 12;
const cardWidth = (SCREEN_WIDTH - cardColumnPadding * 1.6 - cardSpacing * 2) / 3;
const SolutionApps: React.FC<SolutionProps> = ({ route }) => {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const [searchQuery, setSearchQuery] = useState('');
  const { solution, schema, isBackAction } = route.params;

  const { solutionApps, loading, error, refreshing, onRefresh, isFavorite, toggleFavorite } =
    useSolutionAppsData(solution, schema);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedApp, setSelectedApp] = useState<SolutionAppItem | undefined>(undefined);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const theme = useTheme();
  const paperTheme = usePaperTheme();
  const { colors, spacing, fontFamily, fontSize, shadow } = theme;
  const insets = useSafeAreaInsets();
  // Create styles using theme values
  const styles = createStyles(colors, spacing, fontFamily, fontSize, shadow, paperTheme);
  const dispatch = useDispatch();
  // Handle back button press
  const handleBackPress = () => {
    if (navigation.goBack) {
      navigation.goBack();
    }
  };

  // Handle workspace selection
  const handleAppPress = useCallback(
    async (app: SolutionAppItem) => {
      await AsyncStorage.setItem('appData', JSON.stringify(app));
      let data = await AsyncStorage.getAllKeys();
      // Special handling for Sales Management app
      if (
        app.appName === 'Sales Management' ||
        app.appId === '32660522-38a1-40d4-bcf5-a9fd03381f40'
      ) {
        console.log('sales data', app);
        dispatch(setAppData(app));
        navigation.navigate('SalesManagementStack', { appData: app });
        return;
      } else if (
        app.appId == 'ac55b899-df3b-4bdd-a7b2-963b00459332' ||
        app.appName === 'TrackAndTrace'
      ) {
        console.log('i am in shipment screen', app);
        dispatch(setAppData(app));
        navigation.navigate('TrackAndTrace', { appData: app });
      } else if (app.appId === '73037db4-1465-4283-8c29-a3826466f1b8' || app.appName === 'Leads') {
        // console.log("LeadsStack>> appdata",app)
        dispatch(setAppData(app));
        navigation.navigate('LeadsStack', { appData: app });
        // navigation.navigate('Attendance', { appData: app });
      } else if (
        app.appId === '8abf7e29-f073-477a-abf2-ad664f228c23' ||
        app.appName === 'Customer'
      ) {
        // console.log('customer data', app);
        dispatch(setAppData(app));
        navigation.navigate('CustomerStack', {
          screen: 'CustomerDashboard', // Explicitly navigate to CustomerDashboard
          params: { appData: app },
        });
      } else {
        // setSnackbarMessage(`${app?.appName} is yet to be released`);
        // setSnackbarVisible(true);

        // snackbarMessage
        navigation.navigate('Attendance');
        // navigation.navigate('drawer', {appData: app});
      }

      // Default navigation for other apps
    },
    [navigation]
  );

  // Scroll handler for future animations
  const scrollHandler = useAnimatedScrollHandler((event) => {
    // You can use this for scroll-based animations
    // const scrollY = event.contentOffset.y;
  });

  const handleCloseDrawer = useCallback(() => {
    setDrawerVisible(false);
  }, []);

  if (error) {
    return (
      <SafeContainerView backgroundColor={paperTheme.colors.background}>
        <AppHeader title="Apps" handleBack={handleBackPress} hideBackAction={isBackAction} />
        <Surface style={[styles.container, { flex: 1 }]} elevation={0}>
          <Surface style={styles.errorContainer} elevation={1}>
            <Text style={[styles.errorText, { color: paperTheme.colors.error }]}>{error}</Text>
            <Button
              mode="contained"
              onPress={onRefresh}
              style={{ marginTop: spacing.m }}
              buttonColor={paperTheme.colors.primary}
            >
              Retry
            </Button>
          </Surface>
        </Surface>
      </SafeContainerView>
    );
  }

  if (loading && solutionApps.length === 0) {
    return (
      <SafeContainerView backgroundColor={paperTheme.colors.background}>
        <Surface style={[styles.container, { flex: 1 }]} elevation={0}>
          <AppHeader title="Apps" handleBack={handleBackPress} hideBackAction={isBackAction} />
          <View style={{ padding: spacing.m }}>
            <SkeletonLoader count={3} />
          </View>
        </Surface>
      </SafeContainerView>
    );
  }

  return (
    <SafeContainerView backgroundColor={paperTheme.colors.background}>
      <AppHeader title="Apps" handleBack={handleBackPress} hideBackAction={isBackAction} />
      <View style={[{ flex: 1 }]}>
        <View style={{ marginHorizontal: scale(10) }}>
          <SearchBar
            placeholder="Search Apps"
            onChangeText={(text) => setSearchQuery(text)}
            value={searchQuery}
            style={styles.searchBar}
            inputStyle={{ fontFamily: fontFamily.regular }}
            theme={paperTheme}
            mode="bar"
            onSubmitEditing={() => console.log('Search submitted:', searchQuery)}
          />
        </View>
        {/* Solution Apps list */}
        <Animated.FlatList
          data={solutionApps.filter((app) =>
            app.appName.toLowerCase().includes(searchQuery?.toLowerCase())
          )}
          keyExtractor={(item) => item.appId}
          contentContainerStyle={[
            styles.contentContainer,
            {
              paddingBottom: insets.bottom,
              // paddingHorizontal: spacing.m,
              // alignItems: 'center',
            },
          ]}
          showsVerticalScrollIndicator={false}
          onScroll={scrollHandler}
          numColumns={3}
          columnWrapperStyle={styles.columnWrapper}
          refreshControl={
            <RefreshControl
              refreshing={loading}
              onRefresh={onRefresh}
              colors={[paperTheme.colors.primary]}
              tintColor={paperTheme.colors.primary}
              titleColor={paperTheme.colors.primary}
            />
          }
          bounces={false}
          ListEmptyComponent={
            !loading ? (
              <Surface style={styles.emptyStateContainer}>
                <EmptyState onRefresh={onRefresh} data={'Solution Apps'} />
              </Surface>
            ) : null
          }
          renderItem={({ item }) => (
            <Animated.View>
              <TouchableRipple
                style={[styles.appCard, { width: cardWidth }]}
                onPress={() => handleAppPress(item)}
              >
                <View style={styles.cardContent}>
                  {item.logo ? (
                    <FastImage
                      source={{ uri: item.logo }}
                      style={styles.appIcon}
                      resizeMode="contain"
                    />
                  ) : (
                    <Avatar.Icon
                      icon="briefcase-outline"
                      size={50}
                      color={paperTheme.colors.primary}
                      style={{
                        backgroundColor: paperTheme.colors.surfaceVariant,
                        marginBottom: spacing.s,
                      }}
                    />
                  )}
                  <Text
                    style={[styles.appTitle, { color: paperTheme.colors.onSurface }]}
                    variant="semiBold"
                  >
                    {item.appName == 'TrackAndTrace ' ? 'Track & Trace' : item.appName}
                  </Text>
                </View>
              </TouchableRipple>
            </Animated.View>
          )}
        />
        <RightDrawer visible={drawerVisible} onClose={handleCloseDrawer} app={selectedApp} />
      </View>
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => {
          setSnackbarVisible(false);
        }}
        duration={2000}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeContainerView>
  );
};

export default SolutionApps;
