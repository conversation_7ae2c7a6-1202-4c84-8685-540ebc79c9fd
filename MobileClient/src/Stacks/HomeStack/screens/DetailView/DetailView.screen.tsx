import React, {useState, useCallback} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {createDetailViewStyles} from './DetailView.styles';
import {useTheme} from '../../../../Common/Theme/hooks/useTheme';
import Icon from 'react-native-vector-icons/Feather';
import {SafeAreaView} from 'react-native-safe-area-context';
import {scale} from 'react-native-size-matters'; // Adjust path as needed
import SafeContainerView from '../../../../Components/SafeContainer/index';
import {useRoute} from '@react-navigation/native';
import Header from '../../components/Header/Header';
type DetailViewProps = {
  data: any;
  onSave: (data: any) => void;
  onCancel: () => void;
  navigation?: any;
};

const DetailView: React.FC<DetailViewProps> = ({
  onSave,
  onCancel,
  navigation,
}) => {
  const {params} = useRoute();
  const data = params.data;
  console.log('params', params);
  const theme = useTheme();
  const {colors, spacing, fontFamily, fontSize, shadow, borderRadius} = theme;
  const styles = createDetailViewStyles(
    colors,
    spacing,
    fontFamily,
    fontSize,
    shadow,
    borderRadius,
  );

  // Initialize form state with data from json_data
  const [formData, setFormData] = useState({
    name: data.json_data.name || '',
    full_email: data.json_data.full_email || '',
    full_age: data.json_data.full_age?.toString() || '',
    logo: data.json_data.logo || '',
    status: data.status || 'Active',
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleStatusChange = (status: string) => {
    setFormData(prev => ({
      ...prev,
      status,
    }));
  };

  const handleSave = () => {
    // Convert age back to number
    const updatedData = {
      ...data,
      json_data: {
        ...formData,
        full_age: parseInt(formData.full_age, 10) || 0,
      },
      name: formData.name,
      full_email: formData.full_email,
      full_age: parseInt(formData.full_age, 10) || 0,
      logo: formData.logo,
      status: formData.status,
    };
    // onSave(updatedData);
  };

  const renderHeader = () => (
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: spacing.m,
        paddingVertical: spacing.m,
        backgroundColor: colors.primary,
      }}>
      <Text
        style={{
          fontFamily: fontFamily.semiBold,
          fontSize: fontSize.xxl,
          color: colors.background,
        }}>
        Detail View
      </Text>
      <TouchableOpacity
        onPress={onCancel}
        style={{
          width: scale(24),
          height: scale(24),
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Icon name="x" size={scale(24)} color={colors.background} />
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeContainerView backgroundColor={colors.primary}>
      <KeyboardAvoidingView
        style={{flex: 1}}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
        {/* {renderHeader()} */}
        <Header headerTitle="DetailView" />
        <ScrollView style={styles.container} bounces={false}>
          <View style={styles.formContainer}>
            <View style={styles.avatarContainer}>
              {/* <View style={styles.avatar}>
                {formData.logo ? (
                  <Text>Logo</Text>
                ) : (
                  <Text style={styles.avatarText}>{formData.name.charAt(0).toUpperCase()}</Text>
                )}
              </View> */}
              {/* <TouchableOpacity>
                <Text style={{ color: colors.primary, fontFamily: fontFamily.medium }}>Change Photo</Text>
              </TouchableOpacity> */}
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Name</Text>
              <TextInput
                style={styles.input}
                value={formData.name}
                onChangeText={text => handleInputChange('name', text)}
                placeholder="Enter name"
                placeholderTextColor={colors.textSecondary}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Email</Text>
              <TextInput
                style={styles.input}
                value={formData.full_email}
                onChangeText={text => handleInputChange('full_email', text)}
                placeholder="Enter email"
                placeholderTextColor={colors.textSecondary}
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Age</Text>
              <TextInput
                style={styles.input}
                value={formData.full_age}
                onChangeText={text => handleInputChange('full_age', text)}
                placeholder="Enter age"
                placeholderTextColor={colors.textSecondary}
                keyboardType="numeric"
              />
            </View>

            <View style={styles.statusContainer}>
              <Text style={styles.statusLabel}>Status</Text>
              <TouchableOpacity
                style={styles.statusOption}
                onPress={() => handleStatusChange('Active')}>
                <View style={styles.radioOuter}>
                  {formData.status === 'Active' && (
                    <View style={styles.radioInner} />
                  )}
                </View>
                <Text style={styles.radioLabel}>Active</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.statusOption}
                onPress={() => handleStatusChange('Inactive')}>
                <View style={styles.radioOuter}>
                  {formData.status === 'Inactive' && (
                    <View style={styles.radioInner} />
                  )}
                </View>
                <Text style={styles.radioLabel}>Inactive</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.buttonsContainer}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={onCancel}>
                <Text style={[styles.buttonText, styles.cancelButtonText]}>
                  Cancel
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, styles.saveButton]}
                onPress={handleSave}>
                <Text style={[styles.buttonText, styles.saveButtonText]}>
                  Save
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeContainerView>
  );
};

export {DetailView};
