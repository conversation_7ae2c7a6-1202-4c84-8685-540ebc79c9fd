import React, { useCallback, useState } from 'react';
import { View, RefreshControl, StatusBar, TouchableWithoutFeedback } from 'react-native';
import { useNavigation, NavigationProp, DrawerActions } from '@react-navigation/native';
import Animated, { useAnimatedScrollHandler } from 'react-native-reanimated';
import {
  Appbar,
  Card,
  Text,
 
  Surface,
  Button,
  IconButton,
  Avatar,
  useTheme as usePaperTheme,
} from 'react-native-paper';
import { useWorkspaceData } from './Workspace.hooks';
import { useTheme } from '../../../../Common/Theme/hooks/useTheme';
import MyHeader from '../../../../Components/UI/Appbar';
import SafeContainerView from '../../../../Components/SafeContainer/index';
import SkeletonLoader from '../../components/SkeletonLoader';
import EmptyState from '../../components/EmptyState';
import { createStyles } from './Workspace.styles';
import { WorkspaceItem } from './Workspace.types';
import { RootStackParamList } from '../../../../Common/Routes/StackTypes';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import CustomInput from '../../../../Components/UI/TextInput';
import SearchBar from '../../../../Components/SearchInput';
import AppHeader from '../../../../Components/AppHeader';
import MenuCards from '../../components/MenuCards';

const Workspace: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { workspaces, isLoading, error, refreshData, toggleFavorite, isFavorite } =
    useWorkspaceData();

  const insets = useSafeAreaInsets();
  const theme = useTheme();
  const paperTheme = usePaperTheme();
  const { colors, spacing, fontFamily, fontSize, shadow, isDark } = theme;

  const styles = createStyles(
    { ...colors, white: colors.background },
    spacing,
    fontFamily,
    fontSize,
    shadow,
    paperTheme
  );

  const handleWorkspacePress = useCallback(
    async (workspace: WorkspaceItem) => {
      AsyncStorage.setItem('selectedWorkspace', JSON.stringify(workspace));
      navigation.navigate('SchemasScreen', {
        workspace: {
          id: workspace.id,
          name: workspace.name || workspace.workspaceName,
        },
        navigation: null,
        route: null,
      });
    },
    [navigation]
  );

  const handleBackPress = () => {
    if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      navigation.dispatch(DrawerActions.openDrawer());
    }
  };

  const scrollHandler = useAnimatedScrollHandler(() => {});

  if (error) {
    return (
      <SafeContainerView backgroundColor={paperTheme.colors.primary}>
        <AppHeader title={'Workspaces'} hideBackAction  />
        <Surface style={[styles.container, { padding: spacing.m }]} elevation={0}>
          <Surface style={styles.errorContainer} elevation={1}>
            <Text style={[styles.errorText, { color: paperTheme.colors.error }]}>{error}</Text>
            <Button
              mode="contained"
              onPress={refreshData}
              style={{ marginTop: spacing.m }}
              buttonColor={paperTheme.colors.primary}
            >
              Retry
            </Button>
          </Surface>
        </Surface>
      </SafeContainerView>
    );
  }

  if (isLoading && workspaces.length === 0) {
    return (
      <SafeContainerView backgroundColor={paperTheme.colors.primary}>
        <Appbar.Header mode="small">
          <Appbar.Content
            title={'Workspaces'}
            titleStyle={{
              fontSize: moderateScale(20),
              marginLeft: moderateScale(10),
            }}
          />
        </Appbar.Header>

        <View style={[styles.container, { padding: spacing.m }]}>
          <SkeletonLoader count={3} />
        </View>
      </SafeContainerView>
    );
  }

  return (
    <SafeContainerView backgroundColor={paperTheme.colors.background}>
      {/* <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor={paperTheme.colors.elevation.level2}
      /> */}

      <AppHeader title={'Workspaces'} handleBack={handleBackPress} hideBackAction={true}/>
      <Surface style={styles.container} elevation={0}>
        <View style={{paddingHorizontal:scale(10)}}>
          <SearchBar
            placeholder="Search Workspaces"
            onChangeText={(text) => setSearchQuery(text)}
            value={searchQuery}
            style={styles.searchBar}
            inputStyle={{ fontFamily: fontFamily.regular }}
            theme={paperTheme}
            mode="bar"
            onSubmitEditing={() => console.log('Search submitted:', searchQuery)}
          />
        </View>
        <Animated.FlatList
          data={workspaces.filter((workspace) =>
            workspace.workspaceName.toLowerCase().includes(searchQuery.toLowerCase())
          )}
          keyExtractor={(item) => item.id}
          contentContainerStyle={[
            styles.contentContainer,
            { paddingBottom: insets.bottom + spacing.xl8,paddingTop:verticalScale(3) },
          ]}
          showsVerticalScrollIndicator={false}
          onScroll={scrollHandler}
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={refreshData}
              colors={[paperTheme.colors.primary]}
              tintColor={paperTheme.colors.primary}
              titleColor={paperTheme.colors.primary}
            />
          }
          bounces={false}
          ListEmptyComponent={
            !isLoading ? (
              <Surface style={styles.emptyStateContainer}>
                <EmptyState onRefresh={refreshData} data={'Workspaces'} />
              </Surface>
            ) : null
          }
          renderItem={({ item }) => (
            <Animated.View>
              {/* <Card
                style={[styles.workspaceCard]}
                // mode="outlined"
                elevation={2}
                theme={{
                  ...paperTheme,
                  colors: {
                    ...paperTheme.colors,
                    surface: paperTheme.colors.elevation.level1,
                  },
                }}
                onPress={() => handleWorkspacePress(item)}
              >
                <Card.Title
                  title={item.workspaceName}
                  subtitle={item.workspaceDescription}
                  titleStyle={[styles.workspaceTitle]}
                  subtitleStyle={[styles.workspaceDescription]}
                  left={() => (
                    <View style={styles.iconContainer}>
                      {item.workspaceLogo ? (
                        <Avatar.Image
                          source={{ uri: item.workspaceLogo }}
                          size={24}
                          style={{
                            backgroundColor: paperTheme.colors.surfaceVariant,
                          }}
                        />
                      ) : (
                        <Avatar.Icon
                          icon="briefcase-outline"
                          size={24}
                          color={paperTheme.colors.primary}
                          style={{
                            backgroundColor: paperTheme.colors.surfaceVariant,
                          }}
                        />
                      )}
                    </View>
                  )}
                />
                <Card.Actions
                  style={[styles.cardFooter, { borderTopColor: paperTheme.colors.outlineVariant }]}
                >
                  <Surface
                    style={[
                      styles.statusBadge,
                      {
                        backgroundColor:
                          item.statusName === 'Active'
                            ? paperTheme.colors.primaryContainer
                            : paperTheme.colors.errorContainer,
                      },
                    ]}
                    elevation={0}
                    theme={paperTheme}
                  >
                    <Text
                      style={[
                        styles.statusText,
                        {
                          color:
                            item.statusName === 'Active'
                              ? paperTheme.colors.onPrimaryContainer
                              : paperTheme.colors.onErrorContainer,
                        },
                      ]}
                      variant="medium"
                    >
                      {item.statusName}
                    </Text>
                  </Surface>

                  <IconButton
                    icon={isFavorite(item.id) ? 'star' : 'star-outline'}
                    iconColor={
                      isFavorite(item.id) ? paperTheme.colors.primary : paperTheme.colors.outline
                    }
                    size={18}
                    onPress={() => toggleFavorite(item.id)}
                  />
                </Card.Actions>
              </Card> */}
              {/* <TouchableWithoutFeedback
                style={[styles.workspaceCard]}
                onPress={() => handleWorkspacePress(item)}
              >
                <View style={{flexDirection:"row",justifyContent:"space-between",borderWidth:1, borderRadius:8,marginVertical:verticalScale(6),padding:10,borderColor:"#ddd",backgroundColor:"#fff"}}>
                  <View style={styles.cardContainer}>
                    <View></View>
                    <View style={styles.iconContainer}>
                      {item.workspaceLogo ? (
                        <Avatar.Image
                          source={{ uri: item.workspaceLogo }}
                          size={moderateScale(40)}
                          style={{
                            backgroundColor: paperTheme.colors.surfaceVariant,
                          }}
                        />
                      ) : (
                        <Avatar.Icon
                          icon="briefcase-outline"
                          size={24}
                          color={paperTheme.colors.primary}
                          style={{
                            backgroundColor: paperTheme.colors.surfaceVariant,
                          }}
                        />
                      )}
                    </View>
                    <View>
                      <Text variant="semiBold" style={styles.cardText}>
                        {item.workspaceName}
                      </Text>
                      <Text variant="medium" style={styles.cardText}>
                        {item.workspaceDescription}
                      </Text>

                      <Text
                        style={[styles.cardText,
                          {
                            color:
                              item.statusName === 'Active'
                                ? paperTheme.colors.onPrimaryContainer
                                : paperTheme.colors.onErrorContainer,
                          },
                        ]}
                        variant="regular"
                      >
                        {item.statusName}
                      </Text>
                    </View>
                  </View>
                  <IconButton
                    icon={isFavorite(item.id) ? 'star' : 'star-outline'}
                    iconColor={
                      isFavorite(item.id) ? paperTheme.colors.primary : paperTheme.colors.outline
                    }
                    size={18}
                    onPress={() => toggleFavorite(item.id)}
                  />
                </View>
              </TouchableWithoutFeedback> */}
              <MenuCards
                item={item}
                isFavorite={() => isFavorite(item.id)}
                toggleFavorite={() => toggleFavorite(item.id)}
                title={item.workspaceName}
                subTitle={item.workspaceDescription}
                onCardPress={() => {
                  handleWorkspacePress(item);
                }}
              />
            </Animated.View>
          )}
        />
      </Surface>
    </SafeContainerView>
  );
};

export default Workspace;
