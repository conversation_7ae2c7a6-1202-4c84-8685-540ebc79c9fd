import React, { useCallback, useState } from 'react';
import { View, RefreshControl } from 'react-native';
import { NavigationProp } from '@react-navigation/native';
import Animated, { useAnimatedScrollHandler } from 'react-native-reanimated';
import MenuCards from '../../components/MenuCards';
// Custom hooks
import { useSolutionsData } from './Solutions.hooks';
import { useTheme } from '../../../../Common/Theme/hooks/useTheme';

// React Native Paper Components
import {
  Appbar,
  Card,
  Text,
  Searchbar,
  Surface,
  TouchableRipple,
  Button,
  IconButton,
  Avatar,
  useTheme as usePaperTheme,
  Menu,
} from 'react-native-paper';

// Components
import SafeContainerView from '../../../../Components/SafeContainer/index';
import SkeletonLoader from '../../components/SkeletonLoader';
import EmptyState from '../../components/EmptyState';

// Styles and types
import { createStyles } from './Solutions.styles';
import { SolutionItem } from './Solutions.types';
import { RootStackParamList } from '../../../../Common/Routes/StackTypes';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import MyHeader from '../../../../Components/UI/Appbar';
import { useNavigationHook } from '../../../../Hooks/Navigation/useNavigationHook';
import AppHeader from '../../../../Components/AppHeader';
import SearchBar from '../../../../Components/SearchInput';
import { Layers } from 'lucide-react-native';
import { moderateScale, scale } from 'react-native-size-matters';

interface SolutionProps {
  route: any;
}

const Solutions: React.FC<SolutionProps> = ({ route }) => {
  const { schema, workspace, isBackAction } = route.params;
  const {
    solutions,
    loading,
    error,
    refreshing,
    onRefresh,
    fetchSolutions,
    isFavorite,
    toggleFavorite,
  } = useSolutionsData(schema, workspace);
  const { navigation } = useNavigationHook();
  const [searchQuery, setSearchQuery] = useState('');
  const theme = useTheme();
  const paperTheme = usePaperTheme();
  const { colors, spacing, fontFamily, fontSize, shadow } = theme;
  const insets = useSafeAreaInsets();
  // Create styles using theme values
  const styles = createStyles(colors, spacing, fontFamily, fontSize, shadow, paperTheme);

  // Handle back button press
  const handleBackPress = () => {
    if (navigation.goBack) {
      navigation.goBack();
    }
  };

  // Handle workspace selection
  const handleWorkspacePress = useCallback(
    (solution: SolutionItem) => {
      // Navigate to the workspace dashboard or specific screen
      navigation.navigate('SolutionApps', { solution, schema });
    },
    [navigation]
  );

  // Scroll handler for future animations
  const scrollHandler = useAnimatedScrollHandler((event) => {
    // You can use this for scroll-based animations
    // const scrollY = event.contentOffset.y;
  });

  // Render error state
  if (error) {
    return (
      <SafeContainerView backgroundColor={paperTheme.colors.background}>
        <Surface style={[styles.container, { flex: 1 }]} elevation={0}>
          <MyHeader title={'Solutions'} handleBack={handleBackPress} isBackAction={isBackAction} />
          <Surface style={styles.errorContainer} elevation={1}>
            <Text style={[styles.errorText, { color: paperTheme.colors.error }]}>{error}</Text>
            <Button
              mode="contained"
              onPress={onRefresh}
              style={{ marginTop: spacing.m }}
              buttonColor={paperTheme.colors.primary}
            >
              Retry
            </Button>
          </Surface>
        </Surface>
      </SafeContainerView>
    );
  }

  // Render loading state using skeleton loaders
  if (loading && solutions.length === 0) {
    return (
      <SafeContainerView backgroundColor={paperTheme.colors.background}>
        <Surface style={[styles.container, { flex: 1 }]} elevation={0}>
          <AppHeader title={'Solutions'} handleBack={handleBackPress} hideBackAction={isBackAction} />
          <View style={{ padding: spacing.m }}>
            <SkeletonLoader count={3} />
          </View>
        </Surface>
      </SafeContainerView>
    );
  }

  return (
    <SafeContainerView backgroundColor={paperTheme.colors.background}>
      <AppHeader title={'Solutions'} handleBack={handleBackPress} hideBackAction={isBackAction} />
      <View style={[styles.container, { flex: 1 }]}>
        <View style={{marginHorizontal:scale(10)}}>
          <SearchBar
            placeholder="Search Solutions"
            onChangeText={(text) => setSearchQuery(text)}
            value={searchQuery}
            style={styles.searchBar}
            inputStyle={{ fontFamily: fontFamily.regular }}
            theme={paperTheme}
            mode="bar"
            onSubmitEditing={() => console.log('Search submitted:', searchQuery)}
          />
        </View>
        {/* Solutions list */}
        <Animated.FlatList
          data={solutions.filter((solution) =>
            solution.solutionName.toLowerCase().includes(searchQuery.toLowerCase())
          )}
          keyExtractor={(item) => item.solutionId}
          contentContainerStyle={[
            styles.contentContainer,
            { paddingBottom: insets.bottom + spacing.xl8 },
          ]}
          showsVerticalScrollIndicator={false}
          onScroll={scrollHandler}
          refreshControl={
            <RefreshControl
              refreshing={loading}
              onRefresh={onRefresh}
              colors={[paperTheme.colors.primary]}
              tintColor={paperTheme.colors.primary}
              titleColor={paperTheme.colors.primary}
            />
          }
          bounces={false}
          ListEmptyComponent={
            !loading ? (
              <Surface style={styles.emptyStateContainer}>
                <EmptyState onRefresh={onRefresh} data={'Solutions'} />
              </Surface>
            ) : null
          }
          renderItem={({ item }) => (
            <Animated.View>
              {/* <Card
                style={[
                  styles.solutionCard,
                  {
                    borderBottomWidth: 3,
                    borderBottomColor: paperTheme.colors.secondary,
                  },
                ]}
                mode="outlined"
                theme={{
                  ...paperTheme,
                  colors: {
                    ...paperTheme.colors,
                    surface: paperTheme.colors.elevation.level1,
                  },
                }}
              >
                <TouchableRipple
                  onPress={() => handleWorkspacePress(item)}
                  style={{ borderRadius: 12 }}
                >
                  <>
                    <Card.Title
                      title={item.solutionName}
                      subtitle={item.solutionId}
                      titleStyle={[styles.workspaceTitle, { color: paperTheme.colors.onSurface }]}
                      subtitleStyle={[
                        styles.workspaceDescription,
                        { color: paperTheme.colors.onSurfaceVariant },
                      ]}
                      left={() => (
                        <View style={styles.iconContainer}>
                          <Avatar.Icon
                            icon="briefcase-outline"
                            size={48}
                            color={paperTheme.colors.primary}
                            style={{
                              backgroundColor: paperTheme.colors.surfaceVariant,
                            }}
                          />
                        </View>
                      )}
                    />
                    <Card.Actions
                      style={[
                        styles.cardFooter,
                        { borderTopColor: paperTheme.colors.outlineVariant },
                      ]}
                    >
                      <Surface
                        style={[
                          styles.statusBadge,
                          {
                            backgroundColor: paperTheme.colors.primaryContainer,
                          },
                        ]}
                        elevation={0}
                        theme={paperTheme}
                      >
                        <Text
                          style={[
                            styles.statusText,
                            {
                              color: paperTheme.colors.onPrimaryContainer,
                            },
                          ]}
                        >
                          {item.solutionRoleName}
                        </Text>
                      </Surface>

                      <IconButton
                        icon={isFavorite(item.solutionId) ? 'star' : 'star-outline'}
                        iconColor={
                          isFavorite(item.solutionId)
                            ? paperTheme.colors.primary
                            : paperTheme.colors.outline
                        }
                        size={24}
                        onPress={() => toggleFavorite(item.solutionId)}
                      />
                    </Card.Actions>
                  </>
                </TouchableRipple>
              </Card> */}
              {/* {console.log("iteme",item)} */}
              <MenuCards
                item={item}
                title={item.solutionName}
                subTitle={item.solutionRoleName}
                icon={<Layers size={moderateScale(18)} color="#9333EA" />}
                iconBgColor="#F3E8FF"
                statusText={'Active'}
                onCardPress={() => handleWorkspacePress(item)}
                isFavorite={() => isFavorite(item.id)}
                toggleFavorite={() => toggleFavorite(item.id)}
              />
            </Animated.View>
          )}
        />
      </View>
    </SafeContainerView>
  );
};

export default Solutions;
