import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  TextInput,
  FlatList,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  Text,
  Avatar,
  Surface,
  List,
  Divider,
  useTheme as usePaperTheme,
  IconButton,
  Switch,
  Portal,
  Modal,
  Appbar,
  Button,
} from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import { setThemeMode, setThemeColor } from '../../../../State/Slices/Theme/ThemeSlice';
import { useTheme } from '../../../../Common/Theme/hooks/useTheme';
import { moderateScale } from 'react-native-size-matters';
import SafeContainerView from '../../../../Components/SafeContainer';
import {
  <PERSON>,
  Sun,
  <PERSON><PERSON>he<PERSON>,
  <PERSON>2,
  <PERSON>r,
  ChevronRight,
  <PERSON>,
  LogOut,
  Building2,
} from 'lucide-react-native';
import { StatusBar } from 'react-native';
import { setTimezone } from '../../../../State/Slices/TimezoneSlice';
import moment from 'moment-timezone';
import { store } from '../../../../State/Store';
import DynamicUI from '../../../../Utils/WorkFlowRender';
import AppHeader from '../../../../Components/AppHeader';
import useFetchPageData from '../../../../Utils/useFetchPageData';
import ApiClient from '../../../../Common/API/ApiClient';
import { ENDPOINTS } from '../../../../Common/API/ApiEndpoints';
import { getFCMDeviceToken, getSchemaName, getUserId } from '../../../../Common/Utils/Storage';
import LocationAddressSearch from '../../../../Components/LocationAddressSearch';

type ColorOption = 'zinc' | 'red' | 'orange' | 'yellow' | 'green' | 'violet' | 'rose' | 'blue';
type ModeOption = 'light' | 'dark';

interface RootState {
  theme: {
    color: ColorOption;
    mode: ModeOption;
  };
  userId: string;
  UserInfo: any;
  timezone: string;
}
const AVATAR_SIZE = moderateScale(72);
const data = [];
const ProfileScreen = () => {
  const navigation = useNavigation<any>();
  const paperTheme = usePaperTheme();
  const { isDark } = useTheme();
  const dispatch = useDispatch();
  const themeMode = useSelector((state: RootState) => state.theme.mode);
  const userDetails = useSelector((state: RootState) => state.UserInfo.userDetailsInRedux);
  const { uiData, loading, error } = useFetchPageData('dbbc33bd-f655-4825-8005-4e7ec90775ee');
  const timezone = useSelector((state: RootState) => state.timezone);
  const [userName, setUserName] = useState('User');
  const [email, setEmail] = useState('<EMAIL>');
  // State for theme modal visibility
  const [themeModalVisible, setThemeModalVisible] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredTimeZones, setFilteredTimeZones] = useState<string[]>([]);

  useEffect(() => {
    const timeZones = moment.tz.names();
    const filteredTimeZones = timeZones.filter((zone) =>
      zone.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredTimeZones(filteredTimeZones);
  }, [searchTerm]);

  // Update user data when Redux state changes
  useEffect(() => {
    if (userDetails) {
      const userData = userDetails;
      // console.log("userDetails>>>", userDetails.email)
      if (userDetails.name) setUserName(userDetails.name);
      if (userDetails.email) setEmail(userDetails.email);
    }
  }, [userDetails]);

  const handleLogout = async () => {
    try {
      const persisted = await AsyncStorage.getItem('persist:root');
      // console.log("persisted data in profile", persisted)
      //  try {
      //       const schemaName = await getSchemaName();
      //       const userId = await getUserId();
      //       const fcmDeviceToken = await getFCMDeviceToken();

      //       const payload = {
      //         schemaName: 'c1s1_billing',

      //         user_id: userId,

      //         device_id: fcmDeviceToken || '',
      //       };
      //       const response = await ApiClient.post(ENDPOINTS.AUTH.LOGOUT, payload);
      //       console.log("logout response ", response);
      //     } catch (error) {
      //       console.log("error in logout api", error)
      //     }
      if (persisted) {
        const parsed = JSON.parse(persisted);
        delete parsed.userId;
        delete parsed.UserInfo;
        await AsyncStorage.setItem('persist:root', JSON.stringify(parsed));
      }
      store.dispatch({ type: 'RESET_STORE' });
      const keysToRemove = [
        'idToken',
        'accessToken',
        'userRole',
        'userId',
        'subdomain',
        'authScheme',
      ];
      // console.log('logout screen:');
      await Promise.all(keysToRemove.map(key => AsyncStorage.removeItem(key)));
      const authScheme = await AsyncStorage.getItem('authScheme');
      // navigation.reset({
      //   index: 0,
      //   routes: [{name: 'Initial'}],
      // });
      navigation.reset({
        index: 1,
        routes: [{ name: 'DomainEntry' }, { name: 'SignIn' }],
      });
    } catch (error) {
      console.log('Error during logout:', error);
    }
  };

  const toggleTheme = () => {
    dispatch(setThemeMode(isDark ? 'light' : 'dark'));
  };

  const showThemeModal = () => setThemeModalVisible(true);
  const hideThemeModal = () => setThemeModalVisible(false);

  // Get first letter for avatar
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((part) => part.charAt(0))
      .join('')
      .toUpperCase();
  };

  const [timezoneModalVisible, setTimezoneModalVisible] = useState(false);
  const hideTimezoneModal = () => setTimezoneModalVisible(false);
  return (
    <View style={styles.wrapper}>
      <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} />
      <AppHeader title="Profile" hideBackAction={true} />
      <View style={styles.profileInfoSection}>
        <View style={styles.avatarBorder}>
          <View style={styles.avatarCircle}>
            <Text variant="titleLarge" style={styles.avatarInitials}>
              {getInitials(userName).length>2?getInitials(userName).slice(0,2):getInitials(userName)}
            </Text>
          </View>
        </View>
        <View style={styles.profileText}>
          <Text variant="titleMedium" style={styles.nameText}>{userName}</Text>
          <Text variant="bodyMedium" style={styles.emailText}>{email}</Text>
        </View>
        <TouchableOpacity style={styles.timeZoneCard} onPress={() => setTimezoneModalVisible(true)}>
          <Globe2 size={18} color="#999" style={{ marginRight: 8 }} />
          <View style={{ flex: 1 }}>
            <Text variant="bodyMedium" style={styles.tzLabel}>Time Zone</Text>
            <Text variant="bodyMedium" style={styles.tzValue}>{timezone || 'Asia/Calcutta +05:30'}</Text>
          </View>
        </TouchableOpacity>
      </View>
      <ScrollView contentContainerStyle={{ flexGrow: 1 }} bounces={false}>
        <View style={styles.sectionMenu}>
          <TouchableOpacity style={styles.menuRow} onPress={() => navigation.navigate('FollowersDetailsScreen')}>
            <User size={20} color="#555" style={{ marginRight: 16 }} />
            <Text variant="medium" style={styles.menuText}>
              My Profile
            </Text>
            <View style={styles.spacer} />
            <ChevronRight size={18} color="#999" />
          </TouchableOpacity>
          {/* <TouchableOpacity
            style={styles.menuRow}
            onPress={() => navigation.navigate('OrganizationProfile')}
          >
            <Building2 size={20} color="#555" style={{ marginRight: 16 }} />
            <Text variant="medium" style={styles.menuText}>
              Organization Profile
            </Text>
            <View style={styles.spacer} />
            <ChevronRight size={18} color="#999" />
          </TouchableOpacity> */}
          {/* <TouchableOpacity
            style={styles.menuRow}
            onPress={() => navigation.navigate('WorkFlowTasks')}
          >
            <ListChecks size={20} color="#555" style={{ marginRight: 16 }} />
            <Text variant="bodyMedium" style={styles.menuText}>Workflow Tasks</Text>
            <View style={styles.spacer} />
            <ChevronRight size={18} color="#999" />
          </TouchableOpacity> */}
        </View>
        {/* <View style={styles.addressSearchContainer}>
          <LocationAddressSearch
            placeholder="Search for your address"
            onPlaceSelect={(place) => {}}
            onAddressChange={(address) => {}}
          />
        </View> */}
        <TouchableOpacity style={styles.logoutRow} onPress={handleLogout}>
          <LogOut size={18} color="#D00" style={{ marginRight: 10 }} />
          <Text variant="bodyMedium" style={styles.logoutText}>Log Out</Text>
        </TouchableOpacity>
      </ScrollView>
      <Modal
        visible={timezoneModalVisible}
        onDismiss={hideTimezoneModal}
        contentContainerStyle={[styles.modalContainer, { backgroundColor: paperTheme.colors.surface }]}
      >
        <Text variant="titleMedium" style={styles.themeSectionTitle}>Timezone</Text>
        <View>
          <TextInput
            placeholder="Search timezone"
            value={searchTerm}
            onChangeText={setSearchTerm}
            placeholderTextColor={paperTheme.colors.onSurface}
            autoCapitalize="none"
            autoCorrect={false}
            autoComplete="off"
            autoFocus={true}
            style={styles.textInput}
          />
        </View>
        <View style={{ maxHeight: 200 }}>
          <FlatList
            data={filteredTimeZones}
            keyExtractor={(zone) => zone}
            bounces={false}
            renderItem={({ item: zone }) => (
              <TouchableOpacity
                key={zone}
                onPress={() => {
                  dispatch(setTimezone(zone));
                  hideTimezoneModal();
                  setSearchTerm('');
                }}
              >
                <View style={timezone === zone ? styles.selectedTimezoneItem : styles.timezoneItem}>
                  <Text
                    style={{ color: timezone === zone ? paperTheme.colors.onPrimary : paperTheme.colors.onSurface }}
                    variant="bodyMedium"
                  >
                    {zone}
                  </Text>
                </View>
              </TouchableOpacity>
            )}
          />
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    backgroundColor: '#fff',
    flex: 1,
    // paddingTop: Platform.OS === 'android' ? moderateScale(8) : moderateScale(32),
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: moderateScale(12),
    paddingTop: moderateScale(6),
    paddingBottom: moderateScale(8),
  },
  headerIconBtn: {
    padding: 6,
    alignSelf: 'flex-end',
  },
  profileInfoSection: {
    alignItems: 'center',
    marginVertical: moderateScale(16),
    marginBottom: moderateScale(8),
  },
  avatarBorder: {
    borderWidth: 2,
    borderColor: '#eee',
    borderRadius: AVATAR_SIZE / 2 + 4,
    padding: 2,
    marginBottom: 4,
  },
  avatarCircle: {
    backgroundColor: '#e0e6f0',
    width: AVATAR_SIZE,
    height: AVATAR_SIZE,
    borderRadius: AVATAR_SIZE / 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarInitials: {
    color: '#344266',
    fontWeight: 'bold',
    letterSpacing: 1,
  },
  profileText: {
    alignItems: 'center',
    marginTop: moderateScale(4),
  },
  nameText: {
    color: '#111',
    fontSize: 18,
    marginBottom: 1,
    fontWeight: '600',
  },
  emailText: {
    color: '#888',
    fontSize: 14,
  },
  timeZoneCard: {
    backgroundColor: '#fafbfc',
    borderRadius: 10,
    padding: 14,
    marginHorizontal: 20,
    marginVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 1,
    shadowColor: '#f2f2f2',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.07,
    shadowRadius: 2,
  },
  tzLabel: {
    color: '#767676',
    fontSize: 13,
    fontWeight: '600',
    marginBottom: 2,
  },
  tzValue: {
    color: '#111',
    fontSize: 14,
    marginTop: 2,
  },
  sectionMenu: {
    // marginTop: moderateScale(16),
    marginBottom: moderateScale(8),
  },
  menuRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 22,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f1f1',
    backgroundColor: 'transparent',
  },
  menuText: {
    fontSize: moderateScale(13),
    color: '#1a1a1a',
    flexShrink: 1,
  },
  spacer: {
    flex: 1,
  },
  logoutRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 22,
    paddingVertical: 16,
    // marginTop: 18
    marginBottom: 12,
  },
  logoutText: {
    color: '#E50000',
    fontSize: moderateScale(13),
    // fontWeight: '600',
  },
  versionRow: {
    alignItems: 'center',
    position: 'absolute',
    bottom: Platform.OS === 'android' ? 8 : 16,
    width: '100%',
  },
  versionText: {
    color: '#999',
    fontSize: 13,
    letterSpacing: 0.1,
    marginTop: 10,
  },
  modalContainer: {
    marginHorizontal: 20,
    marginVertical: 40,
    borderRadius: 12,
    padding: 20,
  },
  themeSectionTitle: {
    marginBottom: 16,
    // fontWeight: '600',
  },
  themeOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  themeOption: {
    width: '48%',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    paddingVertical: 12,
    alignItems: 'center',
    position: 'relative',
  },
  selectedThemeOption: {
    borderColor: '#3B82F6',
    borderWidth: 2,
  },
  themePreview: {
    width: moderateScale(50),
    height: moderateScale(50),
    borderRadius: moderateScale(25),
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  lightThemePreview: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  darkThemePreview: {
    backgroundColor: '#1F2937',
  },
  themeOptionText: {
    fontSize: moderateScale(14),
  },
  selectedIndicator: {
    position: 'absolute',
    top: moderateScale(4),
    right: moderateScale(4),
  },
  colorOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  colorOption: {
    width: '25%',
    alignItems: 'center',
    marginBottom: moderateScale(16),
  },
  colorPreview: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(20),
    marginBottom: moderateScale(4),
    justifyContent: 'center',
    alignItems: 'center',
  },
  colorName: {
    fontSize: moderateScale(12),
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 12,
    color: 'black',
  },
  timezoneItem: {
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  addressSearchContainer: {
  paddingHorizontal:10,  
  },
  selectedTimezoneItem: {
    padding: 16,
    marginVertical: 4,
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#3B82F6',
    borderRadius: 8,
  },
});

export default ProfileScreen;
