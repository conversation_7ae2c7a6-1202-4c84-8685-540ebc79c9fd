import React from 'react';
import {View, Text, Image, ScrollView} from 'react-native';
import {CloseIcon as UserIcon} from '../../../../../assets/SVG/FeedSvg';
import { ImageGallery } from '../Feed/FeedItemModular/ImageGallery';
interface PostPreviewProps {
  content: string;
  images: string[];
  profile: any;
}

export const PostPreview: React.FC<PostPreviewProps> = ({
  content,
  images,
  profile,
}) => {
  // console.log("images in postpreview",images)
  return (
    <ScrollView className="flex-1 bg-appGray-50" bounces={false}>
      <View className="m-4 bg-white rounded-xl shadow-sm">
        <View className="p-4">
          {/* Author Header */}
          <View className="flex-row items-center space-x-3 mb-4">
            <View className="shadow-sm rounded-full">
              <Image
                source={{uri: profile?.profile_pic}}
                className="w-12 h-12 rounded-full border-2 border-gray-50"
              />
            </View>
            <View>
              <Text className="text-base text-gray-800 font-montserrat-semibold">
                {profile?.user_name}
              </Text>
              <Text className="font-montserrat text-xs text-appGray-500">
                  Just now
                </Text>
            </View>
          </View>

          {/* Content */}
          <Text className="font-montserrat text-appGray-900 mb-4">
            {content}
          </Text>



          {/* Images */}
          {images.length > 0 && 
          // (
          //   <View
          //     className={`gap-1 ${
          //       images.length === 1 ? '' : 'flex-row flex-wrap'
          //     }`}>
          //     {images.map((uri, index) => (
          //       <Image
          //         key={index}
          //         source={{uri}}
          //         className={
          //           images.length === 1
          //             ? 'w-full h-[200] rounded-lg'
          //             : 'w-[48%] h-[150] rounded-lg'
          //         }
          //         resizeMode="cover"
          //       />
          //     ))}
          //   </View>
          // )
          <ImageGallery 
          images = {images}
          />
          }
        </View>
      </View>
    </ScrollView>
  );
};
