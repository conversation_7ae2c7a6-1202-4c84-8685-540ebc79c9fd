// import React from 'react';
// import {
//   View,
//   Text,
//   TextInput,
//   Image,
//   TouchableOpacity,
//   ScrollView,
//   Keyboard,
// } from 'react-native';
// import {
//   CommentIcon as UserIcon,
//   LikeIcon as ImageIcon,
//   CloseIcon,
// } from '../../../../../assets/SVG/FeedSvg';
// import {handleSelectImage} from '../../../ChatStack/ChatRoom/Room/ChatHandlers';
// interface CreatePostFormProps {
//   content: string;
//   setContent: (text: string) => void;
//   images: string[];
//   setImages: (images: string[]) => void;
// }

// export const CreatePostForm: React.FC<CreatePostFormProps> = ({
//   content,
//   setContent,
//   images,
//   setImages,
// }) => {
//   const imageHandler = async () => {
//     const imageResponse = await handleSelectImage();
//     setImages(imageResponse?.map(img => (img.uri)));
//     console.log('image response', imageResponse);
//   };

//   return (
//     <View className="flex-1">
//       <ScrollView className="flex-1 bg-white">
//         <View className="p-4">
//           <View className="flex-row space-x-3">
//             <View className="w-12 h-12 rounded-full bg-appGray-100 items-center justify-center">
//               <UserIcon width={24} height={24} color="#666666" />
//             </View>
//             <View className="flex-1">
//               <TextInput
//                 multiline
//                 placeholder="What's on your mind?"
//                 value={content}
//                 onChangeText={setContent}
//                 className="text-base text-appGray-900 font-montserrat min-h-[120px]"
//                 textAlignVertical="top"
//               />
//             </View>
//           </View>

//           {images.length > 0 && (
//             <View className="flex-row flex-wrap mt-4 gap-2">
//               {images.map((uri, index) => (
//                 <View key={index} className="relative">
//                   <Image source={{uri}} className="w-24 h-24 rounded-lg" />
//                   <TouchableOpacity
//                     className="absolute top-1 right-1 w-6 h-6 bg-black/50 rounded-full items-center justify-center"
//                     onPress={() => {
//                       setImages(images.filter((_, i) => i !== index));
//                     }}>
//                     <CloseIcon width={13} height={13} color="#FFFFFF" />
//                   </TouchableOpacity>
//                 </View>
//               ))}
//             </View>
//           )}
//         </View>
//       </ScrollView>

//       {/* Bottom Action Bar - Fixed at bottom */}
//       <View className="absolute bottom-4 right-7  bg-white  border-appGray-200">
//         <TouchableOpacity
//           className="flex-row items-center space-x-2 p-4 active:bg-appGray-50"
//           onPress={() => {
//             // Keyboard.dismiss();
//             // Handle image selection
//             imageHandler();
//             console.log('Select images');
//           }}>
//           <ImageIcon width={24} height={24} color="#666666" />
//           <Text className="text-appGray-700 font-montserrat">
//             Add Photos/Videos
//           </Text>
//         </TouchableOpacity>
//       </View>
//     </View>
//   );
// };
import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  Image,
  TouchableOpacity,
  ScrollView,
  Keyboard,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {
  CommentIcon as UserIcon,
  CloseIcon,
  ImageIcon,
  PlusIcon as AttachMore,
} from '../../../../../assets/SVG/FeedSvg';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {ImagePreviewModal} from './ImagePreviewModal';
import {useProfile} from '../../Hooks/Feed/useProfile';
import {useSelector} from 'react-redux';
import {handleSelectImage} from '../../../ChatStack/ChatRoom/Room/ChatHandlers';
interface ImageResponse {
  base64: string;
  fileName: string;
  fileSize: number;
  type: string;
  uri: string;
}

interface ImageWithStatus {
  uri: string;
  progress: number;
  status: 'uploading' | 'complete' | 'error';
}

interface CreatePostFormProps {
  content: string;
  setContent: (text: string) => void;
  rawImages: ImageResponse;
  setRawImages: React.Dispatch<React.SetStateAction<ImageResponse[]>>;
  profile: any;
}

export const CreatePostForm: React.FC<CreatePostFormProps> = ({
  content,
  setContent,
  setRawImages,
  rawImages,
  profile,
}) => {
  const user = useSelector((state: any) => state.userId);
  const [isPreviewModalVisible, setIsPreviewModalVisible] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const handleImagePress = (index: number) => {
    console.log("i am in image preview")
    setSelectedImageIndex(index);
    setIsPreviewModalVisible(true);
  };

  const imageHandler = async () => {
    const imageResponse = await handleSelectImage();
    // console.log('imageResponse', imageResponse);
    Array.isArray(imageResponse) &&
      imageResponse.length > 0 &&
      setRawImages(imageResponse);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      style={{flex: 1}}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 63 : 30}>
      <View className="flex-1">
        <ScrollView
          className="flex-1 bg-white"
          keyboardShouldPersistTaps="handled"
          bounces={false}>
          <View className="p-4">
            <View className="flex-row space-x-3">
              <View className="shadow-sm rounded-full">
                <Image
                  source={{uri: profile?.profile_pic}}
                  className="w-12 h-12 rounded-full border-2 border-gray-50"
                />
              </View>
              <View className="flex-1">
                <Text className="text-base text-gray-800 font-montserrat-semibold mb-2">
                  {profile?.user_name}
                </Text>
              </View>
            </View>
            <View className="flex-1 mt-2">
              <TextInput
                autoFocus={true}
                multiline
                placeholder="What's on your mind?"
                value={content}
                onChangeText={setContent}
                className="text-base text-appGray-900 font-montserrat min-h-[100px]"
                textAlignVertical="top"
              />
            </View>

            {Array.isArray(rawImages) && rawImages.length > 0 && (
              <View className="relative">
                {/* Edit and Remove Icons */}
                <TouchableOpacity 
                hitSlop={10}
                onPress={() => handleImagePress(0)}>
                  <View className="absolute right-11 bottom-3 rounded-full bg-appGray-400 p-2">
                    <Icon name="pencil-outline" size={17} color="#ffffff" />
                  </View>
                </TouchableOpacity>

                <TouchableOpacity 
                   hitSlop={10}
                onPress={() => setRawImages([])}>
                  <View className="absolute right-0 bottom-3 rounded-full bg-appGray-400 p-2">
                    <Icon name="close" size={17} color="#ffffff" />
                  </View>
                </TouchableOpacity>

                {/* Image Grid */}
                <View className="border border-appGray-200 p-2 rounded-md">
                  {rawImages?.length === 1 ? (
                    <View style={{flex : 1}}>
                      <TouchableOpacity
                        key={1}
                        onPress={() => handleImagePress(0)}
                        className="relative">
                        <Image
                          source={{uri: rawImages[0]?.uri}}
                          className="w-full aspect-square rounded-lg"
                        />
                      </TouchableOpacity>
                    </View>
                  ): (
                    <View className="flex-row flex-wrap mt-4 gap-2 justify-between">
                    {rawImages.map((image, index) => (
                      <TouchableOpacity
                        key={index}
                        onPress={() => handleImagePress(index)}
                        className="w-[47%] relative">
                        <Image
                          source={{uri: image.uri}}
                          className="w-full aspect-square rounded-lg"
                        />
                      </TouchableOpacity>
                    ))}
                  </View>
                  )}
           
                </View>
              </View>
            )}
          </View>
        </ScrollView>

        <View className="absolute bottom-5 right-8   border-appGray-200 flex-row items-center">
          <TouchableOpacity

            className="space-x-2 p-3 mr-2 active:bg-appGray-50 rounded-full bg-appSystem-link "
            onPress={() => {
              Keyboard.dismiss();
              imageHandler();
            }}>
            <ImageIcon width={22} height={22} color="#fff" />
          </TouchableOpacity>
          {/* <TouchableOpacity
            className="space-x-2 p-3 active:bg-appGray-50 rounded-full bg-appSystem-link"
            onPress={() => {
              Keyboard.dismiss();
            }}>
            <AttachMore width={18} height={18} color="#fff" />
          </TouchableOpacity> */}
        </View>
        {isPreviewModalVisible && (
          <ImagePreviewModal
            images={rawImages}
            initialIndex={selectedImageIndex}
            onClose={() => setIsPreviewModalVisible(false)}
            onConfirm={updatedImages => {
              setRawImages(updatedImages);
              setIsPreviewModalVisible(false);
            }}
          />
        )}
      </View>
    </KeyboardAvoidingView>
  );
};
