
import React, {useState, useRef, useCallback} from 'react';
import {
  View,
  Modal,
  Text,
  Dimensions,
  Image,
  ScrollView,
  NativeSyntheticEvent,
  NativeScrollEvent,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import {
  CloseIcon,
  CloseIcon as TrashIcon,
} from '../../../../../assets/SVG/FeedSvg';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { CustomDialog } from '../Common/DailogBox';
import FastImage from 'react-native-fast-image';
// Custom Dialog Component


interface ImageResponse {
  base64: string;
  fileName: string;
  fileSize: number;
  type: string;
  uri: string;
}

interface ImagePreviewModalProps {
  images: any;
  initialIndex?: number;
  onClose: () => void;
  onConfirm?: (images: ImageResponse) => void;
  showActionButtons?: boolean;
}

export const ImagePreviewModal: React.FC<ImagePreviewModalProps> = ({
  images: initialImages,
  initialIndex = 0,
  onClose,
  onConfirm,
  showActionButtons = true,
}) => {
  const [images, setImages] = useState(initialImages);
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [indexToDelete, setIndexToDelete] = useState<number | null>(null);
  const scrollViewRef = useRef<ScrollView>(null);
  const {width: screenWidth} = Dimensions.get('window');

  const handleScroll = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const position = event.nativeEvent.contentOffset.x;
      const index = Math.round(position / screenWidth);
      setCurrentIndex(index);
    },
    [screenWidth],
  );

  const handleDeletePress = useCallback((index: number) => {
    setIndexToDelete(index);
    setDialogVisible(true);
  }, []);

  const handleDelete = useCallback(() => {
    if (indexToDelete === null) return;
    
    setImages(prevImages => {
      const newImages = prevImages.filter(
        (_, index) => index !== indexToDelete,
      );

      if (newImages.length === 0) {
        onClose();
        return prevImages;
      }

      let newIndex = currentIndex;
      if (indexToDelete === prevImages.length - 1) {
        newIndex = Math.max(currentIndex - 1, 0);
      } else {
        newIndex = Math.min(currentIndex, newImages.length - 1);
      }

      setTimeout(() => {
        scrollViewRef.current?.scrollTo({
          x: newIndex * screenWidth,
          animated: false,
        });
        setCurrentIndex(newIndex);
      }, 0);

      return newImages;
    });

    setDialogVisible(false);
    setIndexToDelete(null);
  }, [currentIndex, indexToDelete, onClose, screenWidth]);

  const handleConfirm = useCallback(() => {
    onConfirm(images);
    onClose();
  }, [images, onConfirm, onClose]);

  return (
    <Modal visible={true} animationType="fade" presentationStyle="fullScreen"
    onRequestClose={() => {
        onClose();
      }}
    >
      <SafeAreaView className="flex-1 bg-appSystem-white">
        {/* Header */}
        <View className="flex-row justify-between items-center px-4 py-3">
          <TouchableOpacity
            onPress={onClose}
            className="w-10 h-10 items-center justify-center rounded-full">
            <CloseIcon width={24} height={24} color="#000" />
          </TouchableOpacity>
          {showActionButtons && (
            <TouchableOpacity
              onPress={handleConfirm}
              className="px-6 py-2 rounded-full bg-appSystem-link">
              <Text className="font-montserrat-semibold  text-white">
                Confirm
              </Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Image Swiper */}
        <ScrollView
          bounces={false}
          ref={scrollViewRef}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={handleScroll}
          scrollEventThrottle={16}
          className="flex-1"
          contentOffset={{x: initialIndex * screenWidth, y: 0}}>
          {images.map((image, index) => (
            <View
              key={index}
              style={{width: screenWidth}}
              className="items-center justify-center">
              <FastImage
                source={{uri: image.uri}}
                style={{width: screenWidth, height: screenWidth}}
                resizeMode="contain"
              />
            </View>
          ))}
        </ScrollView>

        {/* Bottom Controls */}
        <View className="px-4 py-3">
          <View className="flex-row justify-between items-center">
            <Text className="font-montserrat text-black">
              {currentIndex + 1} / {images.length}
            </Text>
            {showActionButtons && images?.length !== 1 && (
              <TouchableOpacity
                onPress={() => handleDeletePress(currentIndex)}
                className="w-10 h-10 items-center justify-center rounded-full bg-red-500">
                <Icon size={25} name="delete" color="#fff" />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Custom Delete Confirmation Dialog */}
        <CustomDialog
          visible={dialogVisible}
          onDismiss={() => setDialogVisible(false)}
          onConfirm={handleDelete}
          title="Delete Image"
          message="Are you sure you want to delete this image?"
          confirmText="Delete"
          cancelText="Cancel"
          confirmColor="#FF0000"
        />
      </SafeAreaView>
    </Modal>
  );
};
