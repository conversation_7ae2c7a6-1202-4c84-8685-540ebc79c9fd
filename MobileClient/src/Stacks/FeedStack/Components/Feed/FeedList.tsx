import React, {useMemo, useCallback} from 'react';
import {View, FlatList, StyleSheet} from 'react-native';
import FeedItem from './FeedItem';
import {Posts} from './FeedTypes';

interface FeedListProps {
  posts: Posts[];
  onCommentPress?: () => void;
}

const FeedList: React.FC<FeedListProps> = ({posts, onCommentPress}) => {
  const memoizedPosts = useMemo(() => posts, [posts]);
  const renderItem = useCallback(
    ({item}: {item: Posts}) => <FeedItem item={item} onCommentPress={onCommentPress} />,
    [onCommentPress]
  );

  return (
    <View className="flex-1 bg-appSystem-white">
      <FlatList
        data={memoizedPosts}
        keyExtractor={(item) => item.post_id}
        renderItem={renderItem}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{paddingBottom: 100}}
        initialNumToRender={10}
        maxToRenderPerBatch={5}
        windowSize={10}
        removeClippedSubviews
        keyboardShouldPersistTaps="always"
        bounces={false}
      />
    </View>
  );
};

export default React.memo(FeedList);
