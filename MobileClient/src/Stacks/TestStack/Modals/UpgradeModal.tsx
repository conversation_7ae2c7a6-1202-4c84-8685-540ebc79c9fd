import React from 'react';
import { View, Text, Modal, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { fontFamily } from '../../../Common/Theme/typography';
import { moderateScale, verticalScale, scale } from 'react-native-size-matters';

const { width, height } = Dimensions.get('window');

interface UpgradeModalProps {
  visible: boolean;
  onClose: () => void;
  onUpgrade: () => void;
  title?: string;
  description?: string;
  buttonText?: string;
}

const UpgradeModal: React.FC<UpgradeModalProps> = ({
  visible,
  onClose,
  onUpgrade,
  title = 'Upgrade Required',
  description = 'Please update our app for an improved experience!. This version is no longer supported.',
  buttonText = 'Upgrade Now',
}) => {
  return (
    <Modal visible={visible} transparent={true} animationType="fade" onRequestClose={onClose}>
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          {/* Close Button */}
          {/* <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity> */}

          {/* Phone Mockup with App Screenshot */}
          <View style={styles.phoneContainer}>
            <View style={styles.phoneFrame}>
              <View style={styles.phoneScreen}>
                <View style={styles.appHeader}>
                  <View style={styles.redBar} />
                  <View style={styles.contentArea}>
                    <View style={styles.listItem} />
                    <View style={styles.listItem} />
                    <View style={styles.listItem} />
                  </View>
                </View>
                {/* Green checkmark overlay */}
                {/* <View style={styles.checkmarkContainer}>
                  <Text style={styles.checkmark}>✓</Text>
                </View> */}
              </View>
            </View>
          </View>

          {/* Title */}
          <Text style={styles.title}>{title}</Text>

          {/* Description */}
          <Text style={styles.description}>{description}</Text>

          {/* Upgrade Button */}
          <TouchableOpacity style={styles.upgradeButton} onPress={onUpgrade}>
            <Text style={styles.upgradeButtonText}>{buttonText}</Text>
          </TouchableOpacity>
          <View style={{ marginTop: verticalScale(20) }}>
            <Text style={[styles.description, { fontFamily: fontFamily.semiBold, marginBottom: 0, }]}>Later</Text>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: width * 0.90,
    maxWidth: scale(350),
    backgroundColor: 'white',
    borderRadius: moderateScale(20),
    padding: moderateScale(30),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: verticalScale(10),
    },
    shadowOpacity: 0.25,
    shadowRadius: moderateScale(20),
    elevation: 10,
  },
  closeButton: {
    position: 'absolute',
    top: verticalScale(15),
    right: scale(20),
    width: scale(30),
    height: scale(30),
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  closeButtonText: {
    fontSize: moderateScale(24),
    color: '#666',
    fontFamily: fontFamily.light,
  },
  iconContainer: {
    marginBottom: verticalScale(20),
  },
  starIcon: {
    fontSize: moderateScale(24),
  },
  phoneContainer: {
    marginBottom: verticalScale(15),
  },
  phoneFrame: {
    width: scale(120),
    height: verticalScale(200),
    backgroundColor: '#f8f9fa',
    borderRadius: moderateScale(20),
    padding: moderateScale(8),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: verticalScale(4),
    },
    shadowOpacity: 0.1,
    shadowRadius: moderateScale(8),
    elevation: 4,
  },
  phoneScreen: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: moderateScale(15),
    overflow: 'hidden',
    position: 'relative',
  },
  appHeader: {
    flex: 1,
  },
  redBar: {
    height: verticalScale(25),
    backgroundColor: '#3355ff',
    width: '100%',
  },
  contentArea: {
    flex: 1,
    padding: moderateScale(12),
    justifyContent: 'flex-start',
  },
  listItem: {
    height: verticalScale(8),
    backgroundColor: '#f1f3f4',
    marginBottom: verticalScale(8),
    borderRadius: moderateScale(2),
  },
  checkmarkContainer: {
    position: 'absolute',
    bottom: verticalScale(15),
    right: scale(15),
    width: scale(24),
    height: scale(24),
    backgroundColor: '#27ae60',
    borderRadius: moderateScale(12),
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmark: {
    color: 'white',
    fontSize: moderateScale(14),
    fontFamily: fontFamily.bold,
  },
  title: {
    fontSize: moderateScale(22),
    fontFamily: fontFamily.bold,
    color: '#2c3e50',
    marginBottom: verticalScale(10),
    textAlign: 'center',
  },
  description: {
    fontSize: moderateScale(14),
    fontFamily: fontFamily.regular,
    color: '#7f8c8d',
    textAlign: 'center',
    lineHeight: verticalScale(22),
    marginBottom: verticalScale(25),
    paddingHorizontal: scale(10),
  },
  upgradeButton: {
    width: '100%',
    // flex : 1,
    backgroundColor: '#3355ff',
    paddingVertical: verticalScale(12),
    borderRadius: moderateScale(12),
    alignItems: 'center',
    shadowColor: '#3355ff',
    shadowOffset: {
      width: 0,
      height: verticalScale(4),
    },
    shadowOpacity: 0.3,
    shadowRadius: moderateScale(8),
    elevation: 6,
  },
  upgradeButtonText: {
    color: 'white',
    fontSize: moderateScale(16),
    fontFamily: fontFamily.semiBold,
  },
});

export default UpgradeModal;