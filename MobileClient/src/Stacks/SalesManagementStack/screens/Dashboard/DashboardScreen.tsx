import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text, Appbar, Card, ActivityIndicator, ProgressBar, useTheme } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit';
import { Dimensions } from 'react-native';

const screenWidth = Dimensions.get('window').width;

const MetricCard = ({ title, value, change, icon, color = '#3B6FF6' }) => {
  const theme = useTheme();
  const isPositive = change && change.includes('+');
  
  return (
    <Card style={styles.metricCard}>
      <Card.Content>
        <View style={styles.metricHeader}>
          <View style={[styles.iconContainer, { backgroundColor: color + '20' }]}>
            {icon}
          </View>
          {change && (
            <Text style={{ 
              color: isPositive ? '#4CAF50' : '#F44336',
              fontWeight: 'bold'
            }}>
              {change}
            </Text>
          )}
        </View>
        
        <Text style={styles.metricValue}>{value}</Text>
        <Text style={styles.metricTitle}>{title}</Text>
      </Card.Content>
    </Card>
  );
};

const DashboardScreen = () => {
  const [loading, setLoading] = useState(true);
  const theme = useTheme();
  const navigation = useNavigation();
  
  // Mock data
  const [metrics] = useState({
    prospects: '1,122',
    opportunities: '978',
    customers: '688',
    leadConversionRate: '68%',
    averageDealSize: '$12,500',
    totalRevenue: '$1.25M',
    timeTracking: {
      thisWeek: 652,
      thisMonth: 2845,
      weeklyProgress: 0.75
    }
  });
  
  useEffect(() => {
    // In a real app, you would fetch dashboard data here
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);
  
  // Chart configuration
  const chartConfig = {
    backgroundGradientFrom: '#fff',
    backgroundGradientTo: '#fff',
    color: (opacity = 1) => `rgba(59, 111, 246, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
    strokeWidth: 2,
    decimalPlaces: 0,
    style: {
      borderRadius: 16,
    },
  };
  
  // Mock chart data
  const salesData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
    datasets: [
      {
        data: [350000, 420000, 380000, 510000, 590000],
      },
    ],
  };
  
  const leadSourceData = {
    labels: ['Website', 'Referral', 'Events', 'Social', 'Direct'],
    data: [0.35, 0.25, 0.18, 0.15, 0.07],
  };
  
  const pipelineData = {
    labels: ['New', 'Qualified', 'Proposal', 'Negotiation', 'Closed'],
    datasets: [
      {
        data: [58, 42, 34, 27, 21],
        color: (opacity = 1) => `rgba(59, 111, 246, ${opacity})`,
      },
    ],
  };
  
  return (
    <View style={styles.container}>
      <Appbar.Header style={{ backgroundColor: theme.colors.primary }}>
        <Appbar.BackAction onPress={() => navigation.goBack()} color={theme.colors.onPrimary} />
        <Appbar.Content title="Sales Dashboard" color={theme.colors.onPrimary} />
      </Appbar.Header>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={{ marginTop: 10 }}>Loading dashboard data...</Text>
        </View>
      ) : (
        <ScrollView contentContainerStyle={styles.scrollContent} bounces={false}>
          {/* Key Metrics */}
          <Text style={styles.sectionTitle}>Key Metrics</Text>
          <View style={styles.metricsGrid}>
            <MetricCard 
              title="Total Prospects" 
              value={metrics.prospects} 
              change="+12%" 
              icon={<Text style={styles.metricIcon}>👥</Text>} 
              color="#3B6FF6"
            />
            <MetricCard 
              title="Total Opportunities" 
              value={metrics.opportunities} 
              change="+0.53%" 
              icon={<Text style={styles.metricIcon}>📊</Text>}
              color="#FF9800"
            />
            <MetricCard 
              title="Total Customers" 
              value={metrics.customers} 
              change="-0.9%" 
              icon={<Text style={styles.metricIcon}>🏢</Text>}
              color="#4CAF50"
            />
          </View>
          
          {/* Performance Metrics */}
          <Text style={styles.sectionTitle}>Performance</Text>
          <View style={styles.metricsGrid}>
            <MetricCard 
              title="Lead Conversion Rate" 
              value={metrics.leadConversionRate} 
              change="+5.2%" 
              icon={<Text style={styles.metricIcon}>📈</Text>}
              color="#9C27B0"
            />
            <MetricCard 
              title="Avg. Deal Size" 
              value={metrics.averageDealSize} 
              icon={<Text style={styles.metricIcon}>💰</Text>}
              color="#E91E63"
            />
            <MetricCard 
              title="Total Revenue" 
              value={metrics.totalRevenue} 
              change="+13.8%" 
              icon={<Text style={styles.metricIcon}>💵</Text>}
              color="#009688"
            />
          </View>
          
          {/* Time Tracking Card */}
          <Card style={styles.timeCard}>
            <Card.Content>
              <Text style={styles.cardTitle}>Working Hours</Text>
              <Text style={styles.timeLabel}>This Week's Progress</Text>
              <ProgressBar 
                progress={metrics.timeTracking.weeklyProgress} 
                color={theme.colors.primary} 
                style={styles.progressBar}
              />
              <View style={styles.timeMetricsRow}>
                <View>
                  <Text style={styles.timeMetricValue}>{metrics.timeTracking.thisWeek}h</Text>
                  <Text style={styles.timeMetricLabel}>This Week</Text>
                </View>
                <View>
                  <Text style={styles.timeMetricValue}>{metrics.timeTracking.thisMonth}h</Text>
                  <Text style={styles.timeMetricLabel}>This Month</Text>
                </View>
              </View>
            </Card.Content>
          </Card>
          
          {/* Monthly Sales Chart */}
          <Card style={styles.chartCard}>
            <Card.Content>
              <Text style={styles.cardTitle}>Monthly Sales</Text>
              <LineChart
                data={salesData}
                width={screenWidth - 64}
                height={220}
                chartConfig={chartConfig}
                bezier
                style={styles.chart}
                formatYLabel={(value) => `$${parseInt(value) / 1000}k`}
              />
            </Card.Content>
          </Card>
          
          {/* Lead Source Distribution */}
          <Card style={styles.chartCard}>
            <Card.Content>
              <Text style={styles.cardTitle}>Lead Sources</Text>
              <PieChart
                data={leadSourceData.labels.map((label, index) => ({
                  name: label,
                  population: leadSourceData.data[index] * 100,
                  color: [
                    '#3B6FF6',
                    '#FF9800',
                    '#4CAF50',
                    '#E91E63',
                    '#009688',
                  ][index],
                  legendFontColor: '#7F7F7F',
                  legendFontSize: 12,
                }))}
                width={screenWidth - 64}
                height={180}
                chartConfig={chartConfig}
                accessor="population"
                backgroundColor="transparent"
                paddingLeft="15"
                style={styles.chart}
              />
            </Card.Content>
          </Card>
          
          {/* Sales Pipeline */}
          <Card style={styles.chartCard}>
            <Card.Content>
              <Text style={styles.cardTitle}>Sales Pipeline</Text>
              <BarChart
                data={pipelineData}
                width={screenWidth - 64}
                height={220}
                chartConfig={chartConfig}
                style={styles.chart}
                showValuesOnTopOfBars
              />
            </Card.Content>
          </Card>
          
          {/* Spacer at bottom */}
          <View style={{ height: 20 }} />
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContent: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginVertical: 12,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  metricCard: {
    width: '31%',
    marginBottom: 12,
    elevation: 2,
  },
  metricHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  metricIcon: {
    fontSize: 18,
  },
  metricValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  metricTitle: {
    fontSize: 12,
    opacity: 0.7,
  },
  timeCard: {
    marginBottom: 16,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  timeLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: 16,
  },
  timeMetricsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  timeMetricValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  timeMetricLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  chartCard: {
    marginBottom: 16,
    elevation: 2,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
});

export default DashboardScreen;