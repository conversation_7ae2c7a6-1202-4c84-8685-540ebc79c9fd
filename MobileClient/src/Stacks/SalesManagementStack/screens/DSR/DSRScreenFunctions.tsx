 import { useCallback } from "react";
 import {ActivityGroup} from './DSRTypes';
 import moment from "moment";

export const transformApiResponseToActivityGroups = (data: any[],allExpanded) => {
  if (!Array.isArray(data)) return [];

  const groupedByDate: Record<string, any[]> = {};
  data.forEach(activity => {
    const date = activity?.start_date_time?.split('T')[0];
    if (!date) return;
    if (!groupedByDate[date]) groupedByDate[date] = [];
    groupedByDate[date].push(activity);
  });

  return Object.keys(groupedByDate).map(date => ({
    date,
    displayDate: moment(date).isSame(moment(), 'day')
      ? 'Today'
      : moment(date).format('MMM D, YYYY').toUpperCase(),
    isToday: moment(date).isSame(moment(), 'day'),
    isExpanded: allExpanded,
    startDateTime: groupedByDate[date][0]?.start_date_time,
    endDateTime: groupedByDate[date][groupedByDate[date].length - 1]?.end_date_time,
    activities: groupedByDate[date].map((activity: any) => ({
      activity,
      id: activity.sales_activity_id ?? `temp-${Math.random().toString(36).substring(7)}`,
      companyName: activity.sales_activity_title || 'No company',
      organizationName: activity.organization_name || '',
      date,
      status: activity.sales_activity_state_name,
      timeAgo: moment(activity.start_date_time).fromNow(),
      type: activity.sales_activity_type_name,
      category: activity.lead_state_name,
      startTime: moment(activity.start_date_time).format('h:mm A'),
      endTime: moment(activity.end_date_time).format('h:mm A'),
      notes: activity.notes || '',
      outcome: activity.sales_activity_outcome_type_name || '',
      isOnline: activity.sales_activity_type_name === 'Online',
      isCompleted: ((activity?.sales_activity_state_name)?.toLowerCase()) == 'completed',
      
      location: activity.area_name || '',
      prospectType: activity.prospect_type_name || '',
      tags: Array.isArray(activity.tags) ? activity.tags : [],
      startDateTime: activity.start_date_time,
      endDateTime: activity.end_date_time,
    })),
  })).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
};
