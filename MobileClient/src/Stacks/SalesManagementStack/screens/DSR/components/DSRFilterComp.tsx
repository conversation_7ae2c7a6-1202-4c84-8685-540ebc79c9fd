import { View, StyleSheet, TouchableHighlight, TouchableOpacity, FlatList } from 'react-native';

import React, { useState, useMemo } from 'react';
import {
  useTheme,
  Text,
  Button,
  Checkbox,
  Divider,
  Surface,
  Searchbar,
  RadioButton,
  Icon,
} from 'react-native-paper';
import SearchBar from '../../../../../Components/SearchInput';
import { ScrollView } from 'react-native-gesture-handler';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { moderateScale, verticalScale } from 'react-native-size-matters';

const DSRFilterComp = ({
  salesActivityTypes = [],
  salesActivityCountries = [],
  salesActivityLeadStates = [],
  salesActivityOutcomeTypes = [],
  filterError = '',
  setSelectedFilters,
  selectedFilters,
  onApplyFilters,
  onClearFilters,
  onClose,
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const { top, bottom } = useSafeAreaInsets();
  const [activeFilterType, setActiveFilterType] = useState('salesActivityTypes');
  const [searchQuery, setSearchQuery] = useState('');

  const filterTypes = [
    {
      key: 'salesActivityTypes',
      label: 'Activity Types',
      data: salesActivityTypes,
      valueKey: 'Value',
      textKey: 'Text',
      selectType: 'singleSelect',
    },
    {
      key: 'salesActivityCountries',
      label: 'Areas',
      data: salesActivityCountries,
      valueKey: 'Value',
      textKey: 'Text',
      selectType: 'singleSelect',
    },
    {
      key: 'salesActivityLeadStates',
      label: 'Lead States',
      data: salesActivityLeadStates,
      valueKey: 'Value',
      textKey: 'Text',
      selectType: 'singleSelect',
    },
    {
      key: 'salesActivityOutcomeTypes',
      label: 'Outcome Types',
      data: salesActivityOutcomeTypes,
      valueKey: 'Value',
      textKey: 'Text',
      selectType: 'singleSelect',
    },
  ];

  const handleFilterSelection = (category, value) => {
    const currentFilter = filterTypes.find((f) => f.key === category);
    const currentSelectType = currentFilter?.selectType || 'multiSelect';

    setSelectedFilters((prev) => {
      const currentSelection = prev[category] || [];
      // console.log("prev data", prev)
      const isSelected = currentSelection.includes(value);
      if (currentSelectType === 'singleSelect') {
        return {
          ...prev,
          [category]: isSelected ? [] : [value],
        };
      } else {
        return {
          ...prev,
          [category]: isSelected
            ? currentSelection.filter((item) => item !== value)
            : [...currentSelection, value],
        };
      }
    });
  };

  const clearAllFilters = () => {
    setSelectedFilters({
      salesActivityTypes: null,
      salesActivityCountries: null,
      salesActivityLeadStates: null,
      salesActivityOutcomeTypes: null,
    });
    onClearFilters && onClearFilters();
  };

  const applyFilters = () => {
    onApplyFilters && onApplyFilters(selectedFilters);
    onClose();
  };

  const getSelectedCount = () => {
    return Object.entries(selectedFilters).reduce((sum, [key, value]) => {
      const filterType = filterTypes.find((f) => f.key === key);
      if (filterType?.selectType === 'singleSelect') {
        return sum + (value?.[0] ? 1 : 0);
      }
      return sum + (value?.length || 0);
    }, 0);
  };

  const getSelectedCountForCategory = (category) => {
    const filterType = filterTypes.find((f) => f.key === category);
    const selected = selectedFilters[category] || [];

    if (filterType?.selectType === 'singleSelect') {
      return selected[0] ? 1 : 0;
    }
    return selected.length;
  };

  const getCurrentFilterData = () => {
    const currentFilter = filterTypes.find((f) => f.key === activeFilterType);
    return currentFilter || filterTypes[0];
  };

  const searchQueryData = (value) => setSearchQuery(value);

  const getFilteredData = useMemo(() => {
    const currentFilter = getCurrentFilterData();
    if (!searchQuery.trim()) return currentFilter.data;
    return currentFilter.data.filter((item) =>
      item[currentFilter.textKey].toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [
    activeFilterType,
    searchQuery,
    salesActivityTypes,
    salesActivityCountries,
    salesActivityLeadStates,
    salesActivityOutcomeTypes,
  ]);

  const handleFilterTypeChange = (filterKey) => {
    setActiveFilterType(filterKey);
    setSearchQuery('');
  };

  const renderLeftSide = () => (
    <View style={styles.leftContainer}>
      <ScrollView showsVerticalScrollIndicator={false} style={{ flex: 1 }} bounces={false}>
        {filterTypes.map((filterType, index) => {
          const isActive = activeFilterType === filterType.key;
          const selectedCount = getSelectedCountForCategory(filterType.key);

          return (
            <View key={filterType.key}>
              <TouchableOpacity
                style={[styles.filterTypeItem, isActive && styles.activeFilterTypeItem]}
                onPress={() => handleFilterTypeChange(filterType.key)}
              >
                <View style={styles.filterTypeContent}>
                  <Text style={[styles.filterTypeText, isActive && styles.activeFilterTypeText]}>
                    {filterType.label}
                  </Text>
                  {selectedCount > 0 && (
                    <View style={styles.selectedBadge}>
                      <Text style={styles.selectedBadgeText}>{selectedCount}</Text>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
              {index < filterTypes.length - 1 && <Divider />}
            </View>
          );
        })}
      </ScrollView>
    </View>
  );

  const renderRightSide = () => {
    const currentFilter = getCurrentFilterData();
    const filteredData = getFilteredData;

    return (
      <View style={styles.rightContainer}>
        <View style={styles.rightHeader}>
          <Text variant="titleMedium" style={{ fontSize: moderateScale(13) }}>
            {currentFilter.label}
          </Text>
        </View>
        <Divider />
        {activeFilterType==='salesActivityCountries'&&<View style={{ margin: 10 }}>
          <SearchBar
            placeholder="Search"
            onChangeText={searchQueryData}
            value={searchQuery}
            
          />
        </View>}
        {/* <ScrollView style={styles.rightScrollView} contentContainerStyle={{height:300}} showsVerticalScrollIndicator={false}> */}
        <View style={{ flex: 1 }}>
          <FlatList
            bounces={false}
            data={filteredData}
            keyExtractor={(item) => String(item[currentFilter.valueKey])}
            contentContainerStyle={{
              paddingBottom: 150,
            }}
            
            renderItem={({ item }) => {
              const currentSelection = selectedFilters[currentFilter.key] || [];
              const isSelected =
                currentFilter.selectType === 'singleSelect'
                  ? currentSelection[0] === item[currentFilter.valueKey]
                  : currentSelection.includes(item[currentFilter.valueKey]);

              return (
                <TouchableOpacity
                  style={styles.filterOptionItem}
                  onPress={() =>
                    handleFilterSelection(currentFilter.key, item[currentFilter.valueKey])
                  }
                >
                  <View style={styles.checkboxRow}>
                    <Text variant="titleSmall" style={styles.checkboxLabel}>
                      {item[currentFilter.textKey]}
                    </Text>
                    {currentFilter.selectType === 'singleSelect' ? (
                      <RadioButton
                        value={item[currentFilter.valueKey]}
                        status={isSelected ? 'checked' : 'unchecked'}
                        color={theme.colors.primary}
                        onPress={() =>
                          handleFilterSelection(currentFilter.key, item[currentFilter.valueKey])
                        }
                      />
                    ) : (
                      <Checkbox
                        status={isSelected ? 'checked' : 'unchecked'}
                        color={theme.colors.primary}
                        onPress={() =>
                          handleFilterSelection(currentFilter.key, item[currentFilter.valueKey])
                        }
                      />
                    )}
                  </View>
                </TouchableOpacity>
              );
            }}
            initialNumToRender={20} // Renders 20 items initially for faster load
            maxToRenderPerBatch={20} // Renders 20 items per batch while scrolling
            onEndReachedThreshold={0.5}
            onEndReached={() => {
              // console.log('onEndReached');
            }}
          />
        </View>

        {filteredData.length === 0 && searchQuery.trim() && (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No results found for "{searchQuery}"</Text>
          </View>
        )}

        {currentFilter.data.length === 0 && (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No options available</Text>
          </View>
        )}

        {/* </ScrollView> */}
      </View>
    );
  };

  return (
    <View
      style={[
        styles.container,
        {
          paddingTop: top,
          // marginBottom: bottom,
        },
      ]}
    >
      <SafeAreaView
        style={{
          flex: 1,
        }}
      >
        <View style={styles.header}>
          <Text variant="headlineSmall" style={styles.headerTitle}>
            Filters
          </Text>
          <TouchableOpacity onPress={onClose}>
            <Icon source="close" size={30} />
          </TouchableOpacity>
        </View>

        <Divider />

        <View style={styles.mainContent}>
          {renderLeftSide()}
          <View style={styles.verticalDivider} />
          {renderRightSide()}
        </View>

        <View style={styles.bottomContainer}>
          <View style={styles.bottomContent}>
            <Button mode="text" onPress={clearAllFilters} textColor={theme.colors.primary}>
              Clear Filters
            </Button>
            <Button
              mode="contained"
              onPress={applyFilters}
              style={styles.applyButton}
              contentStyle={styles.applyButtonContent}
            >
              Apply {getSelectedCount() > 0 && `(${getSelectedCount()})`}
            </Button>
          </View>
        </View>
      </SafeAreaView>
    </View>
  );
};

const createStyles = (theme) =>
  StyleSheet.create({
    container: { flex: 1, backgroundColor: theme.colors.background },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    headerTitle: { fontWeight: 'bold', color: theme.colors.onBackground,fontSize:moderateScale(16) },
    mainContent: { flex: 1, flexDirection: 'row' },
    leftContainer: { width: '35%', backgroundColor: theme.colors.surface },
    verticalDivider: { width: 1, backgroundColor: '#ddd' },
    rightContainer: { flex: 1, backgroundColor: theme.colors.background },
    filterTypeItem: { paddingHorizontal: 16, paddingVertical: 16 },
    activeFilterTypeItem: { backgroundColor: theme.colors.primaryContainer },
    filterTypeContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    filterTypeText: { fontSize: moderateScale(12), color: theme.colors.onSurface, flex: 1 },
    activeFilterTypeText: {
      color: theme.colors.onPrimaryContainer,
      fontWeight: '500',
    },
    selectedBadge: {
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      minWidth: 24,
      height: 24,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 8,
    },
    selectedBadgeText: {
      color: theme.colors.onPrimary,
      fontSize: moderateScale(11),
      fontWeight: 'bold',
    },
    rightHeader: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: theme.colors.surface,
    },
    rightScrollView: { flex: 1 },
    filterOptionItem: { paddingHorizontal: 16, paddingVertical: 4 },
    checkboxRow: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
    },
    checkboxLabel: { marginLeft: 8, flex: 1,fontSize:moderateScale(12) },
    emptyContainer: { padding: 32, alignItems: 'center' },
    emptyText: {
      fontSize: 14,
      color: theme.colors.onSurface,
      opacity: 0.6,
      fontStyle: 'italic',
    },
    bottomSpacer: { height: 100 },
    bottomContainer: {
      // flex : 1,

      backgroundColor: theme.colors.surface,
      borderTopWidth: 0.2,
      borderTopColor: theme.colors.outline,
      paddingBottom: 10,
    },
    bottomContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 6,
    },
    applyButton: {},
    applyButtonContent: { paddingHorizontal: 24, paddingVertical: 4 },
  });

export default DSRFilterComp;
