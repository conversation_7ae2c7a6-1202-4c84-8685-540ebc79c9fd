import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import {
  View,
  FlatList,
  TouchableOpacity,
  NativeSyntheticEvent,
  NativeScrollEvent,
  InteractionManager,
  Modal,
} from 'react-native';
import { ChevronDown, ChevronUp } from 'lucide-react-native';
import { scale, verticalScale } from 'react-native-size-matters';
import { Text, ActivityIndicator, Button, useTheme, Badge, Icon } from 'react-native-paper';
import { RouteProp, useFocusEffect, useNavigation, useRoute } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ActivityGroup, Activity } from './DSRTypes';
import styles from './DSRStyles';
import { DateSection } from './components/DateSection';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchDSREventsRequest,
  setPastActivitiesAvailability,
  setFutureActivitiesAvailability,
  FetchDirection,
} from '../../../../State/Slices/DSR/DSREventsSlice';
import { RootState } from '../../../../State/Store';
import useDebounce from '../../../../Hooks/useDebounce';
import { Filter, Funnel, Plus, SlidersHorizontal } from 'lucide-react-native';
import { moderateScale } from 'react-native-size-matters';
import DSRFilterComp from './components/DSRFilterComp';
import { useFilterData } from './hooks/useFilterData';
import { transformApiResponseToActivityGroups } from './DSRScreenFunctions';
import { RootStackParamList } from '../../../../Common/Routes/StackTypes';
import dayjs from 'dayjs';

import EmptyorErrorComponent from '../../../../Components/EmptyOrError/EmptyOrErrorComponent';

import { getAccountId, getSelectedWorkspaceId } from '../../../../Common/Utils/Storage';
const DSRScreen = ({}) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [refresh, setRefresh] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [activityGroups, setActivityGroups] = useState<ActivityGroup[]>([]);
  const [hasMorePast, setHasMorePast] = useState<boolean>(true);
  const [hasMoreFuture, setHasMoreFuture] = useState<boolean>(true);
  const [showBottomSheet, setShowBottomSheet] = useState<boolean>(false);
  // Modified: Typed selectedFilters with FiltersType
  type FiltersType = {
    salesActivityTypes: string[] | null;
    salesActivityCountries: string[] | null;
    salesActivityLeadStates: string[] | null;
    salesActivityOutcomeTypes: string[] | null;
  };
  const [selectedFilters, setSelectedFilters] = useState<FiltersType>({
    salesActivityTypes: null,
    salesActivityCountries: null,
    salesActivityLeadStates: null,
    salesActivityOutcomeTypes: null,
  });
  const [scrollIndex, setScrollIndex] = useState(0);
  const theme = useTheme();
  const navigation = useNavigation<any>();
  const listRef = useRef<FlatList>(null);
  const lastOffsetYRef = useRef<number>(0);
  const lastFetchTimeRef = useRef<number | null>(null);
  const dispatch = useDispatch();
  const route = useRoute<RouteProp<RootStackParamList, 'DSRScreen'>>();
  // const appData = route.params?.appData;
  const dsrEventsState = useSelector((state: RootState) => state.DSREvents);
  const {
    salesActivityTypes,
    salesActivityCountries,
    salesActivityLeadStates,
    salesActivityOutcomeTypes,
    filterError,
  } = useFilterData();
  const [allExpanded, setAllExpanded] = useState(true);
  const peopleHirerachySelectedData = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedPeopleHirerachy?.data
  );
  const restrictPeopleHirerachyData = useSelector(
    (state: any) => state.GlobalAppStateData?.restrictPeopleHirerachyData
  );

  const handleToggleAll = () => {
    setActivityGroups((prevGroups) =>
      prevGroups.map((group) => ({ ...group, isExpanded: !allExpanded }))
    );
    setAllExpanded((prev) => !prev);
  };
  const userId = useSelector((state: any) => state.userId);
  console.log("userId", userId);
  const selectedUserId = peopleHirerachySelectedData?.account_user_id || userId; 
  const isRestricted = useMemo(() => {
    return restrictPeopleHirerachyData?.data?.includes(selectedUserId);
  }, [selectedUserId, restrictPeopleHirerachyData?.data]);

  const selectedControlUnit = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedControlUnit?.data
  );
  const fetchActivitiesByDirection = useCallback(
    async (direction: 'initial' | 'past' | 'future', filters: FiltersType | null = null) => {
      setError(null);
      try {
        let referenceDate = '';
        if (direction === 'past') {
          const oldestGroup = activityGroups?.[0];
          const oldestActivities = oldestGroup?.activities ?? [];
          referenceDate = oldestActivities[0]?.startDateTime ?? '';
        } else if (direction === 'future') {
          const newestGroup = activityGroups[activityGroups?.length - 1];
          const newestActivities = newestGroup?.activities ?? [];
          referenceDate = newestActivities[0]?.startDateTime ?? '';
        } else {
          referenceDate = '';
        }

        const controlUnitId = selectedControlUnit?.id;
        const payload = await payloadBasedOnScroll(
          direction,
          referenceDate,
          selectedUserId,
          filters,
          controlUnitId
        );

        dispatch(fetchDSREventsRequest(payload));
      } catch (error: any) {
        console.log(error);
        setError(error.message || `Failed to fetch ${direction} activities`);
      }
    },
    [selectedUserId, activityGroups, dsrEventsState.events, selectedControlUnit?.id, dispatch, payloadBasedOnScroll]
  );

  // Modified: Fixed handleApplyFilters
  const handleApplyFilters = useCallback(
    async (filters: FiltersType) => {
      try {
        setSelectedFilters(filters);
        await fetchActivitiesByDirection('initial', filters);
        setShowBottomSheet(false);
      } catch (err: any) {
        setError(err.message || 'Failed to apply filters');
      }
    },
    [fetchActivitiesByDirection]
  );

  // Modified: Added selectCount computation
  const selectCount = useMemo(() => {
    return Object.entries(selectedFilters).reduce((sum, [key, value]) => {
      if (
        key === 'salesActivityTypes' ||
        key === 'salesActivityCountries' ||
        key === 'salesActivityLeadStates' ||
        key === 'salesActivityOutcomeTypes'
      ) {
        return sum + (value?.[0] ? 1 : 0);
      }
      return sum + (value?.length || 0);
    }, 0);
  }, [selectedFilters]);

  // Modified: Added getSelectedFilterChips for rendering filter chips
  const getSelectedFilterChips = useMemo(() => {
    const filterTypes = [
      {
        key: 'salesActivityTypes',
        label: 'Activity Types',
        data: salesActivityTypes,
        valueKey: 'Value',
        textKey: 'Text',
      },
      {
        key: 'salesActivityCountries',
        label: 'Cities',
        data: salesActivityCountries,
        valueKey: 'Value',
        textKey: 'Text',
      },
      {
        key: 'salesActivityLeadStates',
        label: 'Lead States',
        data: salesActivityLeadStates,
        valueKey: 'Value',
        textKey: 'Text',
      },
      {
        key: 'salesActivityOutcomeTypes',
        label: 'Outcome Types',
        data: salesActivityOutcomeTypes,
        valueKey: 'Value',
        textKey: 'Text',
      },
    ];
    // console.log("salesActivityOutcomeTypes >>", salesActivityOutcomeTypes)
    const chips = [];
    filterTypes.forEach((filterType) => {
      const selectedValues = selectedFilters[filterType.key];
      if (selectedValues?.[0]) {
        const selectedItem = filterType.data.find(
          (item) => item[filterType.valueKey] === selectedValues[0]
        );
        if (selectedItem) {
          chips.push({
            category: filterType.key,
            value: selectedValues[0],
            label: selectedItem[filterType.textKey],
          });
        }
      }
    });
    return chips;
  }, [
    selectedFilters,
    salesActivityTypes,
    salesActivityCountries,
    salesActivityLeadStates,
    salesActivityOutcomeTypes,
  ]);

  // Modified: Fixed TypeScript indexing error
  const handleRemoveFilter = useCallback((category: keyof FiltersType, value: string) => {
    setSelectedFilters((prev) => {
      const currentSelection = prev[category] || [];
      const updatedSelection = currentSelection.filter((item) => item !== value);
      const updatedFilters: FiltersType = {
        ...prev,
        [category]: updatedSelection.length > 0 ? updatedSelection : null,
      };
      handleApplyFilters(updatedFilters);
      return updatedFilters;
    });
  }, [handleApplyFilters]);

  const handleClearFilters = () => {
    // Handle clearing filters logic here
  };

  // console.log("peopleHirerachySelectedData in dsr screen", peopleHirerachySelectedData)



  // Navigation handler for activity item clicks
  const handleNavigateToDetail = useCallback(
    (activity: Activity) => {
      navigation.navigate('DSRDetailView', {
        salesActivityId: activity?.activity?.sales_activity_id,
        salesActivityOutcomeTypes : salesActivityOutcomeTypes,
        setRefresh,
        handleRefresh: handleRefresh,
      });
    },
    [navigation, salesActivityOutcomeTypes]
  );
  const fetchFilterValue = (data: any) => {
    return Array.isArray(data) ? (data.length > 0 ? data[0] : null) : null;
  };

  const payloadBasedOnScroll = async (
    scrollType: string,
    fetchDateString: string,
    selectedUserId: string,
    filters: FiltersType | null = null,
    controlUnitId: string
  ) => {
    const schemaName = await AsyncStorage.getItem('schemaName');
    
    const accountId = await getAccountId();
    const workspaceId = await getSelectedWorkspaceId();
   
    return {
      activityTypes: fetchFilterValue(
        filters !== null ? filters?.salesActivityTypes : selectedFilters.salesActivityTypes
      ),
      countries: fetchFilterValue(
        filters !== null ? filters?.salesActivityCountries : selectedFilters.salesActivityCountries
      ),
      leadTypes: fetchFilterValue(
        filters !== null
          ? filters?.salesActivityLeadStates
          : selectedFilters?.salesActivityLeadStates
      ),
      outcomes: fetchFilterValue(
        filters !== null
          ? filters?.salesActivityOutcomeTypes
          : selectedFilters.salesActivityOutcomeTypes
      ),
      schemaName: schemaName ?? undefined,
      searchTerm: '',
      userId: selectedUserId,
      currentDate: dayjs().format('YYYY-MM-DD'), // Use ISO timestamp format
      ...(scrollType !== 'initial' && { startDate: fetchDateString }),
      // startDate: fetchDateString,
      direction: scrollType,
      controllUnitId: controlUnitId,
      scrollDirection: scrollType === 'initial' ? 'today' : scrollType,

      accountId,
      workspaceId,
    };
  };

  const [debouncedFetchActivities, clearDebounce] = useDebounce(
    async (direction: 'past' | 'future') => {
      await fetchActivitiesByDirection(direction);
    },
    100
  );
  const memoizedDebouncedFetchActivities = useMemo(() => debouncedFetchActivities, [debouncedFetchActivities]);

  const shouldFetch = () =>
    !lastFetchTimeRef.current || Date.now() - lastFetchTimeRef.current > 1500;

  // Handle scroll events to detect direction and position
  const handleScroll = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const currentOffset = event.nativeEvent.contentOffset.y;
      const direction = currentOffset > lastOffsetYRef.current ? 'down' : 'up';
      lastOffsetYRef.current = currentOffset;

      if (
        direction === 'up' &&
        currentOffset < 100 && // Slightly increased threshold
        !dsrEventsState.fetchingPast &&
        hasMorePast &&
        activityGroups?.length > 0 &&
        shouldFetch()
      ) {
        // Add a small debounce effect to prevent multiple rapid calls

        lastFetchTimeRef.current = Date.now();
        debouncedFetchActivities('past');
      }
    },
    [hasMorePast, dsrEventsState.fetchingPast, activityGroups]
  );

  const handleEndReached = useCallback(() => {
    if (
      !dsrEventsState.fetchingFuture &&
      hasMoreFuture &&
      activityGroups?.length > 0 &&
      shouldFetch()
    ) {
      lastFetchTimeRef.current = Date.now();
      debouncedFetchActivities('future');
    }
  }, [hasMoreFuture, dsrEventsState.fetchingFuture, activityGroups]);

  useFocusEffect(
    useCallback(() => {
      const state = navigation.getState();
      const routes = state.routes;
      const currentIndex = state.index;

      const previousRoute = routes[currentIndex - 1];
    }, [])
  );
  useEffect(() => {
    setActivityGroups(transformApiResponseToActivityGroups(dsrEventsState.events,allExpanded));
  }, [refresh]);
  useEffect(() => {
    // Handle loading states
    if (dsrEventsState.loading) {
      setLoading(true);
    } else {
      setLoading(false);
    }

    // Handle pagination availability states
    setHasMorePast(dsrEventsState.hasMorePast);
    setHasMoreFuture(dsrEventsState.hasMoreFuture);

    // Handle error state
    if (dsrEventsState.error) {
      setError(dsrEventsState.error);
    }

    // Process fetched events if available
    if (
      dsrEventsState.events &&
      Array.isArray(dsrEventsState.events) &&
      dsrEventsState.events.length > 0
    ) {
      // Transform API data to our format
      const transformedData = transformApiResponseToActivityGroups(dsrEventsState.events,allExpanded);

      if (dsrEventsState.lastFetchDirection === FetchDirection.INITIAL) {
        // For initial fetch, just set the activities
        setActivityGroups(transformedData);
      } else if (dsrEventsState.lastFetchDirection === FetchDirection.PAST) {
        // For past activities, prepend to the current list
        setActivityGroups((prevGroups) => {
          // Avoid duplicates
          const existingDates = new Set(prevGroups.map((group) => group.date));
          const newGroups = transformedData.filter((group) => !existingDates.has(group.date));

          if (newGroups.length === 0) {
            // No new groups, signal we've reached the end
            dispatch(setPastActivitiesAvailability(false));
            return prevGroups;
          }

          // Merge and sort
          const combinedGroups = [...newGroups, ...prevGroups];

          // Maintain scroll position after adding items
          if (listRef.current && newGroups.length > 0) {
            // setLoading(true)
            setTimeout(() => {
              try {
                listRef.current?.scrollToIndex({
                  animated: false,
                  index: newGroups.length,
                  viewPosition: 0,
                });
              } catch (err) {}
            }, 100);
          }

          return combinedGroups;
        });
      } else if (dsrEventsState.lastFetchDirection === FetchDirection.FUTURE) {
        // For future activities, append to the current list
        setActivityGroups((prevGroups) => {
          // Avoid duplicates
          const existingDates = new Set(prevGroups.map((group) => group.date));
          const newGroups = transformedData.filter((group) => !existingDates.has(group.date));

          if (newGroups.length === 0) {
            // No new groups, signal we've reached the end
            dispatch(setFutureActivitiesAvailability(false));
            return prevGroups;
          }

          // Merge and sort
          const combinedGroups = [...prevGroups, ...newGroups];

          return combinedGroups;
        });
      }
    } else if (
      dsrEventsState.events &&
      Array.isArray(dsrEventsState.events) &&
      dsrEventsState.events.length === 0
    ) {
      // Empty response indicates no more data in that direction
      if (dsrEventsState.lastFetchDirection === FetchDirection.PAST) {
        dispatch(setPastActivitiesAvailability(false));
      } else if (dsrEventsState.lastFetchDirection === FetchDirection.FUTURE) {
        dispatch(setFutureActivitiesAvailability(false));
      } else if (dsrEventsState.lastFetchDirection === FetchDirection.INITIAL) {
        setActivityGroups([]);
      }
    }
  }, [dsrEventsState, selectedUserId, refresh]);

  useEffect(() => {
    if (selectedUserId.length > 0 && selectedControlUnit?.id) {
      const fetch = () => fetchActivitiesByDirection('initial');
      fetch();
    }
  }, [selectedUserId, selectedControlUnit]);

  // Auto-scroll to today's activities when activities are loaded

  useEffect(() => {
    if (activityGroups?.length > 0) {
      const todayIndex = activityGroups.findIndex((group) => group.isToday);

      if (todayIndex !== -1 && listRef.current) {
        InteractionManager.runAfterInteractions(() => {
          // Add a small delay to ensure it renders
          setTimeout(() => {
            try {
              listRef.current?.scrollToIndex({
                index: todayIndex,
                animated: true,
                viewOffset: 0,
                viewPosition: 0,
              });
            } catch (error) {
              listRef.current?.scrollToOffset({
                offset: todayIndex * 10, // approximate fallback
                animated: true,
              });
            }
          }, 50); // small buffer delay
        });
      }
    }
  }, [loading]);

  const handleToggleSection = (date: string) => {
    setActivityGroups((prevGroups) =>
      prevGroups.map((group) =>
        group.date === date ? { ...group, isExpanded: !group.isExpanded } : group
      )
    );
  };
  


  const handleRefresh = async () => {
    await fetchActivitiesByDirection('initial');
  };
  const [menuVisible, setMenuVisible] = useState(false);
  const renderLoadMore = (type: 'past' | 'future') => {
    const isFetching =
      type === 'past' ? dsrEventsState.fetchingPast : dsrEventsState.fetchingFuture;
    // console.log("isFetching",isFetching)
    const hasMore = type === 'past' ? hasMorePast : hasMoreFuture;
    return isFetching ? (
      <View style={{ padding: 20, alignItems: 'center' }}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
        <Text style={{ marginTop: 8 }} variant="regular">
          Loading {type === 'past' ? 'earlier' : 'newer'} activities...
        </Text>
      </View>
    ) : activityGroups?.length > 0 && hasMore ? (
      <TouchableOpacity
        style={{ paddingHorizontal: 16, alignItems: 'center' }}
        onPress={async () => !isFetching && (await fetchActivitiesByDirection(type))}
      >
        <Text style={{ color: theme.colors.primary }} variant="medium">
          {type === 'past' ? '' : 'Load newer activities'}
        </Text>
      </TouchableOpacity>
    ) : (
      <></>
      // <View style={{ padding: 16, alignItems: 'center' }}>
      //   <Text style={{ color: 'gray' }}>
      //     No {type === 'past' ? 'earlier' : 'newer'} activities available
      //   </Text>
      // </View>
    );
  };

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: theme.colors.background,
        },
      ]}
    >

      {isRestricted ? (
        <View style={styles.errorContainer}>
          <Text
            variant="titleMedium"
            style={{
              color: theme.colors.outline,
              textAlign: 'center',
            }}
          >
            You don't have access to view data for this user. Only users at the same or lower
            hierarchy can be viewed.
          </Text>
        </View>
      ) : loading && activityGroups?.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size={moderateScale(27)} color={theme.colors.primary} />
          <Text style={{ marginTop: 10 }} variant='medium'>Loading activities...</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text
            variant="titleMedium"
            style={{
              color: theme.colors.outline,
            }}
          >
            {`${error}`}
          </Text>
       
          <Button mode="contained" onPress={handleRefresh} style={{ marginTop: 16 }}>
            Retry
          </Button>
        </View>
      ) : (
        <>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              // margin: 16,
              marginHorizontal: 16,
              // marginTop: 10,
              justifyContent: 'space-between',
              // marginBottom: 25,
              marginBottom: verticalScale(5),
              backgroundColor: theme.colors.background,
            }}
          >
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'center',
                gap: 10,
                alignItems: 'center',
              }}
            >
              <Text style={{ fontSize: moderateScale(13),}} variant='semiBold'>
                Daily Status Report
              </Text>
              <TouchableOpacity
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                 
                  padding: moderateScale(5),
                  borderRadius: 6,

                  marginRight: moderateScale(8),
                }}
                onPress={handleToggleAll}
              >
                {allExpanded ? (
                  <>
                    <ChevronUp 
                     color="#3377FF"  />
                  </>
                ) : (
                  <>
                    <ChevronDown  color="#3377FF"  />
            
                  </>
                )}
              </TouchableOpacity>
            </View>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'flex-end',
                gap: moderateScale(8),
              }}
            >
              <TouchableOpacity
                style={{
                  borderRadius: 5,
                  padding: 5,

                  backgroundColor: selectCount ? '#eff6ff'  : '#FFF',
                  alignItems: 'center',
                  justifyContent: 'center',

                  borderWidth: 1,
                  borderColor: selectCount ?  theme.colors.primary : '#E0E0E0' ,
                }}
                activeOpacity={0.7}
                onPress={() => {
                  setShowBottomSheet(true);
                }}
              >
                
                {selectCount > 0 && (
                  <Badge
                  size={moderateScale(15)}
                    style={{
                      position: 'absolute',
                      top: -moderateScale(8),
                      right:- moderateScale(8),
                      backgroundColor: theme.colors.primary,
                      zIndex:2
                    }}
                  >
                    {selectCount}
                  </Badge>
                )}
                <Funnel size={moderateScale(18)} color={selectCount ? theme.colors.primary : theme.colors.onSurfaceVariant} />
              </TouchableOpacity>
              <TouchableOpacity
                style={{
                  backgroundColor: '#3377FF',
                  padding: moderateScale(5),

                  borderRadius: 5,
                 
                  borderColor: '#3377FF',
                  // marginRight: 8,
                }}
                onPress={() => {
                  navigation.navigate('CreateActivityScreen', {
                    isCustomerNotVisible: true,
                    controlUnitId: selectedControlUnit?.id,
                    handleRefresh: handleRefresh,
                  });
                }}
              >
                <Plus size={moderateScale(18)} color="#FFF"  />
              </TouchableOpacity>
            </View>
          </View>

          <FlatList
            ref={listRef}
            data={activityGroups}
            keyExtractor={(item) => `${item?.date}`}
            renderItem={({ item, index }) => (
              <DateSection
                group={item}
                onToggleSection={handleToggleSection}
                onNavigateToDetail={handleNavigateToDetail} // Pass the
              />
            )}
            onScroll={(event) => handleScroll(event)}
            scrollEventThrottle={16}
            onEndReached={handleEndReached}
            onEndReachedThreshold={0.5}
            contentContainerStyle={styles.listContent}
            maintainVisibleContentPosition={{
              autoscrollToTopThreshold: 1,
              minIndexForVisible: 0,
            }}
            refreshing={loading}
            onRefresh={handleRefresh}
            ListEmptyComponent={
              <EmptyorErrorComponent
                message={'No Activities Found'}
                emptyImage={require('../../../../../assets/icons/no_data.png')}
              />
            }
            ListHeaderComponent={renderLoadMore('past')}
            ListFooterComponent={renderLoadMore('future')}
            bounces={false}
          />
        </>
      )}

      <Modal
        visible={showBottomSheet}
        onDismiss={() => setShowBottomSheet(false)}
        animationType="slide"
        transparent={true}
        onRequestClose={() => {
          setShowBottomSheet(false);
        }}
        style={{
          overflow: 'hidden',
        }}
        // snapPoints={['75%']}
      >
        <DSRFilterComp
          salesActivityTypes={salesActivityTypes}
          salesActivityCountries={salesActivityCountries}
          salesActivityLeadStates={salesActivityLeadStates}
          salesActivityOutcomeTypes={salesActivityOutcomeTypes}
          selectedFilters={selectedFilters}
          setSelectedFilters={setSelectedFilters}
          filterError={filterError}
          onApplyFilters={handleApplyFilters}
          onClearFilters={handleClearFilters}
          onClose={() => setShowBottomSheet(false)}
        />
      </Modal>
    </View>
  );
};
export default DSRScreen;
