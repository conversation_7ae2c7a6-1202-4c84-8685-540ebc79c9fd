import { ChevronDown } from "lucide-react-native";
import React, { ReactNode, useState } from "react";
import {
  View,
  Pressable,
  LayoutAnimation,
  Platform,
  UIManager,
  StyleSheet,
  Animated,
  Easing,
} from "react-native";
import { Text } from "react-native-paper";
import { moderateScale } from "react-native-size-matters";

// Enable LayoutAnimation on Android
if (Platform.OS === "android" && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

type SectionProps = {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
  count?:number
};

export const CollapsibleSection: React.FC<SectionProps> = ({
  title,
  children,
  defaultOpen = true,
  count
}) => {
  const [open, setOpen] = useState(defaultOpen);
  const rotate = useState(new Animated.Value(defaultOpen ? 1 : 0))[0];

  const toggle = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setOpen((prev) => {
      const to = prev ? 0 : 1;
      Animated.timing(rotate, {
        toValue: to,
        duration: 180,
        easing: Easing.out(Easing.quad),
        useNativeDriver: true,
      }).start();
      return !prev;
    });
  };

  const spin = rotate.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "180deg"],
  });

  return (
    <View style={styles.card}>
      <Pressable onPress={toggle} style={styles.header}>
        <View style={styles.titleContainer}>
          <Text variant="medium" style={styles.title}>
            {title}
          </Text>
          {count ? (
            <View style={styles.countBox}>
              <Text variant="semiBold" style={styles.countText}>
                {count}
              </Text>
            </View>
          ) : null}
        </View>

        <Animated.View
          style={[
            { marginRight: moderateScale(18), transform: [{ rotate: spin }] },
          ]}
        >
          <ChevronDown size={24} color="#99a1af" />
        </Animated.View>
      </Pressable>

      <View
        style={{
          borderWidth: moderateScale(0.3),
          borderColor: "#ebe6e7",
          marginHorizontal: moderateScale(6),
          marginVertical: moderateScale(2),
        }}
      ></View>

      {open && <View style={styles.content}>{children}</View>}
    </View>
  );
};

/* ---------- KeyValue reusable ---------- */


type KVProps = {
  label1: ReactNode;
  value1?: ReactNode | string | number | null;
  label2?: ReactNode;
  value2?: ReactNode | string | number | null;
  badge?: string; // optional badge text
};

export const KeyValue: React.FC<KVProps> = ({
  label1,
  value1,
  label2,
  value2,
  badge,
}) => (
  <View style={styles.kvContainerWrapper}>
    {badge && (
      <View style={styles.badgeContainer}>
        <Text style={styles.badgeText}>{badge}</Text>
      </View>
    )}

    <View style={styles.kvContainer}>
      {/* First Key-Value Pair */}
      <View style={[styles.kvColumn, styles.marginRIght]}>
        {typeof label1 === "string" ? (
          <Text style={styles.kvLabel}>{label1}</Text>
        ) : (
          label1
        )}
        {typeof value1 === "string" || typeof value1 === "number" ? (
          <Text numberOfLines={2} ellipsizeMode="tail" style={styles.kvValue}>
            {value1 ?? "—"}
          </Text>
        ) : (
          value1 || <Text style={styles.kvValue}>—</Text>
        )}
      </View>

      {/* Second Key-Value Pair (if provided) */}
      {label2 && (
        <View style={styles.kvColumn}>
          {typeof label2 === "string" ? (
            <Text style={styles.kvLabel}>{label2}</Text>
          ) : (
            label2
          )}
          {typeof value2 === "string" || typeof value2 === "number" ? (
            <Text numberOfLines={2} ellipsizeMode="tail" style={styles.kvValue}>
              {value2 ?? "—"}
            </Text>
          ) : (
            value2 || <Text style={styles.kvValue}>—</Text>
          )}
        </View>
      )}
    </View>
  </View>
);

const styles = StyleSheet.create({
  card: {
    backgroundColor: "#fffefeff",
    borderColor: "#e2e8f0",
    paddingVertical: moderateScale(8),
    paddingHorizontal: moderateScale(8),
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: moderateScale(8),
  },
  title: {
    fontSize: moderateScale(13),
    color: "#0f172a",
    paddingLeft: moderateScale(12),
  },
  content: {},
  kvContainerWrapper: {
    position: "relative", // needed for badge absolute positioning
    marginBottom: moderateScale(12),
  },
  kvContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  kvColumn: {
    flex: 1,
    minWidth: 0, // important for text ellipsis
  },
  kvLabel: {
    fontSize: moderateScale(12),
    color: "#64748b",
    marginBottom: moderateScale(4),
  },
  kvValue: {
    fontSize: moderateScale(11),
    fontWeight: "500",
    color: "#0f172a",
  },
  marginRIght: {
    marginRight: moderateScale(12),
  },
  countBox: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
    backgroundColor: "#e2e8f0",
  },
  countText: {
    fontSize: 12,
    color: "#1a202c",
  },
  titleContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: moderateScale(16),
  },
  badgeContainer: {
    position: "absolute",
    top: -4,
    right: -8,
    backgroundColor: "#3377ee",
    paddingHorizontal: moderateScale(6),
    paddingVertical: moderateScale(2),
    borderRadius: moderateScale(8),
    zIndex: 1,
  },
  badgeText: {
    color: "#fff",
    fontSize: moderateScale(10),
    fontWeight: "600",
  },
});
