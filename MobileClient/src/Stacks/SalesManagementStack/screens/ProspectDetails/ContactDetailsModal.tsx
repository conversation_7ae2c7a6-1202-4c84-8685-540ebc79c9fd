import React from 'react';
import {
  Modal,
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Linking,
  ToastAndroid,
  Platform,
  Alert,
} from 'react-native';
import { Text, Avatar, Chip, useTheme } from 'react-native-paper';
import { X, Phone, Mail, User, CalendarDays, Building2, Briefcase } from 'lucide-react-native';
import { ContactType } from './useProspectDetails';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { fontFamily } from '../../../../Common/Theme/typography';
import Clipboard from '@react-native-clipboard/clipboard';

interface Props {
  visible: boolean;
  onClose: () => void;
  contact: ContactType | null;
  ageGroups: { Text: string; Value: string }[];
}

const ContactDetailsModal: React.FC<Props> = ({ visible, onClose, contact, ageGroups }) => {
  if (!contact) return null;

  const fullName =
    contact.contact_name || `${contact.first_name || ''} ${contact.last_name || ''}`.trim();

  const initials = fullName
    .split(' ')
    .map((word) => word[0])
    .join('')
    .toUpperCase();

  const phoneNumbers = contact.phone_numbers || [];

  const emails =
    contact.emails?.flatMap((e) => {
      try {
        const parsed = JSON.parse(e.email);
        return Array.isArray(parsed) ? parsed : [parsed];
      } catch {
        return [e.email];
      }
    }) || [];

  const isDecisionMaker = contact.decision_maker === 1;
  const isPrimary = contact.is_primary === 1;
  const theme = useTheme();
  const isActive = contact.status_id === 1;

  const getAgeGroupLabel = (id?: string | number): string => {
    if (!id) return '--';
    const match = ageGroups.find((opt) => opt.Value === String(id));
    return match?.Text ?? String(id);
  };

  return (
    <Modal visible={visible} animationType="none" transparent>
      <TouchableOpacity activeOpacity={1} style={styles.overlay} onPress={onClose}>
        <TouchableOpacity activeOpacity={1} style={styles.container} onPress={() => {}}>
          <View style={styles.headerContainer}>
            <Text style={styles.title}>Contact Details</Text>
            <TouchableOpacity onPress={onClose}>
              <X size={20} color="#374151" />
            </TouchableOpacity>
          </View>

          <ScrollView contentContainerStyle={styles.content} bounces={false}>
            {(fullName || contact.gender) && (
              <View style={styles.header}>
                <Avatar.Text
                  size={moderateScale(23)}
                  label={initials}
                  style={{
                    backgroundColor: theme.colors.primaryContainer,
                    height: moderateScale(30),
                    width: moderateScale(30),
                    borderRadius: 60,
                  }}
                  labelStyle={{ color: theme.colors.primary }}
                />
                <View style={{ marginLeft: scale(8), flex: 1 }}>
                  {fullName ? <Text style={styles.name}>{fullName}</Text> : null}
                  {contact.gender ? <Text style={styles.subText}>{contact.gender}</Text> : null}
                </View>
              </View>
            )}

            {(isPrimary || isDecisionMaker) && (
              <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                 {isActive && (
                  <Chip style={styles.activeBadge} textStyle={{ color: '#3377FF' }}>
                    Active
                  </Chip>
                )}
                {isPrimary && (
                  <Chip style={styles.activeBadge} textStyle={{ color: '#3377FF' }}>
                    Primary Contact
                  </Chip>
                )}
                {isDecisionMaker && (
                  <Chip style={styles.activeBadge} textStyle={{ color: '#3377FF' }}>
                    Decision Maker
                  </Chip>
                )}
              </View>
            )}

            {phoneNumbers.length > 0 && (
              <>
                <Text style={styles.section}>Contact Information</Text>
                {phoneNumbers.map((phone, idx) => (
                  <TouchableOpacity
                    key={idx}
                    style={styles.contactRow}
                    onPress={() => Linking.openURL(`tel:${phone.contact_phone_number}`)}
                    onLongPress={() => {
                      Clipboard.setString(phone.contact_phone_number);
                      if (Platform.OS === 'android') {
                        ToastAndroid.show('Phone number copied to clipboard', ToastAndroid.SHORT);
                      } else {
                        Alert.alert('Copied', 'Phone number copied to clipboard');
                      }
                    }}
                  >
                    <Phone size={16} color="#2563EB" />
                    <Text style={styles.email}>{phone.contact_phone_number}</Text>
                  </TouchableOpacity>
                ))}
              </>
            )}
            {emails.length > 0 && (
              <>
                <Text style={styles.section}>E-Mail</Text>
                {emails.map((email, idx) => (
                  <TouchableOpacity
                    key={idx}
                    style={styles.contactRow}
                    onPress={async () => {
                      const url = `mailto:${email}`;
                      const supported = await Linking.canOpenURL(url);
                      if (supported) {
                        Linking.openURL(url);
                      } else {
                        Alert.alert('Email not supported', "This device can't open email links.");
                      }
                    }}
                    onLongPress={() => {
                      Clipboard.setString(email);
                      if (Platform.OS === 'android') {
                        ToastAndroid.show('Email copied to clipboard', ToastAndroid.SHORT);
                      } else {
                        Alert.alert('Copied', 'Email copied to clipboard');
                      }
                    }}
                  >
                    <Mail size={16} color="#2563EB" />
                    <Text style={styles.email}>{email}</Text>
                  </TouchableOpacity>
                ))}
              </>
            )}

            {(contact.gender || contact.age_group_id) && (
              <>
                <Text style={styles.section}>Personal Information</Text>
                <View style={[styles.infoRow, { marginBottom: verticalScale(5) }]}>
                  {contact.gender && (
                    <>
                      <User size={16} color="#2563EB" />
                      <Text style={styles.infoText}>{contact.gender}</Text>
                    </>
                  )}
                  {contact.age_group_id && (
                    <>
                      <CalendarDays
                        size={16}
                        color="#2563EB"
                        style={{ marginLeft: contact.gender ? 16 : 0 }}
                      />
                      <Text style={styles.infoText}>{getAgeGroupLabel(contact.age_group_id)}</Text>
                    </>
                  )}
                </View>
              </>
            )}

            {(contact.department || contact.role) && (
              <View style={styles.infoRow}>
                {contact.department && (
                  <>
                    <Building2 size={16} color="#2563EB" />
                    <Text style={styles.infoText}>{contact.department}</Text>
                  </>
                )}
                {contact.role && (
                  <>
                    <Briefcase size={16} color="#2563EB" style={{ marginLeft: 16 }} />
                    <Text style={styles.infoText}>{contact.role}</Text>
                  </>
                )}
              </View>
            )}
          </ScrollView>
        </TouchableOpacity>
      </TouchableOpacity>
    </Modal>
  );
};

const screenHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.25)',
    justifyContent: 'center',
    padding: moderateScale(14),
  },
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: moderateScale(14),
    maxHeight: screenHeight * 0.9,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: verticalScale(10),
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(10),
  },
  name: {
    fontSize: moderateScale(16),
    fontFamily: fontFamily.medium,
    color: '#000',
  },
  subText: {
    fontSize: moderateScale(13),
    color: '#647488',
  },
  content: {
    paddingBottom: 16,
  },
  activeBadge: {
    paddingHorizontal: scale(4),
    fontSize: moderateScale(12),
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: scale(5),
    color: '#3377FF',
    backgroundColor: '#F0F7FF',
    marginBottom: verticalScale(5),
  },
  section: {
    fontSize: moderateScale(13),
    fontFamily: fontFamily.medium,
    color: '#000',
    marginBottom: verticalScale(7),
    marginTop: verticalScale(7),
  },
  contactRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginBottom: verticalScale(10),
  },
  email: {
    marginLeft: scale(5),
    color: '#2563EB',
    fontSize: moderateScale(13),
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  infoText: {
    marginLeft: scale(5),
    color: '#000',
    fontSize: moderateScale(13),
  },
  title: {
    fontSize: moderateScale(14),
    color: '#000',
    fontFamily: fontFamily.medium,
  },
});

export default ContactDetailsModal;
