export interface Quote {
  profit: number;
  json_data: string;
  status_id: number;
  account_id: string;
  carrier_id: string;
  created_at: string;
  created_by: string | null;
  enquiry_id: string;
  updated_at: string;
  updated_by: string | null;
  customer_id: string;
  supplier_id: string;
  carrier_name: string;
  direction_id: string;
  is_show_book: number;
  workspace_id: string;
  customer_name: string;
  supplier_name: string;
  price_sheet_id: string;
  control_unit_id: string;
  is_send_allowed: number;
  price_sheet_desc: string;
  price_sheet_name: string;
  reference_number: string | null;
  total_cost_price: number;
  enquiry_option_id: string;
  margin_percentage: number;
  is_checkbox_allowed: number;
  mode_of_shipment_id: string;
  total_selling_price: number;
  enq_reference_number: string;
  price_sheet_state_id: number;
  total_cost_price_currency_id: number;
  enquiry_has_booked_price_sheet: number;
  total_cost_price_currency_code: string;
  total_cost_price_currency_name: string;
  total_selling_price_currency_id: string;
  total_cost_price_currency_symbol: string;
  total_selling_price_currency_code: string;
  total_selling_price_currency_name: string;
  total_selling_price_currency_symbol: string;
}

export interface UseQuotesListReturn {
  quotes: Quote[];
  loading: boolean;
  error: string | null;
  refreshing: boolean;
  hasMore: boolean;
  fetchQuotes: () => Promise<void>;
  refreshQuotes: () => Promise<void>;
  loadMoreQuotes: () => Promise<void>;
  searchQuotes: (searchText: string) => Promise<void>;
}