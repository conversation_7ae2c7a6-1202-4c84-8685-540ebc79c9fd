import React from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';
import { ListChecks, DollarSign, Building2, Calendar } from 'lucide-react-native';
import AppHeader from '../../../../../../Components/AppHeader';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { RouteProp } from '@react-navigation/native';

type CostItem = {
  id?: string | number;
  name?: string;
  chargeDescription?: string;
  chargeType?: string;
  quantity?: number;
  unitId?: string;
  totalAgentCost?: number;
  totalSellingPrice?: number;
  margin?: number;
};

type QuoteData = {
  price_sheet_name?: string;
  reference_number?: string;
  enq_reference_number?: string;
  total_cost_price?: number;
  total_cost_price_currency_symbol?: string;
  total_selling_price?: number;
  total_selling_price_currency_symbol?: string;
  profit?: number;
  margin_percentage?: number;
  customer_name?: string;
  supplier_name?: string;
  carrier_name?: string;
  created_at?: string;
  updated_at?: string;
  json_data?: string;
};

type QuoteDetailScreenRouteProp = RouteProp<
  { QuoteDetail: { quoteData: QuoteData } },
  'QuoteDetail'
>;

type Props = {
  route: QuoteDetailScreenRouteProp;
};

const QuoteDetailScreen: React.FC<Props> = ({ route }) => {
  const { quoteData } = route.params || {};

  const parsedData = quoteData?.json_data ? JSON.parse(quoteData.json_data) : null;
  const costItems: CostItem[] = parsedData?.costItems ?? [];

  const formatCurrency = (amount: number | undefined, currencySymbol: string = '$'): string => {
    if (amount == null) return `${currencySymbol} 0.00`;
    return `${currencySymbol} ${parseFloat(amount.toString()).toFixed(2)}`;
  };

 const totalCostPrice = costItems.reduce(
    (sum: number, item: CostItem) => sum + (item.totalAgentCost || 0),
    0
  );

  const totalSellingPrice = costItems.reduce(
    (sum: number, item: CostItem) => sum + (item.totalSellingPrice || 0),
    0
  );
  const totalProfit = totalSellingPrice - totalCostPrice;
  const marginPercentage = totalCostPrice > 0 ? (totalProfit / totalCostPrice) * 100 : 0;

  if (!quoteData) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorIcon}>⚠️</Text>
          <Text style={styles.errorText}>No quote data available</Text>
        </View>
      </View>
    );
  }

  const StatusChip: React.FC<{ status: string }> = ({ status }) => (
    <View
      style={[styles.statusChip, status === 'ACTIVE' ? styles.activeStatus : styles.draftStatus]}
    >
      <Text
        style={[
          styles.statusText,
          status === 'ACTIVE' ? styles.activeStatusText : styles.draftStatusText,
        ]}
      >
        {status}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <AppHeader title="Quote Details" />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false} bounces={false}>
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.headerTitle}>{quoteData.price_sheet_name}</Text>
            <Text style={styles.headerLabel}>
              Quote No:{' '}
              <Text style={styles.partyValue}>{quoteData.reference_number || 'Not Available'}</Text>
            </Text>
            <Text style={styles.headerLabel}>
              Enquiry No:{' '}
              <Text style={styles.partyValue}>
                {quoteData.enq_reference_number || 'Not Available'}
              </Text>
            </Text>
            <View style={styles.statusContainer}>
              <StatusChip status="Draft" />
              <StatusChip status="ACTIVE" />
            </View>
          </View>
        </View>

        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <DollarSign size={moderateScale(18)} color="#007bff" style={styles.cardIcon} />
            <Text style={styles.cardTitle}>Financial Overview</Text>
          </View>

          <View style={styles.financialGrid}>
            <View style={styles.financialItem}>
              <Text style={styles.financialLabel}>Total Cost Price</Text>
              <Text style={styles.costPrice}>
                {formatCurrency(
                  quoteData.total_cost_price,
                  quoteData.total_cost_price_currency_symbol
                )}
              </Text>
            </View>
            <View style={styles.financialItem}>
              <Text style={styles.financialLabel}>Total Selling Price</Text>
              <Text style={styles.sellingPrice}>
                {formatCurrency(
                  quoteData.total_selling_price,
                  quoteData.total_selling_price_currency_symbol
                )}
              </Text>
            </View>
            <View style={styles.financialItem}>
              <Text style={styles.financialLabel}>Profit</Text>
              <Text style={styles.profit}>
                {formatCurrency(quoteData.profit, quoteData.total_selling_price_currency_symbol)}
              </Text>
            </View>
            <View style={styles.financialItem}>
              <Text style={styles.financialLabel}>Margin</Text>
              <Text style={styles.margin}>{quoteData.margin_percentage}%</Text>
            </View>
          </View>
        </View>

        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <ListChecks size={moderateScale(18)} color="#000" style={styles.cardIcon} />
            <Text style={styles.cardTitle}>Line Items</Text>
          </View>

          {costItems.map((item, index) => (
            <View key={item.id || index} style={styles.lineItem}>
              <View style={styles.lineItemHeader}>
                <Text style={styles.lineItemNumber}>{index + 1}</Text>
                <View style={styles.lineItemContent}>
                  <Text style={styles.lineItemName}>{item.name || item.chargeDescription}</Text>
                  <Text style={styles.lineItemType}>
                    {item.chargeType} • Qty: {item.quantity} •{' '}
                    {item.unitId === '4' ? 'Bill of Lading' : 'Unit'}
                  </Text>
                </View>
              </View>

              <View style={styles.lineItemPricing}>
                <View style={styles.priceRow}>
                  <Text style={styles.priceLabel}>Cost Price</Text>
                  <Text style={styles.costPriceText}>USD {item.totalAgentCost?.toFixed(2)}</Text>
                </View>
                <View style={styles.priceRow}>
                  <Text style={styles.priceLabel}>Selling Price</Text>
                  <Text style={styles.sellingPriceText}>
                    AED {item.totalSellingPrice?.toFixed(2)}
                  </Text>
                </View>
                <View style={styles.priceRow}>
                  <Text style={styles.priceLabel}>Margin</Text>
                  <Text style={styles.marginText}>{item.margin}%</Text>
                </View>
              </View>
            </View>
          ))}

          <View style={styles.grandTotal}>
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Total Cost Price:</Text>
              <Text style={styles.totalValue}>
                {formatCurrency(
                  quoteData.total_cost_price,
                  quoteData.total_cost_price_currency_symbol
                )}
              </Text>
            </View>
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Total Selling Price:</Text>
              <Text style={styles.totalValue}>
                {formatCurrency(
                  quoteData.total_selling_price,
                  quoteData.total_selling_price_currency_symbol
                )}
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <Building2 size={moderateScale(18)} color="#000" style={styles.cardIcon} />
            <Text style={styles.cardTitle}>Parties & Carrier</Text>
          </View>

          <View style={styles.partiesGrid}>
            <View style={styles.partyItem}>
              <Text style={styles.partyLabel}>Customer</Text>
              <Text style={styles.partyValue}>{quoteData.customer_name}</Text>
            </View>
            <View style={styles.partyItem}>
              <Text style={styles.partyLabel}>Supplier</Text>
              <Text style={styles.partyValue}>{quoteData.supplier_name}</Text>
            </View>
            <View style={styles.partyItem}>
              <Text style={styles.partyLabel}>Carrier</Text>
              <Text style={styles.partyValue}>{quoteData.carrier_name}</Text>
            </View>
          </View>
        </View>

        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <Calendar size={moderateScale(18)} color="#000" style={styles.cardIcon} />
            <Text style={styles.cardTitle}>Timeline</Text>
          </View>

          <View style={styles.timelineItem}>
            <View style={styles.timelineDot} />
            <View style={styles.timelineContent}>
              <Text style={styles.timelineLabel}>Created</Text>
              <Text style={styles.timelineValue}>
                {quoteData.created_at
                  ? new Date(quoteData.created_at).toLocaleDateString('en-GB', {
                      day: '2-digit',
                      month: 'short',
                      year: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit',
                    })
                  : "N/A"}
              </Text>
            </View>
          </View>

          <View style={styles.timelineItem}>
            <View style={[styles.timelineDot, styles.timelineDotSecondary]} />
            <View style={styles.timelineContent}>
              <Text style={styles.timelineLabel}>Last Updated</Text>
              <Text style={styles.timelineValue}>
                {quoteData.updated_at ? 
                  new Date(quoteData.updated_at).toLocaleDateString('en-GB', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                }) : "N/A"}
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.bottomSpacer} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: moderateScale(20),
  },
  errorIcon: {
    fontSize: moderateScale(64),
    marginBottom: verticalScale(16),
  },
  errorText: {
    fontSize: moderateScale(18),
    color: '#7F8C8D',
    marginTop: verticalScale(16),
    textAlign: 'center',
  },
  header: {
    backgroundColor: '#FFFFFF',
    borderRadius: moderateScale(12),
    padding: moderateScale(16),
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: verticalScale(2) },
    shadowOpacity: 0.1,
    shadowRadius: moderateScale(8),
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    paddingVertical: verticalScale(12),
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    marginHorizontal: scale(16),
    marginBottom: verticalScale(10),
  },
  backButton: {
    padding: moderateScale(8),
    marginRight: scale(8),
  },
  backIcon: {
    fontSize: moderateScale(24),
    color: '#2C3E50',
    fontWeight: 'bold',
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: moderateScale(18),
    fontWeight: '600',
    color: '#2C3E50',
  },
  headerSubtitle: {
    fontSize: moderateScale(14),
    color: '#7F8C8D',
  },
  quoteNumber: {
    fontSize: moderateScale(13),
    color: '#2C3E50',
    marginTop: verticalScale(4),
  },
  moreButton: {
    padding: moderateScale(8),
    marginLeft: scale(8),
  },
  moreIcon: {
    fontSize: moderateScale(20),
    color: '#2C3E50',
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
    paddingTop: verticalScale(10),
  },
  statusContainer: {
    flexDirection: 'row',
    paddingTop: verticalScale(10),
    gap: scale(8),
  },
  statusChip: {
    paddingHorizontal: scale(7),
    paddingVertical: verticalScale(4),
    borderRadius: moderateScale(16),
    borderWidth: 1,
  },
  activeStatus: {
    backgroundColor: '#D4F4DD',
    borderColor: '#27AE60',
  },
  draftStatus: {
    backgroundColor: '#E3F2FD',
    borderColor: '#3498DB',
  },
  statusText: {
    fontSize: moderateScale(12),
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  activeStatusText: {
    color: '#27AE60',
  },
  draftStatusText: {
    color: '#3498DB',
  },
  card: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: scale(16),
    marginBottom: verticalScale(10),
    borderRadius: moderateScale(12),
    padding: moderateScale(16),
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: verticalScale(2) },
    shadowOpacity: 0.1,
    shadowRadius: moderateScale(8),
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(16),
  },
  cardIcon: {
    marginRight: scale(8),
  },
  cardTitle: {
    fontSize: moderateScale(18),
    fontWeight: '600',
    color: '#2C3E50',
  },
  financialGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  financialItem: {
    width: '50%',
    marginBottom: verticalScale(16),
    paddingRight: scale(8),
  },
  financialLabel: {
    fontSize: moderateScale(12),
    color: '#7F8C8D',
    marginBottom: verticalScale(4),
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  costPrice: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: '#E74C3C',
  },
  sellingPrice: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: '#27AE60',
  },
  profit: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: '#F39C12',
  },
  margin: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: '#9B59B6',
  },
  lineItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    paddingBottom: verticalScale(16),
    marginBottom: verticalScale(16),
  },
  lineItemHeader: {
    flexDirection: 'row',
    marginBottom: verticalScale(12),
  },
  lineItemNumber: {
    width: scale(24),
    height: scale(24),
    backgroundColor: '#c3c3cc',
    color: '#FFFFFF',
    textAlign: 'center',
    lineHeight: moderateScale(24),
    borderRadius: moderateScale(12),
    fontSize: moderateScale(12),
    fontWeight: '600',
    marginRight: scale(12),
  },
  lineItemContent: {
    flex: 1,
  },
  lineItemName: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: '#2C3E50',
    marginBottom: verticalScale(4),
  },
  lineItemType: {
    fontSize: moderateScale(12),
    color: '#7F8C8D',
    textTransform: 'capitalize',
  },
  lineItemPricing: {
    backgroundColor: '#F8F9FA',
    borderRadius: moderateScale(8),
    padding: moderateScale(12),
    marginLeft: scale(36),
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: verticalScale(8),
  },
  priceLabel: {
    fontSize: moderateScale(14),
    color: '#7F8C8D',
  },
  costPriceText: {
    fontSize: moderateScale(14),
    fontWeight: '600',
    color: '#E74C3C',
  },
  sellingPriceText: {
    fontSize: moderateScale(14),
    fontWeight: '600',
    color: '#27AE60',
  },
  marginText: {
    fontSize: moderateScale(14),
    fontWeight: '600',
    color: '#9B59B6',
  },
  grandTotal: {
    backgroundColor: '#F8F9FA',
    borderRadius: moderateScale(8),
    padding: moderateScale(16),
    marginTop: verticalScale(8),
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: verticalScale(8),
  },
  totalLabel: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: '#2C3E50',
  },
  totalValue: {
    fontSize: moderateScale(16),
    fontWeight: '700',
    color: '#2C3E50',
  },
  partiesGrid: {
    gap: scale(16),
  },
  partyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: verticalScale(8),
  },
  partyLabel: {
    fontSize: moderateScale(14),
    color: '#7F8C8D',
    fontWeight: '500',
  },
  headerLabel: {
    fontSize: moderateScale(14),
    color: '#7F8C8D',
    marginTop: verticalScale(4),
  },
  partyValue: {
    fontSize: moderateScale(14),
    fontWeight: '600',
    color: '#2C3E50',
    flex: 1,
    textAlign: 'right',
  },
  timelineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(16),
  },
  timelineDot: {
    width: scale(12),
    height: scale(12),
    borderRadius: moderateScale(6),
    backgroundColor: '#27AE60',
    marginRight: scale(12),
  },
  timelineDotSecondary: {
    backgroundColor: '#3498DB',
  },
  timelineContent: {
    flex: 1,
  },
  timelineLabel: {
    fontSize: moderateScale(14),
    color: '#7F8C8D',
    marginBottom: verticalScale(2),
  },
  timelineValue: {
    fontSize: moderateScale(14),
    fontWeight: '600',
    color: '#2C3E50',
  },
  bottomSpacer: {
    height: verticalScale(20),
  },
});

export default QuoteDetailScreen;
