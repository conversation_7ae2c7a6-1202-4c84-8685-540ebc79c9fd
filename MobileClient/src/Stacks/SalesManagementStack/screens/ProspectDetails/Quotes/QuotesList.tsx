import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  RefreshControl,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  ListRenderItem,
} from 'react-native';
import {
  Search,
  X,
  AlertCircle,
  RefreshCw,
  FileText,
  ChevronDown,
  Plus,
} from 'lucide-react-native';
import useQuotesList from './Hooks/useQuoteData';
import { Quote, UseQuotesListReturn } from './QuotesList.types';
import QuotesCard from './Components/QuotesCard';
import { quotesListStyles as styles } from './QuotesList.styles';
import SearchBar from '../../../../../Components/SearchInput';
import AppLoader from '../../../../../Components/Loader/AppLoader/InitialLoaderWithText';
import EmptyorErrorComponent from '../../../../../Components/EmptyOrError/EmptyOrErrorComponent';
import { useNavigationHook } from '../../../../../Hooks/Navigation/useNavigationHook';



const QuotesList = () => {
  const [searchText, setSearchText] = useState('');
  const {navigation} = useNavigationHook();
  const {
    quotes,
    loading,
    error,
    refreshing,
    hasMore,
    fetchQuotes,
    refreshQuotes,
    loadMoreQuotes,
    searchQuotes,
  } = useQuotesList();

  const handleSearch = (text: string) => {
    setSearchText(text);
    if (text.length > 2 || text.length === 0) {
      searchQuotes(text);
    }
  };

  const handleRetry = () => {
    fetchQuotes();
  };

  const handlePress = (item : Quote) => {
     navigation.navigate('QuoteDetailScreen', {
      quoteData : item
    });
  }

  const onEnquiryPress = (item: any) => {
    let transportationMode: string | null = null;

    switch (String(item.mode_of_shipment_id)) {
      case "1":
        transportationMode = "sea";
        break;
      case "2":
        transportationMode = "road";
        break;
      case "3":
        transportationMode = "air";
        break;
      default:
        transportationMode = null;
    }
    navigation.navigate("ViewEnquiryForm", {
      enquiryId: item.enquiry_id,
      transportationMode,
    });
  };

  const renderQuoteCard: ListRenderItem<Quote> = ({ item }) => <QuotesCard
    quote={item}
    onPress = {() => handlePress(item)}
    onEnquiryPress={() => onEnquiryPress(item)}
  />;

  const renderHeader = () => (
    <View style={styles.header}>
      <SearchBar
        placeholder="Search quotes..."
        onChangeText={handleSearch}
        value={searchText}
        //   style={{ flex: 1 }}
      />
    </View>
  );

  const renderLoadMoreButton = () => {
    if (!hasMore || loading) return null;

    return (
      <View style={styles.loadMoreContainer}>
        <TouchableOpacity style={styles.loadMoreButton} onPress={loadMoreQuotes}>
          <ChevronDown size={16} color="#495057" />
          <Text style={styles.loadMoreText}>Load More</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {renderHeader()}
      {loading && quotes.length === 0 ? (
        <AppLoader />
      ) : error && quotes.length === 0 ? (
        <EmptyorErrorComponent
          message="Unable to load quotes"
          handleRefresh={handleRetry}
          lucideIcon={<FileText size={60} color="#94a3b8" />}
        />
      ) : (
        <FlatList
          data={quotes}
          keyExtractor={(item) => item.price_sheet_id}
          renderItem={renderQuoteCard}
          contentContainerStyle={styles.listContent}
          style={styles.listContainer}
          showsVerticalScrollIndicator={false}
          bounces={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={refreshQuotes}
              colors={['#007bff']}
              tintColor="#007bff"
              style={styles.refreshControl}
            />
          }
          ListEmptyComponent={() => {
            return <EmptyorErrorComponent message="No quotes available" />;
          }}
          ListFooterComponent={renderLoadMoreButton}
          onEndReached={() => {
            if (hasMore && !loading) {
              loadMoreQuotes();
            }
          }}
          onEndReachedThreshold={0.1}
        />
      )}
    </View>
  );
};

export default QuotesList;
