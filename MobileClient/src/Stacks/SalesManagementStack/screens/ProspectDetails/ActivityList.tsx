import React, { useState } from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  Modal,
} from 'react-native';
import {
  Card,
  Text,
  Badge,
} from 'react-native-paper';
import { Pencil } from 'lucide-react-native';
import dayjs from 'dayjs';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { fontFamily } from '../../../../Common/Theme/typography';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';

interface AssignedUser {
  first_name: string;
  email: string;
  account_user_id: string;
}

interface Activity {
  sales_activity_id: string;
  sales_activity_title: string;
  sales_activity_description: string;
  sales_activity_date: string;
  start_date_time: string;
  end_date_time: string;
  sales_activity_type_name: string;
  sales_activity_state_name: string;
  sales_activity_estimated_time_name: string;
  area_name: string;
  organization_name: string;
  assigned_users: AssignedUser[];
}

interface Props {
  activities: Activity[];
  fetchActivities:() => {}
}

interface DropdownItem {
  id: string;
  name: string;
}

const getBadgeStyle = (label: string) => {
  switch (label?.toLowerCase()) {
    case 'over due':
      return { backgroundColor: '#fee2e2', color: '#991b1b' };
    case 'cancelled':
      return { backgroundColor: '#e5e7eb', color: '#374151' };
    case 'to do':
      return { backgroundColor: '#e0f2fe', color: '#1e40af' };
    case 'completed':
      return { backgroundColor: '#dcfce7', color: '#166534' };
    case 'face to face':
      return { backgroundColor: '#dcfce7', color: '#166534' };
    case 'online':
      return { backgroundColor: '#e0f2fe', color: '#1e40af' };
    case 'call':
      return { backgroundColor: '#ede9fe', color: '#6b21a8' };
    default:
      return { backgroundColor: '#FFEDD5', color: '#92400E' };
  }
};

const ActivityCard: React.FC<{ activity: Activity; onPress: () => void;onPressEdit:() => void; }> = ({
  activity,
  onPress,
  onPressEdit
}) => (
  <TouchableOpacity style={{ flex: 1 }} onPress={onPress}>
    <Card style={styles.card} mode="contained">
      <View style={styles.rowSpaceBetween}>
        <Text style={styles.title}>{activity.sales_activity_title}</Text>
        <TouchableOpacity
          onPress={onPressEdit}
          style={styles.editButton}
        >
          <Pencil size={moderateScale(16)} color="#2563EB" />
        </TouchableOpacity>
      </View>
      <View style={styles.row}>
        <Badge style={[styles.typeBadge, getBadgeStyle(activity.sales_activity_type_name)]}>
          {activity.sales_activity_type_name}
        </Badge>
        <Badge style={[styles.typeBadge, getBadgeStyle(activity.sales_activity_state_name)]}>
          {activity.sales_activity_state_name}
        </Badge>
      </View>

      <View style={styles.row}>
        {/* <Clock4 size={14} color="#6B7280" /> */}
        <Text style={styles.dateText}>
          {dayjs(activity.sales_activity_date).format('MMM DD, YYYY')} /{' '}
          <Text style={styles.timeRange}>
            {dayjs(activity.start_date_time).format('hh:mm A')} -{' '}
            {dayjs(activity.end_date_time).format('hh:mm A')}
          </Text>
        </Text>
      </View>
      <Text style={styles.locationText}>Location: {activity.area_name || '--'}</Text>
       <Text style={styles.durationText}>
          Duration: {activity.sales_activity_estimated_time_name}
        </Text>

    </Card>
  </TouchableOpacity>
);

const ActivityList: React.FC<Props> = ({ activities,fetchActivities }) => {
  const [selectedType, setSelectedType] = useState<string>('Activity Timeline');
  const navigation=useNavigation()
  const controlUnitId = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedControlUnit?.data?.id
  );
  const typeOptions = ['Activity Timeline', 'Face To Face', 'Online', 'Call'];
const filteredActivities =
  selectedType === 'Activity Timeline'
    ? activities :
    activities.filter(
        (activity) => {
          return activity?.sales_activity_type_name?.toLowerCase() === selectedType?.toLowerCase()
          }
      );

  return (
    <View style={{ flex: 1, }}>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginVertical: verticalScale(10),
          marginHorizontal: scale(14)
        }}
      >
        {typeOptions.map((type) => (
          <TouchableOpacity key={type} onPress={() => setSelectedType(type)}>
            <Text style={[styles.typeChip, selectedType === type && styles.typeChip_active]}>
              {type}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      {filteredActivities.length > 0 ? (
        <FlatList
          data={filteredActivities}
          keyExtractor={(item) => item.sales_activity_id}
          renderItem={({ item }) => (
            <ActivityCard
              activity={item}
              onPress={() => {
                navigation.navigate('ActivityDetailsModal',{activity:item})
              }}
              onPressEdit={() => {
                navigation.navigate('CreateActivityScreen', {
                  selectedEvent:item,
                  controlUnitId: controlUnitId,
                  refreshForActivityTabs:fetchActivities
                })
              }}
            />
          )}
          bounces={false}
          contentContainerStyle={{ paddingBottom: verticalScale(20) }}
          persistentScrollbar={true}
          style={{paddingHorizontal: scale(14)}}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No activities available</Text>
        </View>
      )}
    </View>
  );
};

export default ActivityList;

const styles = StyleSheet.create({
  card: {
    marginBottom: verticalScale(10),
    borderRadius: 8,
    borderColor: '#E5E7EB',
    borderWidth: 1,
    backgroundColor: '#fff',
    elevation: 0,
    padding: moderateScale(10),
  },
  title: {
    fontSize: moderateScale(16),
    fontFamily: fontFamily.medium,
    color: '#000',
    width: '60%',
  },
  durationText: {
    fontSize: moderateScale(12),
    color: '#647488',
    marginLeft: scale(5),
  },
  timeRange: {
    fontSize: moderateScale(12),
    color: '#647488',
  },
  locationText: {
    fontSize: moderateScale(12),
    color: '#647488',
    marginVertical: verticalScale(5),
    marginLeft: scale(5),
  },
  description: {
    fontSize: moderateScale(13),
    color: '#647488',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: verticalScale(5),
  },
  rowSpaceBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateText: {
    fontSize: moderateScale(12),
    color: '#647488',
    marginLeft: scale(5),
  },
  typeBadge: {
    marginRight: scale(5),
    paddingHorizontal: moderateScale(9),
    fontSize: moderateScale(11),
    fontFamily: fontFamily.regular,
  },
  emptyContainer: {
    flex:1,
    justifyContent:"center",
    alignItems: 'center',
  },
  emptyText: {
    color: '#6B7280',
    fontSize: moderateScale(14),
  },
   typeChip_active: {
    fontSize: moderateScale(10),
    backgroundColor: '#3377FF',
    color: '#fff',
    borderRadius: 30,
    paddingHorizontal: scale(15),
    paddingVertical: verticalScale(5),
    fontFamily: fontFamily.medium,
  },
  typeChip: {
    fontSize: moderateScale(10),
    backgroundColor: '#fff',
    color: '#000',
    borderRadius: 30,
    paddingHorizontal: scale(15),
    paddingVertical: verticalScale(5),
    fontFamily: fontFamily.medium,
    borderColor:"#E4E4E7",
    borderWidth:1
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(6),
    paddingVertical: scale(6),
    backgroundColor: '#EFF6FF',
    borderRadius: 30,
  },
});

const dropdownStyles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    maxHeight: '70%',
    paddingHorizontal:scale(14),
    paddingTop: scale(15),
    paddingBottom: verticalScale(25),
  },
  title: {
    fontSize: moderateScale(15),
    fontFamily:fontFamily.semiBold,
    textAlign: 'center',
    marginBottom: verticalScale(10),
  },
  itemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: verticalScale(8),
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  actionRow: {
    flexDirection: 'row',
    gap: scale(10),
    marginTop: scale(16),
  },
  clearBtn: {
    flex: 1,
    borderRadius: 4,
    backgroundColor: 'rgba(239, 241, 246, 1)',
  },
  applyBtn: {
    flex: 1,
    borderRadius: 4,
    backgroundColor: 'rgba(51, 119, 255, 1)',
  },
});
