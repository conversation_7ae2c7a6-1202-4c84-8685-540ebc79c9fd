import React, { useState, useCallback, useEffect } from 'react';
import { View, RefreshControl, FlatList, StatusBar } from 'react-native';
import { useEnquiryData } from './Hooks/useEnquiryData';
import { createStyles } from './EnquiryDetail.styles';
import FilterModal from './FilterModal';
import Header from './RenderComponents/EnquiryHeader';
import EnquiryCard from './RenderComponents/EnquiryCard';
import { TRANSPORTATION_TYPE_ID_MAP } from './Constants/FilterTypes';
import AppLoader from '../../../../../Components/Loader/AppLoader/InitialLoaderWithText';
import EmptyorErrorComponent from '../../../../../Components/EmptyOrError/EmptyOrErrorComponent';
import { useFocusEffect } from '@react-navigation/native';

const EnquiryDetail = ({ type,navigation }: any) => {
  const {
    enquiries,
    loading,
    error,
    refreshing,
    loadMore,
    refresh,
    retry,
    fetchEnquirydata,
    setFilterState,
    handleSearch
  } = useEnquiryData(type);

  const styles = createStyles();
  // console.log("type data", type)
  const [showFilter, setShowFilter] = useState(false);
  const [appliedFilters, setAppliedFilters] = useState({
    enquiryType: 'All types',
    transportationType: 'All types',
  });
  useEffect(()=>{
    setAppliedFilters({
      enquiryType: 'All types',
      transportationType: 'All types',
    })
  },[type])

  const onRefresh = useCallback(() => refresh(), [refresh]);

  useFocusEffect(useCallback(() => {
    onRefresh()
  }, [onRefresh]))
  const loadMoreData = useCallback(() => loadMore(), [loadMore]);
  const handleApplyFilters = useCallback(
    (filters: any) => {
      console.log('filters data', filters);
      const enquiry_type = filters?.enquiryType === 'All types' ? 'ALL' : filters?.enquiryType;
      const transportation_type_id = TRANSPORTATION_TYPE_ID_MAP[filters?.transportationType] ?? 0;

      setAppliedFilters(filters);
      setFilterState({ enquiry_type, transportation_type_id });
      fetchEnquirydata(1, true, enquiry_type, transportation_type_id);
    },
    [fetchEnquirydata, type]
  );

  const onPressEnquiry = ({
    enquiryId,
    transportationMode,
  }: {
    enquiryId: string;
    transportationMode: string;
  }) => {
    navigation.navigate('ViewEnquiryForm', {
      enquiryId,
      transportationMode,
    });
  };

  const handleEnquiryCardNavigation = (item : any, type : string) =>{
    const {
      account_id, workspace_id, org_id, control_unit_id, created_by
    } = item;
    navigation.navigate('EnquiryForm',{orgId:org_id,accountId:account_id,workspaceId:workspace_id,controlUnitId:control_unit_id,createdBy:created_by, type , item : item})
  }

  const hasActiveFilters =
    appliedFilters.enquiryType !== 'All types' || appliedFilters.transportationType !== 'All types';

  if (error ) {
    return (
      <EmptyorErrorComponent
        message="Failed to load data. Please try again."
        handleRefresh={retry}
      />
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#fff" />
      <Header
        hasActiveFilters={hasActiveFilters}
        setShowFilter={setShowFilter}
        appliedFilters={appliedFilters}
        type = {type}
        handleSearch = {handleSearch}
      />

      <FlatList
        data={enquiries}
        renderItem={({ item, index }) => <EnquiryCard item={item} type = {type} index={index} onPressEnquiry={onPressEnquiry} handleEnquiryCardNavigation = {handleEnquiryCardNavigation}/>}
        keyExtractor={(item, index) => item.enquiry_id || index.toString()}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        keyboardDismissMode='on-drag'
        // refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        onEndReached={loadMoreData}
        onEndReachedThreshold={0.5}
        bounces={false}
        ListEmptyComponent={
          loading && enquiries?.length === 0 ? (
            <AppLoader message='Loading data...'/>
          ) : (
            <EmptyorErrorComponent message="No data Found" 
            
            />
          )
        }
        initialNumToRender={10}
        maxToRenderPerBatch={10}
        windowSize={10}
        removeClippedSubviews
        getItemLayout={(_, index) => ({
          length: 200,
          offset: 200 * index,
          index,
        })}
      />

      <FilterModal
        visible={showFilter}
        onClose={() => setShowFilter(false)}
        onApplyFilters={handleApplyFilters}
        currentFilters={appliedFilters}
      />
    </View>
  );
};

export default EnquiryDetail;
