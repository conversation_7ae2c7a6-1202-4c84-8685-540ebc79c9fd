import { StyleSheet, Dimensions } from 'react-native';
import { moderateScale, verticalScale, scale } from 'react-native-size-matters';
import { fontFamily } from '../../../../../Common/Theme/typography';


export const createStyles = () => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  
  header: {
    flexDirection : 'row',
    justifyContent : "space-between",
    alignItems : 'center',
    backgroundColor: '#fff',
    paddingTop: 10,
    paddingHorizontal: 20,

  },
  
  headerTitle: {
    fontSize: moderateScale(20),
    fontFamily : fontFamily.semiBold,
    color: '#1e293b',
    // marginBottom: 4,
  },
  
  headerSubtitle: {
    fontSize: moderateScale(12),
    fontFamily : fontFamily.semiBold,
    color: '#64748b',
    marginBottom: 16,
  },
  
  headerActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  
  actionButton: {
    padding: 8,
    marginLeft: 12,
    borderRadius: 8,
    backgroundColor: '#f1f5f9',
  },
  activeFilterButton: {
    backgroundColor: '#eff6ff',
    borderWidth: 1,
    borderColor: '#bfdbfe',
  },
  
  filterIndicator: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: '#2563eb',
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  filterIndicatorText: {
    fontSize: moderateScale(10),
    color: '#fff',
    fontFamily : fontFamily.semiBold,
  },

  listContainer: {
    padding: 16,
    paddingBottom: 100,
  },
  
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    paddingBottom : 0,
    // paddingTop: 26,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 0.5,
    borderColor: '#3377ff90',
    // borderLeftColor : "#3377ff",
    // borderRightColor : "#3377ff",
    // borderLeftWidth : 0.5
  },
  
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
     fontFamily : fontFamily.regular,
     fontSize : moderateScale(11)
  },
  
  cardHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  
  cardHeaderText: {
    marginLeft: 12,
    flex: 1,
  },
  
  title: {
    fontSize: moderateScale(12),
    fontFamily : fontFamily.semiBold,
    color: '#1e293b',
    marginBottom: 2,
  },
  
  subtitle: {
    fontSize: moderateScale(11),
    color: '#64748b',
    fontFamily : fontFamily.semiBold,
  },
  
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginHorizontal: 12,
    alignSelf : 'center'
  },
  
  statusText: {
    fontSize: moderateScale(8),
    fontFamily : fontFamily.semiBold,
    color: '#fff',
    textTransform: 'uppercase',
  },
  
  // Route Styles
  routeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: moderateScale(16),
    // paddingHorizontal: 8,
  },
  
  routePoint: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  routeIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f1f5f9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  
  routeText: {
    flex: 1,
    marginLeft : moderateScale(9)
  },
  
  routeLabel: {
    fontSize: moderateScale(10),
    color: '#64748b',
    fontFamily : fontFamily.semiBold,
    textTransform: 'uppercase',
    marginBottom: 2,
  },
  
  routeValue: {
    fontSize: moderateScale(10),
    color: '#1e293b',
    fontFamily : fontFamily.semiBold,
    textTransform: 'uppercase',
  },
  
  routeArrow: {
    paddingHorizontal: 16,
  },
  
  // Details Grid
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    // marginBottom: 12,
  },
  
  detailItem: {
    width: '50%',
    flexDirection: 'row',
    // alignItems: 'center',
    marginBottom: moderateScale(16),
    // paddingRight: scale(15),
    paddingRight: scale(15),
  
  },
  
  detailLabel: {
    fontSize: moderateScale(10),
    fontFamily : fontFamily.semiBold,
    color: '#64748b',
    marginBottom: 2,
    textTransform: 'uppercase',
    // flex: 1,
  },
  
  detailValue: {
    fontSize: moderateScale(10),
    color: '#1e293b',
    fontFamily : fontFamily.semiBold,
   
   
    // marginLeft : moderateScale(23)
    // textAlign: 'center',
  },
  
  // Note Container
  noteContainer: {
    backgroundColor: '#f8fafc',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#3b82f6',
  },
  
  noteText: {
    fontSize: moderateScale(10),
    fontFamily : fontFamily.semiBold,
    color: '#475569',
    lineHeight: 18,
  },
  
  // Transshipment Badge
  transshipmentBadge: {
    position: 'absolute',
    top: 40,
    right: 12,
    backgroundColor: '#fef3c7',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#f59e0b',
  },
  
  transshipmentText: {
    fontSize: moderateScale(8),
    color: '#d97706',
    fontFamily : fontFamily.semiBold,
  },
  
  // Footer Loader
  footerLoader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  
  footerText: {
    marginLeft: 8,
    fontSize: moderateScale(12),
    fontFamily : fontFamily.semiBold,
    color: '#64748b',
  },
  
  // Empty State
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  
  emptyTitle: {
    fontSize: moderateScale(13),
    fontFamily : fontFamily.semiBold,
    color: '#1e293b',
    marginTop: 16,
    marginBottom: 8,
  },
  
  emptySubtitle: {
    fontSize: moderateScale(13),
    fontFamily : fontFamily.semiBold,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  
  // Error State
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  
  errorTitle: {
    fontSize: moderateScale(14),
    fontFamily : fontFamily.semiBold,
    color: '#1e293b',
    marginTop: 16,
    marginBottom: 8,
  },
  
  errorSubtitle: {
    fontSize: moderateScale(13),
    fontFamily : fontFamily.semiBold,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  
  // Retry Button
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  
  retryText: {
    fontSize: moderateScale(13),
    color: '#2563eb',
    fontFamily : fontFamily.semiBold,
    marginLeft: 8,
  },
  
  // Loader
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});