import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  StyleSheet,
} from 'react-native';
import { X, Check, RotateCcw } from 'lucide-react-native';
import { moderateScale } from 'react-native-size-matters';
import {
  ENQUIRY_TYPES,
  TRANSPORTATION_TYPES,
} from './Constants/FilterTypes';
import { fontFamily } from '../../../../../Common/Theme/typography';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
const { height: SCREEN_HEIGHT } = Dimensions.get('window');

const FilterModal = ({ visible, onClose, onApplyFilters, currentFilters = {} } : any) => {
  const [selectedEnquiryType, setSelectedEnquiryType] = useState(
    currentFilters.enquiryType || 'All types'
  );
  const [selectedTransportationType, setSelectedTransportationType] = useState(
    currentFilters.transportationType || 'All types'
  );
  const {bottom} = useSafeAreaInsets();
  const handleApplyFilters = () => {
    const filters = {
      enquiryType: selectedEnquiryType,
      transportationType: selectedTransportationType,
    };
    onApplyFilters(filters);
    onClose();
  };

  const handleResetFilters = () => {
    setSelectedEnquiryType('All types');
    setSelectedTransportationType('All types');
  };

  const hasActiveFilters =
    selectedEnquiryType !== 'All types' || selectedTransportationType !== 'All types';

  const renderFilterOption = (option, selectedValue, onSelect)  => (
    <TouchableOpacity
      key={option.value}
      style={[styles.filterOption, selectedValue === option.value && styles.selectedFilterOption]}
      onPress={() => onSelect(option.value)}
      activeOpacity={0.7}
    >
      <Text
        style={[
          styles.filterOptionText,
          selectedValue === option.value && styles.selectedFilterOptionText,
        ]}
      >
        {option.label}
      </Text>
    </TouchableOpacity>
  );

  if (!visible) return null;

  return (
    <Modal
      transparent
      visible={visible}
      animationType="slide"
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={[styles.overlay]}>
          <TouchableOpacity style={styles.overlayTouch} onPress={onClose} activeOpacity={1} />
        </View>

        <View style={[styles.filterContainer, {
          paddingBottom : bottom + 20
        }]}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Filters</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <X size={24} color="#6b7280" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false} bounces={false}>
            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>Enquiry Type</Text>
              <View style={styles.filterOptionsContainer}>
                {ENQUIRY_TYPES.map((option) =>
                  renderFilterOption(option, selectedEnquiryType, setSelectedEnquiryType)
                )}
              </View>
            </View>

            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>Transportation Type</Text>
              <View style={styles.filterOptionsContainer}>
                {TRANSPORTATION_TYPES.map((option) =>
                  renderFilterOption(
                    option,
                    selectedTransportationType,
                    setSelectedTransportationType
                  )
                )}
              </View>
            </View>
          </ScrollView>

          {/* Footer */}
          <View style={styles.footer}>
            <TouchableOpacity
              style={[styles.footerButton, styles.resetButton]}
              onPress={handleResetFilters}
              disabled={!hasActiveFilters}
            >
              <RotateCcw size={16} color={hasActiveFilters ? '#6b7280' : '#d1d5db'} />
              <Text
                style={[styles.resetButtonText, !hasActiveFilters && styles.disabledButtonText]}
              >
                Reset
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.footerButton, styles.applyButton]}
              onPress={handleApplyFilters}
            >
              <Text style={styles.applyButtonText}>Apply Filters</Text>
              {hasActiveFilters && (
                <View style={styles.filterBadge}>
                  <Text style={styles.filterBadgeText}>
                    {
                      Object.values({
                        enquiryType: selectedEnquiryType,
                        transportationType: selectedTransportationType,
                      }).filter((val) => val !== 'All types').length
                    }
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  overlayTouch: {
    flex: 1,
  },
  filterContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: SCREEN_HEIGHT * 0.8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  headerTitle: {
    fontSize: moderateScale(15),
    fontFamily : fontFamily.semiBold,
    color: '#1e293b',
  },
  closeButton: {
    padding: 4,
  },
  content: {
    flexGrow: 1,
    paddingHorizontal: 20,
  },
  filterSection: {
    marginVertical: 20,
  },
  filterSectionTitle: {
    fontSize: moderateScale(12),
    fontFamily : fontFamily.semiBold,
    color: '#1e293b',
    marginBottom: moderateScale(12),
  },
  filterOptionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    backgroundColor: '#fff',
  },
  selectedFilterOption: {
    backgroundColor: '#eff6ff',
    borderColor: '#2563eb',
  },
  filterOptionText: {
    fontSize: moderateScale(11),
    fontFamily : fontFamily.semiBold,
    color: '#475569',

  },
  selectedFilterOptionText: {
    color: '#2563eb',
    fontSize: moderateScale(11),
    fontFamily : fontFamily.semiBold,
  },
  footer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: '#f1f5f9',
    gap: 12,
  },
  footerButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  resetButton: {
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  resetButtonText: {
 
    color: '#6b7280',
    fontSize: moderateScale(12),
    fontFamily : fontFamily.semiBold,
  },
  disabledButtonText: {
    color: '#d1d5db',
  },
  applyButton: {
    backgroundColor: '#2563eb',
    position: 'relative',
  },
  applyButtonText: {
  
    color: '#fff',
    fontSize: moderateScale(12),
    fontFamily : fontFamily.semiBold,
  },
  filterBadge: {
    backgroundColor: '#fff',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  filterBadgeText: {
    fontSize: moderateScale(12),
    fontFamily : fontFamily.semiBold,
    color: '#2563eb',
    
  },
});

export default FilterModal;
