// ViewJobDetails.tsx
import React, { useEffect, useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
  Text,
  TouchableOpacity,
} from 'react-native';
import { verticalScale, scale, moderateScale } from 'react-native-size-matters';
import { Package, MapPin, Truck, Info, FileText, Container, ShieldCheck } from 'lucide-react-native';
import { getSchemaName } from '../../../../../Common/Utils/Storage';
import ApiClient from '../../../../../Common/API/ApiClient';
import AppHeader from '../../../../../Components/AppHeader';
import { encryptPayload } from '../../../../../Utils/payloadEncryption';
import { LEADS_ENDPOINTS } from '../../../../../Common/API/ApiEndpoints';
import { showToast } from '../../../../../Components/AppToaster/AppToaster';
import { fontFamily } from '../../../../../Common/Theme/typography';

export default function ViewJobDetails({ route }: any) {
  const { jobId, transportationMode } = route.params as {
    jobId: string;
    transportationMode: string;
  };
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchJobDetails();
  }, []);

  const fetchJobDetails = async () => {
    try {
      const schemaName = await getSchemaName();
      const payload = { schemaName };

      let endpoint = '';
      let key = '';

      switch (transportationMode) {
        case 'sea':
          endpoint = LEADS_ENDPOINTS.GET_SEA_JOB_DETAILS;
          key = 'sea_job_details';
          payload.sea_job_id = jobId;
          break;
        case 'air':
          endpoint = LEADS_ENDPOINTS.GET_AIR_JOB_DETAILS;
          key = 'air_job_details';
          payload.air_job_id = jobId;
          break;
        case 'road':
          endpoint = LEADS_ENDPOINTS.GET_ROAD_JOB_DETAILS;
          key = 'road_job_details';
          payload.road_job_id = jobId;
          break;
        default:
          showToast.error('Invalid transportation mode');
          setLoading(false);
          return;
      }

      const encrypted = await encryptPayload(payload);
      const res = await ApiClient.post(endpoint, encrypted);

      const jobData = res.data?.data?.[0]?.[key];
      if (jobData) {
        setData(jobData);
      } else {
        showToast.error('No job details found');
      }
    } catch (err) {
      console.log('Error fetching job details:', err);
      showToast.error('Error fetching job details');
    } finally {
      setLoading(false);
    }
  };

  const getIconForTitle = (title: string) => {
    switch (title) {
      case 'Basic Information':
        return <Info size={moderateScale(16)} color="#3377FF" />;
      case 'Route Information':
        return <MapPin size={moderateScale(16)} color="#3377FF" />;
      case 'Cargo Details':
        return <Package size={moderateScale(16)} color="#3377FF" />;
      case 'Additional Information':
        return <FileText size={moderateScale(16)} color="#3377FF" />;
      case 'FCL Job Items':
        return <Container size={moderateScale(16)} color="#3377FF" />;
      case 'LCL Job Items':
        return <Container size={moderateScale(16)} color="#3377FF" />;
      case 'Standard Cargo':
        return <Package size={moderateScale(16)} color="#3377FF" />;
      case 'LTL Jobs Item':
        return <Truck size={moderateScale(16)} color="#3377FF" />;
      case 'FTL Jobs Item':
        return <Truck size={moderateScale(16)} color="#3377FF" />;
      case 'Bulk Jobs Item':
        return <Container size={moderateScale(16)} color="#3377FF" />;
      case 'ULD Jobs Item':
        return <Container size={moderateScale(16)} color="#3377FF" />;
      case 'Associated Services':
        return <ShieldCheck size={moderateScale(16)} color="#3377FF" />;
      default:
        return <Info size={moderateScale(16)} color="#3377FF" />;
    }
  };

  const getBackgroundColorForTitle = (title: string) => {
    switch (title) {
      case 'Basic Information':
        return '#E0E7FF'; 
      case 'Route Information':
        return '#DCFCE7'; 
      case 'Cargo Details':
        return '#FFEDD5';
      case 'Additional Information':
        return '#EDE9FE'; 
      case 'FCL Job Items':
        return '#FEF9C3'; 
      case 'LCL Job Items':
        return '#FEE2E2'; 
      case 'Standard Cargo':
        return '#E0F2FE'; 
      case 'LTL Jobs Item':
        return '#F0FDF4'; 
      case 'FTL Jobs Item':
        return '#FFF7ED'; 
      case 'Bulk Jobs Item':
        return '#F5F3FF'; 
      case 'ULD Jobs Item':
        return '#FEFCE8'; 
      case 'Associated Services':
        return '#fef3c780';
      default:
        return '#F3F4F6'; 
    }
  };


  const hasData = (dataKey: string) => {
    if (!data || !data[dataKey]) return false;
    try {
      const parsed = JSON.parse(data[dataKey] || '[]');
      return Array.isArray(parsed) && parsed.length > 0;
    } catch {
      return false;
    }
  };

  const InfoCard = ({ title, children }: { title: string; children: React.ReactNode }) => (
    <View style={styles.infoCard}>
      <View
        style={[
          styles.cardHeader,
          {
            backgroundColor: getBackgroundColorForTitle(title),
          },
        ]}
      >
        {getIconForTitle(title)}
        <Text style={styles.cardTitle}>{title}</Text>
      </View>
      <View style={styles.cardContent}>
        <View style={styles.table}>{children}</View>
      </View>
    </View>
  );

  const InfoRow = ({ label, value, onlyValue = false }: any) =>
    value && value !== 'null' ? (
      <View
        style={[
          styles.tableRow,
          onlyValue && { justifyContent: 'flex-start' }, 
        ]}
      >
        {!onlyValue && label ? (
          <Text style={styles.cellLabel}>{label}</Text>
        ) : null}
        <Text
          style={[
            styles.cellValue,
            onlyValue && { width: '100%', textAlign: 'left' },
          ]}
        >
          {value}
        </Text>
      </View>
    ) : null;

  if (loading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#3377FF" />
        <Text style={styles.loadingText}>Loading job details...</Text>
      </View>
    );
  }

  if (!data) {
    return (
      <View style={styles.noDataContainer}>
        <AppHeader title="Job Details" />
        <View style={styles.noDataContent}>
          <Package size={moderateScale(48)} color="#9CA3AF" />
          <Text style={styles.noDataText}>No data available.</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#F8FAFC' }}>
      <AppHeader title="Job Details" />
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false} bounces={false}>
        <InfoCard title="Basic Information">
          <InfoRow label="Organization" value={data.organization_name || '--'} />
          <InfoRow label="Incoterm" value={`${data.incoterm_code || '--'} - ${data.incoterm_name || '--'}`} />
          <InfoRow label="Ready to Load" value={data.ready_to_load || '--'} />
          <InfoRow label="Transit Time" value={data.transit_time ? `${data.transit_time} days` : '--'} />
          <InfoRow label="Free Time" value={data.free_time ? `${data.free_time} days` : '--'} />
          <InfoRow label="Carrier" value={data.carrier_name || '--'} />
        </InfoCard>

        <InfoCard title="Route Information">
          <InfoRow
            label="From"
            value={`${data.from_city_name || data.from_port_name || '--'}(${data.from_port_code || data.from_address || '--'})`}
          />
          <InfoRow
            label="To"
            value={`${data.to_city_name || data.to_port_name || '--'}(${data.to_port_code || data.to_address || '--'})`}
          />
          <InfoRow label="Is Transshipment" value={data.is_transshipment || '0'} />
        </InfoCard>

        <InfoCard title="Cargo Details">
          <InfoRow label="Transportation" value={data.transportation_type_name || '--'} />
          <InfoRow
            label="Cargo Value"
            value={(data.currency_code && data.cargo_value) ? `${data.currency_code} ${data.cargo_value}` : '--'}
          />
          <InfoRow label="Cargo Type" value={data.cargo_type_name || '--'} />
          <InfoRow label="Temperature" value={data.temperature_regime ? `${data.temperature_regime}\u00B0C` : '--'} />
          <InfoRow label="HS Code" value={data.hs_code_category_name || '--'} />
          <InfoRow label="HS Sub-Code" value={data.hc_code_sub_category_name || '--'} />
        </InfoCard>

        {hasData('crm_fcl_job_items') && (
          <InfoCard title="FCL Job Items">
            {JSON.parse(data.crm_fcl_job_items || '[]').map((item: any, idx: number) => (
              <View key={idx} style={styles.itemCard}>
                <View style={styles.itemHeader}>
                  <Container size={moderateScale(14)} color="#6B7280" />
                  <Text style={styles.itemHeading}>Item {idx + 1}</Text>
                </View>
                <View style={styles.divider} />
                <InfoRow label="Weight" value={String(item.weight) || '--'} />
                <InfoRow label="Quantity" value={String(item.quantity) || '--'} />
                <InfoRow label="Container Type" value={item.container_type_name || '--'} />
              </View>
            ))}
          </InfoCard>
        )}

        {hasData('crm_lcl_job_items') && (
          <InfoCard title="LCL Job Items">
            {JSON.parse(data.crm_lcl_job_items || '[]').map((item: any, idx: number) => (
              <View key={idx} style={styles.itemCard}>
                <View style={styles.itemHeader}>
                  <Container size={moderateScale(14)} color="#6B7280" />
                  <Text style={styles.itemHeading}>Item {idx + 1}</Text>
                </View>
                <View style={styles.divider} />
                <InfoRow label="Packages" value={String(item.quantity) || '--'} />
                <InfoRow label="Gross Weight" value={String(item.gross_weight) || '--'} />
                <InfoRow label="Volume" value={String(item.volume) || '--'} />
              </View>
            ))}
          </InfoCard>
        )}

        {hasData('crm_standard_cargo_job_items') && (
          <InfoCard title="Standard Cargo">
            {JSON.parse(data.crm_standard_cargo_job_items || '[]').map((item: any, idx: number) => (
              <View key={idx} style={styles.itemCard}>
                <View style={styles.itemHeader}>
                  <Container size={moderateScale(14)} color="#6B7280" />
                  <Text style={styles.itemHeading}>Item {idx + 1}</Text>
                </View>
                <View style={styles.divider} />
                <InfoRow label="Quantity" value={String(item.quantity) || '--'} />
                <InfoRow label="Length" value={String(item.length) || '--'} />
                <InfoRow label="Width" value={String(item.width) || '--'} />
                <InfoRow label="Height" value={item.height || '--'} />
              </View>
            ))}
          </InfoCard>
        )}

        {hasData('crm_ltl_job_items') && (
          <InfoCard title="LTL Jobs Item">
            {JSON.parse(data.crm_ltl_job_items || '[]').map((item: any, idx: number) => (
              <View key={idx} style={styles.itemCard}>
                <View style={styles.itemHeader}>
                  <Container size={moderateScale(14)} color="#6B7280" />
                  <Text style={styles.itemHeading}>Item {idx + 1}</Text>
                </View>
                <View style={styles.divider} />
                <InfoRow label="Quantity" value={String(item.quantity) || '--'} />
                <InfoRow label="Gross Weight" value={String(item.gross_weight) || '--'} />
                <InfoRow label="Length" value={String(item.length) || '--'} />
                <InfoRow label="Width" value={item.width || '--'} />
                <InfoRow label="Height" value={item.height || '--'} />
              </View>
            ))}
          </InfoCard>
        )}

        {hasData('crm_ftl_job_items') && (
          <InfoCard title="FTL Jobs Item">
            {JSON.parse(data.crm_ftl_job_items || '[]').map((item: any, idx: number) => (
              <View key={idx} style={styles.itemCard}>
                <View style={styles.itemHeader}>
                  <Container size={moderateScale(14)} color="#6B7280" />
                  <Text style={styles.itemHeading}>Item {idx + 1}</Text>
                </View>
                <View style={styles.divider} />
                <InfoRow label="Truck Type" value={String(item.truck_type_name) || '--'} />
                <InfoRow label="Quantity" value={String(item.qty_of_trucks) || '--'} />
              </View>
            ))}
          </InfoCard>
        )}

        {hasData('crm_bulk_job_items') && (
          <InfoCard title="Bulk Jobs Item">
            {JSON.parse(data.crm_bulk_job_items || '[]').map((item: any, idx: number) => (
              <View key={idx} style={styles.itemCard}>
                <View style={styles.itemHeader}>
                  <Container size={moderateScale(14)} color="#6B7280" />
                  <Text style={styles.itemHeading}>Item {idx + 1}</Text>
                </View>
                <View style={styles.divider} />
                <InfoRow label="Ship Type" value={String(item.ship_type_name) || '--'} />
                <InfoRow label="Gross Weight" value={String(item.gross_weight) || '--'} />
              </View>
            ))}
          </InfoCard>
        )}

        {hasData('crm_uld_job_items') && (
          <InfoCard title="ULD Jobs Item">
            {JSON.parse(data.crm_uld_job_items || '[]').map((item: any, idx: number) => (
              <View key={idx} style={styles.itemCard}>
                <View style={styles.itemHeader}>
                  <Container size={moderateScale(14)} color="#6B7280" />
                  <Text style={styles.itemHeading}>Item {idx + 1}</Text>
                </View>
                <View style={styles.divider} />
                <InfoRow label="Container Type" value={String(item.container_type_id) || '--'} />
                <InfoRow label="Quantity" value={String(item.quantity) || '--'} />
                <InfoRow label="Weight" value={String(item.weight) || '--'} />
              </View>
            ))}
          </InfoCard>
        )}

        {data.associated_services && (() => {
          const services = JSON.parse(data.associated_services || '[]');
          if (services.length === 0) return null;

          return (
            <InfoCard title="Associated Services">
              <View style={styles.servicesContainer}>
                {services.map((service: any, idx: number) => (
                  <View key={idx} style={styles.serviceTag}>
                    <Text style={styles.serviceText}>
                      {service.associated_services_name || '--'}
                    </Text>
                  </View>
                ))}
              </View>
            </InfoCard>
          );
        })()}

        <View style={{ height: verticalScale(80) }} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8FAFC',
  },
  loadingText: {
    marginTop: verticalScale(16),
    fontSize: moderateScale(14),
    color: '#6B7280',
    fontFamily: fontFamily.medium,
  },
  container: {
    padding: scale(16),
    backgroundColor: '#F8FAFC',
  },
  infoCard: {
    marginBottom: verticalScale(16),
    borderRadius: scale(12),
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    paddingVertical: verticalScale(12),
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    backgroundColor: '#F9FAFB',
    borderTopLeftRadius: scale(12),
    borderTopRightRadius: scale(12),
  },
  cardTitle: {
    fontSize: moderateScale(14),
    fontWeight: '600',
    color: '#1F2937',
    marginLeft: scale(8),
    fontFamily: fontFamily.semiBold,
  },
  cardContent: {
    padding: scale(16),
  },
  table: {
    flexDirection: 'column',
    width: '100%',
  },
  tableRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: verticalScale(12),
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    alignItems: 'flex-start',
  },
  cellLabel: {
    fontSize: moderateScale(12),
    color: '#6B7280',
    width: '48%',
    flexWrap: 'wrap',
    fontFamily: fontFamily.medium,
    lineHeight: moderateScale(16),
  },
  cellValue: {
    fontSize: moderateScale(12),
    fontWeight: '500',
    color: '#1F2937',
    width: '48%',
    textAlign: 'right',
    flexWrap: 'wrap',
    fontFamily: fontFamily.medium,
    lineHeight: moderateScale(16),
  },
  itemCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: scale(8),
    borderColor: '#E5E7EB',
    borderWidth: 1,
    marginBottom: verticalScale(12),
    padding: scale(12),
  },
  servicesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6, 
    marginTop: 2,
  },
  serviceTag: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  serviceText: {
    fontSize: moderateScale(12),
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(8),
  },
  itemHeading: {
    fontSize: moderateScale(13),
    fontWeight: '600',
    color: '#374151',
    marginLeft: scale(6),
    fontFamily: fontFamily.semiBold,
  },
  divider: {
    height: 1,
    backgroundColor: '#E5E7EB',
    marginVertical: verticalScale(8),
  },
  noDataContainer: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  noDataContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: scale(16),
  },
  noDataText: {
    fontSize: moderateScale(16),
    color: '#6B7280',
    textAlign: 'center',
    marginTop: verticalScale(16),
    fontFamily: fontFamily.medium,
  },
});