import { FlatList, Text, View, RefreshControl, TouchableOpacity } from 'react-native';
import React, { useState } from 'react';
import { useJobsListHook } from './useJobsListHook';
import { createStyles } from './JobsList.Styles';
import { JobCard } from './Components/JobsListCard';
import EmptyorErrorComponent from '../../../../../Components/EmptyOrError/EmptyOrErrorComponent';
import AppLoader from '../../../../../Components/Loader/AppLoader/InitialLoaderWithText';
import { useNavigationHook } from '../../../../../Hooks/Navigation/useNavigationHook';

const JobsList = ({}: any) => {
  const styles = createStyles();
  const { jobsListData, error, fetchJobsData, loading } = useJobsListHook();
  const [refreshing, setRefreshing] = useState(false);
  const { navigation } = useNavigationHook();
  const onRefresh = async () => {
    setRefreshing(true);
    await fetchJobsData();
    setRefreshing(false);
  };

  const onPressJob = ({
    jobId,
    transportationMode,
  }: {
    jobId: string;
    transportationMode: string;
  }) => {
    navigation.navigate('ViewJobDetails', {
      jobId,
      transportationMode,
    });
  };

  return (
    <View style={styles.container}>
      {error ? (
        <EmptyorErrorComponent message="Something went wrong" handleRefresh={onRefresh} />
      ) : (
        <FlatList
          data={jobsListData}
          renderItem={({ item }) => <JobCard item={item} onPressJob={onPressJob} />}
          keyExtractor={(item) => item.job_id || Math.random().toString()}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          bounces={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#3498db']}
              tintColor="#3498db"
            />
          }
          ListEmptyComponent={
            loading && jobsListData.length === 0    ? (
              <AppLoader message="Loading jobs..." />
            ) : (
              
              <EmptyorErrorComponent message="No Jobs Found" />
              
            )
           
          }
          onEndReachedThreshold={0.5}
          onEndReached={() => {
            if (!loading && jobsListData.length > 0) {
              fetchJobsData(true); 
            }
          }}
        />
      )}
    </View>
  );
};

export default JobsList;
