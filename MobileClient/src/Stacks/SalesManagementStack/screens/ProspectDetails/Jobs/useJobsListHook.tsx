import { useEffect, useState } from 'react';
import {
  getAccountId,
  getSchemaName,
  getSelectedWorkspaceId,
} from '../../../../../Common/Utils/Storage';
import { useSelector } from 'react-redux';
import { useRoute } from '@react-navigation/native';
import ApiClient from '../../../../../Common/API/ApiClient';
import { ENDPOINTS } from '../../../../../Common/API/ApiEndpoints';
import { encryptPayload } from '../../../../../Utils/payloadEncryption';

export const useJobsListHook = () => {
  const [jobsListData, setJobsListData] = useState<any[]>([]);
  const [error, setError] = useState<null | string>(null);
  const [loading, setLoading] = useState(false);
  const [pageNumber, setPageNumber] = useState(1); // 👈 added state

  const selectedControlUnitID = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedControlUnit?.data?.id
  );
  const route = useRoute();
  const lead = route.params?.leadDetails?.lead || {};
  const { accountId = '', workspaceId = '', controlUnitId = '', orgId = '' } = lead;

  const fetchJobsData = async (loadMore = false) => {
    try {
      setLoading(true);
      const schemaName = await getSchemaName();
      const account_id = await getAccountId();
      const workspace_id = await getSelectedWorkspaceId();

      const nextPage = loadMore ? pageNumber + 1 : 1; // 👈 if load more, increase page
      const payload = {
        schemaName,
        contextJson: {
          account_id,
          workspace_id,
          control_unit_id: selectedControlUnitID,
        },
        payloadJson: {
          page_number: nextPage,
          limit: 10,
          search_text: '',
          org_id: orgId,
        },
      };

      const encryptedPayload = await encryptPayload(payload);
      const response = await ApiClient.post(ENDPOINTS.LEADS.ALL_JOBS, encryptedPayload);

      if (loadMore) {
        setJobsListData((prev) => [...prev, ...response?.data?.data[1]]); // append
        setPageNumber(nextPage);
      } else {
        setJobsListData(response?.data?.data[1] || []);
        setPageNumber(1);
      }
    } catch (error) {
      setError('There was an issue');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (selectedControlUnitID && orgId) {
      console.log('fetching jobs data');
      fetchJobsData();
    }
  }, [selectedControlUnitID, orgId]);

  return {
    jobsListData,
    error,
    fetchJobsData,
    loading,
  };
};

