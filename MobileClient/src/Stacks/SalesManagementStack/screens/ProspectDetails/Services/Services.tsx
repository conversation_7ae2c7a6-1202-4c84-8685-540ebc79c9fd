import React, { useCallback } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Text, Checkbox, Divider, ActivityIndicator } from 'react-native-paper';
import { verticalScale, moderateScale, scale } from 'react-native-size-matters';
import { useFocusEffect } from '@react-navigation/native';
import { useServicesData } from './useServicesData';
import { Pencil } from 'lucide-react-native';
import { NavigationProp, ParamListBase } from '@react-navigation/native';
import { fontFamily } from '../../../../../Common/Theme/typography';

interface Props {
  orgId: string;
  navigation: NavigationProp<ParamListBase>;
}

const Services = ({ orgId, navigation }: Props) => {
  const {
    jobTypes,
    selectedJobTypes,
    specialHandlingRequirements,
    customClearanceNeeds,
    loading,
    refetch,
  } = useServicesData(orgId);

  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [orgId])
  );

  const isSelected = (serviceId: string) => selectedJobTypes?.some((id: any) => id === serviceId);

  const selectedJobObjects = jobTypes.filter((j) => isSelected(j.id));

  if (loading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#2563EB" />
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} bounces={false}>
      <View style={styles.headerRow}>
        <Text
          style={{
            fontSize: moderateScale(14),
            color: '#000',
            fontFamily: fontFamily.medium,
          }}
        >
          Service Information
        </Text>
        <TouchableOpacity
          onPress={() =>
            navigation.navigate('EditServicesForm', {
              orgId,
              jobTypes,
              selectedJobTypes,
              specialHandlingRequirements,
              customClearanceNeeds,
            })
          }
          style={styles.editButton}
        >
          <Pencil size={16} color="#2563EB" />
          {/* <Text style={styles.editText}>Edit</Text> */}
        </TouchableOpacity>
      </View>
      <View style={{paddingHorizontal:scale(5)}} >
        
      <View style={styles.headerRow}>
        <Text style={styles.sectionHeader}>Interested Services</Text>
        <Text style={styles.selectedCount}>{selectedJobObjects.length} selected</Text>
      </View>

      <View style={styles.checkboxContainer}>
        {selectedJobObjects.map((service: any) => (
          <View key={service.id} style={styles.checkboxRow}>
            <Checkbox.Android status="checked" color="#2563EB66" />
            <Text style={styles.checkboxLabel}>{service.job_type_name}</Text>
          </View>
        ))}
      </View>

      <Divider style={styles.divider} />

      <Text style={styles.sectionHeader}>Special Requirements</Text>
      <View style={styles.detailRow}>
        <Text style={styles.label}>Special Handling Requirements</Text>
        <Text style={styles.value}>{specialHandlingRequirements || '--'}</Text>
      </View>
      <View style={styles.detailRow}>
        <Text style={styles.label}>Custom Clearance Needs</Text>
        <Text style={styles.value}>{customClearanceNeeds || '--'}</Text>
      </View>

      {/* <Divider style={styles.divider} /> */}

      {/* <Text style={styles.sectionHeader}>Service Summary</Text>
      <View style={styles.detailRow}>
        <Text style={styles.label}>Total Services</Text>
        <Text style={styles.value}>{selectedJobObjects.length}</Text>
      </View>
      <View style={styles.detailRow}>
        <Text style={styles.label}>Service Categories</Text>
        <Text style={styles.value}>Sea Freight</Text>
      </View>
      <View style={styles.detailRow}>
        <Text style={styles.label}>Special Requirements</Text>
        <Text style={styles.value}>
          {specialHandlingRequirements || customClearanceNeeds ? 'Yes' : 'No'}
        </Text>
      </View> */}
      </View>

    </ScrollView>
  );
};

export default Services;

const styles = StyleSheet.create({
  container: {
    padding: scale(16),
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: moderateScale(15),
    fontWeight: 'bold',
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(6),
    paddingVertical: scale(6),
    backgroundColor: '#EFF6FF',
    borderRadius: 30,
  },
  editText: {
    fontSize: moderateScale(12),
    color: '#2563EB',
    marginLeft: 4,
    fontWeight: '600',
  },
  sectionHeader: {
    fontSize: moderateScale(13),
    fontWeight: '600',
    marginTop: verticalScale(12),
    marginBottom: verticalScale(8),
    color: '#000',
  },
  checkboxContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '50%',
    marginBottom: verticalScale(3),
  },
  checkboxLabel: {
    fontSize: moderateScale(12),
    width:scale(100),
  },
  selectedCount: {
    fontSize: moderateScale(12),
    color: '#6B7280',
    marginTop: verticalScale(4),
  },
  divider: {
    backgroundColor: '#aaa',
    marginTop:verticalScale(5)
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: verticalScale(6),
  },
  label: {
    fontSize: moderateScale(12),
    color: '#6B7280',
    width: scale(150),
  },
  value: {
    fontSize: moderateScale(12),
    color: '#1F2937',
    fontWeight: '500',
    width: scale(150),
  },
});
