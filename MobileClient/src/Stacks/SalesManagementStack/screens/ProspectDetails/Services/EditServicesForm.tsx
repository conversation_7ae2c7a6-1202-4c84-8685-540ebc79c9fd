import React, { useEffect, useState } from 'react';
import { View, ScrollView, StyleSheet, TextInput, SafeAreaView, Platform, KeyboardAvoidingView } from 'react-native';
import { Text, Button, Checkbox } from 'react-native-paper';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import AppHeader from '../../../../../Components/AppHeader';
import { fontFamily } from '../../../../../Common/Theme/typography';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import ApiClient from '../../../../../Common/API/ApiClient';
import {
  getAccountId,
  getSchemaName,
  getSelectedWorkspaceId,
} from '../../../../../Common/Utils/Storage';
import { useSelector } from 'react-redux';
import { LEADS_ENDPOINTS } from '../../../../../Common/API/ApiEndpoints';
import { showToast } from '../../../../../Components/AppToaster/AppToaster';

type JobType = { id: string; job_type_name: string };

type Params = {
  EditServicesForm: {
    jobTypes: JobType[];
    selectedJobTypes: string[];
    specialHandlingRequirements: string;
    customClearanceNeeds: string;
    orgId: string;
  };
};

const EditServicesFormScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<RouteProp<Params, 'EditServicesForm'>>();
  const {
    jobTypes,
    selectedJobTypes: initialSelected,
    specialHandlingRequirements,
    customClearanceNeeds,
    orgId,
  } = route.params;

  const [selectedJobTypes, setSelectedJobTypes] = useState<string[]>(initialSelected || []);
  const [specialHandling, setSpecialHandling] = useState(specialHandlingRequirements?.trim() || '');
  const [clearanceNeeds, setClearanceNeeds] = useState(customClearanceNeeds?.trim() || '');
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const controlUnitId = useSelector(
    (state: any) => state.GlobalAppStateData.selectedControlUnit?.data?.id
  );

  const toggleJobType = (id: string) => {
    setSelectedJobTypes((prev) =>
      prev.includes(id) ? prev.filter((j) => j !== id) : [...prev, id]
    );
    console.log(selectedJobTypes,"LLL")
  };

  useEffect(() => {
    if (selectedJobTypes.length > 0 && errorMessage) {
      setErrorMessage('');
    }
  }, [selectedJobTypes, errorMessage]);

  const onReset = () => {
    setSelectedJobTypes(initialSelected || []);
    setSpecialHandling(specialHandlingRequirements || '');
    setClearanceNeeds(customClearanceNeeds || '');
    setErrorMessage('');
  };

  const onSave = async () => {
    if (selectedJobTypes.length === 0) {
      setErrorMessage('Select at least one interested service');
      return;
    }

    try {
      setLoading(true);
      setErrorMessage('');

      const schemaName = await getSchemaName();
      const accountId = await getAccountId();
      const workspaceId = await getSelectedWorkspaceId();

      const selectedJobs = jobTypes.filter((j) => selectedJobTypes.includes(j.id));

      const payload = {
        payloadJson: JSON.stringify({
          schemaName,
          contextJson: {
            account_id: accountId,
            workspace_id: workspaceId,
            control_unit_id: controlUnitId,
          },
          inputParamsJson: {
            org_id: orgId,
            job_type_json: selectedJobs,
            special_handling_requirements: specialHandling,
            custom_clearance_needs: clearanceNeeds,
          },
        }),
        schemaName,
      };

      const response = await ApiClient.post(LEADS_ENDPOINTS.UPDARE_ORG_SERVICES, payload);

      if (response?.data?.success) {
        navigation.goBack();
        showToast.success('Service information updated successfully');
      } else {
        showToast.error('Something went wrong. Please try again.');
      }
    } catch (error) {
      console.error('Error saving services:', error);
      showToast.error('Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <AppHeader title="Edit Services" customHeaderStyles={{ backgroundColor: '#fff' }} />
 <KeyboardAvoidingView  
              style={{ flex: 1 }}           
              behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            > 
            <ScrollView  
            keyboardShouldPersistTaps="handled"
             contentContainerStyle={styles.scrollContent} 
             showsVerticalScrollIndicator={false} bounces={false}>
      {/* <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}> */}
        <Text style={[styles.sectionLabel, { marginTop: 0 }]}>
          Interested Services <Text style={styles.asterisk}>*</Text>
        </Text>

        <View style={styles.checkboxGrid}>
          {jobTypes.map((job) => (
            <View key={job.id} style={styles.checkboxColumn}>
              <View style={styles.checkboxRow}>
                <Checkbox.Android
                  status={selectedJobTypes.includes(job.id) ? 'checked' : 'unchecked'}
                  onPress={() => toggleJobType(job.id)}
                />
                <Text style={styles.checkboxLabel}>{job.job_type_name}</Text>
              </View>
            </View>
          ))}
        </View>
        {errorMessage !== '' && <Text style={styles.errorText}>{errorMessage}</Text>}
        <Text style={styles.sectionLabel}>Special Handling Requirements</Text>
        <TextInput
          value={specialHandling}
          onChangeText={setSpecialHandling}
          style={styles.textArea}
          placeholder="e.g., temperature-sensitive goods, fragile handling..."
          multiline
          scrollEnabled={Platform.OS === "ios" ? false : true}
        />

        <Text style={styles.sectionLabel}>Custom Clearance Needs</Text>
        <TextInput
          value={clearanceNeeds}
          onChangeText={setClearanceNeeds}
          style={styles.textArea}
          placeholder="e.g., express clearance, special permits..."
          multiline
          scrollEnabled={Platform.OS === "ios" ? false : true}
        />
      </ScrollView>
    </KeyboardAvoidingView>
      <SafeAreaView>
        <View style={styles.actionRow}>
          <Button
            mode="contained"
            style={styles.cancelButton}
            textColor="#000"
            onPress={onReset}
            disabled={loading}
          >
            Reset
          </Button>
          <Button
            mode="contained"
            style={styles.saveButton}
            textColor="#fff"
            onPress={onSave}
            loading={loading}
            disabled={loading}
          >
            Save Changes
          </Button>
        </View>
      </SafeAreaView>
    </View>
  );
};

export default EditServicesFormScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContent: {
    paddingHorizontal: scale(16),
  },
  sectionLabel: {
    fontFamily: fontFamily.semiBold,
    fontSize: moderateScale(13),
    marginBottom: verticalScale(5),
    marginTop: verticalScale(10),
  },
  asterisk: {
    color: 'red',
  },
  helperText: {
    fontSize: moderateScale(10),
    color: '#6B7280',
    marginBottom: verticalScale(10),
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 6,
    padding: scale(10),
    textAlignVertical: 'top',
    fontSize: moderateScale(12),
    backgroundColor: '#fff',
    fontFamily: fontFamily.regular,
    height: verticalScale(100), 
  },
  actionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    gap: scale(10),
    marginVertical: verticalScale(10),
  },
  cancelButton: {
    flex: 1,
    borderRadius: 4,
    backgroundColor: '#E5E7EB',
  },
  saveButton: {
    flex: 1,
    borderRadius: 4,
    backgroundColor: '#2563EB',
  },
  errorText: {
    color: 'red',
    fontSize: moderateScale(11),
    paddingVertical: verticalScale(4),
    fontFamily: fontFamily.regular,
  },
  checkboxGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  checkboxColumn: {
    width: '48%',
    marginBottom: verticalScale(3),
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkboxLabel: {
    fontSize: moderateScale(11),
    fontFamily: fontFamily.regular,
    width:"75%"
  },
});
