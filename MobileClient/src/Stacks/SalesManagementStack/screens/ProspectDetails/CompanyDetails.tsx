import React from 'react';
import { ScrollView, View, StyleSheet } from 'react-native';
import { Text, Card, Icon } from 'react-native-paper';
import { toPascalCase } from '../../../../Components/UI/Menu/DropdownModal';
import { PrimraryCard } from '../../../CustomerStack/CustomerOverview/Cards';
import { CollapsibleSection, KeyValue } from './ReusableCollapseHeader';
import { CircleCheck, CircleX, Facebook, Mail, MessageCircleMore, Phone, Pi } from 'lucide-react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import FontAwesome from "react-native-vector-icons/FontAwesome";
interface Props {
  companyDetails: any;
}
const CompanyDetails: React.FC<Props> = ({ companyDetails }) => {
  const org = companyDetails?.org_details || {};
  const NullDataComponent = () => (
    // <PrimraryCard>
      <View style={styles.nodataComponent}><Text>No data Available</Text>
      </View> 
    // </PrimraryCard>
  );
     const hasValidArrayData = (data: any[]): boolean => {
    return Array.isArray(data) && data?.length > 0;
  };

  return (
    <ScrollView style={styles.content} showsVerticalScrollIndicator={false} bounces={false}>
      <CollapsibleSection title="Basic Information">
        <View style={styles.withoutBox}>
          <KeyValue label1="Company Name" value1={org.company_name || '--'} label2="Industry" value2={org.industry_name || '--'} />
          <KeyValue label1="Company Size" value1={org.company_size || '--'} label2="Year Founded" value2={org.year_founded || '--'} />
          <KeyValue label1="Organization Code" value1={org.org_code || '--'} label2="Organization Type" value2={org.org_type_name || '--'} />
        </View>
      </CollapsibleSection>

      <CollapsibleSection title="Contact Information" count={org.phone_numbers?.length || 0} defaultOpen={hasValidArrayData(org.phone_numbers)}>
        {(org.phone_numbers || []).length > 0 ? (
          (org.phone_numbers || []).map((phone: any, index: number) => (
            <PrimraryCard key={index}>
              <KeyValue
                label1={
                    <Text style={{ fontSize: 13, color: "#64748b" }}>Contact Number</Text>
                }
                value1={
                  <View style={{ flexDirection: 'row', gap: moderateScale(6), alignItems: 'center',marginTop:verticalScale(5) }}>
                    <Phone size={18} color="#64748b" />
                    <Text>{phone?.phone_number || "--"}</Text>
                  </View>
                }
              />

              <View style={{flexDirection:"row",alignItems:"center",flex:1}} >
                <View style={[styles.IconsStyling,{opacity:phone?.is_on_telegram ? 1 : 0.3,paddingBottom:0}]}>
                  <FontAwesome name='telegram' size={moderateScale(16)} color="#229ED9" />
                  <Text style={{ fontSize: moderateScale(12), color: "#64748b" }}>Telegram</Text>
                </View>
                <View style={[styles.IconsStyling,{opacity:phone?.is_on_whatsapp ? 1 : 0.3,paddingBottom:0}]}>
                  <FontAwesome name='whatsapp' size={moderateScale(16)} color="green" />
                  <Text style={{ fontSize: moderateScale(12), color: "#64748b" }}>WhatsApp</Text>
                </View>
              </View>
            </PrimraryCard>
          ))
        ) : (
          <NullDataComponent />
        )}
      </CollapsibleSection>

      <CollapsibleSection title="Email Contacts" count={org.emails?.length || 0} defaultOpen={hasValidArrayData(org.social_medias)}>
        {(org.emails || []).length > 0 ? (
          (org.emails || []).map((email: any, index: number) => (
            <PrimraryCard key={index}>
              <KeyValue
                label1="Email"
                value1={
                  <View style={styles.IconsStyling}>
                    <Mail size={18} />
                    <Text>{email.email || '--'}</Text>
                  </View>
                }
                badge={email.email_type_name}
              />
            </PrimraryCard>
          ))
        ) : (
          <NullDataComponent />
        )}
      </CollapsibleSection>

      <CollapsibleSection title="Social Media" count={org.social_medias?.length || 0} defaultOpen={hasValidArrayData(org.social_medias)}>
        {(org.social_medias || []).length > 0 ? (
          (org.social_medias || []).map((social: any, index: number) => (
            <PrimraryCard key={index}>
              <KeyValue
                label1="Platform"
                value1={social.social_media_type_name || '--'}
                label2="Handle"
                value2={social.social_media_handle || '--'}
              />
            </PrimraryCard>
          ))
        ) : (
          <NullDataComponent />
        )}
      </CollapsibleSection>

      <CollapsibleSection title="Company Addresses" count={org.addresses?.length || 0} defaultOpen={hasValidArrayData(org.addresses)}>
        {(org.addresses || []).length > 0 ? (
          (org.addresses || []).map((addr: any, index: number) => (
            <PrimraryCard key={index}>
              <View>
                <KeyValue
                  label1="Address"
                  value1={addr.address || '--'}
                  label2="Country"
                  value2={addr.country_name || '--'}
                  badge={addr.address_type_name}
                />
                <KeyValue 
                  label1="Postal Code" 
                  value1={addr.postal_code || '--'} 
                  label2="City" 
                  value2={addr.city_name || '--'} 
                />
              </View>
            </PrimraryCard>
          ))
        ) : (
          <NullDataComponent />
        )}
      </CollapsibleSection>

      <CollapsibleSection title="Financial Information">
        <View style={styles.withoutBox}>
          <KeyValue label1="Annual Revenue" value1={String(org.annual_revenue || '--')} label2="Annual Growth Rate" value2={String(org.annual_growth_rate || '--')} />
          <KeyValue label1="Currency" value1={org.currency_name || '--'}  label2="Tax Id/VAT Number" value2={org.tax_vat_id || "--"}/>
        </View>
      </CollapsibleSection>

      <CollapsibleSection title="Additional Information">
        <View style={styles.withoutBox}>
          <KeyValue label1="Source" value1={org.area_name || '--'} label2="Category" value2={toPascalCase(org.category) || '--'} />
          <KeyValue label1="Customer Type" value1={org.customer_type || '--'} label2="Internal Person" value2={org.person_category_text || "--"} />
          <KeyValue label1="Credit Limit" value1={org.credit_limit || '--'} label2="Credit Days" value2={org.credit_days || '--'} />
        </View>
      </CollapsibleSection>

      <CollapsibleSection title="Business Information">
        <View style={styles.withoutBox}>
          <KeyValue label1="Expected Business Value" value1={String(org.expected_business_value || '--')} label2="Decision Timeline" value2={String(org.decision_timeline || '--')} />
          <KeyValue label1="Pain Points" value1={org.pain_points || '--'} label2="Person Category" value2={org.person_category_text || '--'} />
        </View>
      </CollapsibleSection>

      <CollapsibleSection title="Decision Making Process">
        <View style={styles.withoutBox}>
          <KeyValue label1="Key Decision Makers" value1={org.key_decision_makers || '--'} label2="Decision Criteria" value2={org.decision_criteria || '--'} />
        </View>
      </CollapsibleSection>

      <View style={{ height: 80 }} />
    </ScrollView>
  );

}

const styles = StyleSheet.create({
  content: {
    flex: 1,
    // padding: 16,
    marginTop:moderateScale(12)
  },
  nodataComponent:{ justifyContent: 'center', alignItems: 'center',paddingVertical: moderateScale(6),
    paddingHorizontal: moderateScale(16),
    marginVertical: moderateScale(6),
    marginHorizontal: moderateScale(12) },
  infoCard: {
    marginBottom: 16,
    borderRadius: 8,
    borderColor: '#E5E7EB',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    // borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  infoRowLabel: {
    fontSize: moderateScale(11),
    color: '#6B7280',
  },
  infoRowValue: {
    fontSize: moderateScale(11),

    color: '#1F2937',
    maxWidth: '60%',
    textAlign: 'right',
  },
  IconsStyling: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    paddingBottom: moderateScale(5),
    flex:1
  },
  badge: {
    backgroundColor: "#c9dcfdff",      
    paddingHorizontal: moderateScale(8),
    paddingVertical: moderateScale(4),
    borderRadius: moderateScale(12),
    alignSelf: "flex-start",         
    minWidth: moderateScale(70),     
  },
  badgeText: {
    color: "#3377ee",
    fontSize: moderateScale(12),
    fontWeight: "600",
    textAlign: "center",
  },
  withoutBox:{
    paddingTop:moderateScale(12),paddingHorizontal:moderateScale(16)
  }
});

export default CompanyDetails;
