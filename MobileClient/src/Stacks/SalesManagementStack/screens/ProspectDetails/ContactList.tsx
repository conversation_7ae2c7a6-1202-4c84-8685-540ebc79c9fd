import React, { useState } from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import { Text, Avatar, Card, useTheme } from 'react-native-paper';
import { ContactType } from './useProspectDetails';
import ContactDetailsModal from './ContactDetailsModal';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { fontFamily } from '../../../../Common/Theme/typography';
import { Pencil, Plus, Trash2 } from 'lucide-react-native';
import CustomAlert from '../../../../Components/CustomAlert/CustomAlert';
import SearchBar from '../../../../Components/SearchInput';

interface ContactCardProps {
  contact: ContactType;
  onPress: () => void;
  onEdit: (contact:ContactType) => void;
  onDelete: (contact: ContactType) => void;
}
interface ContactListProps {
  contactsData: Array<{ contacts_json: ContactType[] }>;
  ageGroups: { Text: string; Value: string }[];
  newContact: any;
  onDelete: (contact: ContactType) => void;
  showDeleteModal: boolean;
  onConfirmDelete: () => void;
  onCancelDelete: () => void;
}

const ContactCard: React.FC<ContactCardProps> = ({ contact, onPress, onEdit,onDelete }) => {
  const theme = useTheme();
  const fullName =
    contact.contact_name || `${contact.first_name || ''} ${contact.last_name || ''}`.trim();
  const initials = fullName
    .split(' ')
    .map((word) => word[0])
    .join('')
    .toUpperCase();

  const primaryPhone = contact.phone_numbers?.[0]?.contact_phone_number || '--';
  const phoneCount = contact.phone_numbers?.length ?? 0;
  const morePhones = phoneCount > 1 ? `+${phoneCount - 1} more` : '';

  const parseEmail = (rawEmail: string): string[] => {
    try {
      const parsed = JSON.parse(rawEmail);
      return Array.isArray(parsed) ? parsed : [rawEmail];
    } catch {
      return [rawEmail];
    }
  };

  const allEmails: string[] = (contact.emails || []).flatMap((e) => parseEmail(e.email));
  const primaryEmail = allEmails[0] || '--';
  const moreEmails = allEmails.length > 1 ? `+${allEmails.length - 1} more` : '';
  return (
    <Card style={styles.card} mode="contained" onPress={onPress}>
      <View style={styles.header}>
        <Avatar.Text
          size={moderateScale(23)}
          label={initials}
          style={{
            backgroundColor: theme.colors.primaryContainer,
            height: moderateScale(30),
            width: moderateScale(30),
            borderRadius: 60,
          }}
          labelStyle={{ color: theme.colors.primary }}
        />
        <View style={{ marginLeft: scale(8), flex: 1 }}>
          <Text style={styles.name}>{fullName || '--'}</Text>
          {(contact?.is_primary || contact?.is_primary) && (
            <View style={styles.header}>
              {contact?.is_primary ? <Text style={styles.typeChip}>Primary Contact</Text> : null}
              {contact?.decision_maker ? (
                <Text style={[styles.typeChip, styles.decisionText]}>Decision Maker</Text>
              ) : null}
            </View>
          )}
        </View>
        <TouchableOpacity
          onPress={() => {
            onEdit(contact);
          }}
          style={styles.editButton}
        >
          <Pencil size={moderateScale(16)} color="#2563EB" />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.editButton, { marginLeft: scale(5) }]}
          onPress={() => onDelete(contact)}
        >
          <Trash2 size={moderateScale(16)} color="#EF4444" />
        </TouchableOpacity>
      </View>

      <View style={styles.detailRow}>
        <Text style={styles.moreText}>Phone: </Text>
        <Text style={styles.phoneText}>{primaryPhone} </Text>
        <Text style={styles.moreText}>{morePhones}</Text>
      </View>

      <View style={styles.detailRow}>
        <Text style={styles.moreText}>Email: </Text>
        <Text style={styles.phoneText}>{primaryEmail}</Text>
        <Text style={styles.moreText}>{moreEmails}</Text>
      </View>
    </Card>
  );
};

export const ContactList: React.FC<ContactListProps> = ({
  contactsData,
  ageGroups,
  newContact,
  onDelete,
  showDeleteModal,
  onConfirmDelete,
  onCancelDelete,
}) => {
  const contacts = contactsData?.[0]?.contacts_json || [];
  const [selectedContact, setSelectedContact] = useState<ContactType | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const handleCardPress = (contact: ContactType) => {
    setSelectedContact(contact);
    setModalVisible(true);
  };
  const filteredContacts = contacts.filter((contact) => {
    const name = contact.contact_name || `${contact.first_name} ${contact.last_name}`;
    return name?.toLowerCase().includes(searchQuery.toLowerCase());
  });

  return (
    <View style={{ flex: 1 }}>
      <View style={styles.headerRow}>
        <SearchBar
          placeholder="Search contact..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          style={{ flex: 1 }}
        />
        <TouchableOpacity
          style={{
            backgroundColor: '#3377FF',
            borderRadius: 5,
            borderColor: '#3377FF',
            height:verticalScale(32),
            width:verticalScale(32),
            justifyContent:"center",
            alignItems:"center"
          }}
          onPress={() => newContact()}
        >
          <Plus size={moderateScale(22)} color="#FFF" />
        </TouchableOpacity>
      </View>

      {filteredContacts.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No Contacts Found</Text>
        </View>
      ) : (
        <FlatList
          data={filteredContacts}
          keyExtractor={(item) => item.contact_id}
          renderItem={({ item }) => (
            <ContactCard
              contact={item}
              onEdit={newContact}
              onPress={() => handleCardPress(item)}
              onDelete={onDelete}
            />
          )}
          contentContainerStyle={{
            paddingHorizontal: scale(14),
            marginTop: verticalScale(10),
            paddingBottom: verticalScale(20),
          }}
          bounces={false}
        />
      )}

      <ContactDetailsModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        contact={selectedContact}
        ageGroups={ageGroups}
      />
      <CustomAlert
        visible={showDeleteModal}
        title="Delete Contact ?"
        message="Are you sure you want to delete this contact? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        showCancel={true}
        onCancel={onCancelDelete}
        onConfirm={onConfirmDelete}
        button2Styles={{ backgroundColor: '#dc2626' }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  headerRow: {
    flexDirection: 'row',
    paddingHorizontal: scale(14),
    paddingTop: verticalScale(10),
    alignItems: 'center',
    gap: scale(8),
  },
  searchInput: {
    flex: 1,
    height: 40,
    borderColor: '#E5E7EB',
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: scale(10),
    fontSize: moderateScale(12),
    color: '#111827',
    backgroundColor: '#fff',
  },
  addButton: {
    backgroundColor: '#2563EB',
    borderRadius: 6,
    paddingHorizontal: scale(12),
    paddingVertical: verticalScale(8),
  },
  addButtonText: {
    color: '#fff',
    fontSize: moderateScale(12),
    fontFamily: fontFamily.medium,
  },
  card: {
    marginBottom: verticalScale(10),
    borderRadius: 8,
    borderColor: '#E5E7EB',
    borderWidth: 1,
    backgroundColor: '#fff',
    elevation: 0,
    padding: moderateScale(10),
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  name: {
    fontSize: moderateScale(13),
    fontFamily: fontFamily.medium,
    color: '#000',
    marginBottom: verticalScale(5),
  },
  subText: {
    fontSize: moderateScale(13),
    color: '#647488',
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: verticalScale(5),
  },
  phoneText: {
    fontSize: moderateScale(12),
    color: '#647488',
  },
  moreText: {
    fontSize: moderateScale(12),
    color: '#647488',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: verticalScale(50),
  },
  emptyText: {
    color: '#6B7280',
    fontSize: moderateScale(14),
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(7),
    paddingVertical: scale(7),
    backgroundColor: '#EFF6FF',
    borderRadius: 30,
  },
  editText: {
    fontSize: moderateScale(12),
    color: '#2563EB',
    marginLeft: 4,
    fontWeight: '600',
  },
  typeChip: {
    fontSize: moderateScale(10),
    backgroundColor: '#DBEAFE',
    color: '#2563EB',
    borderRadius: 30,
    paddingHorizontal: scale(8),
    paddingVertical: verticalScale(3),
    fontFamily: fontFamily.medium,
  },
  decisionText:{ color: '#7B2FD0', backgroundColor: '#faf5ff', marginLeft: scale(5) }
});
