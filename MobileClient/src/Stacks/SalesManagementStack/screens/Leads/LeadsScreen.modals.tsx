import React, { useEffect, useState } from 'react';
import { View, TouchableOpacity, ScrollView,Modal,TouchableWithoutFeedback, Pressable } from 'react-native';
import {  Portal, Searchbar, Text, Button, useTheme } from 'react-native-paper';
import { ControlUnit } from './LeadsScreen.types';
import { createLeadStyles } from './LeadsScreen.styles';
import { useDispatch, useSelector } from 'react-redux';
import { setSelectedControlUnit, setSelectedPeopleHirerachy } from '../../../../State/Slices/DSR/GlobalAppSlice';
import { moderateScale,scale,verticalScale } from 'react-native-size-matters';
import SearchBar from '../../../../Components/SearchInput';
import { UserRound } from 'lucide-react-native';
// import { TouchableWithoutFeedback } from '@gorhom/bottom-sheet';
export const ControlUnitModal = ({
  visible,
  onDismiss,
  controlUnits,
  onSelect,
}: {
  visible: boolean;
  onDismiss: () => void;
  controlUnits: ControlUnit[];
  onSelect: (item: ControlUnit) => void;
}) => {
  const theme = useTheme();
  const styles = createLeadStyles(theme);
  const dispatch = useDispatch();

  const [selectedUnit, setSelectedUnit] = useState<ControlUnit | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [localControlUnits, setLocalControlUnits] = useState([]);
  // console.log('controlUnits>>>', controlUnits)
    useEffect (() => {
      if(controlUnits?.length > 0){
        setLocalControlUnits(controlUnits) 
      }
    },[controlUnits])

  const controlUnitSelectedData = useSelector(
    (state: any) => state.GlobalAppStateData.selectedControlUnit?.data
  );
  // console.log("check here",controlUnitSelectedData)
  useEffect(() => {
    if (visible) {
      setSelectedUnit(controlUnitSelectedData ?? null);
      setSearchQuery('');
    }
  }, [visible, controlUnitSelectedData]);

  const handleConfirm = () => {
    if (selectedUnit) {
      dispatch(setSelectedControlUnit(selectedUnit));
      onSelect(selectedUnit);
      setLocalControlUnits(controlUnits);
      onDismiss();
    }
  };

  const handleSearchQuery = (text : string) => {
    setSearchQuery(text)

    if(!text?.trim()){
      setLocalControlUnits(controlUnits);
      return;
    }
      const keyword = text.toLowerCase();
      const filteredData = localControlUnits?.filter((item) => {
        const name = item?.controlUnitName?.toLowerCase()
        return name.includes(keyword)
      })
      setLocalControlUnits(filteredData);
    
  }



  return (
    <Modal
      visible={visible}
      transparent={true}
      onDismiss={() => {
        handleSearchQuery('');
        onDismiss();
      }}
      onRequestClose={onDismiss}
      // dismissable={true}
      style={styles.modalContainerStyle}
    >
      <View
        style={{
          flex: 1,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          justifyContent: 'center',
          alignItems: 'center',

          // zIndex: 999,
        }}
      >
        <View
          style={{
            backgroundColor: theme.colors.background,

            // padding: moderateScale(20),
            width: '90%',
            margin: scale(20),
            borderRadius: moderateScale(10),
            height: '45%',
          }}
        >
          <View style={{ paddingHorizontal: moderateScale(20), paddingTop: moderateScale(20) }}>
            <SearchBar
              placeholder="Search location"
              onChangeText={(text) => handleSearchQuery(text)}
              value={searchQuery}
              style={{
                borderRadius: 5,
                borderWidth: 1,
                borderColor: '#E0E0E0',
                backgroundColor: '#FFF',
                height: verticalScale(28),
              }}
              inputStyle={{ fontSize: moderateScale(12), alignSelf: 'center' }}
            />
          </View>

          <ScrollView bounces={false}>
            <View style={{ marginVertical: 15, paddingHorizontal: moderateScale(20) }}>
              {localControlUnits?.length > 0 ? (
                localControlUnits.map((item, index) => {
                  const isSelected = item?.id === selectedUnit?.id;
                  return (
                    <TouchableOpacity
                      key={item.id ?? index}
                      style={[
                        styles.controlUnitItem,
                        {
                          backgroundColor: isSelected
                            ? theme.colors.primary
                            : theme.colors.background,
                        },
                      ]}
                      onPress={() => setSelectedUnit(item)}
                    >
                      <Text
                        variant="bodyLarge"
                        style={[
                          {
                            color: isSelected ? 'white' : theme.colors.onBackground,
                          },
                          { fontSize: moderateScale(12) },
                        ]}
                      >
                        {`${item?.controlUnitName} ${item.parentControlUnit?.code}`}
                      </Text>
                    </TouchableOpacity>
                  );
                })
              ) : (
                <View style={{ alignItems: 'center', marginTop: 20 }}>
                  <Text>No results found</Text>
                </View>
              )}
            </View>
          </ScrollView>

          <View style={styles.buttonContainer}>
            <Button
              mode="contained"
              onPress={() => {
                setSelectedUnit(null);
                handleSearchQuery('');
                onDismiss();
              }}
              style={[styles.buttonStyle, { backgroundColor: '#EFF1F6' }]}
              labelStyle={{ color: theme.colors.onBackground, fontSize: moderateScale(12) }}
              
            >
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleConfirm}
              disabled={!selectedUnit}
              style={styles.buttonStyle}
              labelStyle={{ fontSize: moderateScale(12) }}
              
            >
              Select
            </Button>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export const UserSelectionModal = ({
  visible,
  onDismiss,
  peopleHirerachyData,
  onSelect,
}: {
  visible: boolean;
  onDismiss: () => void;
  peopleHirerachyData: Subordinate[];
  onSelect: (item: Subordinate) => void;
}) => {
  const theme = useTheme();
  const styles = createLeadStyles(theme);
  const [selectedUser, setSelectedUser] = useState<Subordinate | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [localPeopleHirerachyData, setLocalPeopleHirerachyData] = useState([]);
  const dispatch = useDispatch();
  const handleConfirm = () => {
    if (selectedUser) {
      dispatch(setSelectedPeopleHirerachy(selectedUser));
      onSelect(selectedUser);
      handleSearchQuery('');
      onDismiss();
    }
  };
  const peopleHirerachySelectedData = useSelector(
    (state: any) => state.GlobalAppStateData.selectedPeopleHirerachy?.data
  );
  // console.log('selected people hirerachy data', peopleHirerachySelectedData);
  useEffect(() => {
    if (peopleHirerachySelectedData) {
      setSelectedUser(peopleHirerachySelectedData);
    }
  }, [peopleHirerachySelectedData]);
  useEffect(() => {
    if (visible) {
      setSelectedUser(peopleHirerachySelectedData ?? null);
    }
  }, [visible]);

  useEffect(() => {
    if(peopleHirerachyData?.length > 0){
      setLocalPeopleHirerachyData(peopleHirerachyData)
    }
  }, [peopleHirerachyData])

  const handleSearchQuery = (text : string) => {
    setSearchQuery(text)

    if(!text?.trim()){
      setLocalPeopleHirerachyData(peopleHirerachyData)
      return;
    }
      const keyword = text.toLowerCase();
      const filteredData = localPeopleHirerachyData?.filter((item) => {
        const name = item?.first_name?.toLowerCase()
        return name.includes(keyword)
      })
      setLocalPeopleHirerachyData(filteredData);
    
  }

  return (
    <Modal
      visible={visible}
      transparent={true}
      onDismiss={() => {
        handleSearchQuery('');
        onDismiss();
      }}
      onRequestClose={onDismiss}
      style={styles.modalContainerStyle}
    >
      <Pressable
      onPress = {onDismiss}
        style={{
          flex: 1,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          justifyContent: 'center',
          alignItems: 'center',
          // zIndex: 999,
        }}
      >
        <TouchableOpacity
          activeOpacity={1}
          style={{
            backgroundColor: theme.colors.background,
            width: '90%',
            margin: scale(20),
            borderRadius: moderateScale(10),
            height: '45%',
          }}
        >{peopleHirerachyData?.length > 0 ?<>
          <View style={{ paddingHorizontal: moderateScale(20), paddingTop: moderateScale(20) }}>
            <SearchBar
              placeholder="Search Users"
              onChangeText={(text) => handleSearchQuery(text)}
              value={searchQuery}
              style={{ borderRadius: 5 }}
            />
          </View>
          <ScrollView>
            <View style={{ marginVertical: 15, paddingHorizontal: moderateScale(20) }}>
              {localPeopleHirerachyData?.map((item, index) => {
                const isSelected = item?.account_user_id === selectedUser?.account_user_id;
                return (
                  <TouchableOpacity
                    style={[
                      styles.controlUnitItem,
                      {
                        backgroundColor: isSelected
                          ? theme.colors.primary
                          : theme.colors.background,
                      },
                    ]}
                    onPress={() => setSelectedUser(item)}
                  >
                    <Text
                      variant="bodyLarge"
                      style={[
                        {
                          color: isSelected ? 'white' : theme.colors.onBackground,
                        },
                        { fontSize: moderateScale(12) },
                      ]}
                    >
                      {item.first_name}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </ScrollView>
          <View style={styles.buttonContainer}>
            <Button
              mode="contained"
              onPress={() => {
                setSelectedUser(null);
                handleSearchQuery('');
                onDismiss();
              }}
              style={[styles.buttonStyle, { backgroundColor: '#EFF1F6' }]}
              labelStyle={{ color: theme.colors.onBackground + 20,fontSize: moderateScale(12)  }}
            >
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleConfirm}
              style={styles.buttonStyle}
              labelStyle={{ fontSize: moderateScale(12) }}
            >
              Select
            </Button>
          </View>
          </>: <View style={{ alignItems: 'center',justifyContent:"center", marginTop: "50%" }}>
            <UserRound size={32} strokeWidth={2.5} color={"#3377ff"} />
          <Text variant="bodyLarge">No users found</Text>
        </View>}
          
        </TouchableOpacity>
        
      </Pressable>
    </Modal>
  );
};
