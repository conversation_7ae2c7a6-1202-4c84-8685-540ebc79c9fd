import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, TouchableOpacity, Modal, FlatList, Pressable } from 'react-native';
import {
  Appbar,
  TextInput,
  Button,
  Text,
  Chip,
  useTheme,
  HelperText,
  IconButton,
  ActivityIndicator,
  Snackbar,
} from 'react-native-paper';
// import DateTimePicker from '@react-native-community/datetimepicker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import ApiClient from '../../../Common/API/ApiClient';
import {DSREndPoints} from '../../../Common/API/ApiEndpoints';
import {handleApiResponse} from '../../../Common/API/ApiResponseHandler';
import DropdownMenu, {
  MenuOption,
} from '../../../Components/UI/Menu/DropdownMenu';
import {getUserId} from '../../../Common/Utils/Storage';

const activityTypes = [
  {key: 'in_person', label: 'In person'},
  {key: 'online', label: 'Online'},
  {key: 'call', label: 'Call'},
];

const CreateActivityScreen = ({navigation,route}: any) => {
  const theme = useTheme();
  console.log("route",route.params.leadDetails.lead.orgId) //these are essential
  const [activityType, setActivityType] = useState('in_person');
  const [title, setTitle] = useState('');
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());
  const [showStartDate, setShowStartDate] = useState(false);
  const [showEndDate, setShowEndDate] = useState(false);
  const [startTime, setStartTime] = useState('8:00 AM');
  const [endTime, setEndTime] = useState('9:00 AM');
  const [startTimeModalVisible, setStartTimeModalVisible] = useState(false);
  const [endTimeModalVisible, setEndTimeModalVisible] = useState(false);
  const [priority, setPriority] = useState('');
  const [status, setStatus] = useState('');
  const [estimatedTime, setEstimatedTime] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');
  const [areas, setAreas] = useState([]);
  const [statuses, setStatuses] = useState([]);
  const [priorities, setPriorities] = useState([]);
  const [estimatedTimes, setEstimatedTimes] = useState([]);
  const [externalContacts, setExternalContacts] = useState([]);
  const [internalUsers, setInternalUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [area, setArea] = useState('');
  const [customerConnect, setCustomerConnect] = useState('');
  const [assignedTo, setAssignedTo] = useState('');
  const [areaModalVisible, setAreaModalVisible] = useState(false);
  const [customerModalVisible, setCustomerModalVisible] = useState(false);
  const [assignedToModalVisible, setAssignedToModalVisible] = useState(false);
  const [statusModalVisible, setStatusModalVisible] = useState(false);
  const [priorityModalVisible, setPriorityModalVisible] = useState(false);
  const [estimatedTimeModalVisible, setEstimatedTimeModalVisible] =
    useState(false);

  // Add time options
  const timeOptions = [
    '12:00 AM',
    '12:15 AM',
    '12:30 AM',
    '12:45 AM',
    '1:00 AM',
    '1:15 AM',
    '1:30 AM',
    '1:45 AM',
    '2:00 AM',
    '2:15 AM',
    '2:30 AM',
    '2:45 AM',
    '3:00 AM',
    '3:15 AM',
    '3:30 AM',
    '3:45 AM',
    '4:00 AM',
    '4:15 AM',
    '4:30 AM',
    '4:45 AM',
    '5:00 AM',
    '5:15 AM',
    '5:30 AM',
    '5:45 AM',
    '6:00 AM',
    '6:15 AM',
    '6:30 AM',
    '6:45 AM',
    '7:00 AM',
    '7:15 AM',
    '7:30 AM',
    '7:45 AM',
    '8:00 AM',
    '8:15 AM',
    '8:30 AM',
    '8:45 AM',
    '9:00 AM',
    '9:15 AM',
    '9:30 AM',
    '9:45 AM',
    '10:00 AM',
    '10:15 AM',
    '10:30 AM',
    '10:45 AM',
    '11:00 AM',
    '11:15 AM',
    '11:30 AM',
    '11:45 AM',
    '12:00 PM',
    '12:15 PM',
    '12:30 PM',
    '12:45 PM',
    '1:00 PM',
    '1:15 PM',
    '1:30 PM',
    '1:45 PM',
    '2:00 PM',
    '2:15 PM',
    '2:30 PM',
    '2:45 PM',
    '3:00 PM',
    '3:15 PM',
    '3:30 PM',
    '3:45 PM',
    '4:00 PM',
    '4:15 PM',
    '4:30 PM',
    '4:45 PM',
    '5:00 PM',
    '5:15 PM',
    '5:30 PM',
    '5:45 PM',
    '6:00 PM',
    '6:15 PM',
    '6:30 PM',
    '6:45 PM',
    '7:00 PM',
    '7:15 PM',
    '7:30 PM',
    '7:45 PM',
    '8:00 PM',
    '8:15 PM',
    '8:30 PM',
    '8:45 PM',
    '9:00 PM',
    '9:15 PM',
    '9:30 PM',
    '9:45 PM',
    '10:00 PM',
    '10:15 PM',
    '10:30 PM',
    '10:45 PM',
    '11:00 PM',
    '11:15 PM',
    '11:30 PM',
    '11:45 PM',
  ];

  // Form validation
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarType, setSnackbarType] = useState<'success' | 'error'>(
    'success',
  );

  // Dynamic values from AsyncStorage
  const [asyncStorageValues, setAsyncStorageValues] = useState({
    schemaName: '',
    accountId: '',
    controlUnitId: '',
    workspaceId: '',
    userId: '',
    isLoaded: false,
  });

  // State for activity ID related fields
  const [activityTypeId, setActivityTypeId] = useState<number>(1);
  const [statusId, setStatusId] = useState<number | null>(null);
  const [priorityId, setPriorityId] = useState<number | null>(null);
  const [estimatedTimeId, setEstimatedTimeId] = useState<number | null>(null);
  const [areaId, setAreaId] = useState<string | null>(null);
  const [customerConnectId, setCustomerConnectId] = useState<string | null>(
    null,
  );
  const [assignedToId, setAssignedToId] = useState<string | null>(null);
  const [selectedExternalUsers, setSelectedExternalUsers] = useState<string[]>(
    [],
  );
  const [selectedInternalUsers, setSelectedInternalUsers] = useState<string[]>(
    [],
  );
  const [description, setDescription] = useState('');

  // Helper function to get string value from AsyncStorage
  const getStringValueFromAsyncStorage = (value: any): string => {
    if (value === null || value === undefined) return '';

    if (typeof value === 'object' && value !== null) {
      if (typeof value.toString === 'function') {
        return value.toString();
      }

      try {
        return JSON.stringify(value);
      } catch (e) {
        console.error('Error stringifying AsyncStorage value:', e);
        return '';
      }
    }

    return String(value);
  };

  // Helper function to parse workspace from AsyncStorage
  const parseWorkspaceFromAsyncStorage = (workspaceData: string | null) => {
    if (!workspaceData) return null;

    try {
      return JSON.parse(workspaceData);
    } catch (e) {
      console.error('Error parsing workspace data:', e);
      return null;
    }
  };

  const showSnackbar = (
    message: string,
    type: 'success' | 'error' = 'success',
  ) => {
    setSnackbarMessage(message);
    setSnackbarType(type);
    setSnackbarVisible(true);
  };

  // Load async storage values
  useEffect(() => {
    const getAsyncStorageValues = async () => {
      try {
        console.log('Retrieving values from AsyncStorage...');
        const [
          schemaName,
          storedAccountId,
          workspaceData,
          accountCode,
          userId,
        ] = await Promise.all([
          AsyncStorage.getItem('schemaName'),
          AsyncStorage.getItem('accountId'),
          AsyncStorage.getItem('selectedWorkspace'),
          AsyncStorage.getItem('accountCode'),
          getUserId(),
        ]);

        console.log('Raw AsyncStorage values:');
        console.log('schemaName:', schemaName);
        console.log('accountId from AsyncStorage:', storedAccountId);
        console.log('workspaceData:', workspaceData);
        console.log('accountCode (controlUnitId):', accountCode);
        console.log('userId:', userId);

        const processedSchemaName = getStringValueFromAsyncStorage(schemaName);
        const processedControlUnitId =
          getStringValueFromAsyncStorage(accountCode);

        const workspaceObj = parseWorkspaceFromAsyncStorage(workspaceData);
        console.log('Parsed workspace object:', workspaceObj);

        const workspaceId = workspaceObj?.id || '';
        const accountId =
          workspaceObj?.accountId ||
          getStringValueFromAsyncStorage(storedAccountId);

        setAsyncStorageValues({
          schemaName: processedSchemaName,
          accountId: accountId,
          controlUnitId: processedControlUnitId,
          workspaceId: workspaceId,
          userId: userId || '',
          isLoaded: true,
        });

        console.log('Processed values:', {
          schemaName: processedSchemaName,
          accountId: accountId,
          controlUnitId: processedControlUnitId,
          workspaceId: workspaceId,
          userId: userId,
        });
      } catch (error) {
        console.error('Error retrieving data from AsyncStorage:', error);
        showSnackbar('Failed to load user data', 'error');
      }
    };

    getAsyncStorageValues();
  }, []);

  useEffect(() => {
    const fetchOptions = async () => {
      setLoading(true);
      try {
        // Use dynamic values from AsyncStorage instead of hardcoded values
        const {schemaName, controlUnitId} = asyncStorageValues;

        if (!schemaName) {
          console.log(
            "Schema name not available yet, will try again when it's loaded",
          );
          return;
        }
        console.log("async values",schemaName,asyncStorageValues)
        const [
          contactsRes,
          areasRes,
          statusRes,
          priorityRes,
          estTimeRes,
          internalUsersRes,
        ] = await Promise.all([
          // Use ApiClient with dynamic values
          ApiClient.post(DSREndPoints.GET_EXTERNAL_CONTACTS, {
            schemaName,
            orgId: route.params.leadDetails.lead.orgId,
          }),
          ApiClient.get(
            `${DSREndPoints.GET_MASTERS_DROPDOWN}/${schemaName}/crm_area?columns=area_id,area_name`,
          ),
          ApiClient.get(
            `${DSREndPoints.GET_MASTERS_DROPDOWN}/${schemaName}/crm_sales_activity_states?columns=sales_activity_state_id,%20sales_activity_state_name`,
          ),
          ApiClient.get(
            `${DSREndPoints.GET_MASTERS_DROPDOWN}/${schemaName}/crm_sales_activity_priority?columns=sales_activity_priority_id,sales_activity_priority_name`,
          ),
          ApiClient.get(
            `${DSREndPoints.GET_MASTERS_DROPDOWN}/${schemaName}/crm_sales_activity_estimated_time?columns=sales_activity_estimated_time_id,sales_activity_estimated_time_name`,
          ),
          // Fix the internal_users endpoint by using external contacts again temporarily
          ApiClient.post(DSREndPoints.SUBORDINATES, {
            schemaName,
            userId: asyncStorageValues.userId,
          }),
        ]);

        // Process responses with handleApiResponse
        const contactsData = handleApiResponse(contactsRes);
        const areasData = handleApiResponse(areasRes);
        const statusesData = handleApiResponse(statusRes);
        const prioritiesData = handleApiResponse(priorityRes);
        const estimatedTimesData = handleApiResponse(estTimeRes);
        const internalUsersData = handleApiResponse(internalUsersRes);

        // Log API responses for debugging
        console.log('API Responses:');
        console.log('External Contacts:', contactsData.data);
        console.log('Areas:', areasData);
        console.log('Statuses:', statusesData);
        console.log('Priorities:', prioritiesData);
        console.log('EstimatedTimes:', estimatedTimesData);
        console.log('Internal Users:', internalUsersData);

        // For each response, extract the relevant data
      
        setExternalContacts(contactsData.data || []);
        setAreas(areasData || []);
        setStatuses(statusesData|| []);
        setPriorities(prioritiesData || []);
        setEstimatedTimes(estimatedTimesData|| []);
        setInternalUsers(internalUsersData.data || []);
      } catch (e) {
        console.error('Dropdown fetch error', e);
        showSnackbar('Failed to load dropdown data', 'error');
      }
      setLoading(false);
    };

    // Only fetch options once asyncStorageValues are loaded
    if (asyncStorageValues.isLoaded) {
      fetchOptions();
    }
  }, [asyncStorageValues]);

  const onAddTag = () => {
    if (tagInput && !tags.includes(tagInput)) {
      setTags([...tags, tagInput]);
      setTagInput('');
    }
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!title) newErrors.title = 'Activity title is required';
    if (!startTime) newErrors.startTime = 'Start time is required';
    if (!endTime) newErrors.endTime = 'End time is required';
    if (!assignedTo) newErrors.assignedTo = 'Assigned to is required';
    if (!customerConnect)
      newErrors.customerConnect = 'Customer connect is required';
    if (!area) newErrors.area = 'Area is required';
    if (!status) newErrors.status = 'Status is required';
    if (!priority) newErrors.priority = 'Priority is required';
    if (!estimatedTime) newErrors.estimatedTime = 'Estimated time is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Helper function to convert time string like "8:00 AM" to Date object
  const timeStringToDate = (timeStr: string, baseDate: Date) => {
    const date = new Date(baseDate);
    const [time, period] = timeStr.split(' ');
    let [hours, minutes] = time.split(':').map(Number);

    if (period === 'PM' && hours !== 12) {
      hours += 12;
    } else if (period === 'AM' && hours === 12) {
      hours = 0;
    }

    date.setHours(hours, minutes, 0, 0);
    return date;
  };

  const handleCreateActivity = async () => {
    setIsSubmitting(true);

    const isValid = validateForm();

    if (isValid) {
      try {
        const startDateTime = timeStringToDate(startTime, startDate);
        const endDateTime = timeStringToDate(endTime, endDate);

        // Use dynamic values from AsyncStorage
        const {schemaName, accountId, controlUnitId, workspaceId, userId} =
          asyncStorageValues;

        if (!schemaName || !accountId || !workspaceId || !userId) {
          throw new Error('Missing required data. Please try again.');
        }

        const payload = {
          schemaName,
          eventData: {
            organization_id: accountId,
            workspace_id: workspaceId,
            control_unit_id: '3f02b9a9-d732-420b-b773-dfe00cbc5d68',
            account_id: accountId,
            sales_activity_title: title,
            sales_activity_description: description || title,
            sales_activity_date: startDateTime.toISOString().split('T')[0],
            sales_activity_type_id: activityTypeId,
            sales_activity_state_id: statusId,
            sales_activity_priority_id: priorityId,
            sales_activity_estimated_time_id: estimatedTimeId,
            sales_activity_colour: '#80D0F2',
            sales_activity_owner_id: assignedToId || userId,
            start_date_time: startDateTime.toISOString(),
            end_date_time: endDateTime.toISOString(),
            status_id: statusId,
            area_id: areaId, // Add area ID to the payload
            tags: JSON.stringify(tags.length > 0 ? tags : ['Meeting']),
            external_users: JSON.stringify(
              selectedExternalUsers.length > 0 ? selectedExternalUsers : [],
            ),
            internal_users: JSON.stringify(
              selectedInternalUsers.length > 0
                ? selectedInternalUsers
                : [userId],
            ),
            attachment: JSON.stringify(''),
            updated_by: userId,
          },
        };

        console.log('Creating activity with payload:', payload);

        // Use ApiClient with DSREndPoints.CREATE_EVENT
        const response = await ApiClient.post(
          DSREndPoints.CREATE_EVENT,
          payload,
        );

        // Use the handleApiResponse helper function to process the response
        const data = handleApiResponse(response);

        console.log('API Response:', data);

        if (data && data.success) {
          showSnackbar('Activity created successfully', 'success');
          setTimeout(() => {
            navigation.goBack();
          }, 1500);
        } else {
          throw new Error(data?.message || 'Failed to create activity');
        }
      } catch (error) {
        console.error('Error creating activity:', error);
        showSnackbar('Failed to create activity', 'error');
      } finally {
        setIsSubmitting(false);
      }
    } else {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return <ActivityIndicator style={{marginTop: 40}} />;
  }
  {
    console.log(estimatedTime);
  }
  return (
    <View style={{flex: 1, backgroundColor: theme.colors.background}}>
      <Appbar.Header
        mode="small"
        style={{backgroundColor: theme.colors.primary, elevation: 4}}>
        <Appbar.BackAction
          color={theme.colors.onPrimary}
          onPress={() => navigation.goBack()}
        />
        <Appbar.Content
          title="Create Activity"
          titleStyle={{color: theme.colors.onPrimary}}
        />
      </Appbar.Header>
      <ScrollView contentContainerStyle={styles.content} bounces={false}>
        <Text style={styles.sectionTitle}>Activity Type</Text>
        <View style={styles.row}>
          {activityTypes.map((type, index) => (
            <Chip
              key={type.key}
              selected={activityType === type.key}
              onPress={() => {
                setActivityType(type.key);
                setActivityTypeId(index + 1); // Set the type ID based on index
              }}
              style={styles.chip}>
              {type.label}
            </Chip>
          ))}
        </View>
        <TextInput
          label="Activity Title"
          value={title}
          onChangeText={setTitle}
          style={styles.input}
          error={!!errors.title}
        />
        {errors.title && <HelperText type="error">{errors.title}</HelperText>}
        <View style={styles.row}>
          <TouchableOpacity
            onPress={() => setShowStartDate(true)}
            style={{flex: 1}}>
            <TextInput
              label="Start Date"
              value={startDate.toLocaleDateString()}
              editable={false}
              style={styles.input}
            />
          </TouchableOpacity>
          <View style={{width: 12}} />
          <TouchableOpacity
            onPress={() => setShowEndDate(true)}
            style={{flex: 1}}>
            <TextInput
              label="End Date"
              value={endDate.toLocaleDateString()}
              editable={false}
              style={styles.input}
            />
          </TouchableOpacity>
        </View>
        {/* {showStartDate && (
          <DateTimePicker
            value={startDate}
            mode="date"
            display="default"
            onChange={(_, date) => {
              setShowStartDate(false);
              if (date) setStartDate(date);
            }}
          />
        )}
        {showEndDate && (
          <DateTimePicker
            value={endDate}
            mode="date"
            display="default"
            onChange={(_, date) => {
              setShowEndDate(false);
              if (date) setEndDate(date);
            }}
          />
        )} */}

        {/* Start Time Dropdown with DropdownMenu */}
        <Text style={styles.sectionTitle}>Start Time</Text>
        <DropdownMenu
          visible={startTimeModalVisible}
          handleOpen={() => setStartTimeModalVisible(true)}
          handleClose={() => setStartTimeModalVisible(false)}
          dropdownWidth="100%"
          trigger={
            <View
              style={[
                styles.dropdownButton,
                errors.startTime ? {borderColor: theme.colors.error} : null,
              ]}>
              <Text style={{flex: 1, color: startTime ? '#000' : '#888'}}>
                {startTime || 'Select Start Time'}
              </Text>
              <IconButton
                icon="chevron-down"
                size={20}
                color="#888"
                style={{margin: 0}}
              />
            </View>
          }>
          {timeOptions.map(item => (
            <MenuOption
              key={item}
              onSelect={() => {
                setStartTime(item);
                setStartTimeModalVisible(false);
              }}>
              <Text>{item}</Text>
            </MenuOption>
          ))}
        </DropdownMenu>
        {errors.startTime && (
          <HelperText type="error">{errors.startTime}</HelperText>
        )}

        {/* End Time Dropdown with DropdownMenu */}
        <Text style={styles.sectionTitle}>End Time</Text>
        <DropdownMenu
          visible={endTimeModalVisible}
          handleOpen={() => setEndTimeModalVisible(true)}
          handleClose={() => setEndTimeModalVisible(false)}
          dropdownWidth="100%"
          trigger={
            <View
              style={[
                styles.dropdownButton,
                errors.endTime ? {borderColor: theme.colors.error} : null,
              ]}>
              <Text style={{flex: 1, color: endTime ? '#000' : '#888'}}>
                {endTime || 'Select End Time'}
              </Text>
              <IconButton
                icon="chevron-down"
                size={20}
                color="#888"
                style={{margin: 0}}
              />
            </View>
          }>
          {timeOptions.map(item => (
            <MenuOption
              key={item}
              onSelect={() => {
                setEndTime(item);
                setEndTimeModalVisible(false);
              }}>
              <Text>{item}</Text>
            </MenuOption>
          ))}
        </DropdownMenu>
        {errors.endTime && (
          <HelperText type="error">{errors.endTime}</HelperText>
        )}

        {/* Area Dropdown with DropdownMenu */}
        <Text style={styles.sectionTitle}>Area</Text>
        <DropdownMenu
          visible={areaModalVisible}
          handleOpen={() => setAreaModalVisible(true)}
          handleClose={() => setAreaModalVisible(false)}
          dropdownWidth="100%"
          trigger={
            <View
              style={[
                styles.dropdownButton,
                errors.area ? {borderColor: theme.colors.error} : null,
              ]}>
              <Text style={{flex: 1, color: area ? '#000' : '#888'}}>
                {area || 'Select Area'}
              </Text>
              <IconButton
                icon="chevron-down"
                size={20}
                color="#888"
                style={{margin: 0}}
              />
            </View>
          }>
          {areas.map((item: any) => (
            <MenuOption
              key={item.area_id || item.Value}
              onSelect={() => {
                setArea(item.area_name || item.Text || '');
                setAreaId(item.area_id || item.Value); // Store the area ID
                setAreaModalVisible(false);
              }}>
              <Text>{item.area_name || item.Text || 'No Name'}</Text>
            </MenuOption>
          ))}
        </DropdownMenu>
        {errors.area && <HelperText type="error">{errors.area}</HelperText>}

        {/* Customer Connect Dropdown with DropdownMenu */}
        <Text style={styles.sectionTitle}>Customer Connect</Text>
        <DropdownMenu
          visible={customerModalVisible}
          handleOpen={() => setCustomerModalVisible(true)}
          handleClose={() => setCustomerModalVisible(false)}
          dropdownWidth="100%"
          trigger={
            <View
              style={[
                styles.dropdownButton,
                errors.customerConnect
                  ? {borderColor: theme.colors.error}
                  : null,
              ]}>
              <Text style={{flex: 1, color: customerConnect ? '#000' : '#888'}}>
                {customerConnect || 'Select Customer'}
              </Text>
              <IconButton
                icon="chevron-down"
                size={20}
                color="#888"
                style={{margin: 0}}
              />
            </View>
          }>
          {externalContacts.map((item: any) => (
            <MenuOption
              key={item.contact_id}
              onSelect={() => {
                setCustomerConnect(item.contact_name || item.Text || '');
                setCustomerConnectId(item.contact_id); // Store the contact ID
                setSelectedExternalUsers([item.contact_id]); // Update selected external users
                setCustomerModalVisible(false);
              }}>
              <Text>
                {item.contact_name || item.Text || 'No Name'}
              </Text>
            </MenuOption>
          ))}
        </DropdownMenu>
        {errors.customerConnect && (
          <HelperText type="error">{errors.customerConnect}</HelperText>
        )}

        {/* Assigned To Dropdown with DropdownMenu */}
        <Text style={styles.sectionTitle}>Assigned To</Text>
        <DropdownMenu
          visible={assignedToModalVisible}
          handleOpen={() => setAssignedToModalVisible(true)}
          handleClose={() => setAssignedToModalVisible(false)}
          dropdownWidth="100%"
          trigger={
            <View
              style={[
                styles.dropdownButton,
                errors.assignedTo ? {borderColor: theme.colors.error} : null,
              ]}>
              <Text style={{flex: 1, color: assignedTo ? '#000' : '#888'}}>
                {assignedTo || 'Select Assignee'}
              </Text>
              <IconButton
                icon="chevron-down"
                size={20}
                color="#888"
                style={{margin: 0}}
              />
            </View>
          }>
          {internalUsers.map((item: any) => (
            <MenuOption
              key={item.account_user_id}
              onSelect={() => {
                setAssignedTo(item.first_name || item.Text || '');
                setAssignedToId(item.account_user_id); // Store the user ID
                setSelectedInternalUsers([item.account_user_id]); // Update selected internal users
                setAssignedToModalVisible(false);
              }}>
              <Text>
                {item.first_name || item.Text || 'No Name'}
              </Text>
            </MenuOption>
          ))}
        </DropdownMenu>
        {errors.assignedTo && (
          <HelperText type="error">{errors.assignedTo}</HelperText>
        )}

        {/* Status Dropdown with DropdownMenu */}
        <Text style={styles.sectionTitle}>Status</Text>
        <DropdownMenu
          visible={statusModalVisible}
          handleOpen={() => setStatusModalVisible(true)}
          handleClose={() => setStatusModalVisible(false)}
          dropdownWidth="100%"
          trigger={
            <View
              style={[
                styles.dropdownButton,
                errors.status ? {borderColor: theme.colors.error} : null,
              ]}>
              <Text style={{flex: 1, color: status ? '#000' : '#888'}}>
                {status || 'Select Status'}
              </Text>
              <IconButton
                icon="chevron-down"
                size={20}
                color="#888"
                style={{margin: 0}}
              />
            </View>
          }>
          {statuses.map((item: any) => (
            <MenuOption
              key={item.sales_activity_state_id || item.Value}
              onSelect={() => {
                setStatus(item.sales_activity_state_name || item.Text || '');
                setStatusId(Number(item.sales_activity_state_id || item.Value)); // Store the status ID as number
                setStatusModalVisible(false);
              }}>
              <Text>
                {item.sales_activity_state_name || item.Text || 'No Name'}
              </Text>
            </MenuOption>
          ))}
        </DropdownMenu>
        {errors.status && <HelperText type="error">{errors.status}</HelperText>}

        {/* Priority Dropdown with DropdownMenu */}
        <Text style={styles.sectionTitle}>Priority</Text>
        <DropdownMenu
          visible={priorityModalVisible}
          handleOpen={() => setPriorityModalVisible(true)}
          handleClose={() => setPriorityModalVisible(false)}
          dropdownWidth="100%"
          trigger={
            <View
              style={[
                styles.dropdownButton,
                errors.priority ? {borderColor: theme.colors.error} : null,
              ]}>
              <Text style={{flex: 1, color: priority ? '#000' : '#888'}}>
                {priority || 'Select Priority'}
              </Text>
              <IconButton
                icon="chevron-down"
                size={20}
                color="#888"
                style={{margin: 0}}
              />
            </View>
          }>
          {priorities.map((item: any) => (
            <MenuOption
              key={item.sales_activity_priority_id || item.Value}
              onSelect={() => {
                setPriority(
                  item.sales_activity_priority_name || item.Text || '',
                );
                setPriorityId(Number(item.sales_activity_priority_id || item.Value)); // Store the priority ID as number
                setPriorityModalVisible(false);
              }}>
              <Text>
                {item.sales_activity_priority_name || item.Text || 'No Name'}
              </Text>
            </MenuOption>
          ))}
        </DropdownMenu>
        {errors.priority && (
          <HelperText type="error">{errors.priority}</HelperText>
        )}

        {/* Estimated Time Dropdown with DropdownMenu */}
        <Text style={styles.sectionTitle}>Estimated Time</Text>
        <DropdownMenu
          visible={estimatedTimeModalVisible}
          handleOpen={() => setEstimatedTimeModalVisible(true)}
          handleClose={() => setEstimatedTimeModalVisible(false)}
          dropdownWidth="100%"
          trigger={
            <View
              style={[
                styles.dropdownButton,
                errors.estimatedTime ? {borderColor: theme.colors.error} : null,
              ]}>
              <Text style={{flex: 1, color: estimatedTime ? '#000' : '#888'}}>
                {estimatedTime || 'Select Estimated Time'}
              </Text>
              <IconButton
                icon="chevron-down"
                size={20}
                color="#888"
                style={{margin: 0}}
              />
            </View>
          }>
          {estimatedTimes.map((item: any) => (
            <MenuOption
              key={item.Value || item.sales_activity_estimated_time_id}
              onSelect={() => {
                setEstimatedTime(
                  item.Text || item.sales_activity_estimated_time_name || '',
                );
                setEstimatedTimeId(Number(item.Value || item.sales_activity_estimated_time_id)); // Store the estimated time ID as number
                setEstimatedTimeModalVisible(false);
              }}>
              <Text>
                {item.Text ||
                  item.sales_activity_estimated_time_name ||
                  'No Name'}
              </Text>
            </MenuOption>
          ))}
        </DropdownMenu>
        {errors.estimatedTime && (
          <HelperText type="error">{errors.estimatedTime}</HelperText>
        )}
        <Text style={styles.sectionTitle}>Tags</Text>
        <View style={styles.row}>
          <TextInput
            label="Add Tag"
            value={tagInput}
            onChangeText={setTagInput}
            style={[styles.input, {flex: 1}]}
            onSubmitEditing={onAddTag}
          />
          <IconButton icon="plus" onPress={onAddTag} />
        </View>
        <View style={styles.row}>
          {tags.map(tag => (
            <Chip
              key={tag}
              style={styles.chip}
              icon="tag"
              onClose={() => setTags(tags.filter(t => t !== tag))}>
              {tag}
            </Chip>
          ))}
        </View>
        <View style={{height: 32}} />
        <View style={styles.row}>
          <Button
            mode="outlined"
            style={{flex: 1, marginRight: 8}}
            onPress={() => navigation.goBack()}
            disabled={isSubmitting}>
            Cancel
          </Button>
          <Button
            mode="contained"
            style={{flex: 1}}
            onPress={handleCreateActivity}
            loading={isSubmitting}
            disabled={isSubmitting}>
            Create Activity
          </Button>
        </View>
      </ScrollView>
      {/* Snackbar for notifications */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={
          snackbarType === 'error'
            ? styles.errorSnackbar
            : styles.successSnackbar
        }
        action={{
          label: 'Close',
          onPress: () => setSnackbarVisible(false),
        }}>
        {snackbarMessage}
      </Snackbar>
    </View>
  );
};

const styles = StyleSheet.create({
  content: {
    padding: 16,
    paddingBottom: 40,
  },
  sectionTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    marginTop: 16,
    marginBottom: 8,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  chip: {
    marginRight: 8,
    marginBottom: 8,
  },
  input: {
    marginBottom: 12,
    backgroundColor: 'white',
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    height: 48,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#ccc',
    backgroundColor: 'white',
    marginBottom: 12,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 8,
    minWidth: 220,
    maxHeight: 320,
  },
  modalHeader: {
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingBottom: 8,
    marginBottom: 8,
  },
  modalTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    textAlign: 'center',
  },
  modalItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  emptyState: {
    padding: 20,
    alignItems: 'center',
  },
  successSnackbar: {
    backgroundColor: '#4CAF50',
  },
  errorSnackbar: {
    backgroundColor: '#F44336',
  },
});

export default CreateActivityScreen;
