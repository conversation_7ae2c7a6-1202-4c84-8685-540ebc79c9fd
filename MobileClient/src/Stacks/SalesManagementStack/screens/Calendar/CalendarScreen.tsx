import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, Alert, Pressable, Platform } from 'react-native';
import {
  useTheme,
  Text,
  ActivityIndicator,
} from 'react-native-paper';

import {
  Calendar as CalendarIcon,
  ChevronLeft,
  ChevronRight,
  List,
} from 'lucide-react-native';
import { moderateScale, verticalScale } from '../../../../Utils/responsiveUtils';
import {
  Calendar as RNCalendar,
  LocaleConfig,
} from 'react-native-calendars';
import {
  Calendar,
  EventCellStyle,
  CalendarTouchableOpacityProps,
} from 'react-native-big-calendar';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { DSREndPoints } from '../../../../Common/API/ApiEndpoints';
import type {
  CalendarEvent as BigCalendarEvent,
  ICalendarEventBase,
} from 'react-native-big-calendar';
import ApiClient from '../../../../Common/API/ApiClient';
import { handleApiResponse } from '../../../../Common/API/ApiResponseHandler';
import dayjs from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';
import weekday from 'dayjs/plugin/weekday';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import { scale } from 'react-native-size-matters';
import { fontFamily } from '../../../../Common/Theme/typography';
import { useSelector } from 'react-redux';
import { Gesture, GestureDetector, ScrollView } from 'react-native-gesture-handler';
import { encryptPayload } from '../../../../Utils/payloadEncryption';
import { useFocusEffect } from '@react-navigation/native';
import { runOnJS } from 'react-native-reanimated';
import EmptyorErrorComponent from '../../../../Components/EmptyOrError/EmptyOrErrorComponent';
import { useNavigationHook } from '../../../../Hooks/Navigation/useNavigationHook';
import moment from 'moment-timezone';
import { RootState } from '../../../../State/Store';
import isBetween from 'dayjs/plugin/isBetween';
dayjs.extend(isoWeek);
dayjs.extend(weekday);
dayjs.extend(weekOfYear);

LocaleConfig.locales['en'] = {
  monthNames: [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ],
  monthNamesShort: [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ],
  dayNames: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
  dayNamesShort: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
};
LocaleConfig.defaultLocale = 'en';

const CalendarScreen: React.FC = ({ appData }: any) => {
  const controlUnitId = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedControlUnit?.data?.id
  );
  const theme = useTheme();
  const { navigation } = useNavigationHook();
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [calendarData, setCalendarData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [events, setEvents] = useState<BigCalendarEvent[]>([]);
  const [calendarKey, setCalendarKey] = useState<number>(0);
  const [viewMode, setViewMode] = useState<'month' | 'selected'>('selected');
  const [selectedSubView, setSelectedSubView] = useState<'day' | 'week' | 'agenda'>('week');
  const options: Array<'day' | 'week' | 'agenda'> = ['day', 'week', 'agenda'];
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const peopleHirerachySelectedData = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedPeopleHirerachy?.data
  );
  const restrictPeopleHirerachyData = useSelector(
    (state: any) => state.GlobalAppStateData?.restrictPeopleHirerachyData
  );
  const selectedUserId = peopleHirerachySelectedData?.account_user_id || '';
  const isRestricted = useMemo(() => {
    return restrictPeopleHirerachyData?.data?.includes(selectedUserId);
  }, [selectedUserId, restrictPeopleHirerachyData?.data]);
  const timeZone = useSelector((state: RootState) => state.selectedTimezone.timezone);

  const getCalendarRangeForMonth = (
    year?: number,
    month?: number
  ) => {
    const today = dayjs();
    const selectedYear = year ?? today.year();
    const selectedMonth = month ?? today.month();
    const firstDayOfMonth = dayjs(new Date(selectedYear, selectedMonth, 1));
    const lastDayOfMonth = firstDayOfMonth.endOf('month');
    const calendarStart = firstDayOfMonth.startOf('week');
    const calendarEnd = lastDayOfMonth.endOf('week');
    return {
      startDate: calendarStart.format('YYYY-MM-DD'),
      endDate: calendarEnd.format('YYYY-MM-DD'),
    };
  };

  const fetchCalendarData = async (
    page = currentPage,
    shouldAppend = false,
    startDate?: string,
    endDate?: string,
    isAgenda = false
  ) => {
    setIsLoading(true);
    try {
      const userSel = selectedUserId || (await AsyncStorage.getItem('userId'));
      const schemaName = await AsyncStorage.getItem('schemaName');
      const dateRange = getCalendarRangeForMonth();
      const start = startDate ?? dateRange.startDate;
      const end = endDate ?? dateRange.endDate;
      const payload = {
        controlUnitId,
        schemaName,
        userSel,
        startDate: start,
        endDate: end,
      };
      const encrptedPayload = await encryptPayload(payload);
      const response = await ApiClient.post(DSREndPoints.SALES_ACTIVITY_EVENTS, encrptedPayload);

      const data = handleApiResponse(response);
      if (shouldAppend && calendarData) {
        setCalendarData(() => ({
          ...calendarData,
          data: [...(calendarData.data || []), ...(data.data || [])],
        }));
      } else {
        setCalendarData(() => data);
      }
      transformSalesActivitiesToEvents(data.data, isAgenda);
      setCurrentPage(page);
      setStartDate(start);
      setEndDate(end);
    } catch (error) {
      console.error('Error fetching calendar data:', error);
      setError('Failed to fetch calendar data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const transformSalesActivitiesToEvents = (data:any, isAgenda:boolean) => {
    if (!data || !Array.isArray(data[0])) {
      setEvents([]);
      return;
    }

    try {
      const salesActivities = data[0];

      const transformedEvents = salesActivities
        .map((activity) => {
          try {
            const startStr = moment
              .utc(activity.start_date_time)
              .tz(timeZone)
              .format('YYYY-MM-DD HH:mm:ss');
            const endStr = activity.end_date_time
              ? moment.utc(activity.end_date_time).tz(timeZone).format('YYYY-MM-DD HH:mm:ss')
              : null;

            const start = new Date(startStr);
            const end = endStr
              ? new Date(endStr)
              : new Date(new Date(startStr).getTime() + 15 * 60000);

            const isAllDay =
              start.getHours() === 0 &&
              start.getMinutes() === 0 &&
              (!end || (end.getHours() === 0 && end.getMinutes() === 0));
            return {
              id: activity.sales_activity_id,
              title: isRestricted ? 'Busy' : activity.sales_activity_title,
              start,
              end,
              isAllDay,
              organization: activity.organization_name,
              description: activity.sales_activity_description,
              type: activity.sales_activity_type_name,
              color: activity.sales_activity_colour || getTagColor(activity.tags),
              tags: activity.tags,
              priority: activity.sales_activity_priority_name,
              state: activity.sales_activity_state_name,
              startFormatted: moment(start).format('hh:mm A'),
              endFormatted: moment(end).format('hh:mm A'),
            };
          } catch (err) {
            console.error(`❌ Error on activity "${activity.sales_activity_title}":`, err);
            return null;
          }
        })
        .filter((event) => event && !isNaN(event.start.getTime()) && !isNaN(event.end.getTime()));
      setEvents(() => transformedEvents);
      if (isAgenda) setSelectedSubView('agenda');
    } catch (error) {
      console.error('Error transforming sales activities:', error);
      setEvents([]);
    }
  };

  const getTagColor = (tags:any) => {
    if (!tags || !Array.isArray(tags) || tags.length === 0) {
      return '#3B6FF6';
    }

    const tagColors = {
      Meeting: '#3B6FF6', // Blue
      Call: '#8E44AD', // Purple
      Email: '#16A085', // Teal
      'Follow Up': '#F39C12', // Orange
      'Site Visit': '#27AE60', // Green
    };

    const tag = tags[0];
    return tagColors[tag] || '#3B6FF6'; // Default to blue if tag not found
  };

  const handleEventPress = (event: ICalendarEventBase) => {
    const selectedEvent = calendarData.data[0].filter(
      (activity: { sales_activity_id: string }) => activity.sales_activity_id === event.id
    )?.[0];
    navigation.navigate('CreateActivityScreen', { selectedEvent, controlUnitId: controlUnitId });
  };
  const getIconForEventType = (type: string) => {
    switch (type) {
      case 'Online':
        return '💻'; // or <Laptop size={14} />
      case 'Face to Face':
        return '🤝'; // or <Handshake size={14} />
      case 'Call':
        return '📞'; // or <Phone size={14} />
      default:
        return '💻';
    }
  };
  const getTextColorForEvent = (type: string) => {
    switch (type) {
      case 'Online':
        return '#0A7200';
      case 'Call':
        return '#7E22CE';
      case 'Face to Face':
        return '#2563EB';
      default:
        return '#0A7200';
    }
  };
  const renderEvent = (
    event: any,
    touchableOpacityProps: CalendarTouchableOpacityProps
  ) => {
    const icon = getIconForEventType(event.type);
    if (event.isAllDay) {
      return (
        <TouchableOpacity
          {...touchableOpacityProps}
          style={[
            touchableOpacityProps.style,
            styles.allDayEvent,
            {
              backgroundColor: event.color || '#3B6FF6',
              borderLeftWidth: 0,
              borderRightWidth: 0,
              height: verticalScale(20),
            },
          ]}
        >
          <Text style={styles.allDayEventTitle}>{event.title}</Text>
          <Text style={styles.allDayEventLabel}>All day</Text>
          {event.notes}
        </TouchableOpacity>
      );
    }
    const textColor = getTextColorForEvent(event.type);
    const textStyles = [styles.eventTitle, { color: textColor }];
    const backgroundColor =
      event.type === 'Online'
        ? '#E7FFE2'
        : event.type === 'Call'
        ? '#F6EDFF'
        : event.type === 'Face to Face'
        ? '#DBF0FF'
        : '#E7FFE2';
    return (
      <TouchableOpacity
        {...touchableOpacityProps}
        style={[
          touchableOpacityProps.style,
          {
            backgroundColor,
            borderRadius: 10,
            // padding: moderateScale(12),
            paddingHorizontal:scale(15),
            marginVertical: verticalScale(2),
            height: moderateScale(65),
            justifyContent: "space-around",
          },
        ]}
      >
        <View style={{ flexDirection: 'row', alignItems: 'center', gap: scale(5)}}>
          {icon && <Text style={{ fontSize: moderateScale(12), marginRight: scale(5) }}>{icon}</Text>}
          <Text
            style={[
              {
                fontSize: moderateScale(13),
                flexShrink: 1,
              },
              textStyles,
            ]}
            variant='medium'
          >
            {event.title}
          </Text>
        </View>

        {!isRestricted && (
          <Text
            style={[
              {
                fontSize: moderateScale(12),
                marginTop: verticalScale(2),
              },
              textStyles,
            ]}
            variant='medium'
          >
            {event.organization || "Internal Meeting"}
          </Text>
        )}

        <Text
          style={[
            {
              fontSize: moderateScale(12),
              marginTop: verticalScale(2),
            },
            textStyles,
          ]}
          variant='medium'
        >
          {event.startFormatted} - {event.endFormatted}
        </Text>
      </TouchableOpacity>
    );
  };

  const eventCellStyle: EventCellStyle<any> = (event) => {
    return {
      backgroundColor: event.color || '#3B6FF6',
      borderRadius: 4,
      padding: 4,
      opacity: 0.9,
    };
  };
  useFocusEffect(
    useCallback(() => {
      getDataOnMode();
      return () => {};
    }, [viewMode,selectedSubView,controlUnitId, selectedUserId, timeZone])
  );

  useEffect(() => {
    const isInRange = dayjs(selectedDate).isBetween(
      dayjs(startDate),
      dayjs(endDate),
      'day',
      '[]'
    );
    if(!isInRange){
      getDataOnMode()
    }
  },[selectedDate])

  function getDataOnMode () {
    if (viewMode === 'month') {
      const startOfMonth = dayjs(selectedDate).startOf('month').format('YYYY-MM-DD');
      const endOfMonth = dayjs(selectedDate).endOf('month').format('YYYY-MM-DD');
      fetchCalendarData(0, false, startOfMonth, endOfMonth);
    } else {
      if (selectedSubView === 'agenda') {
        fetchCalendarData(0, false, dayjs().format('YYYY-MM-DD'), dayjs().add(1, 'month').format('YYYY-MM-DD'), true);
      } else if (selectedSubView === 'week') {
        const startOfWeek = dayjs(selectedDate).startOf('week');
        const weekDates = [...Array(7).keys()].map((i) => startOfWeek.add(i, 'day').toDate());
        const weekStart = dayjs(weekDates[0]).format('YYYY-MM-DD');
        const weekEnd = dayjs(weekDates[6]).format('YYYY-MM-DD');
        fetchCalendarData(0, false, weekStart, weekEnd);
      } else if (selectedSubView === 'day') {
        const date = dayjs(selectedDate).format('YYYY-MM-DD');
        const nextDate = dayjs(selectedDate).add(1, 'day').format('YYYY-MM-DD');
        fetchCalendarData(0, false, date, nextDate);
      }
    }
  }
  const dotColors: Record<string, string> = {
    call: '#f97316',
    deadline: '#dc2626',
    'Face to Face': '#3377FF',
    Online: '#0A7200',
    Call: '#7E22CE',
    meeting: '#3b82f6',
  };

  const getDot = (type: string) => ({
    key: type,
    color: dotColors[type],
  });
  const markedDates = (Array.isArray(events) ? events : []).reduce<Record<string, any>>(
    (acc, event) => {
      const date = dayjs(event.start).format('YYYY-MM-DD');
      const dot = getDot(event.type || 'Online');
      if (!dot.color) return acc;

      if (!acc[date]) {
        acc[date] = { dots: [dot] };
      } else {
        const exists = acc[date].dots.find((d: any) => d.key === dot.key);
        if (!exists) acc[date].dots.push(dot);
      }

      return acc;
    },
    {}
  );
  
  const renderMonthView = () => {
    const safeEvents = Array.isArray(events) ? events : [];
    const renderDay = ({ date, state }) => {
      if (!date) return <View />;
      const dateObj = new Date(date.dateString);
      const dayOfWeek = dateObj.getDay();
      const isSunday = dayOfWeek === 0;
      const selectDateString = new Date(selectedDate).toISOString().substring(0, 10);
      const dots = markedDates[date.dateString]?.dots || [];
      const todayDateString = dayjs().format('YYYY-MM-DD');
      const isToday = todayDateString === date.dateString;
      let textColor = 'black';

      if (state === 'disabled') {
        textColor = isSunday ? 'rgba(225, 100, 116, 0.25)' : 'rgba(0, 0, 0, 0.25)';
      } else if (isToday) {
        textColor = '#3377FF';
      } else if (isSunday) {
        textColor = 'red';
      }
      return (
        <TouchableOpacity
          onPress={() => {
            setSelectedDate(dateObj);
          }}
        >
          <View
            style={{
              alignItems: 'center',
              justifyContent: "flex-start",
              height: verticalScale(40),
              width: scale(35),
              borderRadius: 4,
              backgroundColor: selectDateString === date.dateString ? '#E9F0FF' : 'transparent',
            }}
          >
            <Text
              style={{
                color: textColor,
                fontSize: moderateScale(14),
                marginTop:verticalScale(5),
              }}
              variant='semiBold'
            >
              {date.day}
            </Text>
            {/* {dots.length > 0 && ( */}
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: scale(2),
                  // marginBottom:verticalScale(5)
                  height:verticalScale(15),
                  // backgroundColor:"red",
                  width:"100%"
                }}
              >
                {dots.map((dot, index) => (
                  <View
                    key={dot.key + index}
                    style={{
                      height: moderateScale(5),
                      width: moderateScale(5),
                      borderRadius: 60,
                      backgroundColor: dot.color,
                    }}
                  />
                ))}
              </View>
            {/* )} */}
          </View>
        </TouchableOpacity>
      );
    };
    return (
      <View style={styles.monthViewContainer}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          bounces={false}
          contentContainerStyle={{ paddingBottom: verticalScale(200) }}
        >
          <View
            style={{
              elevation: 500,
              shadowColor: 'rgba(51, 65, 85, 0.25)',
              shadowOffset: { width: 0, height: 0 },
              shadowOpacity: 0.8, // 40/255 = ~0.25
              shadowRadius: 5,
              borderRadius: 8,
              flex: 1,
              borderWidth:Platform.OS === "ios" ? 0 : 1,
              borderColor:"#E2E4E9",
            }}
          >
            <RNCalendar
              // current={selectedDate}
              current={dayjs(selectedDate).format('YYYY-MM-DD')}
              // pastScrollRange={12}
              // futureScrollRange={12}
              // onVisibleMonthsChange={months => {}}
              markingType="custom"
              dayComponent={renderDay}
              onMonthChange={(month: { dateString: Date }) => {
                setSelectedDate(month.dateString);
              }}
              renderArrow={(direction: 'left' | '') =>
                direction === 'left' ? (
                  <View style={{ backgroundColor: '#E9F0FF', borderRadius: 60 }}>
                    <ChevronLeft color="#000" width={moderateScale(18)} height={moderateScale(18)} />
                  </View>
                ) : (
                  <View style={{ backgroundColor: '#E9F0FF', borderRadius: 60 }}>
                    <ChevronRight color="#000" width={moderateScale(18)} height={moderateScale(18)} />
                  </View>
                )
              }
              // markedDates={{
              //   [selectedDate]: {
              //     selected: true,
              //     selectedColor: '#E9F0FF',
              //     selectedTextColor: '#3377FF',
              //     customStyles: {
              //       container: {
              //         borderRadius: 4,
              //       },
              //     },
              //   },
              // }}
              markedDates={{
                ...markedDates,
                [dayjs(selectedDate).format('YYYY-MM-DD')]: {
                  ...(markedDates[dayjs(selectedDate).format('YYYY-MM-DD')] || {}),
                  selected: true,
                  selectedColor: '#E9F0FF',
                  selectedTextColor: '#3377FF',
                  customStyles: {
                    container: {
                      borderRadius: 4,
                    },
                  },
                },
              }}
              theme={{
                'stylesheet.calendar.header': {
                  header: {
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    paddingVertical: verticalScale(10),
                    paddingHorizontal: scale(2),
                  },
                  monthText: {
                    fontSize: moderateScale(14),
                    fontFamily: fontFamily.semiBold,
                    
                    color: '#000',
                  },
                  dayTextAtIndex0: {
                    color: 'red',
                    fontSize: moderateScale(12),
                    fontFamily: fontFamily.semiBold,
                    paddingVertical: verticalScale(5),
                    width: scale(40),
                  },
                  dayTextAtIndex1: styles.dayindexText,
                  dayTextAtIndex2: styles.dayindexText,
                  dayTextAtIndex3: styles.dayindexText,
                  dayTextAtIndex4: styles.dayindexText,
                  dayTextAtIndex5: styles.dayindexText,
                  dayTextAtIndex6: styles.dayindexText,
                },
                selectedDayBackgroundColor: '#E9F0FF',
                todayTextColor: '#00adf5',
                arrowColor: '#000',
                textDayFontWeight: '500',
                textMonthFontWeight: 'bold',
                arrowBackgroundColor: '#E9F0FF',
              }}
              style={{
                // height: verticalScale(415),
                borderRadius: 8,
                marginTop: verticalScale(5),
              }}
            />
          </View>
           <View style={{height: verticalScale(30)}}/>
          <Calendar
            events={safeEvents}
            minHour={0}
            maxHour={23}
            mode="day"
            date={selectedDate}
            height={verticalScale(100)}
            ampm={true}
            showTime={true}
            onPressEvent={isRestricted ? () => {} : handleEventPress}
            eventCellStyle={eventCellStyle}
            renderEvent={renderEvent}
            renderHeader={() => null}
            hourStyle={{
              color: 'rgba(1, 9, 41, 0.25)',
            }}
            hourRowHeight={verticalScale(80)}
            onSwipeEnd={(date) => {
              setSelectedDate(date);
            }}
            showVerticalScrollIndicator={false}
            verticalScrollEnabled={false}
            // swipeEnabled={true}
          />
        </ScrollView>
      </View>
    );
  };

  const renderWeekView = () => {
    const safeEvents = Array.isArray(events) ? events : [];
    const processedEvents = safeEvents.map((event) => {
      const startDate = new Date(event.start);
      const endDate = new Date(event.end);
      return {
        ...event,
        start: startDate,
        end: endDate,
      };
    });
    const filteredEvents = processedEvents.filter((event) =>
      dayjs(event.start).isSame(dayjs(selectedDate), 'day')
    );
    const startOfWeek = dayjs(selectedDate).startOf('week');
    const weekDates = [...Array(7).keys()].map((i) => startOfWeek.add(i, 'day').toDate());

    const handlerSetDate = (isNext = false) => {
      isNext ? setSelectedDate((prev) => dayjs(prev).add(7, 'day').toDate())
       : setSelectedDate((prev) => dayjs(prev).subtract(7, 'day').toDate());
    };
    const panGesture = Gesture.Pan().onEnd((event) => {
      const { translationX } = event;
      if (translationX < -50) {
        runOnJS(handlerSetDate)(true);
      } else if (translationX > 50) {
        runOnJS(handlerSetDate)();
      }
    });

    return (
      <View style={styles.week_day_view}>
        <GestureDetector gesture={panGesture}>
          <View style={styles.headerContainer}>
            {weekDates.map((date) => {
              const day = dayjs(date);
              const isToday = day.isSame(dayjs(), 'day');
              const isSelected = day.isSame(dayjs(selectedDate), 'day');
              const dots = markedDates[day.format('YYYY-MM-DD')]?.dots || [];
              return (
                <TouchableOpacity
                  key={date.toISOString()}
                  onPress={() => setSelectedDate(date)}
                  style={[styles.dayContainer, isSelected && { backgroundColor: '#E9F0FF' }]}
                >
                  <Text
                    style={[
                      styles.dayName,
                      day.day() === 0 && { color: 'red' },
                      isToday && { color: '#3377FF' },
                    ]}
                    variant='medium'
                  >
                    {day.format('ddd')}
                  </Text>
                  <Text
                    style={[
                      styles.dayNumber,
                      day.day() === 0 && { color: 'red' },
                      isToday && { color: '#3377FF' },
                    ]}
                    variant='medium'
                  >
                    {day.format('D')}
                  </Text>
                  {/* {dots.length > 0 && ( */}
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'center',
                        gap: scale(2),
                      }}
                    >
                      {dots.map((dot, index) => (
                        <View
                          key={dot.key + index}
                          style={{
                            height: moderateScale(5),
                            width: moderateScale(5),
                            borderRadius: 60,
                            backgroundColor: dot.color,
                          }}
                        />
                      ))}
                    </View>
                  {/* )} */}
                </TouchableOpacity>
              );
            })}
          </View>
        </GestureDetector>
         <View style={{height: verticalScale(30)}}/>
        <Calendar
          events={filteredEvents}
          scrollOffsetMinutes={8 * verticalScale(80)}
          minHour={0}
          maxHour={23}
          mode="day"
          date={selectedDate}
          height={verticalScale(100)}
          ampm={true}
          showTime={true}
          onPressEvent={isRestricted ? () => {} : handleEventPress}
          eventCellStyle={eventCellStyle}
          renderEvent={renderEvent}
          renderHeader={() => null}
          hourStyle={{
            color: 'rgba(1, 9, 41, 0.25)',
          }}
          hourRowHeight={verticalScale(80)}
          onSwipeEnd={(date) => {
            setSelectedDate(date);
          }}
          showVerticalScrollIndicator={false}
          hideNowIndicator={true}
          // verticalScrollEnabled={false}
          // swipeEnabled={true}
        />
      </View>
    );
  };
  const renderDayView = () => {
    const safeEvents = Array.isArray(events) ? events : [];
    const processedEvents = safeEvents.map((event) => {
      const startDate = new Date(event.start);
      const endDate = new Date(event.end);
      return {
        ...event,
        start: startDate,
        end: endDate,
      };
    });
    const dayButtonHandler = (isNext=false) => {
      isNext ? setSelectedDate((prev) => dayjs(prev).add(1, 'day').toDate()) 
      : setSelectedDate((prev) => dayjs(prev).subtract(1, 'day').toDate())
    }
    return (
      <View style={styles.week_day_view}>
        <View style={styles.dayActionRow}>
          <TouchableOpacity
            style={styles.leftArrow}
            onPress={() =>dayButtonHandler()}
          >
            <ChevronLeft size={moderateScale(18)} color={'#000'} />
          </TouchableOpacity>
          <Text
            style={{
              fontSize: moderateScale(12),
              color: '#000',
            }}
            variant='semiBold'
          >
            {dayjs(selectedDate).format('dddd, DD MMMM YYYY')}
          </Text>
          <TouchableOpacity
            style={styles.leftArrow}
             onPress={() =>dayButtonHandler(true)}
          >
            <ChevronRight size={moderateScale(18)} color={'#000'} />
          </TouchableOpacity>
        </View>
         <View style={{height: verticalScale(30)}}/>
        <Calendar
          events={processedEvents}
          minHour={0}
          maxHour={23}
          scrollOffsetMinutes={8 * verticalScale(80)}
          mode="day"
          date={selectedDate}
          height={verticalScale(100)}
          ampm={true}
          showTime={true}
          onPressEvent={isRestricted ? () => {} : handleEventPress}
          eventCellStyle={eventCellStyle}
          renderEvent={renderEvent}
          renderHeader={() => null}
          hourStyle={{
            color: 'rgba(1, 9, 41, 0.25)',
          }}
          overlapOffset={70}
          hourRowHeight={verticalScale(80)}
          onSwipeEnd={(date) => {
            setSelectedDate(date);
          }}
          showVerticalScrollIndicator={false}
          verticalScrollEnabled={true}
          swipeEnabled={true}
          hideNowIndicator={true}
        />
      </View>
    );
  };
  const renderAgendaView = () => {
    const getPreviousMonthData = () => {
      const date = dayjs(startDate);
      const start_date = date.subtract(1, 'month').format('YYYY-MM-DD');
      const end_date = date.format('YYYY-MM-DD');
      fetchCalendarData(0, false, start_date, end_date, true);
    };
    const getNextMonthData = () => {
      const date = dayjs(endDate);
      const end_date = date.add(1, 'month').format('YYYY-MM-DD');
      const start_date = date.format('YYYY-MM-DD');
      fetchCalendarData(0, false, start_date, end_date, true);
    };
    const formateText = (date: string) => dayjs(date).format('DD/MM/YYYY');
    return (
      <View style={{ flex: 1 }}>
        <View style={styles.dayActionRow}>
          <TouchableOpacity style={styles.leftArrow} onPress={getPreviousMonthData}>
            <ChevronLeft size={moderateScale(18)} color={'#000'} />
          </TouchableOpacity>
          <Text
            style={{
              fontSize: moderateScale(12),
              color: '#000',
            }}
            variant="semiBold"
          >
            {formateText(startDate) + ' - ' + formateText(endDate)}
          </Text>
          <TouchableOpacity style={styles.leftArrow} onPress={getNextMonthData}>
            <ChevronRight size={moderateScale(18)} color={'#000'} />
          </TouchableOpacity>
        </View>
        {events && events?.length ? (
          <Calendar
            key={calendarKey}
            mode="schedule"
            renderHeader={() => null}
            showTime={false}
            height={Platform.OS === 'android' ? verticalScale(630) : verticalScale(550)}
            events={events}
            renderEvent={renderEvent}
            verticalScrollEnabled={false}
            showVerticalScrollIndicator={false}
            onPressEvent={isRestricted ? () => {} : handleEventPress}
            bodyContainerStyle={{ paddingBottom: verticalScale(120) }}
          />
        ) : (
          <EmptyorErrorComponent message="There are no events in this range." />
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.calendarContainer}>
        <View style={styles.calanderRow}>
          <View style={styles.calanderRow}>
            <Text
              style={{
                fontSize: moderateScale(14),
                color: '#000',
              }}
              variant='semiBold'
            >
              {dayjs(selectedDate).format('MMM')}
            </Text>
            <Text> - </Text>
            <Text
              style={{
                fontSize: moderateScale(14),
                color: '#000',
              }}
              variant='semiBold'
            >
              {dayjs(selectedDate).format('YYYY')}
            </Text>
          </View>
          <View style={viewMode === "selected" ? styles.toggleBar : {height:0,width:0}}>
            {viewMode === 'selected' &&
              options.map((option, index) => {
                const isActive = selectedSubView === option;
                return (
                  <Pressable
                    key={option}
                    onPress={() => {
                     setSelectedSubView(option)
                    }}
                    style={({ pressed }) => [
                      styles.buttonText,
                      isActive ? styles.active : pressed ? styles.pressed : styles.inactive,
                      index !== 0 && { marginLeft: -1 },
                    ]}
                  >
                    <Text
                      variant={isActive ? 'semiBold' : 'medium'}
                      style={[
                        styles.toggleButtonText,
                        isActive ? styles.activeText : styles.inactiveText,
                      ]}
                    >
                      {option.charAt(0).toUpperCase() + option.slice(1)}
                    </Text>
                  </Pressable>
                );
              })}
          </View>
          <View
            style={{
              backgroundColor: '#F1F5F9',
              padding: verticalScale(5),
              borderRadius: 60,
              flexDirection: 'row',
              gap: scale(3),
            }}
          >
            {(['month', 'selected'] as const).map((mode, index) => {
              const isActive = viewMode === mode;
              const Icon = mode === 'month' ? CalendarIcon : List;
              return (
                <Pressable
                  key={mode}
                  onPress={() => setViewMode(mode)}
                  style={({ pressed }) => [
                    styles.button,
                    isActive ? styles.active : pressed ? styles.pressed : styles.inactive,
                  ]}
                >
                  {/* <Icon size={17} color={isActive ? '#fff' : '#09090B'} /> */}
                  <Icon size={17} color={isActive ? '#3B6FF6' : '#09090B'} />
                  {/* <Icon size={17} color={isActive ? '#FFFFFF' : '#09090B'} /> */}
                </Pressable>
              );
            })}
          </View>
        </View>
       
        <View style={styles.calendarContent}>
          {isLoading ? (
            <View style={styles.loaderContainer}>
              <ActivityIndicator size={moderateScale(27)} color={theme.colors.primary} />
              <Text style={{ marginTop: 8 }}>Loading data...</Text>
            </View>
          ) : (
            <>
              {viewMode === 'month' && renderMonthView()}
              {viewMode === 'selected' && selectedSubView === 'week' && renderWeekView()}
              {viewMode === 'selected' && selectedSubView === 'day' && renderDayView()}
              {viewMode === 'selected' && selectedSubView === 'agenda' && renderAgendaView()}
            </>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    paddingTop:10,
  },
  calendarContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  calendarHeaderCard: {
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
  },
  dateNavigationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  navButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  viewSelector: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderRadius: 10,
    backgroundColor: '#f0f0f0',
    padding: 8,
  },
  viewOption: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 6,
  },
  activeViewOption: {
    backgroundColor: '#e0e8ff',
  },
  calendarContent: {
    flex: 1,
    paddingTop: verticalScale(7),
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: '#3B6FF6',
  },

  bigCalendarContainer: {
    backgroundColor: 'white',
    height: 500,
  },
  calendarEvent: {
    borderRadius: 4,
    padding: 4,
  },
  eventTitle: {
    color: 'white',
    // fontWeight: 'bold',
    fontSize: moderateScale(12),
  },
  eventTime: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 10,
  },
  allDayEvent: {
    borderRadius: 4,
    padding: 4,
    opacity: 0.9,
  },
  allDayEventTitle: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
  allDayEventLabel: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 10,
    fontStyle: 'italic',
  },
  eventNotes: {
    marginTop: 3,
  },
  eventNoteText: {
    fontSize: 10,
    color: 'white',
    opacity: 0.9,
  },

  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  agendaContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  agendaHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  agendaButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginRight: 6,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
    backgroundColor: '#fff',
  },
  agendaButtonText: {
    fontSize: 14,
    color: '#333',
  },
  agendaDateRange: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    textAlign: 'center',
    marginHorizontal: 10,
  },
  viewTypeSwitcher: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#3B6FF6',
    borderRadius: 4,
    overflow: 'hidden',
  },
  viewTypeButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: '#fff',
  },
  viewTypeButtonText: {
    fontSize: 14,
    color: '#333',
  },
  activeViewTypeButton: {
    backgroundColor: '#3B6FF6',
  },
  activeViewTypeButtonText: {
    color: '#fff',
  },
  agendaTableHeader: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#f5f5f5',
  },
  agendaTableHeaderText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
  },
  dateColumnHeader: {
    flex: 1.2,
  },
  timeColumnHeader: {
    flex: 1,
  },
  eventColumnHeader: {
    flex: 2,
  },
  agendaEventsContainer: {
    flex: 1,
  },
  agendaEventRow: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 14,
    marginBottom: 1,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  agendaDateText: {
    flex: 1.2,
    fontSize: 14,
    color: '#444',
  },
  agendaTimeText: {
    flex: 1,
    fontSize: 14,
    color: '#444',
  },
  agendaEventContent: {
    flex: 2,
  },
  agendaEventTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#444',
    marginBottom: 4,
  },
  agendaEventTimeDetail: {
    fontSize: 12,
    color: '#666',
  },
  noEventsContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  noEventsText: {
    fontSize: 16,
    color: '#888',
  },

  monthViewContainer: {
    backgroundColor: '#fff',
  },
  weekdayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 10,
    paddingBottom: 10,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  weekdayHeaderCell: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  calendarGrid: {
    flex: 1,
    backgroundColor: '#f9f9f9',
  },
  calendarRow: {
    flex: 1,
    flexDirection: 'row',
  },
  calendarCell: {
    flex: 1,
    aspectRatio: 1,
    borderWidth: 0.5,
    borderColor: '#eee',
    padding: 2,
    justifyContent: 'flex-start',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  cellContent: {
    width: '100%',
    alignItems: 'center',
    paddingTop: 5,
  },
  dayText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 5,
  },
  todayCell: {
    backgroundColor: '#e6f3ff',
  },
  todayText: {
    color: '#3B6FF6',
    fontWeight: 'bold',
  },
  eventIndicators: {
    flexDirection: 'row',
    marginTop: 2,
  },
  eventDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#3B6FF6',
    marginHorizontal: 1,
  },

  simpleAgendaContainer: {
    backgroundColor: '#fff',
    flex: 1,
  },
  simpleAgendaHeader: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#f5f5f5',
  },
  simpleAgendaHeaderText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
  },
  simpleAgendaRow: {
    flexDirection: 'row',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  simpleAgendaDateCell: {
    width: '25%',
    fontSize: 14,
    color: '#444',
  },
  simpleAgendaTimeCell: {
    width: '25%',
    fontSize: 14,
    color: '#444',
  },
  simpleAgendaEventCell: {
    width: '50%',
  },
  simpleAgendaEventTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#444',
    marginBottom: 4,
  },
  simpleAgendaEventTime: {
    fontSize: 12,
    color: '#666',
  },
  dayViewContainer: {
    flex: 1,
    backgroundColor: '#fff',
    height: 650,
  },
  monthAllDayEvent: {
    borderRadius: 4,
    padding: 2,
    opacity: 0.9,
    height: 22,
  },
  monthAllDayEventTitle: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 10,
  },
  monthEvent: {
    borderRadius: 4,
    padding: 2,
    height: 22,
  },
  monthEventTitle: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 10,
  },
  monthAllDayEventContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  allDayIndicator: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: 'white',
    marginRight: 4,
  },

  headerContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingVertical: verticalScale(5),
    justifyContent: 'space-around',
    paddingHorizontal: scale(5),
  },
  week_day_view: { flex: 1, paddingBottom: verticalScale(30) },
  dayContainer: {
    alignItems: 'center',
    width: scale(40),
    gap:verticalScale(3),
    paddingVertical:verticalScale(5),
    borderRadius:8
  },
  dayName: {
    fontSize: moderateScale(12),
    color: '#000',
  },
  dayNumber: {
    fontSize: moderateScale(12),
    color: '#000',
  },
  button: {
    width: verticalScale(25),
    height: verticalScale(25),
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 60,
  },
  buttonText: {
    width: scale(50),
    height: verticalScale(20),
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 60,
  },
  toggleButtonText: {
    fontSize: moderateScale(11),
  },
  leftRounded: {
    borderTopLeftRadius: 999,
    borderBottomLeftRadius: 999,
  },
  rightRounded: {
    borderTopRightRadius: 999,
    borderBottomRightRadius: 999,
  },
  active: {
    // backgroundColor: '#3B6FF6',
    backgroundColor: '#fff',
    // backgroundColor: '#09090B',
  },
  inactive: {
    backgroundColor: '#F1F5F9',
  },
  pressed: {
    backgroundColor: '#E2E8F0',
  },
  text: {
    fontSize: 14,
    fontWeight: '500',
  },
  activeText: {
    // color: '#09090B',
    color:"#3B6FF6"
    // color: '#FFFFFF',
  },
  inactiveText: {
    color: '#09090B',
  },
  toggleBar: {
    backgroundColor: '#F1F5F9',
    padding: verticalScale(5),
    borderRadius: 60,
    flexDirection: 'row',
    gap: scale(3),
  },
  calanderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dayActionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingVertical: verticalScale(20),
  },
  leftArrow: {
    backgroundColor: '#F1F5F9',
    borderRadius: 60,
    padding: scale(2),
  },
  dayindexText: {
    color: '#000',
    fontSize: moderateScale(12),
    fontFamily: fontFamily.semiBold,
    paddingVertical: verticalScale(5),
    width: scale(40),
  },
});

export default CalendarScreen;
