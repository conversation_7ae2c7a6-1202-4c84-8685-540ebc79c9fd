import { View, ScrollView, TouchableOpacity, Modal, Image, Keyboard, KeyboardAvoidingView, Platform,TouchableWithoutFeedback, ToastAndroid } from 'react-native';
import React, { useState, useEffect, useLayoutEffect } from 'react';
import {
  Text,
  Button,
  TextInput,
  Snackbar,
  useTheme,
  Chip,
} from 'react-native-paper';
import { modifiedOutComeTypeData, secondaryOptions } from './DSRDetailViewConstants';
import { Activity } from '../../types/DSRTypes';
import { DatePickerModal, TimePickerModal } from 'react-native-paper-dates';
import AppHeader from '../../../../Components/AppHeader';
import moment from 'moment-timezone';
import styles from './DSRDetailViewStyles';
import { useSelector } from 'react-redux';
import VoiceMessageRecorder from '../../../../Components/VoiceRecorder';
import useCompleteActivity from './hooks/useCompleteActivity';
import useCheckInAndOutHandler from './hooks/useCheckInAndOutHandler';
import useRescheduleAndFollowUp from './hooks/useRescheduleAndFollowUp';
import { DSRDetailsCard, PreviewCard } from './components';
import {
  CheckInOutLoader,
  CheckInOutButton,
  ValidationMessage,
  SectionLabel,
  TimeCard,
  NoteCard,
  LogsList,
} from './components';
import { DropdownField } from '../../../../Components/UI/Menu/DropdownModal';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { RootState } from '../../../../State/Store';
import CustomToast from '../../../../Components/AppToaster/CustomToast';
import { moderateScale, scale } from 'react-native-size-matters';

import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { generateTimeSlots } from '../../utils/generateTimeSlots';
import { timeStringToDate } from '../../utils/generateTimeStringToDate';
import VoiceTranscription from '../../../../Components/VoiceTranscription';

interface DSRDetailViewProps {
  activity?: Activity;
  route?: any;
  navigation: any;
}
export const DSRDetailView = ({ navigation, route }: DSRDetailViewProps) => {
  const [dummyState, setDummyState] = useState(1);
  const refs = {
    outcome: React.useRef<View>(null),
    notes: React.useRef<any>(null),
    buttons: React.useRef<View>(null),
    scrollView: React.useRef<ScrollView>(null),
  };
  const theme = useTheme();
  const timezone = useSelector((state: RootState) => state.selectedTimezone.timezone);
  console.log('startTime');
  const { salesActivityId, salesActivityOutcomeTypes, setRefresh, handleRefresh } = route?.params;
  const dsrEvents = useSelector((state: RootState) => state.DSREvents.events);
  // console.log("route params in detial view", route?.params)
  const activity = dsrEvents.find((event) => event.sales_activity_id === salesActivityId);
   const photoEnable = useSelector((state: RootState) => state.DSREvents.photoEnable);
     const photoSettings = photoEnable?.[0] || {
    is_photo_checkin: 0,
    is_photo_checkout: 0,
     is_call_checkin: 0,
                is_online_meeting_checkin: 0
  };

  console.log("photoSettings", photoSettings)
  const isCallCheckIn = photoSettings?.is_call_checkin && activity?.sales_activity_type_name === 'Call'
  const isOnlineCheckIn = photoSettings?.is_online_meeting_checkin && activity?.sales_activity_type_name === 'Online'
  const outcomeOptions = modifiedOutComeTypeData(salesActivityOutcomeTypes);
  const callCustomToaster = (message: string) => {
    console.log('i am in custom toaster function call');
    return <CustomToast message={message} visible={true} duration={5400} />;
  };

  const formatted = (date: Date | number) => {
    return String(moment(date).format('MMM DD, YYYY').toUpperCase());
  };
  const handleDiscard = () => {
    navigation.goBack();
  };

  const {
    handleCheckInAndOut,
    cameraLoading,
    checkoutLoading,
    setEarliestCheckin,
    handlePreviewDone,
    checkInButtonDisabled,
    checkInDone,
    checkOutDone,
    hasFullCheck,
    // logs,
    setLogs,
    checkInEnabled,
    checkOutEnabled,
    loading,
    previewData,
    previewVisible,
    setPreviewVisible,
    doneLoading,
  } = useCheckInAndOutHandler(activity, callCustomToaster);
  console.log(activity?.voice_note_url, 'url voice note');
  const {
    startDate,
    endDate,
    startTime,
    endTime,
    setShowPicker,
    showPicker,
    onSelectStartTimer,
    onSelectEndTimer,
    startTimeSlots,
    endTimeSlots,
    selectedPickerType,
    onConfirmDatePicker,
  } = useRescheduleAndFollowUp();
  const userId = useSelector((state: any) => state.userId);
  const peopleHirerachySelectedData = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedPeopleHirerachy?.data
  );
  const logs = activity?.checkin_checkout_logs || [];
  const selectedUserId = peopleHirerachySelectedData?.account_user_id || userId;
  const isUser = selectedUserId === userId;
  console.log('isUser', isUser);
  const {
    handleCompleteActivity,
    setVoiceNotePath,
    voiceNotePath,
    handleValidations,
    validationErrors,
    completeActivityLoading,
    onDismissSnackBar,
    snackbarState,
  } = useCompleteActivity(activity,handleRefresh, outcomeOptions);
const getCheckMessages = (errors: string[]) => {
  return errors.some((e) => e.includes('Check In'))
    ? errors.filter((e) => e.includes('Check In'))
    : errors.filter((e) => e.includes('Check Out'));
};

  const [notes, setNotes] = useState(activity?.notes || null);
  const [selectedOutcome, setSelectedOutcome] = useState(activity?.outcome || '');
  const isRescheduleOrFollowUp =
    selectedOutcome === 'Reschedule' || selectedOutcome === 'Follow-up';
  // const dsrEvents = useSelector((state: any) => state.DSREvents.events);
  //   useEffect(() => {
  //     setLogs(activity?.activity?.checkin_checkout_logs || []);
  //     setEarliestCheckin(activity?.activity.earliest_checkin || null);
  //   }, [dsrEvents.events,activity]);

  const scrollToRef = (targetRef: React.RefObject<View>) => {
    if (!targetRef.current || !refs.scrollView.current) return;

    setTimeout(() => {
      targetRef.current?.measureLayout(
        refs?.scrollView?.current!,
        (x, y) => {
          if (y !== undefined) {
            refs.scrollView.current?.scrollTo({ y: Math.max(0, y - 20), animated: true });
          }
        },
        (error) => console.log('measureLayout error:', error)
      );
    }, 100);
  };

  return (
    <KeyboardAvoidingView
      style={[styles.mainContainer]}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 40}
    >
      <View style={[styles.container, { flex: 1 }]}>
        <AppHeader
          title={'Back'}
          showMenu={false}
          showLocationInfo={false}
          menuVisible={false}
          setMenuVisible={() => {}}
          // handleControlUnits={() => {}}
          handleUsers={() => {}}
        />
        <ScrollView
          bounces={false}
          contentContainerStyle={styles.scrollContainer}
          ref={refs.scrollView}
          showsVerticalScrollIndicator={false}
          automaticallyAdjustKeyboardInsets
          
          keyboardDismissMode='on-drag'
          keyboardShouldPersistTaps="handled"
        >
          <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View style={styles.activityItemContainer}>
              <DSRDetailsCard activity={activity} navigation={navigation} />
              {activity?.sales_activity_state_name?.toUpperCase() == 'COMPLETED' && (
                <View style={styles.completedNotesContainer}>
                  <View style={styles.notesView}>
                    <View style={styles.noteHeader}>
                      <Text style={styles.noteLabel} variant="medium">
                        Note:
                      </Text>
                      {activity?.sales_activity_outcome_type_name && (
                        <NoteCard activity={activity} />
                      )}
                      {!activity?.sales_activity_outcome_type_name && (
                        <View style={styles.noOutcomeTag}>
                          <Text style={styles.noOutcomeText}>No outcome</Text>
                        </View>
                      )}
                    </View>
                    <Text style={styles.notesText}>{activity.notes || 'No notes provided'}</Text>
                  </View>
                </View>
              )}
              {activity?.sales_activity_state_name?.toUpperCase() == 'COMPLETED' && (
                <>
                  {activity?.voice_note_url && (
                    <View style={styles.notesSection}>
                      <VoiceMessageRecorder
                        record={activity?.voice_note_url ? activity.voice_note_url : ''}
                        hasDelete={false}
                      />
                    </View>
                  )}
                  <View style={styles.timeContainer}>
                    <TimeCard activity={activity} type="planned" logs={logs} />

                    <TimeCard activity={activity} type="actual" logs={logs} />
                  </View>

                  {logs.length > 0 && <LogsList logs={logs} />}
                </>
              )}
              {!(activity?.sales_activity_state_name?.toUpperCase() === 'COMPLETED') && isUser && (
                <View style={styles.expandedContent}>
                  {/* Check-in/Check-out Buttons and Logs */}
                  {(isCallCheckIn || isOnlineCheckIn || activity?.sales_activity_type_name === 'Face to Face' ) && (
                  <View style={styles.buttonsContainer} ref={refs.buttons}>
                    <View style={styles.buttonsSection}>
                      <View style={styles.buttonsView}>
                        <CheckInOutButton
                          name={'Check In'}
                          onPress={() => handleCheckInAndOut(false)}
                          disabled={!checkInEnabled || cameraLoading || checkInButtonDisabled}
                          loading={cameraLoading}
                          style={{
                            backgroundColor:
                              checkInEnabled && !checkInButtonDisabled ? '#3377FF' : '#F3F4F6',
                            borderColor:
                              checkInEnabled && !checkInButtonDisabled ? '#3377FF' : '#E5E7EB',
                            opacity: cameraLoading || checkInButtonDisabled ? 0.9 : 1,
                          }}
                          textStyle={{
                            color: checkInEnabled && !checkInButtonDisabled ? '#fff' : '#A0A0A0',
                            fontSize: moderateScale(12),
                          }}
                        />
                      </View>
                      {activity?.sales_activity_type_name === 'Face to Face' && (
                        <View style={{ flex: 1 }}>
                          <CheckInOutButton
                            name={'Check Out'}
                            onPress={() => handleCheckInAndOut(true)}
                            disabled={!checkOutEnabled || checkoutLoading}
                            // disabled={false}
                            loading={checkoutLoading}
                            style={{
                              backgroundColor: checkOutEnabled ? '#3377FF' : '#F3F4F6',
                              borderColor: checkOutEnabled ? '#3377FF' : '#E5E7EB',
                              opacity: !checkOutEnabled || checkoutLoading ? 0.9 : 1,
                            }}
                            textStyle={{
                              color: checkOutEnabled ? '#fff' : '#A0A0A0',
                              fontSize: moderateScale(12),
                            }}
                          />
                        </View>
                      )}
                    </View>
                    <View style={{ marginLeft: 16 }}>
                      <ValidationMessage
                        messages={getCheckMessages(validationErrors.check)}
                      />
                    </View>
                    {/* Check-in/Check-out Logs */}
                    {logs.length > 0 && <LogsList logs={logs} />}
                  </View>
                  )}
                  {/* Outcome Section (existing) */}
                  <View style={styles.outcomeSection} ref={refs.outcome}>
                    <SectionLabel label="Outcome" important={true}>
                      <ValidationMessage messages={validationErrors.outcome} />
                    </SectionLabel>
                    <View style={styles.outcomeOptions}>
                      {outcomeOptions.map((option) => (
                        <TouchableOpacity
                          key={option.value}
                          style={[
                            styles.outcomeButton,
                            selectedOutcome === option.value
                              ? {
                                  backgroundColor: option.color,
                                  borderWidth: 0,
                                }
                              : { borderColor: option.color },
                          ]}
                          onPress={() => {
                            console.log('option.value', option.value);
                            setSelectedOutcome(option.value);
                          }}
                        >
                          <Text
                            style={[
                              styles.outcomeButtonText,
                              selectedOutcome === option.value
                                ? { color: '#fff' }
                                : { color: option.color },
                            ]}
                          >
                            {option.label}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>

                    {/* Secondary options group */}
                    <View style={[styles.outcomeOptions]}>
                      {secondaryOptions.map((option) => (
                        <TouchableOpacity
                          key={option.value}
                          style={[
                            styles.outcomeButton,
                            selectedOutcome === option.value
                              ? {
                                  backgroundColor: option.color,
                                  borderWidth: 0,
                                }
                              : { borderColor: option.color },
                          ]}
                          onPress={() => setSelectedOutcome(option.value)}
                        >
                          <Text
                            style={[
                              styles.outcomeButtonText,
                              selectedOutcome === option.value
                                ? { color: '#fff' }
                                : { color: option.color },
                            ]}
                          >
                            {option.label}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </View>

                  {isRescheduleOrFollowUp && (
                    <View style={{ marginHorizontal: 16 }}>
                      <TouchableOpacity onPress={() => {}} style={{ flex: 1 }}>
                        <Text variant="semiBold" style={styles.label}>
                          Meeting Date
                        </Text>
                        <TouchableOpacity
                          onPress={() => {
                            setShowPicker(true);
                            // setActiveField('start');
                            console.log(showPicker, 'bbbb');
                          }}
                        >
                          <Chip
                            icon={
                              <Icon
                                name="calendar-blank-outline"
                                size={moderateScale(22)}
                                color={'rgba(109, 121, 142, 1)'}
                              />
                            }
                            textStyle={styles.dateText}
                            style={[styles.chip_date]}
                            mode="outlined"
                          >
                            {formatted(startDate || new Date())}
                          </Chip>
                        </TouchableOpacity>
                      </TouchableOpacity>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          gap: 12,
                        }}
                      >
                        <DropdownField
                          label="Start Time"
                          value={startTime}
                          data={startTimeSlots}
                          keyField="Value"
                          valueField="Text"
                          onSelect={onSelectStartTimer}
                          // error={!!errors.customerConnect}
                          icon={'clock-time-four-outline'}
                          showAsterisk={false}
                        />
                        <DropdownField
                          label="End Time"
                          value={endTime}
                          data={endTimeSlots}
                          keyField="Value"
                          valueField="Text"
                          onSelect={onSelectEndTimer}
                          // error={!!errors.customerConnect}
                          disable={startTime === ''}
                          icon={'clock-time-four-outline'}
                          showAsterisk={false}
                        />
                        {/* {errors.endTime && <HelperText type="error">{errors.endTime}</HelperText>} */}
                      </View>
                      <DateTimePickerModal
                        isVisible={showPicker}
                        isDarkModeEnabled={false}
                        mode="date"
                        onConfirm={(date) => {
                          console.log('date selected ', date);
                          onConfirmDatePicker({ date });
                        }}
                        onCancel={() => setShowPicker(false)}
                        date={selectedPickerType === 'start' ? startDate : endDate}
                        // minimumDate={activeField === 'start' ? fiveYearsAgo : startDate}
                        // maximumDate={activeField === 'end' ? startDate : undefined}
                        accentColor="#3377ff"
                        themeVariant="light"
                        // textColor="#3377ff"
                        locale="en-IN"
                        // display={Platform.select({ ios: 'spinner', android: 'default' })}
                        pickerContainerStyleIOS={
                          Platform.OS === 'ios'
                            ? {
                                // alignSelf: 'center',
                                // justifyContent: 'center',
                                // backgroundColor: '#fff',
                                // borderRadius: 16,
                                // padding: 10,
                                // width: '90%',
                              }
                            : undefined
                        }
                      />
                    </View>
                  )}
                  {!isRescheduleOrFollowUp && (
                    <View style={styles.notesSection} ref={refs.notes}>
                      <SectionLabel label="Notes" important={true}>
                        <ValidationMessage messages={validationErrors.notes} />
                      </SectionLabel>

                      <TextInput
                        style={styles.notesInput}
                        placeholder="Type your notes here."
                        value={notes}
                        onChangeText={(text: string) => setNotes(text)}
                        multiline
                        textAlignVertical="top"
                        onSubmitEditing={() => Keyboard.dismiss()}
                        returnKeyType="done"
                      />
                      <SectionLabel label="Voice Note" important={false}>
                        <></>
                      </SectionLabel>

                      <VoiceMessageRecorder
                        recordedPath={voiceNotePath}
                        setRecordedPath={setVoiceNotePath}
                      />
                      <VoiceTranscription />
                    </View>
                  )}
                </View>
              )}
            </View>
          </TouchableWithoutFeedback>
          {!(activity?.sales_activity_state_name?.toUpperCase() === 'COMPLETED') && isUser && (
            <View style={[styles.completeButtonContainer, { zIndex: 1 }]}>
              <Button
                mode="contained"
                onPress={() => {
                  Keyboard.dismiss();
                  handleDiscard();
                }}
                style={{
                  flex: 1,
                  borderRadius: 4,
                  backgroundColor: 'rgba(239, 241, 246, 1)',
                }}
                textColor="rgba(0, 0, 0, 1)"
              >
                Discard
              </Button>
              <Button
                mode="contained"
                loading={completeActivityLoading}
                disabled={completeActivityLoading}
                onPress={() => {
                  Keyboard.dismiss();
                  if( selectedOutcome === 'Reschedule' ){
                    handleCompleteActivity(
                    activity.sales_activity_id,
                    selectedOutcome === 'Reschedule' || selectedOutcome === 'Follow-up'
                      ? JSON.stringify({
                          startDateTime: timeStringToDate(startTime, startDate).toISOString(),
                          endDateTime: timeStringToDate(endTime, endDate).toISOString(),
                        })
                      : notes,
                    selectedOutcome,
                    setRefresh
                  );
                  }
                  else {

                    const isNotValid = handleValidations(
                      checkInDone,
                      checkOutDone,
                      activity,
                      selectedOutcome,
                      startDate,
                      endDate,
                      hasFullCheck,
                      notes
                    );
                    if (isNotValid) {
                      if (validationErrors.check?.some((msg) => msg.includes('Check'))) {
                        scrollToRef(refs.buttons);
                      } else if (validationErrors.outcome?.length > 0) {
                        scrollToRef(refs.outcome);
                      } else if (validationErrors.notes?.length > 0) {
                        scrollToRef(refs.notes);
                        setTimeout(() => {
                          refs.notes.current?.focus?.();
                        }, 350);
                      }
                      setDummyState(45);
                      return;
                    }
                    handleCompleteActivity(
                      activity.sales_activity_id,
                      selectedOutcome === 'Reschedule' || selectedOutcome === 'Follow-up'
                        ? JSON.stringify({
                            startDateTime: timeStringToDate(startTime, startDate).toISOString(),
                            endDateTime: timeStringToDate(endTime, endDate).toISOString(),
                          })
                        : notes,
                      selectedOutcome,
                      setRefresh
                    );
                  }
                }}
                style={{
                  flex: 1,
                  borderRadius: 4,
                  backgroundColor: 'rgba(51, 119, 255, 1)',
                }}
                textColor="rgba(255, 255, 255, 1)"
              >
                Complete
              </Button>
            </View>
          )}
        </ScrollView>
        <Snackbar
          visible={snackbarState?.visible}
          onDismiss={onDismissSnackBar}
          duration={2000}
          style={snackbarState?.type === 'error' ? styles.errorSnackbar : styles.successSnackbar}
          action={{
            label: 'Close',
            onPress: onDismissSnackBar,
            textColor: '#fff',
          }}
        >
          <Text
            style={{
              color: '#fff',
              fontFamily: theme.fontFamily.medium,
              fontSize: moderateScale(14),
            }}
          >
            {snackbarState?.message}
          </Text>
        </Snackbar>
        <Modal
          visible={previewVisible}
          transparent
          animationType="fade"
          onRequestClose={() => setPreviewVisible(false)}
        >
          <PreviewCard
            previewData={previewData}
            setPreviewVisible={setPreviewVisible}
            handlePreviewDone={handlePreviewDone}
            doneLoading={doneLoading}
          />
        </Modal>
        {/* Full-screen loader for camera actions */}
        {loading && <CheckInOutLoader loadType={loading} />}
      </View>
    </KeyboardAvoidingView>
  );
};

export default DSRDetailView;
