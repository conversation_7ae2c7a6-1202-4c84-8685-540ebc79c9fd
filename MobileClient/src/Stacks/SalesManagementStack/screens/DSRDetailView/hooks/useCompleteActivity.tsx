import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { uploadToS3 } from '../../../../ChatStack/ChatRoom/Attachment/aws.functions';
import { DSREndPoints } from '../../../../../Common/API/ApiEndpoints';
import { encryptPayload } from '../../../../../Utils/payloadEncryption';
import { updateDSREvent } from '../../../../../State/Slices/DSR/DSREventsSlice';
import Config from 'react-native-config';
import ApiClient from '../../../../../Common/API/ApiClient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { showToast } from '../../../../../Components/AppToaster/AppToaster';
import { RootState } from '../../../../../State/Store';
import { convertToTimeZoneUTC } from '../../../../../Utils';
import { getOutComeTypeId } from '../DSRDetailViewConstants';

const useCompleteActivity = (activity: any, handleRefresh: () => void, outcomeOptions: any) => {
  const dispatch = useDispatch();
  const navigation = useNavigation<any>();
  const [voiceNotePath, setVoiceNotePath] = useState<string | null>(null);
  const [uploadingToS3, setUploadingToS3] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [completeActivityLoading, setCompleteActivityLoading] = useState(false);
  const [snackbarState, setSnackbarState] = useState<{
    visible: boolean;
    message: string;
    type: 'success' | 'error';
  }>({
    visible: false,
    message: '',
    type: 'success',
  });

  const [validationErrors, setValidationErrors] = useState<{
    check: string[];
    outcome: string[];
    notes: string[];
  }>({ check: [], outcome: [], notes: [] });

  const controlUnitId = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedControlUnit?.data?.id
  );
  const timezone = useSelector((state: RootState) => state.selectedTimezone.timezone);
  console.log('controlUnitId', controlUnitId);

  const photoEnable = useSelector((state: RootState) => state.DSREvents.photoEnable);
  const photoSettings = photoEnable?.[0] || {
    is_photo_checkin: 0,
    is_photo_checkout: 0,
    is_call_checkin: 0,
    is_online_meeting_checkin: 0,
  };
  const requiresCallCheckIn =
    photoSettings?.is_call_checkin && activity?.sales_activity_type_name === 'Call';
  const requiresOnlineCheckIn =
    photoSettings?.is_online_meeting_checkin && activity?.sales_activity_type_name === 'Online';
  const handleCompleteActivity = async (
    activityId: string,
    notes: string,
    outcome: string,
    setRefresh: any
  ) => {
    try {
      setCompleteActivityLoading(true);
      const outcomeId = getOutComeTypeId(outcomeOptions, outcome);
      console.log('outcomeId', outcomeId);
      const outcomeTypeId = outcomeId;
      const [userId, schemaName] = await Promise.all([
        AsyncStorage.getItem('userId'),
        AsyncStorage.getItem('schemaName'),
      ]);

      if (!userId || !schemaName) {
        throw new Error('Missing user ID or schema name');
      }
      // Check if this is a reschedule or follow-up outcome
      const isRescheduleOrFollowUp = outcome === 'Reschedule' || outcome === 'Follow-up';
      let payload;
      let endpoint = DSREndPoints.UPDATE_TASK;
      let voiceNoteUrl = null;
      if (voiceNotePath) {
        const fileObj = {
          uri: voiceNotePath,
          name: `voice_note_${Date.now()}.m4a`, // or .aac/.mp3 depending on your recorder
          mime_type: 'audio/m4a', // adjust if needed
        };
        try {
          const uploaded = await uploadToS3(
            [fileObj],
            setUploadingToS3, // you can define this state if you want a loader
            setUploadProgress, // same for progress
            Config.DSR_S3_BUCKET
          );
          voiceNoteUrl = uploaded[0]?.url || '';
        } catch (e) {
          // handle error or show a message
          console.log('error in voice note upload', e);
        }
      }
      if (isRescheduleOrFollowUp) {
        try {
          const dateTimeData = JSON.parse(notes);

          const startdate = convertToTimeZoneUTC(dateTimeData.startDateTime, timezone);
          const enddate = convertToTimeZoneUTC(dateTimeData.endDateTime, timezone);
          // console.log("startdate in reschedule",dateTimeData.startDateTime , "type of date", typeof(dateTimeData.startDateTime), "newstartdate", startdate)
          console.log('start', startdate, enddate);
          payload = {
            accountuserId: userId,
            schemaName: schemaName,
            salesActivityId: activityId,
            startDateTime: startdate,
            endDateTime: enddate,
            controllUnitId: controlUnitId,
          };
          console.log('payload for reschedule', payload);
          endpoint = DSREndPoints.RESCHEDULE;
        } catch (error) {
          console.log('Error parsing date time data:', error);
          throw new Error('Invalid date/time format for reschedule/follow-up');
        }
      } else {
        // Standard payload for normal outcomes
        payload = {
          notes: notes,
          salesActivityId: activityId,
          salesActivityOutcomeType: outcomeTypeId,
          controllUnitId: controlUnitId,
          salesActivityUserId: userId,
          schemaName: schemaName,
          voiceNote: voiceNoteUrl,
        };
      }
      const encryptedPayload = await encryptPayload(payload);

      const payloadEncrypted = {
        iv: encryptedPayload.iv,
        ...(endpoint === DSREndPoints.RESCHEDULE
          ? { encryptedPayload: encryptedPayload.encryptedPayload }
          : { encryptPayload: encryptedPayload.encryptedPayload }),
      };

      const response = await ApiClient.post(endpoint, payloadEncrypted);

      const responseData = response;

      console.log('Update task response: data', responseData.data);

      if (
        responseData &&
        (responseData?.data?.message === 'Updated Successfully' ||
          responseData?.data?.message === 'Sales activity rescheduled successfully')
      ) {
        if (responseData?.data?.message === 'Updated Successfully') {
          // Update Redux state
          console.log('response data in complete activity', response?.data?.data);
          dispatch(
            updateDSREvent({
              event: response?.data?.data,
            })
          );
          setSnackbarState({
            visible: true,
            message: 'Activity Completed',
            type: 'success',
          });

          showToast.success('Activity Completed');
        } else if (responseData?.data?.message === 'Sales activity rescheduled successfully') {
          showToast.success('Updated Successfully');
          handleRefresh();
          // setSnackbarState({
          //   visible: true,
          //   message: 'Updated Successfully',
          //   type: 'success'
          // });
        }
      } else {
        throw new Error(responseData?.data?.message || 'Failed to update activity');
      }
    } catch (error: any) {
      showToast.error('There was an error!');
      console.log('Error completing activity:', error?.message);
      // Show error toast notification
    } finally {
      setCompleteActivityLoading(false);
      setRefresh((prev) => !prev);
      navigation.goBack();
    }
  };
  const handleValidations = (
    checkInDone: any,
    checkOutDone: any,
    activity: any,
    selectedOutcome: any,
    startDate: any,
    endDate: any,
    hasFullCheck: any,
    notes: any
  ) => {
    const checkErrors: string[] = [];
    const outcomeErrors: string[] = [];
    const notesErrors: string[] = [];

    const isFaceToFace = activity?.sales_activity_type_name === 'Face to Face';
    console.log("requiresCallCheckIn || requiresOnlineCheckIn || isFaceToFace", requiresCallCheckIn || requiresOnlineCheckIn || isFaceToFace)
    // Check-In is required for all
    if ((requiresCallCheckIn || requiresOnlineCheckIn || isFaceToFace) && !checkInDone) {
      checkErrors.push('Please complete Check In.');
    }

    //    if ( !checkInDone) {
    //   checkErrors.push('Please complete Check In.');
    // }
    // Check-Out is required only for Face-to-Face activities when full check is not done
    if (isFaceToFace && !hasFullCheck && !checkOutDone) {
      checkErrors.push('Please complete Check Out.');
    }

    // Outcome validation goes here
    if (!selectedOutcome) {
      outcomeErrors.push('Please select an Outcome.');
    }

    if (
      (selectedOutcome === 'Reschedule' || selectedOutcome === 'Follow-up') &&
      (!startDate || !endDate)
    ) {
      outcomeErrors.push('Please select both start and end date/time.');
    }

    // Notes are required for outcomes other than Reschedule and Follow-up
    if (selectedOutcome !== 'Reschedule' && selectedOutcome !== 'Follow-up' && !notes?.trim()) {
      notesErrors.push('Please enter notes.');
    }

    // If there are any validation errors
    if (checkErrors.length || outcomeErrors.length || notesErrors.length) {
      console.log('i am in validation error');

      setValidationErrors({
        check: checkErrors,
        outcome: outcomeErrors,
        notes: notesErrors,
      });

      setSnackbarState({
        visible: true,
        message: checkErrors?.[0] || outcomeErrors?.[0] || notesErrors?.[0],
        type: 'error',
      });

      return true; // validation failed
    }

    // Clear previous errors
    setValidationErrors({ check: [], outcome: [], notes: [] });
    return false;
  };

  const onDismissSnackBar = () => {
    // console.log("i am in dismisssnack bar")
    setSnackbarState((prev) => ({
      ...prev,
      visible: false,
    }));
  };
  return {
    handleCompleteActivity,
    setVoiceNotePath,
    handleValidations,
    voiceNotePath,
    validationErrors,
    completeActivityLoading,

    snackbarState,

    onDismissSnackBar,
  };
};

export default useCompleteActivity;
