import { useState, useEffect } from 'react';
import { Alert } from 'react-native';
import moment from 'moment';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../../../../State/Store';
import locationAccess from '../../../../../Common/Permissions/Location/locationAccess';
import { getCurrentPosition } from '../../../../../Utils/getCurrentPosition';
import { getLocationDetails } from '../../../../../Utils/getLocationDetails';
import { getPictureFromCamera } from '../../../../../Utils/getPictureFromCamera';
import cameraAccess from '../../../../../Common/Permissions/Camera/cameraAccess';
import { getCurrentPositionWithFallback } from '../../../../../Utils/getCurrentPosition';
import { Activity } from '../../../types/DSRTypes';
import { DSREndPoints } from '../../../../../Common/API/ApiEndpoints';
import { uploadToS3 } from '../../../../ChatStack/ChatRoom/Attachment/aws.functions';
import ApiClient from '../../../../../Common/API/ApiClient';
import { updateDSREventLogs } from '../../../../../State/Slices/DSR/DSREventsSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Config from 'react-native-config';
import { showToast } from '../../../../../Components/AppToaster/AppToaster';

const useCheckInAndOutHandler = (activity: any, callCustomToaster: (message: string) => {}) => {
  const dispatch = useDispatch();
  const photoEnable = useSelector((state: RootState) => state.DSREvents.photoEnable);
  const dsrEvents = useSelector((state: RootState) => state.DSREvents.events);
  const currentEvent = dsrEvents.find(
    (event: any) => event.sales_activity_id === activity?.sales_activity_id
  );
  const latestActivity = currentEvent || activity;
  const photoSettings = photoEnable?.[0] || {
    is_photo_checkin: 0,
    is_photo_checkout: 0,
     is_call_checkin: 0,
                is_online_meeting_checkin: 0
  };
  const [checkInLog, setCheckInLog] = useState<any>(null);
  const [checkInDone, setCheckInDone] = useState(false);
  const [checkOutDone, setCheckOutDone] = useState(false);
  const [checkInButtonDisabled, setCheckInButtonDisabled] = useState(false);
  const [earliestCheckin, setEarliestCheckin] = useState(latestActivity?.earliest_checkin || null);
  const [doneLoading, setDoneLoading] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewData, setPreviewData] = useState<any>(null);
  const [cameraLoading, setCameraLoading] = useState(false);
  const [checkoutLoading, setCheckoutLoading] = useState(false);
  const [loading, setLoading] = useState<'checkin' | 'checkout' | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadingToS3, setUploadingToS3] = useState(false);
  const [checkoutId, setCheckoutId] = useState(null);
  const [currentCheckInId, setCurrentCheckInId] = useState<string | null>(null);
  const [logs, setLogs] = useState<any[]>(latestActivity?.checkin_checkout_logs || []);
  useEffect(() => {
    // update local state from activity
    setLogs(activity?.checkin_checkout_logs || []);
    // ... any other state that depends on activity
  }, [activity]);
  const timezone = useSelector((state: RootState) => state.selectedTimezone.timezone);
  const formatTime = (time: string) => {
    return time && timezone && moment(time).isValid()
      ? moment.utc(time).tz(timezone).format('hh:mm A')
      : 'N/A';
  };
  useEffect(() => {
    if (currentEvent) {
      const latestLogs = currentEvent.checkin_checkout_logs || [];
      const latestEarliestCheckin = currentEvent.earliest_checkin || null;
      // console.log("latestLogs",latestLogs);
      // console.log("latestEarliestCheckin",latestEarliestCheckin);
      setLogs(latestLogs);
      setEarliestCheckin(latestEarliestCheckin);

      // Update check-in/check-out states based on latest logs
      const hasCheckin = latestLogs.some((log: any) => log.checkin_timestamp);
      const hasCheckout = latestLogs.some((log: any) => log.checkout_timestamp);

      setCheckInDone(hasCheckin);
      setCheckOutDone(hasCheckout);
      setCheckInButtonDisabled(hasCheckin);
    }
  }, [currentEvent]);
  const hasFullCheck = logs.some((log: any) => log.checkin_timestamp && log.checkout_timestamp);
  const checkInEnabled = !earliestCheckin && !hasFullCheck;
  const checkOutEnabled = !!earliestCheckin && !hasFullCheck;
  const handleCheckInAndOut = async (checkout = false) => {
    const loadValue = checkout ? 'checkout' : 'checkin';
    setLoading(loadValue);
    const hasLocationPermission = await locationAccess();
    console.log('hasLocationPermission', hasLocationPermission);
    if (!hasLocationPermission) {
      setLoading(null);
      return;
    }
    let location = '';
    let locationDetails = null;
    let time = '';
    let date = '';
    let coords = '';
    try {
      console.log('getting current position');
      const position = await getCurrentPositionWithFallback();
      console.log('Position obtained:', position);

      if (position && position.coords) {
        coords = `${position.coords.latitude}, ${position.coords.longitude}`;

        try {
          locationDetails = await getLocationDetails(
            position.coords.latitude,
            position.coords.longitude
          );
          console.log('locationDetails>>>', locationDetails);
          location = locationDetails?.formattedAddress || locationDetails?.city || coords;
        } catch (err) {
          console.log('Error getting location details:', err);
          location = coords;
          locationDetails = null;
        }
      } else {
        throw new Error('No coordinates received');
      }
    } catch (e) {
      showToast.error("Couldn't fetch location. Please try again.");
      console.log('Error getting location:', e);

      // Fallback to activity location or show error
      if (activity.location) {
        location = activity.location;
        coords = '';
        locationDetails = null;
        console.log('Using fallback location from activity');
      } else {
        // Alert.alert(
        //   'Location Required',
        //   'Unable to get your current location. Please ensure location services are enabled and try again.',
        //   [
        //     { text: 'Cancel', onPress: () => setLoading(null) },
        //     { text: 'Retry', onPress: () => handleCheckInAndOut(checkout) },
        //   ]
        // );
        setLoading(null);
        return;
      }
    }
    console.log('utc time zone', moment.utc().format('YYYY-MM-DD HH:mm:ss'));
    time = moment.utc().format('YYYY-MM-DD HH:mm:ss');
    date = moment().format('YYYY-MM-DD');
    console.log('time and date data', time, 'date data', date);
    let imageUri;
    const imageCapture =
      loadValue === 'checkin' ? photoSettings.is_photo_checkin : photoSettings.is_photo_checkout;
    try {
      if (imageCapture && activity?.sales_activity_type_id == 1) {
        const hasPermission = await cameraAccess();
        if (!hasPermission) {
          console.log('Camera permission denied');
          return;
        }

        imageUri = await getPictureFromCamera();

        if (!imageUri) {
          // showToast.error("Couldn't capture image. Try again.");
          return;
        }
      }

      setPreviewData({
        ...(imageUri && { image: imageUri }),
        location,
        coords,
        locationDetails,
        time,
        date,
        isCheckout: checkout,
      });

      setPreviewVisible(true);
    } catch (error) {
      console.log('Error during image or preview setup:', error);
      showToast.error('Something went wrong. Please try again.');
    } finally {
      setLoading(null);
    }
  };

  const postCheckInOut = async (payload: any, isCheckout: boolean, checkOutId?: string) => {
    let url = isCheckout ? DSREndPoints.DSR_CHECKOUT : DSREndPoints.DSR_CHECKIN;
    // For checkout, add checkOutId to payload if provided
    console.log();
    try {
      if (isCheckout && checkOutId) {
        payload.checkOutId = checkOutId;
        console.log('Using checkOutId for checkout:', checkOutId);
      }
      const response = await ApiClient.post(url, payload);
      console.log('response of checkin and out');
      return response;
    } catch (error) {
      // callCustomToaster("Something went wrong")
      console.log('Error in check-in/out API:', error);
      showToast.error('Failed to submit details. Please try again.');
      throw new Error('Something went wrong while submitting data');
    }
  };
  const handlePreviewDone = async () => {
    setDoneLoading(true);
    setUploadProgress(0);
    let newPreviewData: any = {};
    const [accountUserID, schemaName] = await Promise.all([
      AsyncStorage.getItem('userId'),
      AsyncStorage.getItem('schemaName'),
    ]);

    if (previewData?.image) {
      const fileObj = {
        uri: previewData.image,
        name: `checkin_${Date.now()}.jpg`,
        mime_type: 'image/jpeg',
      };
      console.log("file obj for check in and out", fileObj)
      try {
        const uploaded = await uploadToS3(
          [fileObj],
          setUploadingToS3,
          setUploadProgress,
          Config.DSR_S3_BUCKET
        );
        const s3Url = uploaded[0]?.url;
        console.log("s3url of check in or out", s3Url)
        if (s3Url) {
          newPreviewData = { ...previewData, image: s3Url };
        }
      } catch (e) {
        console.log('S3 upload error in checkin and checkout:', e);
        showToast.error('Failed to upload image. Please try again.');
        //Add Error logs here
      }
    }
    const coordsArr = (previewData.coords || '').split(',').map((v: string) => v.trim());
    const latitude = coordsArr[0] || '';
    const longitude = coordsArr[1] || '';
    const locationDetails = previewData.locationDetails || {};
    // For checkout, get the latest checkin_checkout_logs_id from logs
    let checkOutId = undefined;
    if (previewData?.isCheckout && logs.length > 0) {
      // Find the latest log with a checkin and no checkout
      if (currentCheckInId) {
        checkOutId = currentCheckInId;
      } else {
        // Fallback to finding the last unclosed check-in
        const lastCheckin = logs.find(
          (log: any) => log.checkin_timestamp && !log.checkout_timestamp
        );
        checkOutId = lastCheckin?.checkin_checkout_logs_id;
      }
    }

    try {
      // console.log('payloadddd', previewData.time);
      const payload: any = {
        schemaName: schemaName || '',
        salesActivityId: activity?.sales_activity_id || '',
        accountUserID: accountUserID || '',
        logititude: longitude,
        longitude:longitude,
        latitude: latitude,
        time:
          previewData.date && previewData.time
            ? moment.utc(previewData.time, 'YYYY-MM-DD HH:mm:ss').toISOString()
            : '',
        imgUrl: newPreviewData?.image ? [newPreviewData.image] : [],
        area: locationDetails.area || '',
        city: locationDetails.city || '',
        country: locationDetails.country || '',
        address: locationDetails.formattedAddress || '',
      };
      console.log("time preview data",
        previewData.date && previewData.time
            ? moment.utc(previewData.time, 'YYYY-MM-DD HH:mm:ss').toISOString()
            : '',
      )
      console.log(
        'payload data for checkin and out',
        !!previewData?.isCheckout,
        'checkOutId',
        checkOutId,
        payload
      );
      const response = await postCheckInOut(payload, !!previewData?.isCheckout, checkOutId);
      console.log('response');
      // console.log('response check in', response?.data);
      const newCheckInId = response?.data?.data?.[0]?.checkin_checkout_logs_id;

      console.log('newCheckin', newCheckInId);
      if (response?.data) {
        // Update Redux state
        if (!previewData?.isCheckout) {
          console.log(response.data.data, 'yoyoy');
          const newCheckInId = response?.data?.data?.[0]?.checkin_checkout_logs_id;
          setCurrentCheckInId(newCheckInId);
          setCheckoutId(newCheckInId);
        }
        console.log('details here', activity.id, response.data.data);
        dispatch(
          updateDSREventLogs({
            eventId: activity.sales_activity_id,
            logs: response?.data?.data,
            earliest_checkin: newCheckInId ? { checkin_checkout_logs_id: newCheckInId } : null,
          })
        );
      }
      // console.log('checkInResponse', response?.data?.data);
      setCheckoutId(response?.data?.data?.[0]?.[0]?.checkin_checkout_logs_id);
      if (previewData?.isCheckout) {
        setCheckInLog((prev: any) => ({ ...prev, checkout: newPreviewData }));

        setCheckOutDone(true);
        // Update logs state for checkout
        setLogs((prevLogs) => {
          // Find the last checkin without checkout and update it
          const idx = prevLogs.findIndex(
            (log: any) => log.checkin_timestamp && !log.checkout_timestamp
          );
          if (idx !== -1) {
            const updatedLogs = [...prevLogs];
            updatedLogs[idx] = {
              ...updatedLogs[idx],
              checkout_timestamp: payload.time,
              checkout_image_url: newPreviewData?.image ? [newPreviewData.image] : '',
              checkout_area: locationDetails.area || '',
              checkout_city: locationDetails.city || '',
              checkout_country: locationDetails.country || '',
              checkout_address: locationDetails.formattedAddress || '',
              checkout_latitude: latitude,
              checkout_longitude: longitude,
            };
            return updatedLogs;
          }
          return prevLogs;
        });
      } else {
        setCheckInLog(newPreviewData);
        setCheckInDone(true);
        setCheckInButtonDisabled(true);
        setLogs((prevLogs) => [
          ...prevLogs,
          {
            checkin_timestamp: payload.time,
            checkin_image_url: newPreviewData?.image ? [newPreviewData.image] : [],
            checkin_area: locationDetails.area || '',
            checkin_city: locationDetails.city || '',
            checkin_country: locationDetails.country || '',
            checkin_address: locationDetails.formattedAddress || '',
            checkin_latitude: latitude,
            checkin_longitude: longitude,
          },
        ]);
        setEarliestCheckin(payload.time); // Set earliestCheckin after check-in
      }
    } catch (e) {
      // Optionally show error
    }
    setPreviewVisible(false);
    setDoneLoading(false); //
  };
  return {
    earliestCheckin,
    setEarliestCheckin,
    handlePreviewDone,
    postCheckInOut,
    cameraLoading,
    checkoutLoading,
    handleCheckInAndOut,
    checkInButtonDisabled,
    checkInDone,
    checkOutDone,
    hasFullCheck,
    logs,
    setLogs,
    checkInEnabled,
    checkOutEnabled,
    loading,
    previewData,
    setPreviewData,
    previewVisible,
    setPreviewVisible,
    doneLoading,
  };
};
export default useCheckInAndOutHandler;
