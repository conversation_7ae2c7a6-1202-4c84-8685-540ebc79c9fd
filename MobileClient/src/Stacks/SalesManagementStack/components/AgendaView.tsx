import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Text, Divider, List, Chip, Avatar, Card, FAB } from 'react-native-paper';
import { CalendarProps } from '../types';
import { moderateScale, verticalScale } from '../../../Utils/responsiveUtils';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

const AgendaView: React.FC<CalendarProps> = ({
  selectedDate,
  setSelectedDate
}) => {
  const [selectedFilter, setSelectedFilter] = useState<string>('all');

  // Generate sample data for the upcoming week
  const generateAgendaItems = () => {
    const items: any[] = [];
    const today = new Date();
    
    // Generate events for the next 14 days
    for (let i = 0; i < 14; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      
      // Random number of events per day (0-3)
      const numEvents = Math.floor(Math.random() * 4);
      
      for (let j = 0; j < numEvents; j++) {
        const hour = Math.floor(Math.random() * 10) + 8; // 8 AM to 6 PM
        const minute = Math.random() > 0.5 ? 30 : 0; // On the hour or half hour
        
        const companies = ['Surendra Coo FF', 'Avenue Supermarts', 'Vortex Textiles', 'GreenGrow Exports', 'Orion Traders'];
        const randomCompany = companies[Math.floor(Math.random() * companies.length)];
        
        const types = ['Call', 'Meeting', 'Follow-up', 'Presentation', 'Demo'];
        const randomEventType = types[Math.floor(Math.random() * types.length)];
        
        const contactTypes = ['Prospect', 'Opportunity'];
        const randomContactType = contactTypes[Math.floor(Math.random() * contactTypes.length)];
        
        const contacts = ['Surendra', 'Balaji K S', 'Nisha Jain', 'Reema Kulkarni', 'Anil Mehra'];
        const randomContact = contacts[Math.floor(Math.random() * contacts.length)];
        
        items.push({
          id: `event-${i}-${j}`,
          date: new Date(date),
          time: `${hour === 12 ? '12' : hour % 12}:${minute === 0 ? '00' : minute} ${hour >= 12 ? 'PM' : 'AM'}`,
          title: `${randomEventType} with ${randomCompany}`,
          contact: randomContact,
          contactType: randomContactType,
          company: randomCompany,
          location: Math.random() > 0.5 ? 'Virtual Meeting' : 'Client Office',
          icon: getIconForEventType(randomEventType),
          color: getColorForEventType(randomEventType),
          completed: i === 0 && Math.random() > 0.7 // Some events from today might be marked complete
        });
      }
    }
    
    // Sort by date and time
    return items.sort((a, b) => {
      if (a.date.getTime() !== b.date.getTime()) {
        return a.date.getTime() - b.date.getTime();
      }
      return a.time.localeCompare(b.time);
    });
  };
  
  const agendaItems = generateAgendaItems();
  
  // Function to get appropriate icon for event type
  function getIconForEventType(type: string): string {
    const icons: { [key: string]: string } = {
      'Call': 'phone',
      'Meeting': 'account-group',
      'Follow-up': 'arrow-right-circle',
      'Presentation': 'presentation',
      'Demo': 'monitor-screenshot',
    };
    return icons[type] || 'calendar';
  }
  
  // Function to get color for event type
  function getColorForEventType(type: string): string {
    const colors: { [key: string]: string } = {
      'Call': '#4caf50',
      'Meeting': '#2196f3',
      'Follow-up': '#ff9800',
      'Presentation': '#9c27b0',
      'Demo': '#f44336',
    };
    return colors[type] || '#757575';
  }
  
  // Format date for display
  const formatDate = (date: Date): string => {
    const today = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(today.getDate() + 1);
    
    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    } else {
      const options: Intl.DateTimeFormatOptions = { 
        weekday: 'long',
        month: 'short',
        day: 'numeric'
      };
      return date.toLocaleDateString(undefined, options);
    }
  };
  
  // Filter agenda items based on selection
  const getFilteredItems = () => {
    if (selectedFilter === 'all') {
      return agendaItems;
    } else if (selectedFilter === 'today') {
      const today = new Date();
      return agendaItems.filter(item => 
        item.date.getDate() === today.getDate() && 
        item.date.getMonth() === today.getMonth() &&
        item.date.getFullYear() === today.getFullYear()
      );
    } else if (selectedFilter === 'upcoming') {
      const today = new Date();
      return agendaItems.filter(item => item.date > today && !item.completed);
    } else if (selectedFilter === 'completed') {
      return agendaItems.filter(item => item.completed);
    }
    return agendaItems;
  };
  
  const filteredItems = getFilteredItems();
  
  // Group items by date
  const groupedItems: { [key: string]: any[] } = {};
  filteredItems.forEach(item => {
    const dateString = item.date.toDateString();
    if (!groupedItems[dateString]) {
      groupedItems[dateString] = [];
    }
    groupedItems[dateString].push(item);
  });
  
  const sortedDates = Object.keys(groupedItems).sort((a, b) => 
    new Date(a).getTime() - new Date(b).getTime()
  );

  return (
    <View>
      {/* Filter chips */}
      <ScrollView bounces={false}>
        <Chip 
          mode="outlined" 
          selected={selectedFilter === 'all'} 
          onPress={() => setSelectedFilter('all')}
          style={styles.filterChip}
        >
          All
        </Chip>
        <Chip 
          mode="outlined" 
          selected={selectedFilter === 'today'} 
          onPress={() => setSelectedFilter('today')}
          style={styles.filterChip}
        >
          Today
        </Chip>
        <Chip 
          mode="outlined" 
          selected={selectedFilter === 'upcoming'} 
          onPress={() => setSelectedFilter('upcoming')}
          style={styles.filterChip}
        >
          Upcoming
        </Chip>
        <Chip 
          mode="outlined" 
          selected={selectedFilter === 'completed'} 
          onPress={() => setSelectedFilter('completed')}
          style={styles.filterChip}
        >
          Completed
        </Chip>
      </ScrollView>
      
      <Divider />
      
      {/* Agenda list */}
      <ScrollView style={styles.agendaList} contentContainerStyle={{ paddingBottom: 80 }} bounces={false}>
        {sortedDates.map(dateString => (
          <View key={dateString}>
            <Text style={styles.dateHeader}>
              {formatDate(new Date(dateString))}
            </Text>
            
            {groupedItems[dateString].map(item => (
              <TouchableOpacity
                key={item.id}
                onPress={() => setSelectedDate(item.date)}
              >
                <Card style={styles.eventCard} mode="outlined">
                  <Card.Content style={styles.eventCardContent}>
                    <Avatar.Icon 
                      icon={item.icon} 
                      size={moderateScale(40)}
                      color="white" 
                      style={{ backgroundColor: item.color }}
                    />
                    
                    <View style={styles.eventDetails}>
                      <View style={styles.eventHeader}>
                        <Text style={styles.eventTime}>{item.time}</Text>
                        <Chip 
                          mode="flat"
                          style={[
                            styles.typeChip,
                            { backgroundColor: item.contactType === 'Prospect' ? '#e3f2fd' : '#e8f5e9' }
                          ]}
                          textStyle={{ color: item.contactType === 'Prospect' ? '#1976d2' : '#388e3c' }}
                        >
                          {item.contactType}
                        </Chip>
                      </View>
                      
                      <Text style={styles.eventTitle}>{item.title}</Text>
                      
                      <View style={styles.eventFooter}>
                        <Text style={styles.eventLocation}>
                          <MaterialCommunityIcons name="map-marker" size={12} /> {item.location}
                        </Text>
                        
                        {item.completed && (
                          <Chip 
                            mode="flat"
                            style={styles.completedChip}
                            icon="check"
                          >
                            Completed
                          </Chip>
                        )}
                      </View>
                    </View>
                  </Card.Content>
                </Card>
              </TouchableOpacity>
            ))}
          </View>
        ))}
        
        {filteredItems.length === 0 && (
          <View style={styles.emptyState}>
            <MaterialCommunityIcons name="calendar-blank" size={60} color="#ccc" />
            <Text style={styles.emptyStateText}>No events found</Text>
            <Text style={styles.emptyStateSubText}>Try changing your filter</Text>
          </View>
        )}
      </ScrollView>
      
      {/* Add event FAB */}
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => console.log('Add event')}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  filtersContainer: {
    flexDirection: 'row',
    paddingVertical: moderateScale(12),
    paddingHorizontal: moderateScale(8),
    backgroundColor: '#f5f5f5',
  },
  filterChip: {
    marginHorizontal: moderateScale(4),
  },
  agendaList: {
    flex: 1,
    padding: moderateScale(12),
  },
  dateHeader: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: '#333',
    marginTop: verticalScale(16),
    marginBottom: verticalScale(8),
  },
  eventCard: {
    marginBottom: verticalScale(12),
    borderRadius: moderateScale(8),
  },
  eventCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: moderateScale(8),
  },
  eventDetails: {
    flex: 1,
    marginLeft: moderateScale(12),
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(4),
  },
  eventTime: {
    fontSize: moderateScale(12),
    color: '#666',
  },
  typeChip: {
    height: verticalScale(24),
  },
  eventTitle: {
    fontSize: moderateScale(14),
    fontWeight: '600',
    marginBottom: verticalScale(4),
  },
  eventFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  eventLocation: {
    fontSize: moderateScale(12),
    color: '#666',
  },
  completedChip: {
    backgroundColor: '#e8f5e9',
    height: verticalScale(24),
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: verticalScale(40),
  },
  emptyStateText: {
    fontSize: moderateScale(16),
    fontWeight: '500',
    color: '#666',
    marginTop: verticalScale(16),
  },
  emptyStateSubText: {
    fontSize: moderateScale(14),
    color: '#999',
    marginTop: verticalScale(8),
  },
  fab: {
    position: 'absolute',
    margin: moderateScale(16),
    right: 0,
    bottom: 0,
  },
});

export default AgendaView;