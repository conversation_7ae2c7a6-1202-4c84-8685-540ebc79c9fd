import React from 'react';
import { View, StyleSheet, TouchableOpacity, ScrollView, Dimensions } from 'react-native';
import { Text, Divider, useTheme } from 'react-native-paper';
import { CalendarProps } from '../types';
import { moderateScale, verticalScale } from '../../../Utils/responsiveUtils';

const { width } = Dimensions.get('window');
const TIME_COLUMN_WIDTH = 60;
const DAY_COLUMN_WIDTH = (width - TIME_COLUMN_WIDTH - 16) / 7; // Account for padding

const WeekView: React.FC<CalendarProps> = ({
  selectedDate,
  setSelectedDate,
  currentView,
  setCurrentView,
}) => {
  const theme = useTheme();

  // Get the start of the week (Sunday)
  const getStartOfWeek = (date: Date): Date => {
    const result = new Date(date);
    const day = date.getDay();
    result.setDate(date.getDate() - day);
    return result;
  };

  // Generate week days
  const generateWeekDays = (): Date[] => {
    const weekDays: Date[] = [];
    const startOfWeek = getStartOfWeek(selectedDate);
    
    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      weekDays.push(day);
    }
    
    return weekDays;
  };

  const weekDays = generateWeekDays();
  const today = new Date();
  
  // Time slots for a full day (8am to 8pm)
  const timeSlots = Array.from({ length: 12 }, (_, i) => i + 8);
  
  // Color palette matching the screenshot
  const getEventColor = (type: string) => {
    const colors = {
      meeting: '#3B6FF6',  // Blue
      call: '#8E44AD',     // Purple
      demo: '#16A085',     // Green
      follow: '#F39C12',   // Orange
      mail: '#FF4F70',     // Red
      review: '#28CC9E'    // Turquoise
    };
    return colors[type as keyof typeof colors] || '#3B6FF6';
  };

  // Generate sample events that match what's in the screenshot
  // Assuming the week starts on Sunday, May 4, 2025
  const sampleEvents = [
    {
      id: 'event-1',
      day: weekDays[1], // Monday
      startHour: 10,
      startMinute: 0,
      duration: 1,
      title: 'SCF',
      description: '10:00am - 11:00am',
      type: 'meeting'
    },
    {
      id: 'event-2',
      day: weekDays[1], // Monday
      startHour: 14,
      startMinute: 0,
      duration: 1.5,
      title: 'ASM',
      description: '2:00pm - 3:30pm',
      type: 'call'
    },
    {
      id: 'event-3',
      day: weekDays[2], // Tuesday
      startHour: 12,
      startMinute: 30,
      duration: 1,
      title: 'VTX',
      description: '12:30pm - 1:30pm',
      type: 'demo'
    },
    {
      id: 'event-4',
      day: weekDays[3], // Wednesday
      startHour: 9,
      startMinute: 0,
      duration: 1,
      title: 'GGE',
      description: '9:00am - 10:00am',
      type: 'follow'
    },
    {
      id: 'event-5',
      day: weekDays[4], // Thursday
      startHour: 11,
      startMinute: 0,
      duration: 1,
      title: 'WZ',
      description: '11:00am - 12:00pm',
      type: 'mail'
    },
    {
      id: 'event-6',
      day: weekDays[5], // Friday
      startHour: 15,
      startMinute: 30,
      duration: 1.5,
      title: 'SCF',
      description: '3:30pm - 5:00pm',
      type: 'review'
    }
  ];

  const isToday = (date: Date) => {
    return date.getDate() === today.getDate() &&
           date.getMonth() === today.getMonth() &&
           date.getFullYear() === today.getFullYear();
  };
  
  const isSelected = (date: Date) => {
    return date.getDate() === selectedDate.getDate() &&
           date.getMonth() === selectedDate.getMonth() &&
           date.getFullYear() === selectedDate.getFullYear();
  };
  
  // Format the dates for display in the column headers
  const formatColumnDate = (date: Date) => {
    const day = date.getDate().toString().padStart(2, '0');
    return day;
  };

  // Get day name (Sun, Mon, etc.)
  const getDayName = (date: Date) => {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return days[date.getDay()];
  };
  
  // Render the time column
  const renderTimeColumn = () => {
    return (
      <View style={styles.timeColumn}>
        <View style={styles.timeHeader} />
        {timeSlots.map((hour) => (
          <View key={`time-${hour}`} style={styles.timeSlot}>
            <Text style={styles.timeText}>
              {hour === 12 ? '12:00 PM' : 
                hour > 12 ? `${hour-12}:00 PM` : `${hour}:00 AM`}
            </Text>
          </View>
        ))}
      </View>
    );
  };
  
  // Render day columns with events
  const renderDayColumns = () => {
    return (
      <ScrollView horizontal showsHorizontalScrollIndicator={false} bounces={false}>
        <View style={styles.dayColumnsContainer}>
          {weekDays.map((day, index) => (
            <View key={`col-${index}`} style={styles.dayColumn}>
              {/* Day header */}
              <TouchableOpacity
                style={[
                  styles.dayHeader,
                  isToday(day) && styles.todayHeader,
                  isSelected(day) && styles.selectedDayHeader
                ]}
                onPress={() => setSelectedDate(day)}
              >
                <Text style={[
                  styles.dayName,
                  isToday(day) && styles.todayText,
                  isSelected(day) && styles.selectedDayText
                ]}>
                  {getDayName(day)}
                </Text>
                <Text style={[
                  styles.dayNumber,
                  isToday(day) && styles.todayText,
                  isSelected(day) && styles.selectedDayText
                ]}>
                  {formatColumnDate(day)}
                </Text>
              </TouchableOpacity>
              
              {/* Day column content */}
              <View style={[
                styles.dayContent,
                (day.getDay() === 6 || day.getDay() === 0) && styles.weekendColumn,
                isToday(day) && styles.todayColumn
              ]}>
                {/* Time slots */}
                {timeSlots.map((hour) => (
                  <View key={`cell-${day.toISOString()}-${hour}`} style={styles.dayTimeSlot}>
                    <Divider />
                  </View>
                ))}
                
                {/* Render events for this day */}
                {sampleEvents.filter(event => 
                  event.day.getDate() === day.getDate() &&
                  event.day.getMonth() === day.getMonth()
                ).map(event => {
                  // Calculate position based on time
                  const top = (event.startHour - timeSlots[0]) * 60 + event.startMinute;
                  const height = event.duration * 60;
                  
                  return (
                    <TouchableOpacity
                      key={event.id}
                      style={[
                        styles.eventBlock,
                        {
                          top,
                          height,
                          backgroundColor: getEventColor(event.type),
                        }
                      ]}
                    >
                      <Text style={styles.eventTitle}>{event.title}</Text>
                      <Text style={styles.eventDescription}>{event.description}</Text>
                    </TouchableOpacity>
                  );
                })}
              </View>
            </View>
          ))}
        </View>
      </ScrollView>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.weekGrid}>
        {renderTimeColumn()}
        {renderDayColumns()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 10,
    overflow: 'hidden',
  },
  weekGrid: {
    flex: 1,
    flexDirection: 'row',
  },
  timeColumn: {
    width: TIME_COLUMN_WIDTH,
    backgroundColor: 'white',
  },
  timeHeader: {
    height: 50,
  },
  timeSlot: {
    height: 60, // Each hour is 60px
    justifyContent: 'flex-start',
    paddingTop: 5,
    paddingRight: 8,
    alignItems: 'flex-end',
  },
  timeText: {
    fontSize: moderateScale(10),
    color: '#666',
  },
  dayColumnsContainer: {
    flexDirection: 'row',
  },
  dayColumn: {
    width: DAY_COLUMN_WIDTH,
  },
  dayHeader: {
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  todayHeader: {
    backgroundColor: '#e3f2fd',
  },
  selectedDayHeader: {
    backgroundColor: '#3B6FF6',
  },
  dayName: {
    fontSize: moderateScale(12),
    color: '#555',
    fontWeight: '500',
  },
  dayNumber: {
    fontSize: moderateScale(16),
    fontWeight: 'bold',
    color: '#333',
  },
  todayText: {
    color: '#1976d2',
  },
  selectedDayText: {
    color: 'white',
  },
  dayContent: {
    position: 'relative',
    height: 60 * 12, // 12 hours x 60px
  },
  dayTimeSlot: {
    height: 60,
    borderRightWidth: 1,
    borderRightColor: '#e0e0e0',
  },
  eventBlock: {
    position: 'absolute',
    left: 2,
    right: 2,
    paddingHorizontal: 6,
    paddingVertical: 4,
    borderRadius: 6,
    overflow: 'hidden',
    elevation: 2,
  },
  eventTitle: {
    color: 'white',
    fontSize: moderateScale(12),
    fontWeight: '600',
  },
  eventDescription: {
    color: 'white',
    fontSize: moderateScale(10),
    marginTop: 2,
  },
  weekendColumn: {
    backgroundColor: '#f8f9fa', // Light gray for weekends
  },
  todayColumn: {
    backgroundColor: '#f0f7ff', // Light blue for today
  }
});

export default WeekView;