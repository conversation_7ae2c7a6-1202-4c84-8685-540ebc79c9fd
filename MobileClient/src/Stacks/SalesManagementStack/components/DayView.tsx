import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text, Card, Avatar, Divider } from 'react-native-paper';
import { CalendarProps } from '../types';
import { moderateScale, verticalScale } from '../../../Utils/responsiveUtils';

const DayView: React.FC<CalendarProps> = ({
  selectedDate
}) => {
  // Format date for display
  const formatDate = (date: Date): string => {
    const options: Intl.DateTimeFormatOptions = { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    };
    return date.toLocaleDateString(undefined, options);
  };
  
  // Time slots from 8 AM to 6 PM
  const timeSlots = Array.from({ length: 11 }, (_, i) => i + 8);

  const getRandomEventColor = () => {
    const colors = ['#e57373', '#81c784', '#64b5f6', '#ba68c8', '#ffb74d'];
    return colors[Math.floor(Math.random() * colors.length)];
  };
  
  // Generate mock events for the day
  const generateMockEvents = () => {
    const events: any[] = [];
    
    // Create 2-4 random events
    const numEvents = Math.floor(Math.random() * 3) + 2;
    
    for (let i = 0; i < numEvents; i++) {
      const hour = Math.floor(Math.random() * 10) + 8; // Random hour between 8 and 18
      const minute = Math.random() > 0.5 ? 30 : 0; // Either on the hour or half hour
      const duration = Math.floor(Math.random() * 3) + 1; // Duration of 1-3 hours
      
      const companies = ['Surendra Coo FF', 'Avenue Supermarts', 'Vortex Textiles', 'GreenGrow Exports', 'Orion Traders'];
      const randomCompany = companies[Math.floor(Math.random() * companies.length)];
      
      const types = ['Call', 'Meeting', 'Follow-up', 'Presentation', 'Demo'];
      const randomType = types[Math.floor(Math.random() * types.length)];
      
      const contacts = ['Surendra', 'Balaji K S', 'Nisha Jain', 'Reema Kulkarni', 'Anil Mehra'];
      const randomContact = contacts[Math.floor(Math.random() * contacts.length)];

      events.push({
        id: `event-${i}`,
        startHour: hour,
        startMinute: minute,
        duration,
        title: `${randomType} with ${randomCompany}`,
        contact: randomContact,
        location: Math.random() > 0.5 ? 'Virtual Meeting' : 'Client Office',
        color: getRandomEventColor(),
        type: Math.random() > 0.5 ? 'Prospect' : 'Opportunity'
      });
    }
    
    // Sort events by start time
    return events.sort((a, b) => {
      if (a.startHour === b.startHour) {
        return a.startMinute - b.startMinute;
      }
      return a.startHour - b.startHour;
    });
  };
  
  const mockEvents = generateMockEvents();
  
  // Format time (e.g., "9:00 AM")
  const formatTime = (hour: number, minute: number): string => {
    const period = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour > 12 ? hour - 12 : (hour === 0 ? 12 : hour);
    return `${displayHour}:${minute === 0 ? '00' : minute} ${period}`;
  };
  
  // Render an event in the day timeline
  const renderEvent = (event: any, index: number) => {
    const startTime = formatTime(event.startHour, event.startMinute);
    const endHour = event.startHour + event.duration;
    const endTime = formatTime(endHour, event.startMinute);
    
    // Get initials for avatar
    const getInitials = (name: string) => {
      return name
        .split(' ')
        .map(part => part[0])
        .join('')
        .toUpperCase();
    };
    
    return (
      <Card 
        key={event.id} 
        style={[styles.eventCard, { borderLeftColor: event.color, borderLeftWidth: 4 }]}
        mode="outlined"
      >
        <Card.Content style={styles.eventCardContent}>
          <View style={styles.eventHeader}>
            <Text style={styles.eventTime}>{startTime} - {endTime}</Text>
            <Text style={[
              styles.eventType, 
              event.type === 'Prospect' ? styles.prospectText : styles.opportunityText
            ]}>
              {event.type}
            </Text>
          </View>
          
          <Text style={styles.eventTitle}>{event.title}</Text>
          
          <View style={styles.eventDetails}>
            <Avatar.Text 
              size={moderateScale(24)} 
              label={getInitials(event.contact)} 
              style={{ backgroundColor: event.color }}
            />
            <View style={styles.contactDetails}>
              <Text style={styles.contactName}>{event.contact}</Text>
              <Text style={styles.eventLocation}>{event.location}</Text>
            </View>
          </View>
        </Card.Content>
      </Card>
    );
  };

  return (
    <View style={styles.container}>
      {/* Date header */}
      <View style={styles.dateHeader}>
        <Text style={styles.dateText}>{formatDate(selectedDate)}</Text>
      </View>
      
      <Divider />
      
      {/* Time slots with events */}
      <ScrollView contentContainerStyle={styles.dayContent} bounces={false}>
        {/* Morning schedule section */}
        <Text style={styles.sectionHeader}>Morning</Text>
        {mockEvents
          .filter(event => event.startHour < 12)
          .map((event, index) => renderEvent(event, index))}
        
        {/* Afternoon schedule section */}
        <Text style={styles.sectionHeader}>Afternoon</Text>
        {mockEvents
          .filter(event => event.startHour >= 12)
          .map((event, index) => renderEvent(event, index))}
        
        {/* If no events for the day */}
        {mockEvents.length === 0 && (
          <View style={styles.noEvents}>
            <Text style={styles.noEventsText}>No events scheduled for today</Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  dateHeader: {
    padding: moderateScale(12),
    backgroundColor: '#f5f5f5',
  },
  dateText: {
    fontSize: moderateScale(16),
    fontWeight: '600',
    color: '#333',
  },
  dayContent: {
    padding: moderateScale(12),
  },
  sectionHeader: {
    fontSize: moderateScale(14),
    fontWeight: '600',
    color: '#555',
    marginTop: verticalScale(8),
    marginBottom: verticalScale(8),
  },
  eventCard: {
    marginBottom: verticalScale(12),
    borderRadius: moderateScale(8),
  },
  eventCardContent: {
    padding: moderateScale(8),
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(4),
  },
  eventTime: {
    fontSize: moderateScale(12),
    color: '#666',
  },
  eventType: {
    fontSize: moderateScale(12),
    fontWeight: '500',
  },
  prospectText: {
    color: '#1976d2',
  },
  opportunityText: {
    color: '#388e3c',
  },
  eventTitle: {
    fontSize: moderateScale(14),
    fontWeight: '600',
    marginBottom: verticalScale(8),
  },
  eventDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contactDetails: {
    marginLeft: moderateScale(8),
  },
  contactName: {
    fontSize: moderateScale(12),
    fontWeight: '500',
  },
  eventLocation: {
    fontSize: moderateScale(11),
    color: '#666',
  },
  noEvents: {
    padding: moderateScale(24),
    alignItems: 'center',
  },
  noEventsText: {
    color: '#999',
    fontStyle: 'italic',
  },
});

export default DayView;