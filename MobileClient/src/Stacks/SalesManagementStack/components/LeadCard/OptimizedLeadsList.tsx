import React, {useCallback, useMemo} from 'react';
import {FlatList, View, ActivityIndicator} from 'react-native';
import {Text} from 'react-native-paper';
import {useTheme} from 'react-native-paper';
import LeadCard from './LeadCard'; // Your optimized LeadCard component

const OptimizedLeadsList = ({
  leads,
  handleEndReached,
  refreshing,
  onRefresh,
  hasMoreData,
  styles
}) => {
  const theme = useTheme();

  // Memoize the renderItem function
  const renderItem = useCallback(({item}) => {
    return <LeadCard item={item} />;
  }, []);

  // Memoize the keyExtractor function
  const keyExtractor = useCallback((item) => item.id + 1, []);

  // Memoize the ItemSeparatorComponent
  const ItemSeparatorComponent = useCallback(() => (
    <View style={{height: 10}} />
  ), []);

  // Memoize the ListFooterComponent
  const ListFooterComponent = useCallback(() => {
    return hasMoreData ? (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
      </View>
    ) : null;
  }, [hasMoreData, styles.footerLoader, theme.colors.primary]);

  // Memoize the ListEmptyComponent
  const ListEmptyComponent = useCallback(() => (
    <Text
      style={{
        textAlign: 'center',
        marginTop: 40,
        color: theme.colors.onSurfaceVariant,
      }}>
      No leads found
    </Text>
  ), [theme.colors.onSurfaceVariant]);

  // Memoize the contentContainerStyle
  const contentContainerStyle = useMemo(() => [
    styles.listContent,
    leads.length === 0 && styles.emptyListContent,
  ], [styles.listContent, styles.emptyListContent, leads.length]);

  return (
    <FlatList
      data={leads}
      keyExtractor={keyExtractor}
      renderItem={renderItem}
      onEndReached={handleEndReached}
      onEndReachedThreshold={0.1}
      refreshing={refreshing}
      onRefresh={onRefresh}
      // ItemSeparatorComponent={ItemSeparatorComponent}
      contentContainerStyle={contentContainerStyle}
      ListFooterComponent={ListFooterComponent}
      ListEmptyComponent={ListEmptyComponent}
      // Performance optimizations
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      updateCellsBatchingPeriod={50}
      initialNumToRender={10}
      windowSize={10}
      bounces={false}
    //   getItemLayout={undefined} // Only use if you have fixed height items
    />
  );
};

export default OptimizedLeadsList;