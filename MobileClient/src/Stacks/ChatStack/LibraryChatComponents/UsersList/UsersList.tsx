import {View, Text, TouchableOpacity, FlatList} from 'react-native';
import React, {useEffect, useState} from 'react';
import {useQuery} from '@apollo/client';
import {GET_ALL_USERS, GET_ACCOUNT_USERS} from '../../AppSync/queries';
import {styles} from './UsersListStyles';
import {letterColors, textColors} from '../../Constants/Colors';
import {useSelector} from 'react-redux';
import LoaderSpin from '../../../../Components/Loader/ChatLoader/ChatLoader';
import Icon from 'react-native-vector-icons/Ionicons';
import {getAccountId} from '../../../../Common/Utils/Storage';
interface UsersListProps {
  selectedUsers: string[];
  setSelectedUsers: React.Dispatch<React.SetStateAction<string[]>>;
  existUsers?: string[];
}

const UsersList: React.FC<UsersListProps> = ({
  selectedUsers,
  setSelectedUsers,
  existUsers,
}) => {
  const [accountId, setAccountId] = useState<string | null>('');
  useEffect(() => {
    getAccountId()
      .then(accountIdResponse => {
        setAccountId(accountIdResponse);
      })
      .catch(error => {
        console.log('Failed to fetch account ID:', error);
      });
  }, []);
  console.log('accountId', accountId);
  const {error, data, loading, refetch} = useQuery(GET_ACCOUNT_USERS, {
    variables: {account_id: accountId},
    skip: !accountId,
  });

  useEffect(() => {
    if (accountId) {
      refetch();
    }
  }, [accountId, refetch]);

  const userId = useSelector((state: any) => state.userId);
  console.log('userId', userId);
  console.log('data', data);
  const toggleUserSelection = (userId: string) => {
    setSelectedUsers(prevSelected =>
      prevSelected.includes(userId)
        ? prevSelected.filter(id => id !== userId)
        : [...prevSelected, userId],
    );
  };

  const renderUser = ({
    item,
  }: {
    item: {user_name: string; email_id?: string; user_id: string};
  }) => {
    const firstLetter = item.user_name?.substring(0, 1).toUpperCase();
    const backgroundColor = letterColors[firstLetter] || '#ccc';
    const textColor = textColors[firstLetter] || '#000';
    const isSelected = selectedUsers.includes(item.user_id);
    if (existUsers?.includes(item.user_id) || userId === item.user_id) {
      return null;
    }

    return (
      <TouchableOpacity onPress={() => toggleUserSelection(item.user_id)}>
        <View style={[styles.userRow]}>
          <View style={[styles.avatar, {backgroundColor}]}>
            <Text style={[styles.avatarText, {color: textColor}]}>
              {item.user_name?.substring(0, 2).toUpperCase() || 'U'}
            </Text>
          </View>

          {isSelected && (
            <View
              style={{
                position: 'absolute',
                backgroundColor: '#3377ff',
                borderRadius: 20,
                left: 45,
                bottom: 4,
                padding: 2,
              }}>
              <Icon name={'checkmark'} size={12} color="#fff" />
            </View>
          )}
          {/* User Info */}
          <View style={styles.userContainer}>
            <Text style={styles.userName}>{item.user_name}</Text>
            {/* <Text style={styles.userEmail}>{item.email_id}</Text> */}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.userListContainer}>
      {loading && <LoaderSpin />}
      {error && <Text>Error fetching users: {error.message}</Text>}
      {data?.GetAccountUsers?.length > 0 ? (
        <FlatList
          data={data.GetAccountUsers.map(user => user.user_details).flat()}
          renderItem={renderUser}
          keyExtractor={item => item.user_id}
          contentContainerStyle={{paddingBottom: 250}}
          keyboardShouldPersistTaps="always"
          bounces={false}
        />
      ) : (
        !loading && <Text>No users found.</Text>
      )}
    </View>
  );
};

export default UsersList;
