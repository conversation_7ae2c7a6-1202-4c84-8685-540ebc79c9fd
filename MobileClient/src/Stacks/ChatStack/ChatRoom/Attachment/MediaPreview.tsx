// MediaPreview.tsx
import React from 'react';
import {
  View,
  Image,
  Text,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { styled } from "nativewind";
 
const StyledView = styled(View)
const StyledText = styled(Text)
const StyledImage = styled(Image)
const StyledTouchableOpacity = styled(TouchableOpacity)
const StyledScrollView = styled(ScrollView)
 
interface MediaItem {
  uri: string;
  type?: string;
  name?: string;
  size?: number;
}
 
interface MediaPreviewProps {
  selectedMedia: MediaItem[];
  onRemoveItem: (index: number) => void;
  maxPreviewItems?: number;
}
 
const MediaPreview: React.FC<MediaPreviewProps> = ({
  selectedMedia,
  onRemoveItem,
  maxPreviewItems = 5
}) => {
  if (!selectedMedia?.length) return null;
 
  const getFileIcon = (type?: string) => {
    if (type?.includes('pdf')) {
      return 'file-pdf-box';
    }
    if (type?.includes('doc')) {
      return 'file-word-box';
    }
    if (type?.includes('xls')) {
      return 'file-excel-box';
    }
    return 'file-document-outline';
  };
 
  return (
    <StyledView className="w-full bg-gray-100 border-t border-gray-200 p-2">
      <StyledScrollView 
        horizontal
        showsHorizontalScrollIndicator={false}
        className="flex-row"
        bounces={false}
      >
        {selectedMedia.slice(0, maxPreviewItems).map((item, index) => (
          <StyledView key={index} className="mr-2 relative">
            {item.type?.startsWith('image') ? (
              // Image Preview
              <StyledView className="w-16 h-16 rounded-lg overflow-hidden">
                <StyledImage
                  source={{ uri: item.uri }}
                  className="w-full h-full"
                  resizeMode="cover"
                />
              </StyledView>
            ) : (
              // Document Preview
              <StyledView className="w-16 h-16 bg-blue-100 rounded-lg items-center justify-center">
                <Icon 
                  name={getFileIcon(item.type)}
                  size={24}
                  color="#3B82F6"
                />
                <StyledText className="text-xs text-blue-500 mt-1 px-1" numberOfLines={1}>
                  {item.name || 'File'}
                </StyledText>
              </StyledView>
            )}
            
            {/* Remove button */}
            <StyledTouchableOpacity 
              onPress={() => onRemoveItem(index)}
              className="absolute -top-1 -right-1 bg-gray-800 rounded-full w-5 h-5 items-center justify-center"
            >
              <Icon name="close" size={12} color="#FFFFFF" />
            </StyledTouchableOpacity>
          </StyledView>
        ))}
        
        {selectedMedia.length > maxPreviewItems && (
          <StyledView className="w-16 h-16 bg-gray-200 rounded-lg items-center justify-center">
            <StyledText className="text-sm text-gray-600">
              +{selectedMedia.length - maxPreviewItems}
            </StyledText>
          </StyledView>
        )}
      </StyledScrollView>
    </StyledView>
  );
};
 
export default MediaPreview;
 