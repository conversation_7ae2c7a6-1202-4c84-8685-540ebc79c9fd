import React, {useState, useEffect, useCallback} from 'react';
import {
  TouchableOpacity,
  View,
  Text,
  Image,
  FlatList,
  StatusBar,
  Platform,
} from 'react-native';
import {useMutation, useQuery} from '@apollo/client';
import {
  BackArrowIcon,
  VerticalDotsIcon,
  AddUserIcon,
} from '../../SVG/ChatRoomSvg';
import {styles} from './RoomInfoStyles';
import {
  useNavigation,
  NavigationProp,
  useFocusEffect,
} from '@react-navigation/native';
import {RootStackParamList} from '../../../../Common/Routes/StackTypes';
import {UserProfile} from '../../chatImages';
import {GET_ROOM_DETAILS_BY_ID} from '../../AppSync/queries';
import {letterColors, textColors} from '../../Constants/Colors';
import ListModal from '../../LibraryChatComponents/ListModal/ListModal';
import {
  UPDATE_ROOM_ROLE_FOR_USER,
  REMOVE_MEMBER_FROM_GROUP,
} from '../../AppSync/mutations';
import {useSelector} from 'react-redux';
import LoaderSpin from '../../../../Components/Loader/ChatLoader/ChatLoader';

import SafeContainerView from '../../../../Components/SafeContainer/index';
type UserDetails = {
  user_id: string;
  user_name: string;
};

type RoomUser = {
  user_details: UserDetails;
  room_role: 'Admin' | 'Member';
};

const RoomInfo: React.FC = () => {
  const {roomId} = useSelector((state: any) => state.roomId);
  console.log('roomId', roomId);
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const userId = useSelector((state: any) => state.userId);
  const [modalOptions, setModalOptions] = useState<string[]>([
    'Make an admin',
    'Remove From group',
  ]);
  const [loginUserRole, setLoginUserRole] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [userSelected, setUserSelected] = useState('');
  const {
    data,
    error,
    refetch: refetchRoomDetails,
    loading,
  } = useQuery(GET_ROOM_DETAILS_BY_ID, {
    variables: {roomId: roomId},
  });

  useFocusEffect(
    useCallback(() => {
      if (roomId) {
        refetchRoomDetails();
      }
    }, [roomId]),
  );

  const [removeMemberFromGroup] = useMutation(REMOVE_MEMBER_FROM_GROUP);
  // console.log('data of rooms', data);
  const [updateRoomRoleForUser] = useMutation(UPDATE_ROOM_ROLE_FOR_USER);
  useEffect(() => {
    if (data?.getRoomDetails?.associatedUsers) {
      const loginUser = data?.getRoomDetails?.associatedUsers.find(
        (user: any) => user?.user_details?.user_id === userId,
      );
      if (loginUser) {
        setLoginUserRole(loginUser.room_role);
      }
    }
  }, [data, userId]);

  const updateUser = async (updateRole: string) => {
    let updateUserRole = {
      room_id: roomId,
      updated_by: userId,
      updated_for: userSelected,
      updated_room_role: updateRole,
    };

    try {
      const response = await updateRoomRoleForUser({
        variables: {input: updateUserRole},
      });
      refetchRoomDetails();
      console.log('successfully changes the role', response);
    } catch (e) {
      console.log('error in changing Role', e);
    }
  };

  const RemoveUser = async () => {
    console.log('i am in remove user', userSelected);
    if (!roomId || !userId) return;
    try {
      const {data} = await removeMemberFromGroup({
        variables: {
          input: {
            room_id: roomId,
            user_id: userSelected,
            removed_by_user_id: userId,
          },
        },
      });
      refetchRoomDetails();
      console.log('response data in remove user', data);
    } catch (err) {
      console.log('Error in Removing the user in group', err);
    }
  };

  const handleOptionSelect = (option: string) => {
    console.log('option selected', option);
    switch (option) {
      case 'Make an admin':
        updateUser('Admin');
        // Handle making admin
        break;
      case 'Remove an admin':
        updateUser('Member');
        // Handle removing admin
        break;
      case 'Remove From group':
        RemoveUser();
        console.log('userSelected for remove user', userSelected);
        // Handle removing from group
        break;
    }
    setShowModal(false);
  };

  const selectTheUser = (item: RoomUser) => {
    setUserSelected(item.user_details.user_id);
    // Create a new array for options instead of mutating the existing one
    const newOptions =
      item.room_role === 'Admin'
        ? ['Remove an admin', 'Remove From group']
        : ['Make an admin', 'Remove From group'];
    setModalOptions(newOptions);
    setShowModal(true);
  };

  const renderCard = ({item}: {item: any}) => {
    if (!item?.user_details) return null;
    const firstLetter = item?.user_details?.user_name
      ?.substring(0, 1)
      .toUpperCase();
    const backgroundColor = letterColors[firstLetter] || '#ccc';
    const textColor = textColors[firstLetter] || '#000';
    // console.log("data for the admin", data.getRoomDetails.created_by_user_id, " ",item.user_details.user_id, " ", userId )
    const canModifyUser =
      data?.getRoomDetails?.created_by_user_id !==
        item?.user_details?.user_id && item?.user_details?.user_id !== userId;

    return (
      <TouchableOpacity
        activeOpacity={canModifyUser && loginUserRole === 'Admin' ? 0.1 : 1}
        style={[styles.userCard, !canModifyUser && styles.userCardDisabled]}
        onPress={() =>
          canModifyUser && loginUserRole === 'Admin' && selectTheUser(item)
        }>
        <View style={styles.userRow}>
          {/* Avatar */}
          <View style={[styles.avatar, {backgroundColor}]}>
            <Text style={[styles.avatarText, {color: textColor}]}>
              {item?.user_details?.user_name?.substring(0, 2).toUpperCase() ||
                'U'}
            </Text>
          </View>
          {/* User Info */}
          <View style={styles.userContainer}>
            <Text style={styles.userName}>
              {item?.user_details?.user_id === userId
                ? 'You'
                : item?.user_details?.user_name}
            </Text>
            {item?.room_role === 'Admin' && (
              <Text style={styles.userRole}>{item.room_role}</Text>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeContainerView backgroundColor="#fff">
      <View style={styles.Container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <BackArrowIcon />
          </TouchableOpacity>
          {/* <TouchableOpacity>
            <VerticalDotsIcon />
          </TouchableOpacity> */}
        </View>
        {loading && <LoaderSpin />}
        {error && <Text>Error fetching users: {error?.message}</Text>}
        {/* Use FlatList as the main scrollable container */}
        <FlatList
          data={data?.getRoomDetails?.associatedUsers || []}
          renderItem={renderCard}
          keyExtractor={item => item?.user_details?.user_id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{marginHorizontal: 10, paddingBottom: 100}}
          bounces={false}
          ListHeaderComponent={() => (
            <>
              {/* Room Details */}
              <View style={styles.RoomDetails}>
                <TouchableOpacity style={{marginBottom: 15}}>
                  <Image source={UserProfile} style={styles.roomProfile} />
                </TouchableOpacity>
                <Text style={styles.roomName}>
                  {data?.getRoomDetails?.name || 'Room Name'}
                </Text>
                <Text style={styles.roomMembersCount}>
                  {data?.getRoomDetails?.associatedUsers?.length || 0} members
                </Text>
              </View>

              {/* Add Members */}
              {loginUserRole === 'Admin' && (
                <View style={{flex: 1}}>
                  <TouchableOpacity
                    style={styles.addMemberContainer}
                    onPress={() => {
                      navigation.navigate('UsersListScreen', {
                        currentUsers: data?.getRoomDetails?.associatedUsers,
                      });
                      // handleNavigateToUsersListScreen()
                    }}>
                    <View
                      style={{
                        backgroundColor: '#3B6FF6',
                        padding: 10,
                        borderRadius: 23,
                        marginRight: 12,
                      }}>
                      <AddUserIcon height={23} width={23} />
                    </View>
                    <Text style={styles.addMember}>Add members</Text>
                  </TouchableOpacity>
                </View>
              )}
            </>
          )}
        />
        <ListModal
          visible={showModal}
          onClose={() => setShowModal(false)}
          options={modalOptions}
          onSelect={handleOptionSelect}
        />
      </View>
    </SafeContainerView>
  );
};

export default RoomInfo;
