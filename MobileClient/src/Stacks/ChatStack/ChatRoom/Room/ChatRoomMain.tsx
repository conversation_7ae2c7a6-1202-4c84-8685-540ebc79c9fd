type ChatRoomProps = {
  route: RouteProp<RootStackParamList, 'ChatRoom'>;
};

import React, {useEffect, useState, useCallback, useRef} from 'react';
import {
  View,
  Text,
  FlatList,
  ImageBackground,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
} from 'react-native';
import {useQuery, useMutation, useSubscription} from '@apollo/client';
import {
  useNavigation,
  NavigationProp,
  RouteProp,
  useFocusEffect,
} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
// Components
import ChatRoomHeader from './ChatRoomHeader';
import ChatInput from './ChatInput';

import Message from '../Message/Message';
import LoaderSpin from '../../../../Components/Loader/ChatLoader/ChatLoader';
import UploadLoader from '../../../../Components/Loader/ChatLoader/ChatInputLoader';
// grapql queries
import {GET_ALL_MESSAGES_BY_ROOM} from '../../AppSync/queries';
import {CLEAR_MESSAGE_COUNT} from '../../AppSync/mutations';
import {CREATE_MESSAGE_SUBSCRIPTION} from '../../AppSync/Subscriptions';
import Animated from 'react-native-reanimated';
// Types and Styles
import {RootStackParamList} from '../../../../Common/Routes/StackTypes';
import {MessageType, GroupedMessageType, FileItem} from './ChatRoom.types';


// others
import moment from 'moment';
import SpinLoader from '../../../../Components/Loader/SpinLoader/SpinLoader';
import {groupMessagesByDate} from '../ChatRoomFunctions';

import {useUpdateMessageStatus} from './ChatRoomHooks/UpdateMessageStatus';
import {useCreateMessageHandler} from './ChatRoomFunctions/createMessageHandler.function';

import {useTypingHandlers} from './ChatRoomHooks/TypingHandler';
import SafeContainerView from '../../../../Components/SafeContainer/index';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import { createStyles } from './ChatRoomStyles';
const ChatRoom: React.FC<ChatRoomProps> = ({route}) => {
  const {data: routeData} = route.params;
  const styles = createStyles()
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMoreMessages, setHasMoreMessages] = useState(true);
  // State Management
  const [userId, setUserId] = useState<string | null>(null);
  const [messages, setMessages] = useState<MessageType[]>([]);
  const [groupedMessages, setGroupedMessages] = useState<GroupedMessageType[]>(
    [],
  );
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [nextToken, setNextToken] = useState<string | null>(null);
  const [messageInput, setMessageInput] = useState('');

  const [messageResponse, setMessageResponse] = useState();
  const {updateMessageStatus} = useUpdateMessageStatus();
  const [loading, setLoading] = useState(false);
  const [replyMessage, setReplyMessage] = useState<any | null>(null);

  const [images, setImages] = useState<any[]>([]);
  const [filesSelected, setFilesSelected] = useState<FileItem[]>([]);
  const [clearMessageCount] = useMutation(CLEAR_MESSAGE_COUNT);
  // console.log("rooomid",routeData?.room_id, "userid",userId)
  const {
    data,
    loading: queryLoading,
    error,
    refetch: refetchMessages,
    fetchMore,
  } = useQuery(GET_ALL_MESSAGES_BY_ROOM, {
    variables: {
      room_id: routeData?.room_id,
      user_id: userId,
      nextToken: null,
      limit: 10,
    },
    skip: !routeData?.room_id,
  });

  const loadMoreMessages = async () => {
    if (!hasMoreMessages || isLoadingMore) return;

    try {
      setIsLoadingMore(true);

      if (!nextToken) {
        setHasMoreMessages(false);
        return;
      }

      const {data: nextFetchedData} = await fetchMore({
        variables: {
          room_id: routeData?.room_id,
          user_id: userId,
          nextToken,
          limit: 10,
        },
      });

      const newNextToken =
        nextFetchedData?.getAllMessagesByRoom?.nextToken || null;
      setNextToken(newNextToken);
      setHasMoreMessages(!!newNextToken);

      const newMessages = Array.isArray(
        nextFetchedData?.getAllMessagesByRoom?.Messages,
      )
        ? (nextFetchedData?.getAllMessagesByRoom?.Messages).reverse()
        : [];
      // console.log("new messages:", JSON.stringify(newMessages, null, 2));
      if (newMessages.length > 0) {
        setMessages(prevMessages => {
          const messageMap = new Map(
            prevMessages.map(msg => [msg.message_id, msg]), // Maintain previous messages first
          );

          // Add new messages at the beginning (unshift behavior)
          newMessages.forEach((newMsg: any) => {
            if (!messageMap.has(newMsg.message_id)) {
              messageMap.set(newMsg.message_id, newMsg);
            }
          });

          return Array.from(messageMap.values());
        });
      }
    } catch (error) {
      console.log('Error loading more messages:', error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  const handleEndReached = () => {
    if (!isLoadingMore && hasMoreMessages) {
      loadMoreMessages();
    }
  };

  // Render footer (loading indicator when fetching more messages)
  const renderFooter = () => {
    if (!isLoadingMore) return null;
    // return <LoaderSpin />;
    return <SpinLoader size={25} color="#3377ff" />;
  };

  const {createMessageHandler, loading: messageLoading} =
    useCreateMessageHandler({
      routeData,
      userId,
      setUploading,
      setProgress,
      setMessageInput,
      setFilesSelected,
      setImages,
      setReplyMessage,
      setMessageResponse,
      messageInput,
      images,
      filesSelected,
      replyMessage,
      refetchMessages,
    });

  const {typingState, startTyping, stopTyping} = useTypingHandlers(
    routeData?.room_id,
    userId,
  );

  useFocusEffect(
    useCallback(() => {
      if (userId) {
        refetchMessages();
      }
    }, [userId]),
  );

  const typingMessage =
    typingState.typingUsers.length > 0
      ? `${typingState.typingUsers.slice(0, 2).join(', ')}${
          typingState.typingUsers.length > 2
            ? `, and ${typingState.typingUsers.length - 2} more`
            : ''
        } ${typingState.typingUsers.length > 1 ? 'are' : 'is'} typing...`
      : null;

  useEffect(() => {
    const fetchUserId = async () => {
      const id = await AsyncStorage.getItem('userId');
      setUserId(id);
    };
    fetchUserId();
  }, []);

  const {data: subscriptionData} = useSubscription(
    CREATE_MESSAGE_SUBSCRIPTION,
    {
      variables: {room_id: routeData?.room_id},
      onData: () => {
        refetchMessages();
        if (subscriptionData) {
          console.log('Hi i am subscription of messages', subscriptionData);
        }

        setUploading(false);
      },
    },
  );

  useEffect(() => {
    const newNextToken = data?.getAllMessagesByRoom?.nextToken || null;
    setNextToken(newNextToken);
    if (data?.getAllMessagesByRoom?.Messages) {
      const msgs = [...data.getAllMessagesByRoom.Messages].reverse();
      // console.log("messages:", JSON.stringify(msgs, null, 2));
      setMessages(msgs);

      setGroupedMessages(groupMessagesByDate(msgs));
      if (userId)
        data?.getAllMessagesByRoom?.Messages.forEach(
          async (eachMessage: any) => {
            if (
              userId !== eachMessage.sender_id &&
              !eachMessage.read_by_user_ids?.includes(userId)
            ) {
              try {
                await updateMessageStatus(
                  routeData?.room_id,
                  userId,
                  eachMessage.message_id,
                );
              } catch (err: any) {
                console.log('Error updating read status:', err);
              }
            }
          },
        );
    }
  }, [data]);

  useEffect(() => {
    if (!userId || !routeData?.room_id) return;

    clearMessageCount({
      variables: {
        room_id: routeData?.room_id,
        user_id: userId,
      },
    }).catch(err => console.log('Mutation Error:', err.message));
  }, [subscriptionData, data]);

  const renderDateSeparator = (date: string) => {
    // Parse the date string properly to avoid moment.js warnings
    const messageDate = moment(new Date(date));
    const today = moment().startOf('day');
    const yesterday = moment().subtract(1, 'day').startOf('day');

    let formattedDate = messageDate.isSame(today, 'day')
      ? 'Today'
      : messageDate.isSame(yesterday, 'day')
      ? 'Yesterday'
      : messageDate.format('ddd DD MMMM YYYY');

    return (
      <View style={styles.dateSeparatorContainer}>
        <Text style={styles.dateSeparatorText}>{formattedDate}</Text>
      </View>
    );
  };

  const messageRef = useRef<FlatList<any>>(null);

  // const renderMessage = ({item, index}: {item: MessageType; index: number}) => {
  //   const previousMessage = messages[index - 1];
  //   const currentDate = new Date(item.created_date).toDateString();
  //   const previousDate = previousMessage
  //     ? new Date(previousMessage.created_date).toDateString()
  //     : null;

  //   return (
  //     <>
  //     {currentDate !== previousDate && renderDateSeparator(currentDate)}
  //       <Message
  //         key={item.message_id}
  //         item={item}
  //         messages={messages}
  //         setReplyOnSwipeOpen={setReplyMessage}
  //       />

  //     </>
  //   );
  // };

  const renderMessage = ({item, index}: {item: MessageType; index: number}) => {
    // if flatlist inverted = {false} use previous message to compare
    // const previousMessage = messages[index - 1];
    //   const currentDate = new Date(item.created_date).toDateString();
    //   const previousDate = previousMessage
    //     ? new Date(previousMessage.created_date).toDateString()
    //     : null;
    const nextMessage = messages[index + 1]; // Check next message instead
    const currentDate = new Date(item.created_date).toDateString();
    const nextDate = nextMessage
      ? new Date(nextMessage.created_date).toDateString()
      : null;

    return (
      <>
        {/* Render separator only if the next message is a different date */}

        <Message
          key={item.message_id}
          item={item}
          messages={messages}
          setReplyOnSwipeOpen={setReplyMessage}
        />
        {currentDate !== nextDate && renderDateSeparator(currentDate)}
      </>
    );
  };

  // if (queryLoading || loading) return <LoaderSpin />;

  if (error) return <Text>Error: {error.message}</Text>;
  const {top, bottom} = useSafeAreaInsets();
  return (
    <SafeContainerView backgroundColor="#fff" style={{flex: 1}}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{flex: 1}}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}>
        <ChatRoomHeader
          roomName={routeData?.room_details?.name}
          typingMessage={!typingState.loggedUser ? typingMessage : null}
          onBackPress={() => navigation.goBack()}
          // onOptionsPress={() => {
          //   setShowListMenu(true);
          // }}
        />
        <ImageBackground
          source={require('../../chat-bg.png')}
          style={styles.backgroundImage}>
          {queryLoading || loading ? (
            <SpinLoader size={40} color="#3377ff" />
          ) : (
            <FlatList
              ref={messageRef}
              data={messages}
              keyExtractor={item => item.message_id}
              renderItem={renderMessage}
              contentContainerStyle={styles.messagesList}
              showsVerticalScrollIndicator={false}
              inverted
              maxToRenderPerBatch={10}
              windowSize={10}
              removeClippedSubviews
              initialNumToRender={10} // Reduced for better performance
              onEndReached={handleEndReached}
              onEndReachedThreshold={0.8} // Triggers closer to end
              ListFooterComponent={renderFooter}
              updateCellsBatchingPeriod={50}
              getItemLayout={
                data?.length
                  ? (data, index) => ({
                      length: 60, // Ensure this is correct, or remove if variable height
                      offset: 60 * index,
                      index,
                    })
                  : undefined
              } // Avoid potential crashes if data is empty
              bounces={false}
            />
          )}

          <ChatInput
            message={messageInput}
            onChangeText={setMessageInput}
            onSendMessage={createMessageHandler}
            onFocus={startTyping}
            onBlur={stopTyping}
            setImages={setImages}
            images={images}
            filesSelected={filesSelected}
            setFilesSelected={setFilesSelected}
            setReplyMessage={setReplyMessage}
            replyMessage={replyMessage}
            // onScroll={scrollToEnd}
          />
          {/* </KeyboardAvoidingView> */}
        </ImageBackground>
        <UploadLoader visible={uploading} progress={progress} />
      </KeyboardAvoidingView>
    </SafeContainerView>
  );
};

export default ChatRoom;
