import React, {useEffect, useState, useCallback} from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  Platform,
} from 'react-native';
import {useLazyQuery, useQuery, useSubscription} from '@apollo/client';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTheme } from '../../../Common/Theme/hooks/useTheme';
import { moderateScale,scale,verticalScale } from '../../../Utils/responsiveUtils';
import {
  useNavigation,
  NavigationProp,
  useFocusEffect,
} from '@react-navigation/native';
import SafeContainerView from '../../../Components/SafeContainer/index';
import {
  GET_USER_ROOMS,
  GET_USER_ROOMS_BY_SEARCH,
  GET_USER_UNREAD_MESSAGE_COUNT,
} from '../AppSync/queries';
import { createStyles } from './ChatRoomStyles';
import {RootStackParamList} from '../../../Common/Routes/StackTypes';
import ChatRoomItem from './ChatRoomItem';
import {SearchIcon, PlusIcon} from '../SVG/ChatRoomSvg';
// import SettingsIcon from '../../../../assets/SVG/Vector.svg'
import LoaderSpin from '../../../Components/Loader/ChatLoader/ChatLoader';
import {ON_CREATE_MESSAGE_USER_ROOM_FILTER} from '../AppSync/Subscriptions';
import {setUnreadMessageCount} from '../../../State/Slices/ChatSlices/TotalUnreadMessageCount';
import {useDispatch} from 'react-redux';
import { showToast } from '../../../Components/AppToaster/AppToaster';
const ChatRoomsListScreen: React.FC = () => {
  const [userId, setUserId] = useState<string | null>(null);
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const {colors}=  useTheme()
  const styles = createStyles();  // Styles
  interface ChatRoom {
    unread_message_count: number;
    room_id: string;
    room_details: {
      name?: string;
      lastMessage?: {
        payload?: string;
        created_date?: string;
        message_type?: string;
      };
    };
    last_updated: string;
  }
  const [chatRooms, setChatRooms] = useState<ChatRoom[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const dispatch = useDispatch();
  // Regular rooms query
  const {
    loading,
    error,
    data,
    refetch: refetchUserRooms,
  } = useQuery(GET_USER_ROOMS, {
    variables: {user_id: userId},
    skip: !userId,
  });

  const {
    loading: totalMessageCount,
    data: totalMessagesCount,
    refetch: refetchTotalMessages,
  } = useQuery(GET_USER_UNREAD_MESSAGE_COUNT, {
    variables: {user_id: userId},
    skip: !userId,
  });

  // console.log('totalMessagesCount', totalMessagesCount);

  useEffect(() => {
    if (totalMessagesCount?.getUserUnReadMessageCount?.unread_message_count) {
      dispatch(
        setUnreadMessageCount(
          totalMessagesCount?.getUserUnReadMessageCount?.unread_message_count,
        ),
      );
    }
  }, [totalMessagesCount]);

  const {data: roomSubscriptionData} = useSubscription(
    ON_CREATE_MESSAGE_USER_ROOM_FILTER,
    {
      variables: {user_id: userId},
      onData: () => {
        if (roomSubscriptionData?.onCreateMessageUserRoomFilter) {
          refetchUserRooms();
          // console.log("roomSubscriptionData",roomSubscriptionData)
        }
      },
    },
  );

  // Lazy query for search
  const [getUserRoomsBySearch, {loading: searchLoading, data: searchData}] =
    useLazyQuery(GET_USER_ROOMS_BY_SEARCH);

  // Debounce search function
  const debouncedSearch = useCallback(
    (query: string) => {
      // Only search if query is not empty
      if (query.trim()) {
        // Wrap in setTimeout to create debounce effect
        const timeoutId = setTimeout(() => {
          getUserRoomsBySearch({
            variables: {
              user_id: userId,
              userSearchInput: query.toLowerCase(),
            },
          });
        }, 300);

        // Clean up the timeout to prevent multiple unnecessary calls
        return () => clearTimeout(timeoutId);
      }
    },
    [userId, getUserRoomsBySearch],
  );

  // Effect to handle search query changes
  useEffect(() => {
    const cleanup = debouncedSearch(searchQuery);
    return cleanup;
  }, [searchQuery, debouncedSearch]);

  // Update chat rooms based on search or default query
  useEffect(() => {
    if (searchQuery.trim() && searchData?.getUserRoomsBySearch?.userRooms) {
      // Use search results when there's a search query
      setChatRooms(searchData.getUserRoomsBySearch.userRooms);
    } else if (data?.getUserRooms?.userRooms) {
      // Use default rooms when no search is active
      setChatRooms(data.getUserRooms.userRooms);
    }
  }, [searchQuery, data, searchData]);

  const fetchUserId = useCallback(async () => {
    try {
      const id = await AsyncStorage.getItem('userId');
      setUserId(id);
    } catch (error) {
      console.log('Error fetching user ID:', error);
    }
  }, []);

  useEffect(() => {
    fetchUserId();
  }, [fetchUserId]);

  useFocusEffect(
    useCallback(() => {
      if (userId) {
        refetchUserRooms();
      }
      return () => {
        // cleanup if needed
      };
    }, [userId, refetchUserRooms]),
  );

  const handleCreateRoom = () => {
    // navigation.navigate('CreateRoom');
    // navigation.navigate("AdaptiveCardExample");
    navigation.navigate('ChatStack', {screen: 'CreateRoom'});
  };

  if (loading)
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <LoaderSpin />
      </View>
    );
  if (error) return <Text>Error: {error.message}</Text>;
  
  return (
    <SafeContainerView backgroundColor={colors.background}>
      {/* <TouchableOpacity onPress={()=>{showToast.success("check message")}}><Text>check</Text></TouchableOpacity> */}
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerText}>Recent Chats</Text>
          <TouchableOpacity style={styles.addButton} onPress={handleCreateRoom}>
            <PlusIcon width={scale(14)} height={verticalScale(14)} color="#ffffff" />
          </TouchableOpacity>
        </View>
        <View
          style={[
            styles.searchContainer,
            isFocused && {borderColor: '#3377ff'},
          ]}>
          <SearchIcon height={verticalScale(18)} width={scale(18)} color="#C1C1C1" />
          <TextInput
            style={styles.searchBar}
            placeholder="Search"
            placeholderTextColor="#C1C1C1"
            value={searchQuery}
            onChangeText={setSearchQuery}
            onFocus={() => {
              setIsFocused(true);
            }}
          />
        </View>

        {(searchLoading || loading) && (
          <View
            style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
            <LoaderSpin />
          </View>
        )}

        <FlatList
          data={chatRooms}
          bounces={false}
          keyExtractor={item => item?.room_id}
          renderItem={({item}) => <ChatRoomItem item={item} />}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{paddingBottom: 130}}
          keyboardShouldPersistTaps="always"
          ListEmptyComponent={
            <Text style={styles.emptyText}>
              {searchQuery.trim() ? 'No rooms found' : 'No rooms available'}
            </Text>
          }
        />
      </View>
      
    </SafeContainerView>
  );
};

export default ChatRoomsListScreen;
