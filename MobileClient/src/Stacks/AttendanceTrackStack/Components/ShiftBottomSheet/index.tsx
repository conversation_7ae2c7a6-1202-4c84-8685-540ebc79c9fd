import React, { useEffect, useState, useCallback, useMemo, useRef, memo } from 'react';
import {
  View,
 
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  StyleSheet,
  Modal,
  Dimensions,
  Animated,
  PanResponder,
} from 'react-native';
import { Text } from 'react-native-paper';
import { useAttendanceTracker } from '../../Screens/AttendanceTrackingScreen/AttendanceScreen.hooks';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { scale, verticalScale } from '../../../../Utils/responsiveUtils';
import { moderateScale } from 'react-native-size-matters';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

const ShiftBottomSheet = ({ visible, shift, onClose }) => {
  // if (!shift) return null;

  const { bottom } = useSafeAreaInsets();
  const [activityDetails, setActivityDetails] = useState([]);
  const animatedValue = useRef(new Animated.Value(0)).current;

  const {
    isCheckedIn,
    isOnBreak,
    isLoading,
    handleCheckIn,
    handleCheckOut,
    handleBreakStart,
    handleBreakEnd,
    getStatusText,
    getLocationText,
    todayActivities,
  } = useAttendanceTracker();

  // Format time to display in a readable format
  const formatTime = useCallback((date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }, []);

  // Handle check-in with activity tracking
const onCheckIn = useCallback(async () => {
  try {
    await handleCheckIn();
    setActivityDetails([
      {
        type: 'Check In',
        time: new Date(),
        formattedTime: formatTime(new Date()),
      },
    ]);
  } catch (error) {
    console.error('Failed to check in:', error);
    // Optionally show user feedback here
  }
}, [handleCheckIn, formatTime]);

  // Handle check-out with activity tracking
  const onCheckOut = useCallback(async () => {
    await handleCheckOut();
    setActivityDetails((prev) => [
      ...prev,
      {
        type: 'Check Out',
        time: new Date(),
        formattedTime: formatTime(new Date()),
      },
    ]);
  }, [handleCheckOut, formatTime]);

  // Handle break start with activity tracking
  const onBreakStart = useCallback(() => {
    handleBreakStart();
    setActivityDetails((prev) => [
      ...prev,
      {
        type: 'Break Start',
        time: new Date(),
        formattedTime: formatTime(new Date()),
      },
    ]);
  }, [handleBreakStart, formatTime]);

  // Handle break end with activity tracking
  const onBreakEnd = useCallback(() => {
    handleBreakEnd();
    setActivityDetails((prev) => [
      ...prev,
      {
        type: 'Break End',
        time: new Date(),
        formattedTime: formatTime(new Date()),
      },
    ]);
  }, [handleBreakEnd, formatTime]);

  // Update activities from hook when todayActivities changes
  useEffect(() => {
    if (todayActivities && todayActivities.length > 0) {
      const formattedActivities = todayActivities.map((activity) => ({
        type: activity.type,
        time: activity.time,
        formattedTime: formatTime(activity.time),
      }));
      setActivityDetails(formattedActivities);
    }
  }, [todayActivities, formatTime]);

  // Animation logic
  useEffect(() => {
    if (visible) {
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(animatedValue, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, animatedValue]);

  // Pan responder for swipe down to close
  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      return gestureState.dy > 0 && gestureState.vy > 0;
    },
    onPanResponderMove: (evt, gestureState) => {
      if (gestureState.dy > 0) {
        const progress = Math.min(gestureState.dy / 200, 1);
        animatedValue.setValue(1 - progress);
      }
    },
    onPanResponderRelease: (evt, gestureState) => {
      if (gestureState.dy > 100 || gestureState.vy > 0.5) {
        onClose();
      } else {
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }).start();
      }
    },
  });

  // Render action buttons based on current state
  const renderActionButtons = useCallback(() => {
    if (!isCheckedIn) {
      return (
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#4CAF50' }]}
          onPress={onCheckIn}
          activeOpacity={0.8}
        >
          <Text style={styles.actionButtonText}>Check In</Text>
        </TouchableOpacity>
      );
    }

    if (isOnBreak) {
      return (
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#3377FF' }]}
          onPress={onBreakEnd}
          activeOpacity={0.8}
        >
          <Text style={styles.actionButtonText}>End Break</Text>
        </TouchableOpacity>
      );
    }

    return (
      <View style={styles.buttonsRow}>
        <TouchableOpacity
          style={[styles.actionButton, styles.halfButton, { backgroundColor: '#FF9800' }]}
          onPress={onBreakStart}
          activeOpacity={0.8}
        >
          <Text style={styles.actionButtonText}>Start Break</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.halfButton, { backgroundColor: '#F44336' }]}
          onPress={onCheckOut}
          activeOpacity={0.8}
        >
          <Text style={styles.actionButtonText}>Check Out</Text>
        </TouchableOpacity>
      </View>
    );
  }, [isCheckedIn, isOnBreak, onCheckIn, onBreakEnd, onBreakStart, onCheckOut]);

  const translateY = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [SCREEN_HEIGHT, 0],
  });

  const backdropOpacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 0.5],
  });

  return (
    <View>
      <Modal visible={visible} transparent animationType="none" onRequestClose={onClose}>
        {/* Backdrop */}
        <Animated.View style={[styles.backdrop, { opacity: backdropOpacity }]}>
          <TouchableOpacity style={styles.backdropTouchable} activeOpacity={1} onPress={onClose} />
        </Animated.View>

        {/* Bottom Sheet */}
        <Animated.View
          style={[
            styles.bottomSheet,
            {
              transform: [{ translateY }],
              paddingBottom: bottom,
            },
          ]}
          {...panResponder.panHandlers}
        >
          {/* Handle */}
          <View style={styles.handleContainer}>
            <View style={styles.handle} />
          </View>

          <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false} bounces={false}>
            {/* Shift Info */}
            <View style={styles.shiftInfoContainer}>
              <View style={styles.shiftHeaderContainer}>
                <Text style={styles.shiftTitle} variant="semiBold">
                  {shift?.title}
                </Text>
                <View style={styles.shiftStatusChip}>
                  <Text style={styles.chipText} variant="medium">
                    {shift?.status}
                  </Text>
                </View>
              </View>
              <Text style={styles.shiftTime}>{shift?.time}</Text>
            </View>

            {/* Divider */}
            <View style={styles.divider} />

            {/* Current status */}
            <View style={styles.statusCard}>
              <View style={styles.statusRow}>
                <Text style={styles.statusLabel}>Status:</Text>
                <Text style={styles.statusValue}>{getStatusText()}</Text>
              </View>
              <View style={styles.statusRow}>
                <Text style={styles.statusLabel}>Location:</Text>
                <Text style={styles.statusValue}>{getLocationText()}</Text>
              </View>
            </View>

            {/* Action Buttons */}
            {isLoading ? (
              <ActivityIndicator size="large" color="#3377FF" style={styles.loader} />
            ) : (
              <View style={styles.buttonsContainer}>{renderActionButtons()}</View>
            )}

            {/* Activity Details Section */}
            {activityDetails.length > 0 && (
              <View style={styles.activitySection}>
                <Text style={styles.activityHeader}>Activity Details:</Text>
                <View style={styles.activityList}>
                  {activityDetails.map((activity, index) => (
                    <View key={index} style={styles.activityItem}>
                      <Text style={styles.activityType}>{activity.type}</Text>
                      <Text style={styles.activityTime}>{activity.formattedTime}</Text>
                    </View>
                  ))}
                </View>
              </View>
            )}
          </ScrollView>
        </Animated.View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#000',
  },
  backdropTouchable: {
    flex: 1,
  },
  bottomSheet: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: SCREEN_HEIGHT * 0.8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 25,
  },
  handleContainer: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  handle: {
    width: 40,
    height: 4,
    backgroundColor: '#ddd',
    borderRadius: 2,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  shiftInfoContainer: {
    marginBottom: 20,
  },
  shiftHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  shiftTitle: {
    fontSize: moderateScale(13),
    color: '#333',
  },
  shiftStatusChip: {
    backgroundColor: '#3377FF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  chipText: {
    color: '#fff',
    fontSize: moderateScale(12),
  },
  shiftTime: {
    fontSize: moderateScale(12),
    color: '#666',
  },
  divider: {
    height: 1,
    backgroundColor: '#eee',
    marginBottom: 20,
  },
  statusCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  statusLabel: {
    fontSize: 14,
    color: '#666',
  },
  statusValue: {
    fontSize: moderateScale(12),
    color: '#333',
    fontWeight: '600',
  },
  loader: {
    marginVertical: 20,
  },
  buttonsContainer: {
    marginBottom: 20,
  },
  buttonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  actionButton: {
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  halfButton: {
    flex: 1,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  activitySection: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  activityHeader: {
    fontSize: 16,
    color: '#333',
    marginBottom: 12,
  },
  activityList: {
    gap: 8,
  },
  activityItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  activityType: {
    fontSize: 14,
    color: '#333',
  },
  activityTime: {
    fontSize: 14,
    color: '#666',
  },
});

export default memo(ShiftBottomSheet);
