import React, { useState } from 'react';
import { View, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { Surface, Divider, TextInput, Button, Appbar, Text, useTheme, TouchableRipple } from 'react-native-paper';
import { Calendar, Clock } from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';
import {createStyles} from './styles';
import { DatePickerModal, TimePickerModal } from 'react-native-paper-dates';
import moment from 'moment';
import {moderateScale} from '../../../../Utils/responsiveUtils';
import {useBottomSheet} from '../../../../Common/Context/BottomSheetContext';

interface Shift {
  id?: string;
  title: string;
  time: string;
  status: string;
}

const shiftOptions = [
  {
    value: 'general',
    shift: {
      id: 'general-shift-1',
      title: 'General shift',
      time: '09:30-18:30',
      status: 'WFO',
    },
  },
  {
    value: 'night',
    shift: {
      id: 'night-shift-1',
      title: 'Night shift',
      time: '18:30-22:30',
      status: 'WFH',
    },
  },
];

const CreateAttendanceScreen: React.FC = () => {
  const theme = useTheme();
  const styles = createStyles();
  const navigation = useNavigation();
  const {showShiftBottomSheet} = useBottomSheet();

  // Custom styles for reduced font sizes
  const reducedFontStyles = {
    sectionTitle: {
      ...styles.sectionTitle,
      fontSize: moderateScale(13),
    },
    shiftTypeText: {
      ...styles.shiftTypeText,
      fontSize: moderateScale(14),
    },
    selectedShiftTypeText: {
      ...styles.selectedShiftTypeText,
      fontSize: moderateScale(14),
    },
    shiftTimeText: {
      ...styles.shiftTimeText,
      fontSize: moderateScale(12),
    },
    statusBadgeText: {
      ...styles.statusBadgeText,
      fontSize: moderateScale(11),
    },
    dateButtonLabel: {
      ...styles.dateButtonLabel,
      fontSize: moderateScale(13),
    },
    timeLabel: {
      ...styles.timeLabel,
      fontSize: moderateScale(12),
    },
    timeButtonLabel: {
      ...styles.timeButtonLabel,
      fontSize: moderateScale(12),
    },
    timeDisplayText: {
      ...styles.timeDisplayText,
      fontSize: moderateScale(12),
    },
    cancelButtonLabel: {
      ...styles.cancelButtonLabel,
      fontSize: moderateScale(13),
    },
    createButtonLabel: {
      ...styles.createButtonLabel,
      fontSize: moderateScale(13),
    },
  };

  // State
  const [reason, setReason] = useState('');
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedShift, setSelectedShift] = useState<Shift | null>(null);
  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [startTime, setStartTime] = useState({hours: 9, minutes: 0});
  const [endTime, setEndTime] = useState({hours: 18, minutes: 0});
  const [startTimePickerVisible, setStartTimePickerVisible] = useState(false);
  const [endTimePickerVisible, setEndTimePickerVisible] = useState(false);

  // Handlers
  const handleShiftSelect = (shift: Shift) => {
    setSelectedShift(shift);
    // Use the context to show the shift bottom sheet
    showShiftBottomSheet({
      id: shift.id || '',
      title: shift.title,
      status: shift.status,
      time: shift.time,
    });
  };

  const handleCreateAttendance = () => {
    if (!selectedShift || !reason.trim()) return;
    // TODO: Add attendance creation logic
    navigation.goBack();
  };
  const formatDate = (date: Date) => moment(date).format('DD MMM YYYY');
  const formatTime = (time: {hours: number; minutes: number}) =>
    moment().set(time).format('hh:mm A');
  const onConfirmDate = ({date}: {date: Date}) => {
    setSelectedDate(date);
    setDatePickerVisible(false);
  };
  const onConfirmStartTime = ({
    hours,
    minutes,
  }: {
    hours: number;
    minutes: number;
  }) => {
    setStartTime({hours, minutes});
    setStartTimePickerVisible(false);
    const startMoment = moment().set({hours, minutes});
    const endMoment = moment().set(endTime);
    if (endMoment.isBefore(startMoment)) {
      setEndTime({hours: Math.min(23, hours + 1), minutes});
    }
  };
  const onConfirmEndTime = ({
    hours,
    minutes,
  }: {
    hours: number;
    minutes: number;
  }) => {
    setEndTime({hours, minutes});
    setEndTimePickerVisible(false);
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title="Create Attendance" />
      </Appbar.Header>
      <ScrollView bounces={false}>
        <Surface style={styles.modalContent}>
          {/* Reason Section */}
          <Text style={reducedFontStyles.sectionTitle}>
            Reason for Attendance
          </Text>
          <TextInput
            mode="outlined"
            value={reason}
            onChangeText={setReason}
            placeholder="Enter your reason..."
            multiline
            numberOfLines={3}
            style={styles.reasonInput}
            accessibilityLabel="Reason for Attendance"
          />

          {/* Shift Section */}
          <Text style={reducedFontStyles.sectionTitle}>Select Shift</Text>
          <View style={styles.shiftSelectionContainer}>
            {shiftOptions.map(option => (
              <TouchableRipple
                key={option.value}
                onPress={() => {
                  setSelectedShift(option.shift);
                }}
                style={[
                  styles.shiftOption,
                  selectedShift?.title === option.shift.title &&
                    styles.selectedShiftOption,
                ]}
                borderless
                accessibilityRole="radio"
                accessibilityState={{
                  checked: selectedShift?.title === option.shift.title,
                }}
                accessibilityLabel={`${option.shift.title}, ${option.shift.time}, ${option.shift.status}`}>
                <View style={styles.shiftOptionContent}>
                  <View style={styles.shiftDetails}>
                    <Text
                      style={[
                        selectedShift?.title === option.shift.title
                          ? reducedFontStyles.selectedShiftTypeText
                          : reducedFontStyles.shiftTypeText,
                      ]}>
                      {option.shift.title}
                    </Text>
                    <Text style={reducedFontStyles.shiftTimeText}>
                      {option.shift.time}
                    </Text>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      gap: 12,
                    }}>
                    <View
                      style={[
                        styles.statusBadge,
                        {
                          backgroundColor:
                            option.shift.status === 'WFO'
                              ? theme.colors.tertiary
                              : theme.colors.primary,
                        },
                      ]}>
                      <Text style={reducedFontStyles.statusBadgeText}>
                        {option.shift.status}
                      </Text>
                    </View>
                    <View
                      style={[
                        styles.radioIcon,
                        selectedShift?.title === option.shift.title &&
                          styles.selectedRadioIcon,
                      ]}>
                      {selectedShift?.title === option.shift.title && (
                        <View style={styles.radioIconInner} />
                      )}
                    </View>
                  </View>
                </View>
              </TouchableRipple>
            ))}
          </View>

          {/* Date Section */}
          <Text style={reducedFontStyles.sectionTitle}>Select Date</Text>
          <Button
            mode="outlined"
            icon={() => (
              <Calendar size={moderateScale(18)} color={theme.colors.primary} />
            )}
            onPress={() => setDatePickerVisible(true)}
            style={styles.dateButton}
            labelStyle={reducedFontStyles.dateButtonLabel}
            accessibilityLabel="Select Date">
            {formatDate(selectedDate)}
          </Button>
          <DatePickerModal
            locale="en"
            mode="single"
            visible={datePickerVisible}
            onDismiss={() => setDatePickerVisible(false)}
            date={selectedDate}
            onConfirm={onConfirmDate}
            saveLabel="Confirm"
            label="Select date"
            animationType="slide"
            presentationStyle="pageSheet"
            startYear={moment().year() - 1}
            endYear={moment().year() + 1}
          />

          {/* Time Section */}
          <Text style={reducedFontStyles.sectionTitle}>Time Range</Text>
          <View style={styles.timeContainer}>
            <View style={styles.timeRow}>
              <Text style={reducedFontStyles.timeLabel}>Start Time:</Text>
              <Button
                mode="outlined"
                icon={() => (
                  <Clock
                    size={moderateScale(14)}
                    color={theme.colors.primary}
                  />
                )}
                onPress={() => setStartTimePickerVisible(true)}
                style={styles.timeButton}
                contentStyle={styles.timeButtonContent}
                labelStyle={reducedFontStyles.timeButtonLabel}
                accessibilityLabel="Select Start Time">
                {formatTime(startTime)}
              </Button>
            </View>
            <View style={styles.timeRow}>
              <Text style={reducedFontStyles.timeLabel}>End Time:</Text>
              <Button
                mode="outlined"
                icon={() => (
                  <Clock
                    size={moderateScale(14)}
                    color={theme.colors.primary}
                  />
                )}
                onPress={() => setEndTimePickerVisible(true)}
                style={styles.timeButton}
                contentStyle={styles.timeButtonContent}
                labelStyle={reducedFontStyles.timeButtonLabel}
                accessibilityLabel="Select End Time">
                {formatTime(endTime)}
              </Button>
            </View>
            <View style={styles.timeDisplay}>
              <View style={styles.timeDisplayItem}>
                <Clock size={moderateScale(14)} color={theme.colors.primary} />
                <Text style={reducedFontStyles.timeDisplayText}>
                  {formatTime(startTime)} - {formatTime(endTime)}
                </Text>
              </View>
            </View>
          </View>
          <TimePickerModal
            visible={startTimePickerVisible}
            onDismiss={() => setStartTimePickerVisible(false)}
            onConfirm={onConfirmStartTime}
            hours={startTime.hours}
            minutes={startTime.minutes}
            label="Select start time"
            cancelLabel="Cancel"
            confirmLabel="Confirm"
            animationType="fade"
            locale="en"
          />
          <TimePickerModal
            visible={endTimePickerVisible}
            onDismiss={() => setEndTimePickerVisible(false)}
            onConfirm={onConfirmEndTime}
            hours={endTime.hours}
            minutes={endTime.minutes}
            label="Select end time"
            cancelLabel="Cancel"
            confirmLabel="Confirm"
            animationType="fade"
            locale="en"
          />

          <Divider style={styles.divider} />

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <Button
              mode="outlined"
              onPress={() => navigation.goBack()}
              style={[styles.actionButton, styles.cancelButton]}
              labelStyle={reducedFontStyles.cancelButtonLabel}
              accessibilityLabel="Cancel">
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleCreateAttendance}
              style={[styles.actionButton, styles.createButton]}
              labelStyle={reducedFontStyles.createButtonLabel}
              disabled={!selectedShift || !reason.trim()}
              accessibilityLabel="Create Attendance">
              Create Attendance
            </Button>
          </View>
        </Surface>
        {/* Bottom Sheet is now rendered at App root level */}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default CreateAttendanceScreen;
