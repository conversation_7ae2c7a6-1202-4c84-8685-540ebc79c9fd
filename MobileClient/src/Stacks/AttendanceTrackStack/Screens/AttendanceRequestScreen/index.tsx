import React from 'react';
import {View, ScrollView, Alert} from 'react-native';
import {
  Surface,
  Text,
  Card,
  TextInput,
  Button,
  IconButton,
  useTheme,
} from 'react-native-paper';
import {ChevronLeft} from 'lucide-react-native';
import {useNavigation} from '@react-navigation/native';
import {createStyles} from './styles';
import {scale} from '../../../../Utils/responsiveUtils';
import {LeaveTypeSelector} from './components/LeaveTypeSelector';
import {DateRangeSelector} from './components/DateRangeSelector';
import {LeaveBalance} from './components/LeaveBalance';
import {useAttendanceRequest} from './hooks/useAttendanceRequest';

const AttendanceRequestScreen = () => {
  const navigation = useNavigation();
  const theme = useTheme();
  const styles = createStyles();
  const {
    leaveType,
    setLeaveType,
    startDate,
    endDate,
    reason,
    setReason,
    handleStartDateSelect,
    handleEndDateSelect,
    isFormValid,
    createRequest,
  } = useAttendanceRequest();

  const handleSubmit = () => {
    if (!isFormValid()) {
      Alert.alert('Error', 'Please fill all fields');
      return;
    }

    const request = createRequest();
    console.log('Submitting request:', request);
    Alert.alert('Success', 'Request submitted successfully!');
    navigation.goBack();
  };

  // Mock leave balance data - in real app, this would come from an API
  const leaveBalance = {
    sickLeave: 12,
    vacation: 15,
    personal: 5,
    wfh: 8,
  };

  return (
    <Surface style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <IconButton
          icon={() => (
            <ChevronLeft size={scale(24)} color={theme.colors.onSurface} />
          )}
          onPress={() => navigation.goBack()}
        />
        <Text variant="titleLarge" style={styles.headerTitle}>
          Request Time Off
        </Text>
        <View style={{width: scale(40)}} />
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent} bounces={false}>
          
        <Card style={styles.card}>
          <Card.Content>
            {/* Leave Type Selector */}
            <LeaveTypeSelector
              selectedType={leaveType}
              onTypeSelect={setLeaveType}
            />

            {/* Date Range Selector */}
            <DateRangeSelector
              startDate={startDate}
              endDate={endDate}
              onStartDateSelect={handleStartDateSelect}
              onEndDateSelect={handleEndDateSelect}
            />

            {/* Reason Input */}
            <Text
              variant="titleMedium"
              style={[styles.sectionTitle, {marginTop: scale(20)}]}>
              Reason
            </Text>
            <TextInput
              mode="outlined"
              value={reason}
              onChangeText={setReason}
              placeholder="Enter reason for leave"
              multiline
              numberOfLines={4}
              style={styles.reasonInput}
            />

            {/* Submit Button */}
            <Button
              mode="contained"
              onPress={handleSubmit}
              style={styles.submitButton}
              disabled={!isFormValid()}
              labelStyle={{fontFamily: 'Montserrat-Medium'}}>
              Submit Request
            </Button>
          </Card.Content>
        </Card>

        {/* Leave Balance */}
        <LeaveBalance balance={leaveBalance} />
      </ScrollView>
    </Surface>
  );
};

export default AttendanceRequestScreen;
