import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text, Button, Surface, SegmentedButtons, Menu, Chip, useTheme } from 'react-native-paper';
import { Calendar, ChevronDown } from 'lucide-react-native';
import { scale, moderateScale } from '../../../../../Utils/responsiveUtils';
import moment from 'moment';
import { FilterOptions } from '../types';

interface FilterSectionProps {
  filterOptions: FilterOptions;
  onFilterChange: (options: Partial<FilterOptions>) => void;
  onResetFilters: () => void;
  onApplyFilters: () => void;
}

interface FilterChipsProps {
  filterOptions: FilterOptions;
  onFilterChange: (options: Partial<FilterOptions>) => void;
  onChipPress: () => void;
}

// Helper function to generate date options for the last 30 days and next 30 days
const generateDateOptions = () => {
  const dates = [];

  // Past 30 days
  for (let i = 30; i >= 0; i--) {
    const date = moment().subtract(i, 'days');
    dates.push(date);
  }

  // Future 30 days
  for (let i = 1; i <= 30; i++) {
    const date = moment().add(i, 'days');
    dates.push(date);
  }

  return dates;
};

const dateOptions = generateDateOptions();

// Format date for display
const formatDate = (date: moment.Moment | null) => {
  if (!date) return 'Select Date';
  return date.format('DD MMM YYYY');
};

// Format date for chip display
const formatDateForChip = (date: moment.Moment) => {
  return date.format('DD MMM');
};

const FilterSection: React.FC<FilterSectionProps> & { 
  Chips: React.FC<FilterChipsProps> 
} = ({ 
  filterOptions, 
  onFilterChange, 
  onResetFilters, 
  onApplyFilters 
}) => {
  const theme = useTheme();

  const [showStartDateMenu, setShowStartDateMenu] = useState(false);
  const [showEndDateMenu, setShowEndDateMenu] = useState(false);

  const styles = StyleSheet.create({
    filterContainer: {
      padding: scale(16),
      backgroundColor: theme.colors.surfaceVariant,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    filterTitle: {
      fontFamily: 'Montserrat-SemiBold',
      fontSize: moderateScale(15),
      color: theme.colors.onSurfaceVariant,
      marginBottom: scale(16),
    },
    filterRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: scale(16),
    },
    filterLabel: {
      width: '25%',
      fontFamily: 'Montserrat-Medium',
      fontSize: moderateScale(13),
      color: theme.colors.onSurfaceVariant,
    },
    dateFilterContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
    },
    dateFilterButton: {
      flex: 1,
      height: moderateScale(40),
    },
    dateButtonLabel: {
      fontFamily: 'Montserrat-Regular',
      fontSize: moderateScale(12),
    },
    dateMenu: {
      maxHeight: 300,
      width: '45%',
    },
    dateMenuScroll: {
      maxHeight: 250,
    },
    dateMenuItem: {
      fontFamily: 'Montserrat-Regular',
      fontSize: moderateScale(13),
    },
    segmentedButtons: {
      flex: 1,
    },
    filterActions: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      marginTop: scale(16),
    },
    actionButton: {
      borderRadius: moderateScale(8),
    },
    actionButtonLabel: {
      fontFamily: 'Montserrat-Medium',
      fontSize: moderateScale(12),
    }
  });

  // Handle date selection
  const handleStartDateSelect = (date: moment.Moment) => {
    onFilterChange({ startDate: date });
    setShowStartDateMenu(false);

    // If end date is before start date, reset end date
    if (filterOptions.endDate && date.isAfter(filterOptions.endDate)) {
      onFilterChange({ endDate: null });
    }
  };

  const handleEndDateSelect = (date: moment.Moment) => {
    onFilterChange({ endDate: date });
    setShowEndDateMenu(false);
  };

  return (
    <Surface style={styles.filterContainer} elevation={1}>
      <Text style={styles.filterTitle}>
        Filter Attendance
      </Text>

      {/* Date Range */}
      <View style={styles.filterRow}>
        <Text style={styles.filterLabel}>Date Range:</Text>
        <View style={styles.dateFilterContainer}>
          <Menu
            visible={showStartDateMenu}
            onDismiss={() => setShowStartDateMenu(false)}
            anchor={
              <Button
                mode="outlined"
                onPress={() => setShowStartDateMenu(true)}
                style={styles.dateFilterButton}
                contentStyle={{ justifyContent: 'flex-start' }}
                icon={() => <Calendar size={moderateScale(14)} color={theme.colors.primary} />}
                right={() => (
                  <ChevronDown
                    size={moderateScale(12)}
                    color={theme.colors.primary}
                  />
                )}
                labelStyle={styles.dateButtonLabel}
              >
                {formatDate(filterOptions.startDate)}
              </Button>
            }
            style={styles.dateMenu}
          >
            <ScrollView style={styles.dateMenuScroll} bounces={false}>
              {dateOptions.map((date, index) => (
                <Menu.Item
                  key={index}
                  onPress={() => handleStartDateSelect(date)}
                  title={date.format('DD MMM YYYY')}
                  titleStyle={[
                    styles.dateMenuItem,
                    filterOptions.startDate && date.isSame(filterOptions.startDate, 'day') ? { color: theme.colors.primary } : {}
                  ]}
                />
              ))}
            </ScrollView>
          </Menu>

          <Text style={{ marginHorizontal: 4, fontSize: moderateScale(12), fontFamily: 'Montserrat-Regular' }}>to</Text>

          <Menu
            visible={showEndDateMenu}
            onDismiss={() => setShowEndDateMenu(false)}
            anchor={
              <Button
                mode="outlined"
                onPress={() => setShowEndDateMenu(true)}
                style={styles.dateFilterButton}
                contentStyle={{ justifyContent: 'flex-start' }}
                icon={() => <Calendar size={moderateScale(14)} color={theme.colors.primary} />}
                right={() => (
                  <ChevronDown
                    size={moderateScale(12)}
                    color={theme.colors.primary}
                  />
                )}
                disabled={!filterOptions.startDate}
                labelStyle={styles.dateButtonLabel}
              >
                {formatDate(filterOptions.endDate)}
              </Button>
            }
            style={styles.dateMenu}
          >
            <ScrollView style={styles.dateMenuScroll} bounces={false}>
              {dateOptions
                .filter(date => !filterOptions.startDate || date.isSameOrAfter(filterOptions.startDate, 'day'))
                .map((date, index) => (
                  <Menu.Item
                    key={index}
                    onPress={() => handleEndDateSelect(date)}
                    title={date.format('DD MMM YYYY')}
                    titleStyle={[
                      styles.dateMenuItem,
                      filterOptions.endDate && date.isSame(filterOptions.endDate, 'day') ? { color: theme.colors.primary } : {}
                    ]}
                  />
                ))}
            </ScrollView>
          </Menu>
        </View>
      </View>

      {/* Status Filter */}
      <View style={styles.filterRow}>
        <Text style={styles.filterLabel}>Status:</Text>
        <SegmentedButtons
          value={filterOptions.filterValue}
          onValueChange={(value) => onFilterChange({ filterValue: value as 'all' | 'wfo' | 'wfh' })}
          buttons={[
            { value: 'all', label: 'All' },
            { value: 'wfo', label: 'WFO' },
            { value: 'wfh', label: 'WFH' },
          ]}
          style={styles.segmentedButtons}
        />
      </View>

      {/* Filter Actions */}
      <View style={styles.filterActions}>
        <Button 
          mode="text" 
          onPress={onResetFilters}
          textColor={theme.colors.error}
          style={styles.actionButton}
          labelStyle={styles.actionButtonLabel}
        >
          Reset
        </Button>
        <Button
          mode="contained"
          onPress={onApplyFilters}
          style={{ 
            marginLeft: scale(8),
            ...styles.actionButton
          }}
          labelStyle={styles.actionButtonLabel}
        >
          Apply
        </Button>
      </View>
    </Surface>
  );
};

// Filter Chips Subcomponent
const FilterChips: React.FC<FilterChipsProps> = ({ 
  filterOptions, 
  onFilterChange, 
  onChipPress
}) => {
  const theme = useTheme();
  
  const styles = StyleSheet.create({
    filterChipsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      paddingHorizontal: scale(16),
      paddingTop: scale(8),
      paddingBottom: scale(4),
    },
    filterChip: {
      marginRight: scale(8),
      marginBottom: scale(8),
    },
    filterChipText: {
      fontFamily: 'Montserrat-Regular',
      fontSize: moderateScale(12),
    },
  });

  return (
    <View style={styles.filterChipsContainer}>
      {filterOptions.startDate && filterOptions.endDate && (
        <Chip 
          icon="calendar" 
          style={styles.filterChip}
          textStyle={styles.filterChipText}
          onPress={onChipPress}
          closeIcon="close"
          onClose={() => { onFilterChange({ startDate: null, endDate: null }); }}
        >
          {formatDateForChip(filterOptions.startDate)} - {formatDateForChip(filterOptions.endDate)}
        </Chip>
      )}
      
      {filterOptions.filterValue !== 'all' && (
        <Chip 
          icon="account-check"
          style={styles.filterChip}
          textStyle={styles.filterChipText}
          onPress={onChipPress}
          closeIcon="close"
          onClose={() => onFilterChange({ filterValue: 'all' })}
        >
          {filterOptions.filterValue === 'wfo' ? 'Office' : 'Remote'}
        </Chip>
      )}
    </View>
  );
};

// Attach Chips as a static property of FilterSection
FilterSection.Chips = FilterChips;

export default FilterSection;