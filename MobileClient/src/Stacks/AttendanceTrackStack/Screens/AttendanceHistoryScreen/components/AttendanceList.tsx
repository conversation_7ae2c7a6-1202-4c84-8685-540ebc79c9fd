import React, { useMemo } from 'react';
import { View, FlatList, StyleSheet } from 'react-native';
import { Text, Card, Button, FAB, useTheme } from 'react-native-paper';
import { Calendar, Clock, MapPin, AlertTriangle, Download } from 'lucide-react-native';
import { moderateScale, scale, verticalScale } from '../../../../../Utils/responsiveUtils';
import { AttendanceRecord } from '../types';

interface AttendanceListProps {
  data: AttendanceRecord[];
  onResetFilters: () => void;
  hasActiveFilters: boolean;
}

const AttendanceList: React.FC<AttendanceListProps> = ({
  data,
  onResetFilters,
  hasActiveFilters
}) => {
  const theme = useTheme();

  const styles = StyleSheet.create({
    listContent: {
      padding: scale(16),
    },
    emptyContainer: {
      alignItems: 'center', 
      padding: scale(24)
    },
    emptyText: {
      fontFamily: 'Montserrat-Medium',
      fontSize: moderateScale(14),
      textAlign: 'center',
      marginTop: scale(24),
      color: theme.colors.onSurfaceVariant,
    },
    resetButton: {
      marginTop: scale(16),
    },
    resetButtonLabel: {
      fontFamily: 'Montserrat-Medium',
      fontSize: moderateScale(12),
    },
    // Card styles
    logCard: {
      marginBottom: scale(16),
      borderRadius: scale(8),
      overflow: 'hidden',
      elevation: 2,
    },
    leftBorder: {
      position: 'absolute',
      left: 0,
      top: 0,
      bottom: 0,
      width: moderateScale(4),
      borderTopLeftRadius: scale(8),
      borderBottomLeftRadius: scale(8),
    },
    cardHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: scale(16),
      paddingVertical: scale(8),
      borderBottomWidth: 1,
    },
    dateContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    dateIcon: {
      marginRight: scale(4),
    },
    dateText: {
      fontFamily: 'Montserrat-Medium',
      fontSize: moderateScale(13),
    },
    cardContent: {
      padding: scale(16),
    },
    shiftInfoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: scale(16),
    },
    shiftInfoContainer: {
      flex: 1,
    },
    shiftTitle: {
      fontFamily: 'Montserrat-SemiBold',
      fontSize: moderateScale(14),
      marginBottom: scale(4),
    },
    shiftTime: {
      fontFamily: 'Montserrat-Regular',
      fontSize: moderateScale(12),
      color: theme.colors.onSurfaceVariant,
    },
    statusBadge: {
      paddingHorizontal: scale(16),
      paddingVertical: scale(4),
      borderRadius: moderateScale(20),
      alignSelf: 'flex-start',
    },
    statusText: {
      fontFamily: 'Montserrat-SemiBold',
      fontSize: moderateScale(11),
      color: '#FFFFFF',
    },
    holidayBadge: {
      width: scale(110),
      height: verticalScale(30),
      backgroundColor: '#DC3545',
      borderRadius: moderateScale(4),
      justifyContent: 'center',
      alignItems: 'center',
    },
    holidayText: {
      color: '#FFFFFF',
      fontSize: moderateScale(11),
      fontFamily: 'Montserrat-Medium',
    },
    detailsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: scale(8),
    },
    detailItem: {
      width: '50%',
      marginBottom: scale(16),
    },
    detailLabel: {
      fontFamily: 'Montserrat-Regular',
      fontSize: moderateScale(11),
      color: theme.colors.onSurfaceVariant,
      marginBottom: scale(4),
    },
    detailValue: {
      fontFamily: 'Montserrat-Medium',
      fontSize: moderateScale(13),
      color: theme.colors.onSurface,
    },
    fabButton: {
      position: 'absolute',
      right: scale(16),
      bottom: scale(16),
      backgroundColor: theme.colors.primary,
    },
  });

  // Function to get shift color based on shift type
  const getShiftColor = (shiftType: string) => {
    if (shiftType.toLowerCase().includes('general')) {
      return theme.colors.tertiary || '#4CAF50';
    } else if (shiftType.toLowerCase().includes('night')) {
      return theme.colors.primary || '#3377FF';
    } else {
      return theme.colors.secondary || '#FF9800';
    }
  };

  // Function to get status color based on status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'WFO':
        return theme.colors.tertiary || '#4CAF50';
      case 'WFH':
        return theme.colors.primary || '#3377FF';
      default:
        return theme.colors.primary || '#3377FF';
    }
  };

  // Render empty component
  const renderEmptyComponent = useMemo(() => {
    return (
      <View style={styles.emptyContainer}>
        <AlertTriangle 
          size={moderateScale(32)} 
          color={theme.colors.onSurfaceVariant} 
          style={{ opacity: 0.6, marginBottom: scale(16) }} 
        />
        <Text style={styles.emptyText}>No attendance records found</Text>
        <Button 
          mode="contained-tonal" 
          onPress={onResetFilters}
          style={styles.resetButton}
          labelStyle={styles.resetButtonLabel}
          disabled={!hasActiveFilters}
        >
          Reset Filters
        </Button>
      </View>
    );
  }, [hasActiveFilters, onResetFilters, styles, theme.colors.onSurfaceVariant]);

  // Render attendance record item
  const renderItem = ({ item }: { item: AttendanceRecord }) => (
    <Card style={styles.logCard}>
      {/* Left border indicator */}
      <View
        style={[
          styles.leftBorder,
          { backgroundColor: getShiftColor(item.shift) }
        ]}
      />

      {/* Card Header with Date */}
      <View style={[
        styles.cardHeader,
        {
          backgroundColor: theme.colors.surfaceVariant,
          borderBottomColor: theme.colors.outline
        }
      ]}>
        <View style={styles.dateContainer}>
          <Calendar
            size={moderateScale(14)}
            color={theme.colors.onSurface}
            style={styles.dateIcon}
          />
          <Text style={styles.dateText}>{item.date}</Text>
        </View>

        {/* Status Badge */}
        <View
          style={[
            styles.statusBadge,
            { backgroundColor: getStatusColor(item.status) }
          ]}
        >
          <Text style={styles.statusText}>{item.status}</Text>
        </View>
      </View>

      {/* Card Content */}
      <View style={styles.cardContent}>
        {/* Shift Info Row */}
        <View style={styles.shiftInfoRow}>
          <View style={styles.shiftInfoContainer}>
            <Text
              style={[
                styles.shiftTitle,
                { color: getShiftColor(item.shift) }
              ]}
            >
              {item.shift}
            </Text>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Clock
                size={moderateScale(12)}
                color={theme.colors.onSurfaceVariant}
                style={{ marginRight: 4 }}
              />
              <Text style={styles.shiftTime}>{item.time}</Text>
            </View>
          </View>

          {/* Holiday Badge if applicable */}
          {item?.isHoliday && (
            <View style={styles.holidayBadge}>
              <Text style={styles.holidayText}>Public Holiday</Text>
            </View>
          )}
        </View>

        {/* Details Grid */}
        <View style={styles.detailsGrid}>
          {/* Total Hours */}
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Total Hours</Text>
            <Text style={styles.detailValue}>{item.total}</Text>
          </View>

          {/* Overtime */}
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Overtime</Text>
            <Text style={styles.detailValue}>{item.overtime}</Text>
          </View>

          {/* Breaks */}
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Breaks</Text>
            <Text style={styles.detailValue}>{item.breaks}</Text>
          </View>

          {/* Location */}
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Location</Text>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <MapPin
                size={moderateScale(12)}
                color={theme.colors.onSurface}
                style={{ marginRight: 4 }}
              />
              <Text style={styles.detailValue}>{item.location}</Text>
            </View>
          </View>
        </View>
      </View>
    </Card>
  );

  return (
    <>
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={renderEmptyComponent}
        bounces={false}
      />

      {/* FAB for downloading report */}
      {data.length > 0 && (
        <FAB
          icon={props => <Download {...props} />}
          style={styles.fabButton}
          onPress={() => {
            // Handle download or export attendance report
            console.log('Download attendance report');
          }}
          color={theme.colors.surface}
          size="small"
          accessibilityLabel="Download attendance report"
        />
      )}
    </>
  );
};

export default AttendanceList;