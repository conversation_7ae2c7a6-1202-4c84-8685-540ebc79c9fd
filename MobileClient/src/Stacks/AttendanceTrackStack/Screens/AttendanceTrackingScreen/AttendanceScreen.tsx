import React, {useCallback, useMemo} from 'react';
import {ScrollView, View} from 'react-native';
import {Button, FAB, Portal, useTheme} from 'react-native-paper';
import {useNavigation} from '@react-navigation/native';
import {NavigationProp} from '@react-navigation/native';
import moment, {Moment} from 'moment';

// Components
import SafeContainerView from '../../../../Components/SafeContainer';
import {
  Head<PERSON>,

  AttendanceCalendar,
  ShiftList as Shifts,
  SummaryView as Summary,
  LogsView as Logs,
  ShiftBottomSheet,
} from '../../Components';
import Tabs from '../../../../Components/Tabs';

// Context and Hooks
import {useBottomSheet} from '../../../../Common/Context/BottomSheetContext';
import createStyles from './AttendanceScreen.styles';
import {useAttendance} from './AttendanceScreen.hooks';
import {scale} from '../../../../Utils/responsiveUtils';
import AppHeader from '../../../../Components/AppHeader';

type AttendanceStackParamList = {
  AttendanceScreen: undefined;
  AttendanceHistory: undefined;
  CreateAttendance: undefined;
  AttendanceRequest: undefined;
};

/**
 * AttendanceScreen component for managing employee attendance and tracking
 */
const AttendanceScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp<AttendanceStackParamList>>();
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const {showShiftBottomSheet} = useBottomSheet();

  const {
    selectedTab,
    setSelectedTab,
    selectedDate,
    setCurrentMonth,
    selectedShift,
    calendarRef,
    tabs,
    summaryData,
    logsData,
    handleDateSelect,
    handleShiftSelect: originalHandleShiftSelect,
    closeModal,
  } = useAttendance();

  // Override shift selection to use the global bottom sheet
  const handleShiftSelect = useCallback(
    shift => {
      originalHandleShiftSelect(shift);
      if (shift) {
        showShiftBottomSheet({
          id: shift.id || '',
          title: shift.title || '',
          status: shift.status || '',
          time: shift.time || '',
        });
      }
    },
    [originalHandleShiftSelect, showShiftBottomSheet],
  );

  // Compute enhanced styles with proper component spacing
  const enhancedStyles = useMemo(
    () => ({
      ...styles,
      scrollContent: {
        ...styles.scrollContent,
        paddingBottom: scale(80), // Add enough space for the FAB and extra padding
      },
      actionButtonsContainer: {
        ...styles.actionButtonsContainer,
        marginBottom: scale(24), // Ensure button is not covered by the FAB
      },
    }),
    [styles],
  );

  // Memoized callback for navigating to attendance history
  const navigateToHistory = useCallback(() => {
    navigation.navigate('AttendanceHistory');
  }, [navigation]);

  // Memoized callback for navigating to create attendance
  const navigateToCreateAttendance = useCallback(() => {
    navigation.navigate('CreateAttendance');
  }, [navigation]);

  // Memoized callback for handling week scroll end
  const handleWeekScrollEnd = useCallback(
    (start: Moment) => {
      setCurrentMonth(start);
    },
    [setCurrentMonth],
  );

  // Render the tab content based on the selected tab
  const renderTabContent = useMemo(() => {
    switch (selectedTab) {
      case 'My Attendance':
        return (
          <View style={styles.attendanceContent}>
            <AttendanceCalendar
              selectedDate={selectedDate.format('YYYY-MM-DD')}
              calendarRef={calendarRef}
              onDateSelect={handleDateSelect}
              onWeekScrollEnd={handleWeekScrollEnd}
            />
            <Shifts onShiftSelect={handleShiftSelect} />
          </View>
        );

      case 'Summary':
        return (
          <View style={styles.tabContent}>
            <Summary data={summaryData} />
          </View>
        );

      case 'Logs':
        return (
          <View style={styles.tabContent}>
            <Logs
              data={logsData.map(item => {
                if (item.isHoliday) {
                  return {
                    ...item,
                    holidayStyle: {
                      badgeColor: theme.colors.error,
                      textColor: theme.colors.surface,
                      fontWeight: '500',
                    },
                  };
                }
                return item;
              })}
            />
            <View style={enhancedStyles.actionButtonsContainer}>
              <Button
                mode="contained"
                onPress={navigateToHistory}
                style={styles.actionButton}>
                View Full History
              </Button>
            </View>
          </View>
        );

      default:
        return null;
    }
  }, [
    selectedTab,
    selectedDate,
    calendarRef,
    handleDateSelect,
    handleWeekScrollEnd,
    summaryData,
    logsData,
    styles,
    enhancedStyles,
    theme.colors,
    navigateToHistory,
    handleShiftSelect,
  ]);

  return (
    <>
      <SafeContainerView backgroundColor={theme.colors.background}>
        <AppHeader title='Attendance' customHeaderStyles={{paddingBottom:0}}/>

        <Tabs
          tabs={tabs}
          activeTab={selectedTab}
          onTabPress={setSelectedTab}
         
        />
        

        <View style={styles.contentContainer}>
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={enhancedStyles.scrollContent} bounces={false}>
            {renderTabContent}
          </ScrollView>
        </View>

        {/* FAB for creating attendance */}
        {/* <FAB
          icon="plus"
          style={styles.fab}
          onPress={navigateToCreateAttendance}
          label="Create Attendance"
          color={theme.colors.surface}
        /> */}
      </SafeContainerView>

     

      {/* Note: ShiftBottomSheet is now rendered at App root level */}
     
    </>
  );
};

export default AttendanceScreen;
