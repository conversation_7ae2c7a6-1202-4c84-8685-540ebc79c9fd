import { useSelector } from 'react-redux';
import { generateUniqueId } from '../../../Utils/util';
import { EnquiryFormData } from '../types';
import { getDimensionUnit, getWeightUnit, getPerUnitWeight, getVolumeUnit } from './unitUtils';

export const getInitialFormData = (unitSystem : string): EnquiryFormData => ({
  type: 'New',
  unitSystem: 'SI',

  // Mode & Direction
  selectedMode: 'sea',
  selectedModeId: null,
  selectedDirection: 'Import',
  selectedDirectionId: null,
  countryOfOriginName : '',
  countryOfOriginId : '',
  // Locations
  fromLocation: '',
  from_city_id: null,
  from_port_id: null,
  toLocation: '',
  to_city_id: null,
  to_port_id: null,
  fromAddress: null,
  toAddress: null,

  // Transshipment
  is_transshipment: false,
  transitLocation: '',
  transshipment_port_id: null,

  // Remarks / Border Charges
  remarks: '',
  is_border_charges: false,
  selectedBorderCharges: null,
  selectedBorderChargesId: null,

  // HS Codes - Main Category
  category_hs_code: '',
  hs_code_category_id: null,
  hs_code_category_name: '',
  isMainCategory: false,
  subCodes: [],

  // HS Codes - Sub Category
  hs_code_sub_category_id: null,
  hs_code_sub_category_name: '',
  sub_category_hs_code: '',

  // Cargo Details
  cargoType: 'general cargo',
  cargo_type_id: '5',
  imoClass: '',
  imo_class_id: null,
  imo_sub_class_id: null,
  imo_sub_class_name: '',
  unNumber: '',
  uploadedDocs: [],
  srNumber: '',
  Rnd: '',
  Rfl: '',

  // Perishable Cargo
  perishableTemperature: '',
  perishableTemperatureUnitLabel: '°C',
  perishableTemperatureUnitValue: '',
  perishableHumidity: '',

  // Temperature Controlled Cargo
  tempRegimeOfTemperatureCargo: '',
  temperatureCargoUnitLabel: '°C',
  temperatureCargoUnitValue: '',
  humidityOfTemperatureCargo: '',

  // Oversized Cargo
  oversizedCargoDimensions: { length: '', breadth: '', height: '' },
  OversizeddimensionUnitLabel: '',
  OversizeddimensionUnitValue: 'cm',

  // Incoterms
  incoterms_id: null,
  incoterm_name: '',
  incoterm_code: '',
  exWorksAddress : '',
  // Carrier Info
  selectedSeaCarrierObj: {},
  selectedSeaCarrierId: null,
  selectedSeaCarrierName: '',
  selectedAirlineObj: {},
  selectedAirlineId: null,
  selectedAirlineName: '',

  // Dates & Duration
  readyToLoadDate: '',
  transitDays: '',
  freeDays: '',

  // Services
  selectedServiceIds: [],
  selectedServicesObj: {},

  // Cargo Value & Currency
  cargoValueAmount: '0',
  currency_id: null,
  currency_name: '',
  currency_symbol: '',
  currency_code: '',
  exchange_rate: '',

  // Additional Info
  additionalInfo: '',

  // Transportation
  transportationBy: '',
  transportationTypeid: null,
  transportationByCode: '',

  // UI / State Extras
  shipmentType: '',
  isShowOrganization: false,
  selectedProspectOrgIdValue: '',

  enquiryDocuments: [],
  detailData: undefined, // used for update
  modes: [],

  containerOptions: [
    //used
    {
      fcl_container_option_id: generateUniqueId(),
      name_fcl_option: 'Option 1',
      description: '',
      fcl_enquiry_items: [
        {
          id: generateUniqueId(),
          containerType: '',
          containerTypeId: '',
          containerQuantity: '1',
          weight: '1',
          perUnitWeight: '0',
        },
      ],
    },
  ],

  lclOptions: [
    //used
    {
      lcl_option_id: generateUniqueId(),
      name_lcl_option: 'Option 1',
      description: '',
      lcl_enquiry_items: [
        {
          id: generateUniqueId(),
          byUnits: true,
          weight: '',
          volume: '',
          lots: [
            {
              id: generateUniqueId(), // For new items
              dimensions: {
                length: '0',
                width: '0',
                height: '0',
                unit: unitSystem === 'SI' ? 'cm' : 'in',
              },
              quantity: '1',
              weight: '0',
              weightUnit: getWeightUnit(unitSystem),
              perUnitWeight: '0',
              volumeMetricWeight: '0',
              package_type_item_id: null,
              type_package_id: null,
            },
          ],
          weightVolumeEntries: [
            {
              id: generateUniqueId(),
              weight: '',
              volume: '',
              weightUnit: getPerUnitWeight(unitSystem),
              volumeUnit: getVolumeUnit(unitSystem),
              package_type_item_id: null,
              type_package_id: null,
            },
          ],
        },
      ],
    },
  ],

  shipOptions: [
    //used
    {
      bulk_option_id: generateUniqueId(),
      name_bulk_option: 'Option 1',
      description: '',
      bulk_enquiry_items: [
        {
          id: generateUniqueId(),
          shipType: '',
          shipTypeId: '',
          grossWeight: '1',
          loadingRate: '',
          dischargingRate: '',
        },
      ],
    },
  ],

  uldOptions: [
    //used
    {
      uld_option_id: generateUniqueId(),
      name_uld_option: 'Option 1',
      description: '',
      uld_enquiry_items: [
        {
          id: generateUniqueId(),
          uldType: '', // For display
          uldTypeId: '',
          uldQuantity: '1',
          weight: '1',
          perUnitWeight: '1.00',
        },
      ],
    },
  ],

  standardCargoOptions: [
    //used
    {
      standard_cargo_option_id: generateUniqueId(),
      name_standard_cargo_option: 'Option 1',
      description: '',
      standard_cargo_enquiry_items: [
        {
          id: generateUniqueId(),
          byUnits: true,
          weight: '',
          volume: '',
          lots: [
            {
              id: generateUniqueId(),
              dimensions: {
                length: '0',
                width: '0',
                height: '0',
                unit: getDimensionUnit(unitSystem),
              },
              quantity: '1',
              weight: '',
              weightUnit: getWeightUnit(unitSystem),
              perUnitWeight: '0',
              volumeMetricWeight: '0',
              package_type_item_id: null,
              type_package_id: null,
            },
          ],
          weightVolumeEntries: [
            {
              id: generateUniqueId(),
              weight: '',
              volume: '',
              weightUnit: getPerUnitWeight(unitSystem),
              volumeUnit: getVolumeUnit(unitSystem),
              package_type_item_id: null,
              type_package_id: null,
            },
          ],
        },
      ],
    },
  ],

  truckOptions: [
    //used
    {
      ftl_option_id: generateUniqueId(),
      name_ftl_option: 'Option 1',
      description: '',
      ftl_enquiry_items: [
        {
          id: generateUniqueId(),
          truckType: '',
          truckTypeId: '',
          truckQuantity: '1',
          weight: '1',
          perUnitWeight: '1.00',
        },
      ],
    },
  ],

  ltlOptions: [
    //used
    {
      ltl_option_id: generateUniqueId(),
      name_ltl_option: 'Option 1',
      description: '',
      ltl_enquiry_items: [
        {
          id: generateUniqueId(),
          byUnits: true,
          weight: '',
          volume: '',
          lots: [
            {
              id: generateUniqueId(),
              dimensions: {
                length: '0',
                width: '0',
                height: '0',
                unit: getDimensionUnit(unitSystem),
              },

              quantity: '1',
              weight: '0',
              weightUnit: getWeightUnit(unitSystem),
              perUnitWeight: '0',
              volumeMetricWeight: '0',
              package_type_item_id: null,
              type_package_id: null,
            },
          ],
          weightVolumeEntries: [
            {
              id: generateUniqueId(),
              weight: '',
              volume: '',
              volumeUnit: getVolumeUnit(unitSystem),
              weightUnit: getPerUnitWeight(unitSystem),
              package_type_item_id: null,
              type_package_id: null,
            },
          ],
        },
      ],
    },
  ],
});
