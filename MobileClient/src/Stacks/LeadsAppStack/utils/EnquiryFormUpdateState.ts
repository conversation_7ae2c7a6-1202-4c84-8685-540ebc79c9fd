// formUpdateUtils.ts
import { convertIntegerArrayIntoString } from '../../../Utils/util';
import { EnquiryFormData } from '../types';
import { generateUniqueId } from '../../../Utils/util';
export const enquiryUpdateState = (
  baseFormData: EnquiryFormData,
  enquiryItemData: any,
  type?: string,
  packageTypes : any
): EnquiryFormData => {
  if (!enquiryItemData) return baseFormData;
  const isNotDetailed = type !== 'Detail';
  const jsonData = enquiryItemData.json_data || {};

const fetchPackageTypeName = (id: string) => {
  const matched = packageTypes?.find((item: any) => item?.Value == id);
  return matched?.Text ?? null; 
};
  try {
    return {
      ...baseFormData,


      type ,
      enquiryId: enquiryItemData.enquiry_id || enquiryItemData.id || '',
      detailData: enquiryItemData,

      // Shipment basics

      unitSystem: enquiryItemData?.json_data?.unitSystem || 'SI',
      selectedMode: enquiryItemData?.enquiry_type?.toLowerCase() || 'sea',
      selectedDirection: enquiryItemData?.direction_name || '',
      fromLocation: enquiryItemData?.json_data?.fromLocation || '',
      isFromLocationAddress:
        enquiryItemData?.json_data?.fromLocation?.type?.toLowerCase() === 'city' || false,
      toLocation: enquiryItemData?.json_data?.toLocation || '',
      isToLocationAddress:
        enquiryItemData?.json_data?.toLocation?.type?.toLowerCase() === 'city' || false,
      transitLocation: enquiryItemData?.json_data?.transitLocation || '',
      fromAddress: enquiryItemData?.from_address != 'null' ? enquiryItemData?.from_address || null : baseFormData?.fromAddress,
      exWorksAddress : enquiryItemData?.ex_works_address || enquiryItemData?.json_data?.ex_works_address || baseFormData?.exWorksAddress,
      toAddress: enquiryItemData?.to_address != 'null' ? enquiryItemData?.to_address  || null : baseFormData?.toAddress,
      is_transshipment: enquiryItemData?.json_data?.is_transshipment,
      remarks: enquiryItemData?.remarks !== 'null' ? enquiryItemData?.remarks : '',
      countryOfOriginId : jsonData?.country_of_origin_id || baseFormData?.countryOfOriginId,
      is_border_charges: !!enquiryItemData?.json_data?.border_charges_id, // check this too
      selectedBorderChargesId: enquiryItemData?.json_data?.border_charges_id || null, // check this
      selectedBorderCharges: enquiryItemData?.border_charges_name
        ? `${enquiryItemData?.border_charges_name}(${enquiryItemData?.border_charges_cost})`
        : null,
      hs_code_category_id: enquiryItemData?.json_data?.hs_code_category_id,
      hs_code_category_name: enquiryItemData?.hs_code_category_name || '',
      hs_code_sub_category_id: enquiryItemData?.json_data?.hs_code_sub_category_id,
      hs_code_sub_category_name: enquiryItemData?.hs_code_sub_category_name || '',
      cargoType: enquiryItemData?.json_data?.cargoType || baseFormData?.cargoType,
      imo_sub_class_name: enquiryItemData?.imo_sub_class_name || '',
      imo_sub_class_id: enquiryItemData?.json_data?.imo_sub_class_id,
      unNumber: enquiryItemData?.json_data?.unNumber || enquiryItemData?.un_number || '',

      srNumber: enquiryItemData?.json_data?.srNumber || enquiryItemData?.sr_number || '',
      Rnd: enquiryItemData?.json_data?.Rnd || enquiryItemData?.Rnd || '',
      Rfl: enquiryItemData?.json_data?.Rfl || enquiryItemData?.Rfl || '',
      perishableTemperature: enquiryItemData?.json_data?.temperature || '',
      perishableTemperatureUnitLabel: enquiryItemData?.json_data?.temperatureUnit || '',
      perishableHumidity: enquiryItemData?.json_data?.humidity || '', // check this
      tempRegimeOfTemperatureCargo: enquiryItemData?.json_data?.tempRegimeOfTemperatureCargo || '', // check this
      temperatureCargoUnitLabel:
        enquiryItemData?.json_data?.tempRegimeOfTemperatureCargoUnits ||
        baseFormData?.temperatureCargoUnitLabel, // check this
      humidityOfTemperatureCargo: enquiryItemData?.json_data?.humidityOfTemperatureCargo || '', // check this
      oversizedCargoDimensions: {
        length: enquiryItemData?.length || '',
        breadth: enquiryItemData?.breadth || '',
        height: enquiryItemData?.height || '',
      },
      selectedSeaCarrierObj: enquiryItemData?.json_data?.selectedCarrier?.[0] || {},
      selectedAirlineObj: enquiryItemData?.json_data?.selectedAirLine?.[0] || {}, // Not found // check this
      incoterms_id: enquiryItemData?.json_data?.incoterm_id,
      incoterm_name: enquiryItemData?.incoterm_name || '',
      readyToLoadDate: enquiryItemData?.ready_to_load || '',
      transitDays: enquiryItemData?.json_data?.transitDays || '',
      freeDays: enquiryItemData?.json_data?.freeDays || '',
      selectedServiceIds: (enquiryItemData?.json_data?.associated_services_id || []).map((id : any) =>
        String(id)
      ),
      selectedServicesObj: enquiryItemData?.json_data?.services || {},
      shipmentType: enquiryItemData?.json_data?.shipmentType || '',
      cargoValueAmount: enquiryItemData?.json_data?.invoiceAmount || '',
      currency_id: enquiryItemData?.currency_id || null,
      additionalInfo: enquiryItemData?.json_data?.additionalInfo || '',
      transportationTypeid: enquiryItemData?.json_data?.transportationTypeid || null,
      transportationBy: enquiryItemData?.transportation_type || '',
      isShowOrganization: !!enquiryItemData?.org_id,
      selectedProspectOrgIdValue: enquiryItemData?.org_id || '',
      enquiryDocuments: type === 'Detail' ? enquiryItemData?.json_data?.enquiry_documents || [] : [],

      containerOptions: jsonData.fclOptions
        ? jsonData.fclOptions.map((option: any) => ({
            fcl_container_option_id: isNotDetailed ? generateUniqueId() : option.optionId,
            name_fcl_option: option.optionName,
            description: option.description,
            fcl_enquiry_items:
              option.containers?.map((container: any) => ({
                id: isNotDetailed ? generateUniqueId() : container.id,
                containerType: container.containerType,
                containerTypeId: container.containerTypeId,
                containerQuantity: container.containerQuantity,
                weight: container.weight,
              })) || [],
          }))
        : baseFormData.containerOptions,

      lclOptions: jsonData.lclOptions
        ? jsonData.lclOptions.map((option: any) => ({
            lcl_option_id: isNotDetailed ? generateUniqueId() : option.optionId,
            name_lcl_option: option.optionName,
            description: option.description,
            lcl_enquiry_items:
              option.lclItems?.map((item: any) => ({
                ...item,
                id: isNotDetailed ? generateUniqueId() : item?.id,
                lots:
                  item.lots?.map((lot: any) => ({
                    ...lot,
                    packageType : fetchPackageTypeName(lot?.package_type_item_id),
                    id: isNotDetailed ? generateUniqueId() : lot.id,
                  })) || [],
                weightVolumeEntries:
                  item.weightVolumeEntries?.map((weightVolumeEntry: any) => ({
                    id: isNotDetailed ? generateUniqueId() : weightVolumeEntry?.id,
                    ...weightVolumeEntry,
                  })) || [],
              })) || [],
          }))
        : baseFormData.lclOptions,

      shipOptions: jsonData.bulkOptions
        ? jsonData.bulkOptions.map((option: any) => ({
            bulk_option_id: isNotDetailed ? generateUniqueId() : option.optionId,
            name_bulk_option: option.optionName,
            description: option.description,
            bulk_enquiry_items:
              option.ships?.map((ship: any) => ({
                id: isNotDetailed ? generateUniqueId() : ship.id,
                shipType: ship.shipType,
                shipTypeId: ship.shipTypeId,
                grossWeight: ship.grossWeight,
                loadingRate: ship.loadingRate,
                dischargingRate: ship.dischargingRate,
              })) || [],
          }))
        : baseFormData.shipOptions,

      standardCargoOptions: jsonData.standardCargoOptions
        ? jsonData.standardCargoOptions.map((option: any) => ({
            standard_cargo_option_id: isNotDetailed ? generateUniqueId() : option.optionId,
            name_standard_cargo_option: option.optionName,
            description: option.description,
            standard_cargo_enquiry_items:
              option.standardCargoItems?.map((item: any) => ({
                ...item,
                id: isNotDetailed ? generateUniqueId() : item?.id,
                lots:
                  item.lots?.map((lot: any) => ({
                    ...lot,
                         packageType : fetchPackageTypeName(lot?.package_type_item_id),
                    id: isNotDetailed ? generateUniqueId() : lot?.id,
                  })) || [],
                weightVolumeEntries:
                  item.weightVolumeEntries?.map((weightVoltumeEntry: any) => ({
                    ...weightVoltumeEntry,
                    id: isNotDetailed ? generateUniqueId() : weightVoltumeEntry,
                  })) || [],
              })) || [],
          }))
        : baseFormData.standardCargoOptions,

      uldOptions: jsonData.uldOptions
        ? jsonData.uldOptions.map((option: any) => ({
            uld_option_id: isNotDetailed ? generateUniqueId() : option.optionId,
            name_uld_option: option.optionName,
            description: option.description,
            uld_enquiry_items:
              option.ulds?.map((uld: any) => ({
                id: isNotDetailed ? generateUniqueId() : uld.id,
                uldType: uld.uldType,
                uldTypeId: uld.uldTypeId,
                uldQuantity: uld.uldQuantity,
                weight: uld.weight,
              })) || [],
          }))
        : baseFormData.uldOptions,

      truckOptions: jsonData.ftlOptions
        ? jsonData.ftlOptions.map((option: any) => ({
            ftl_option_id: isNotDetailed ? generateUniqueId() : option.optionId,
            name_ftl_option: option.optionName,
            description: option.description,
            ftl_enquiry_items:
              option.trucks?.map((truck: any) => ({
                id: isNotDetailed ? generateUniqueId() : truck.id,
                truckType: truck.truckType,
                truckTypeId: truck.truckTypeId,
                truckQuantity: truck.truckQuantity,
                weight: truck.weight,
              })) || [],
          }))
        : baseFormData.truckOptions,

      ltlOptions: jsonData.ltlOptions
        ? jsonData.ltlOptions.map((option: any) => ({
            ltl_option_id: isNotDetailed ? generateUniqueId() : option.optionId,
            name_ltl_option: option.optionName,
            description: option.description,
            ltl_enquiry_items:
              option.ltlItems?.map((item: any) => ({
                ...item,
                id: isNotDetailed ? generateUniqueId() : item?.id,
                // lots: item.lots || [],
                lots:
                  item.lots?.map((lot: any) => ({
                    ...lot,
                         packageType : fetchPackageTypeName(lot?.package_type_item_id),
                    id: isNotDetailed ? generateUniqueId() : lot?.id,
                  })) || [],
                weightVolumeEntries:
                  item.weightVolumeEntries?.map((weightVoltumeEntry: any) => ({
                    ...weightVoltumeEntry,
                    id: isNotDetailed ? generateUniqueId() : weightVoltumeEntry,
                  })) || [],
              })) || [],
          }))
        : baseFormData.ltlOptions,

      // these are extra fields for maintianing the states (initial)
      // selectedModeId: enquiryItemData?.json_data?.modeOfShimpent || null, // don't have this field response, need to map i think while rendering
      selectedDirectionId: enquiryItemData?.json_data?.direction_id || null,

      // Location IDs
      from_city_id: enquiryItemData?.json_data?.from_city_id?.id || null,
      from_port_id: enquiryItemData?.json_data?.from_port_id?.id || null,
      to_city_id: enquiryItemData?.json_data?.to_city_id?.id || null,
      to_port_id: enquiryItemData?.json_data?.to_port_id?.id || null,

      // Transshipment
      transshipment_port_id:
        enquiryItemData?.json_data?.transshipment_port_id ||
        enquiryItemData?.json_data?.transitLocation?.id ||
        null,

      // HS Codes
      category_hs_code: enquiryItemData?.hs_code_category_code || '',
      isMainCategory: !enquiryItemData?.json_data?.hs_code_sub_category_id,
      subCodes: enquiryItemData?.json_data?.subCodes || [],
      sub_category_hs_code: enquiryItemData?.hs_code_sub_category_code || '',

      // Cargo Details
      cargo_type_id: enquiryItemData?.json_data?.cargo_type_id || baseFormData?.cargo_type_id,

      imo_class_id: enquiryItemData?.json_data?.imo_class_id || null, // not have this data

      // Perishable Cargo
      perishableTemperatureUnitValue:
        enquiryItemData?.json_data?.perishableTemperatureUnitValue || '',
      temperatureCargoUnitValue:
        enquiryItemData?.json_data?.temperatureCargoUnitValue ||
        baseFormData?.temperatureCargoUnitValue,

      // Oversized Cargo

      OversizeddimensionUnitLabel: enquiryItemData?.json_data?.OversizeddimensionUnitLabel || '',
      OversizeddimensionUnitValue:
        enquiryItemData?.json_data?.OversizeddimensionUnitValue ||
        baseFormData?.OversizeddimensionUnitValue,

      // Incoterms
      incoterm_code: enquiryItemData?.incoterm_code || '',

      // Carrier Info
      selectedSeaCarrierId: enquiryItemData?.json_data?.selectedCarrier?.[0]?.org_id || null,
      selectedSeaCarrierName:
        enquiryItemData?.json_data?.selectedCarrier?.[0]?.organization_name || '',
      selectedAirlineId: enquiryItemData?.json_data?.selectedAirLine?.[0]?.org_id || null,
      selectedAirlineName:
        enquiryItemData?.json_data?.selectedAirLine?.[0]?.organization_name || '',

      // Currency
      currency_name: enquiryItemData?.currency_name || '',
      currency_symbol: enquiryItemData?.currency_symbol || '',
      currency_code: enquiryItemData?.currency_code || '',
      exchange_rate: enquiryItemData?.exchange_rate || '',

      // Transportation
      transportationByCode: enquiryItemData?.json_data?.transportationByCode || '',
    };
  } catch (error) {
    console.error('Error populating form data from enquiry:', error);
    return {
      ...baseFormData,
      type,
      enquiryId: enquiryItemData?.enquiry_id || enquiryItemData?.id || '',
      detailData: enquiryItemData,
    };
  }
};
