import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Platform, TouchableOpacity } from 'react-native';
import { TextInput, Button, Text } from 'react-native-paper';
import { CountryButton, CountryPicker } from 'react-native-country-codes-picker';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import AppHeader from '../../../../Components/AppHeader';
import { useLeadSuspectMasters } from './useLeadSuspectMasters';
import { DropdownField } from '../../../../Components/UI/Menu/DropdownModal';
import { fontFamily } from '../../../../Common/Theme/typography';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ActivityIndicator } from 'react-native-paper';
import CustomAlert from '../../../../Components/CustomAlert/CustomAlert';
type RootStackParamList = {
  LeadProspectForm: { lead?: any, fromTab?: string};
};

export type Props = NativeStackScreenProps<RootStackParamList, 'LeadProspectForm'>;

export default function LeadSuspectForm({ navigation, route }: Props) {
  const [show, setShow] = useState(false);
  const [countryCode, setCountryCode] = useState({
    code: 'IN',
    dial_code: '+91',
    flag: '🇮🇳',
    name: {
      ar: 'الهند',
      bg: 'Индия',
      by: 'Індыя',
      cn: '印度',
      cz: 'Indie',
      da: 'Indien',
      de: 'Indien',
      ee: 'India',
      el: 'Ινδία',
      en: 'India',
      es: 'India',
      fr: 'Inde',
      he: 'הוֹדוּ',
      it: 'India',
      jp: 'インド',
      nl: 'India',
      pl: 'Indie',
      pt: 'Índia',
      ro: 'India',
      ru: 'Индия',
      tr: 'Hindistan',
      ua: 'Індія',
      zh: '印度',
    },
  });
  const { lead, fromTab } = route.params ?? {};
  const {
    form,
    handleChange,
    industryOptions,
    sourceOptions,
    internalUsers,
    isSubmitting,
    formErrors,
    submitLeadForm,
    isModified,
    loading,
    handleLeftAction,
    handleNextAction,
    showAlert,
    setShowAlert,
  } = useLeadSuspectMasters(navigation, lead,fromTab|| "");

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  function ListHeaderComponent({ countries, lang, onPress }) {
    return (
      <View
        style={{
          paddingBottom: 20,
        }}
      >
        <Text>Popular countries</Text>
        {countries?.map((country, index) => {
          return (
            <CountryButton
              key={index}
              item={country}
              name={country?.name?.[lang || 'en']}
              onPress={() => onPress(country)}
            />
          );
        })}
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <AppHeader
        title={lead ? 'Update Suspect' : 'New Suspect'}
        menuVisible={false}
        showMenu={false}
        showPeopleHirerachy={false}
        showControlUnit={false}
        customHeaderStyles={{ backgroundColor: '#FFFFFF' }}
      />
      <ScrollView
        style={{ marginHorizontal: moderateScale(16) }}
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        <View style={styles.inputWrapper}>
          <Text style={styles.label}>
            Organization Name <Text style={styles.asterisk}>*</Text>
          </Text>
          <TextInput
            placeholderTextColor="#888"
            value={form.organizationName}
            onChangeText={(text) => handleChange('organizationName', text)}
            style={styles.input}
            mode="outlined"
            placeholder="Enter organization name"
          />
          {formErrors.organizationName && (
            <Text style={styles.errorText}>{formErrors.organizationName}</Text>
          )}
        </View>

        <View style={styles.inputWrapper}>
          <Text style={styles.label}>
            Contact Name <Text style={styles.asterisk}>*</Text>
          </Text>
          <TextInput
            placeholderTextColor="#888"
            value={form.contactName}
            onChangeText={(text) => handleChange('contactName', text)}
            style={styles.input}
            mode="outlined"
            placeholder="Enter contact name"
          />
          {formErrors.contactName && (
            <Text style={styles.errorText}>{formErrors.contactName}</Text>
          )}
        </View>

        <DropdownField
          label="Select Industry"
          value={form.industry}
          data={industryOptions}
          keyField="Value"
          valueField="Text"
          onSelect={(item) => handleChange('industry', item)}
          placeholder="Select an industry"
          enableSearch
        />

        <DropdownField
          label="Select Source"
          value={form.source}
          data={sourceOptions}
          keyField="Value"
          valueField="Text"
          onSelect={(item) => handleChange('source', item)}
          placeholder="Select a source"
          enableSearch
        />
        {formErrors.source && <Text style={styles.errorText}>{formErrors.source}</Text>}

        {form.source?.Text === 'External' && (
          <View style={styles.inputWrapper}>
            <Text style={styles.label}>
              External Person <Text style={styles.asterisk}>*</Text>
            </Text>
            <TextInput
              placeholderTextColor="#888"
              value={form.externalPerson}
              onChangeText={(text) => handleChange('externalPerson', text)}
              style={styles.input}
              mode="outlined"
              placeholder="Enter external person name"
            />
            {formErrors.externalPerson && (
              <Text style={styles.errorText}>{formErrors.externalPerson}</Text>
            )}
          </View>
        )}

        {form.source?.Text === 'Internal' && (
          <>
            <DropdownField
              label="Select Internal User"
              value={form.selectedInternalUser}
              data={internalUsers}
              keyField="id"
              valueField="name"
              onSelect={(item) => handleChange('selectedInternalUser', item)}
              placeholder="Select internal user"
              enableSearch
            />
            {formErrors.selectedInternalUser && (
              <Text style={styles.errorText}>{formErrors.selectedInternalUser}</Text>
            )}
          </>
        )}
        <View style={styles.inputWrapper}>
          <Text style={styles.label}>
            Email <Text style={styles.asterisk}>*</Text>
          </Text>
          <TextInput
            placeholderTextColor="#888"
            value={form.email}
            onChangeText={(text) => handleChange('email', text)}
            style={styles.input}
            mode="outlined"
            keyboardType="email-address"
            placeholder="Enter email"
          />
          {formErrors.email && <Text style={styles.errorText}>{formErrors.email}</Text>}
        </View>

        <View style={styles.inputWrapper}>
          <Text style={styles.label}>
            Phone <Text style={styles.asterisk}>*</Text>
          </Text>
          <View style={styles.phoneRow}>
            <TouchableOpacity style={styles.countryCodeBox} onPress={() => setShow(true)}>
              <Text style={styles.flag}>{countryCode?.flag}</Text>
              <Text style={styles.countryCode}>{countryCode?.dial_code}</Text>
            </TouchableOpacity>
            <TextInput
              value={form.phone}
              onChangeText={(text) => handleChange('phone', text)}
              style={[styles.phoneInput, styles.input]}
              mode="outlined"
              keyboardType="phone-pad"
              placeholder="Enter phone number"
            />
          </View>
          {formErrors.phone && <Text style={styles.errorText}>{formErrors.phone}</Text>}
        </View>

        <View style={styles.inputWrapper}>
          <Text style={styles.label}>Notes</Text>
          <TextInput
            value={form.notes}
            onChangeText={(text) => handleChange('notes', text)}
            style={[styles.input, styles.notesInput]}
            mode="outlined"
            multiline
            numberOfLines={4}
            placeholder="Add additional information..."
            placeholderTextColor="#ccc"
          />
        </View>
      </ScrollView>

      <View style={styles.actionRow}>
        <Button
          mode="contained"
          style={{
            flex: 1,
            borderRadius: 4,
            backgroundColor: lead ? 'rgba(51, 119, 255, 1)' : 'rgba(229, 231, 235, 1)',
          }}
          textColor={lead ? '#fff' : '#000'}
          onPress={handleLeftAction}
          loading={isSubmitting}
        >
          {lead ? 'Update' : 'Cancel'}
        </Button>
        <Button
          mode="contained"
          style={{ flex: 1, borderRadius: 4, backgroundColor: 'rgba(51, 119, 255, 1)' }}
          textColor="#fff"
          onPress={handleNextAction}
          disabled={isSubmitting}
        >
          {lead ? 'Next' : 'Save'}
        </Button>
      </View>
      <CustomAlert
        visible={showAlert}
        title="Unsaved Changes"
        message="You have modified the form. Do you want to update before proceeding?"
        showCancel={true}
        cancelText="Discard"
        confirmText="Update"
        onCancel={() => setShowAlert(false)}
        onConfirm={() => {
          setShowAlert(false);
          submitLeadForm(true);
        }}
        button2Styles={{ backgroundColor: '#3377FF' }}
      />

      <CountryPicker
        show={show}
        style={{
          // Styles for whole modal [View]
          modal: {
            height: 800,
            // backgroundColor: 'red'
          },
        }}
        // when picker button press you will get the country object with dial code
        pickerButtonOnPress={(item) => {
          setCountryCode(item);
          setShow(false);
        }}
        lang="en"
        ListHeaderComponent={ListHeaderComponent}
        popularCountries={['AE', 'QA', 'OM', 'KW', 'SA', 'BH', 'IN']}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { backgroundColor: '#fff', flex: 1 },
  inputWrapper: {},
  label: {
    fontFamily: fontFamily.semiBold,
    fontSize: moderateScale(14),
    marginTop: verticalScale(10),
    marginBottom: verticalScale(5),
  },
  asterisk: { color: 'red', fontSize: moderateScale(14) },
  input: { height: 48, backgroundColor: 'white' },
  notesInput: {
    height: verticalScale(100),
    textAlignVertical: 'top',
  },
  phoneRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(8),
  },
  countryCodeBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f3f4f6',
    paddingHorizontal: scale(8),
    paddingVertical: verticalScale(6),
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  flag: { fontSize: moderateScale(16), marginRight: 4 },
  countryCode: { fontSize: moderateScale(13), color: '#111827' },
  phoneInput: {
    flex: 1,
    backgroundColor: 'white',
    height: 48,
  },
  errorText: {
    color: 'red',
    fontSize: moderateScale(12),
    marginTop: verticalScale(4),
  },
  actionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    padding: 16,
    gap: scale(10),
    marginBottom: Platform.OS === 'ios' ? verticalScale(10) : 0,
  },
});
