import { View, Text, StyleSheet, ScrollView } from 'react-native';
import React, { useEffect, useState } from 'react';
import AppHeader from '../../../../Components/AppHeader';
import {
  StepIndicator,
  ShipmentDetailsStep,
  ContainerDetailsStep,
  AdditionalInfoStep,
  ShipmentCategory,
} from '../../components';
import { useTheme } from 'react-native-paper';
import { EnquiryFormData } from '../../types';
import { getInitialFormData } from '../../utils/StateInitialization';
import { convertEnquiryUnits } from '../../utils/convertEnquiryUnits';
import { enquiryUpdateState } from '../../utils/EnquiryFormUpdateState';
import { useDispatch, useSelector } from 'react-redux';
import { setUnitSystem } from '../../../../State/Slices/LeadsAppSlices/EnquiryUnitSystem';
import { useDropdownData } from '../../components/ContainerDetailsStep/hooks/useContainerDetails';
const EnquiryForm = ({ route, navigation }: any) => {
  const [localUnitSystem, setLocalUnitSystem] = useState('SI');   //use local one for now
const enqUnitSystem = useSelector((state : any) => state?.enqUnitSystem?.unitSystemRedux)
// console.log("enqUnitSystem", enqUnitSystem)
  const {
    accountId,
    controlUnitId,
    createdBy,
    workspaceId,
    orgId,
    type,
    item: enquiryItemData,
    setActiveTabName,
  } = route.params;
  const initialFormState = {
    accountId,
    controlUnitId,
    workspaceId,
    createdBy,
    orgId,
    type,
    ...getInitialFormData(localUnitSystem),
  };
  const dispatch = useDispatch();
  const [formData, setFormData] = useState<EnquiryFormData>(initialFormState);
   const {
      containerTypes,
      packageTypes,
      stackabilityTypes,
      shipTypes,
      truckTypes,
      uldContainerTypes,
    } = useDropdownData();

  const steps = [
    { id: 1, title: 'Shipment Details', component: ShipmentDetailsStep },
    // { id: 2, title: 'ShipmentCategory', component: ShipmentCategory },
    // { id: 3, title: 'Container Details', component: ContainerDetailsStep },

    { id: 4, title: 'Additional Info', component: AdditionalInfoStep },
  ];

  const [currentStep, setCurrentStep] = useState(1);
  const CurrentStepComponent = steps[currentStep - 1].component;

  const handleUnitSystemChange = (unitValue: string, unitId: string) => {
    // console.log("unit value",unitValue , "unitId",unitId )
    const updatedUnitsForm = convertEnquiryUnits(formData, unitValue);
    dispatch(setUnitSystem(unitValue));
    setLocalUnitSystem(unitValue);

    updateFormData({
      ...updatedUnitsForm,
      unitSystem: unitValue,
      unit_system_id: unitId,
    });
  };
  useEffect(() => {
    if (enquiryItemData && type !== 'New') {
      console.log('Populating form with existing data for update');
      const populatedData = enquiryUpdateState(initialFormState, enquiryItemData, type, packageTypes);
      setFormData(populatedData);
    }
  }, [enquiryItemData, type, packageTypes]);

  useEffect(() => {
    if (formData?.fromLocation && formData?.toLocation) {
      const fromIsCityOrCountry = ['city', 'CITY', 'COUNTRY'].includes(
        formData?.fromLocation?.type
      );
      const toIsCityOrCountry = ['city', 'CITY', 'COUNTRY'].includes(formData?.toLocation?.type);
      const fromIsPort = ['SEA PORT', 'AIR PORT'].includes(formData?.fromLocation?.type);
      const toIsPort = ['SEA PORT', 'AIR PORT'].includes(formData?.toLocation?.type);
      if (fromIsCityOrCountry && toIsCityOrCountry) {
        updateFormData({ shipmentType: 'Door to Door' });
      } else if (fromIsCityOrCountry && toIsPort) {
        updateFormData({ shipmentType: 'Door to Port' });
      } else if (fromIsPort && toIsCityOrCountry) {
        updateFormData({ shipmentType: 'Port to Door' });
      } else if (fromIsPort && toIsPort) {
        updateFormData({ shipmentType: 'Port to Port' });
      }
    }
  }, [formData?.fromLocation, formData?.toLocation]);

  const updateFormData = (updates: any) => {
    setFormData((prev) => ({ ...prev, ...updates }));
  };

  const goToNextStep = async () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const theme = useTheme();

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: theme.colors.background,
        },
      ]}
    >
      <AppHeader
        title="Enquiry Form"
        menuVisible={false}
        showMenu={false}
        showPeopleHirerachy={false}
        showControlUnit={false}
        showUnitSelector={true}
        selectedUnit={formData?.unitSystem}
        onUnitChange={handleUnitSystemChange}
      />

      <View style={[styles.header, { backgroundColor: theme.colors.background }]}>
        <StepIndicator currentStep={currentStep} totalSteps={steps.length} />
      </View>
      <CurrentStepComponent
        formData={formData}
        updateFormData={updateFormData}
        currentStep={currentStep}
        totalSteps={steps?.length}
        onNext={goToNextStep}
        onPrevious={goToPreviousStep}
        initialFormState={initialFormState}
        setActiveTabName={setActiveTabName}
      />
    </View>
  );
};

export default EnquiryForm;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
  },
});
