import React, { useCallback, useEffect, useRef, useState, useMemo } from 'react';
import {
  View,
  TouchableOpacity,
  ScrollView,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { AnimatedFAB, FAB, Switch, Text, useTheme } from 'react-native-paper';
import { Plus, SlidersHorizontal } from 'lucide-react-native';
import AppHeader from '../../../../Components/AppHeader';
import ApiClient from '../../../../Common/API/ApiClient';
import { LEADS_ENDPOINTS } from '../../../../Common/API/ApiEndpoints';
import LeadHeaderCard from './LeadHeaderCard';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import SearchBar from '../../../../Components/SearchInput';
import {
  NavigationProp,
  ParamListBase,
  useFocusEffect,
  useNavigation,
} from '@react-navigation/native';
import { useRoute, RouteProp } from '@react-navigation/native';
import {
  getAccountId,
  getSchemaName,
  getSelectedWorkspaceId,
  getUserId,
} from '../../../../Common/Utils/Storage';
import { useSelector } from 'react-redux';
import AppLoader from '../../../../Components/Loader/AppLoader/InitialLoaderWithText';
import { ControlUnitModal } from '../../../SalesManagementStack/screens/Leads/LeadsScreen.modals';
import {
  useControlUnitsData,
  usePeopleHirerachy,
} from '../../../SalesManagementStack/Hooks/GlobalControlData';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { showToast } from '../../../../Components/AppToaster/AppToaster';
import CustomAlert from '../../../../Components/CustomAlert/CustomAlert';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import Tabs from '../../../../Components/Tabs';
import { RootState } from '../../../../State/Store';

const getTabsConfig = (leadsCount: any, showMyLeads: boolean) => {
  const baseTabs = [
    { 
      label: showMyLeads ? 'Leads' : 'All Leads',
      ids: [], 
      endpoint: LEADS_ENDPOINTS.GET_ALL_LEADS, 
      requiresLeadId: false,
      count: leadsCount?.all_leads ?? 0,
    },
    { 
      label: 'Prospects', 
      ids: [2], 
      endpoint: LEADS_ENDPOINTS.GET_PROSPECTS, 
      requiresLeadId: true,
      count: leadsCount?.prospects ?? 0,
    },
    {
      label: 'Opportunities',
      ids: [3],
      endpoint: LEADS_ENDPOINTS.GET_OPPORTUNITIES,
      requiresLeadId: true,
      count: leadsCount?.opportunities ?? 0,
    },
    { 
      label: 'Customers', 
      ids: [4], 
      endpoint: LEADS_ENDPOINTS.GET_CUSTOMERS, 
      requiresLeadId: true,
      count: leadsCount?.customers ?? 0,
    },
  ];

  if (!showMyLeads) {
    baseTabs.splice(1, 0, {
      label: 'Suspects', 
      ids: [1], 
      endpoint: LEADS_ENDPOINTS.GET_SUSPECTS, 
      requiresLeadId: true,
      count: leadsCount?.suspects ?? 0,
    });
  }

  return baseTabs;
};

const LeadsScreenMain: React.FC = () => {
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const route = useRoute<RouteProp<{ params?: { tab?: string } }, 'params'>>();
  const [showMyLeads, setShowMyLeads] = useState(false);
  const [leads, setLeads] = useState([]);
  const [allLeads, setAllLeads] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [hasMoreData, setHasMoreData] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [fabOpen, setFabOpen] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [leadToDelete, setLeadToDelete] = useState<any>(null);
  const [leadsCount, setLeadsCount] = useState<any>(0);
  const [apiContext, setApiContext] = useState<{
    schemaName: string | null;
    accountId: string | null;
    workspaceId: string | null;
  } | null>(null);

  const tabs = useMemo(() => getTabsConfig(leadsCount,showMyLeads), [leadsCount,showMyLeads]);
   const initialTab = route.params?.tab ?? tabs[0]?.label ?? 'All Leads';
  const [activeTab, setActiveTab] = useState(initialTab);

  const theme = useTheme();
  const controlUnitId = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedControlUnit?.data?.id
  );
  const appData = route.params?.appData;
  const controlUnitsResult = useControlUnitsData(appData?.controlUnitLevelId);
  const selectedControlUnitData = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedControlUnit?.data
  );
  const peopleHirerachySelectedData = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedPeopleHirerachy?.data
  );

  const {
    controlUnits,
    showControlUnitsModal,
    error: controlUnitsError,
    handleControlUnits,
    setShowControlUnitsModal,
    setSelectControlUnit,
  } = controlUnitsResult;
  const {
    handlePeopleHirerachy,
    showPeopleHirerachyModal,
    peopleHirerachyData,
    setShowPeopleHirerachyModal,
  } = usePeopleHirerachy();
  
  const handleControlUnitSelect = (controlUnit: any) => {
    setSelectControlUnit(controlUnit);
  };
  
  const loadMoreTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const insets = useSafeAreaInsets();
  const fabActions = [
    {
      icon: 'plus',
      label: 'New Lead',
      onPress: () => navigation.navigate('LeadForm', { fromTab: activeTab }),
    },
    { icon: 'calendar-month', label: 'Meetings', onPress: () => console.log('Meetings') },
    { icon: 'chart-bar', label: 'Reports', onPress: () => console.log('Reports') },
  ];

  const setActiveTabName = (tab: string) => setActiveTab(tab);

  const initializeAppData = async (page: number = 1, isLoadMore: boolean = false, searchText: string = searchQuery) => {
    try {
      if (!apiContext || !controlUnitId) return;

      fetchLeads(page, isLoadMore, {
        schemaName: apiContext.schemaName,
        accountId: apiContext.accountId,
        workspaceId: apiContext.workspaceId,
        controlUnitId,
      }, searchText);
    } catch (e) {
      console.log('Failed to initialize app data:', e);
    }
  };

  const fetchLeads = async (
    page: number = 1,
    isLoadMore: boolean = false,
    context?: {
      schemaName: string;
      accountId: string;
      workspaceId: string;
      controlUnitId: string;
    },
    searchText: string = ''
  ) => {
    if (!context) return;
    const { schemaName, accountId, workspaceId, controlUnitId } = context;

    if (!controlUnitId || !schemaName || !accountId || !workspaceId) return;

    try {
      const selectedTab = tabs.find((t) => t.label === activeTab);
      if (!selectedTab) return;

      const endpoint = selectedTab.endpoint;
      const requiresLeadId = selectedTab.requiresLeadId;
      const basePayload: any = {
        account_id: accountId,
        workspace_id: workspaceId,
        control_unit_id: controlUnitId,
        page_number: page,
        limit: 10,
        search_text: searchText,
      };
      if (requiresLeadId && selectedTab.ids.length > 0) {
        basePayload.lead_id = selectedTab.ids[0];
      }
      if (showMyLeads) {
        const salesPersonId = await getUserId();
        if (salesPersonId) {
          basePayload.sales_person_id = salesPersonId;
        }
      }
      const encrypted = await {
        schemaName,
        payloadJson: JSON.stringify(basePayload),
      };
      const res = await ApiClient.post(endpoint, encrypted);
      const data = res.data?.data || [];
   
      const mapped = data.map((lead: any, idx: number) => ({
        id: lead.contactId || String(idx),
        initials: lead.company ? lead.company[0] : '',
        company: lead.organizationName,
        contactName: lead.contactName,
        location: lead.area,
        category: lead.industryName,
        type: lead.leadStateName,
        typeGrade: lead.prospectTypeName,
        lead,
        totalEnquiries:lead.totalEnquiries,
        totalQuotes:lead.totalQuotes,
        totalJobs:lead.totalJobs,

      }));
  
      if (isLoadMore) {
        setLeads((prev) => [...prev, ...mapped]);
        setAllLeads((prev) => [...prev, ...mapped]);
        if (mapped.length < 10) setHasMoreData(false);
      } else {
        setLeads(mapped);
        setAllLeads(mapped);
        setHasMoreData(mapped.length >= 10);
      }
    } catch (err: any) {
      console.log('Error fetching leads:', err.message);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const fetchLeadsCount = useCallback(async () => {
    if (!apiContext || !controlUnitId) return;

    try {
      const contextJson = {
          account_id: apiContext.accountId,
          workspace_id: apiContext.workspaceId,
          control_unit_id: controlUnitId,
          search_text: searchQuery || '',
        }
      
      if (showMyLeads) {
        const salesPersonId = await getUserId();
        if (salesPersonId) {
          contextJson.sales_person_id = salesPersonId;
        }
      }
      const payload = {
        schemaName: apiContext.schemaName,
        contextJson: JSON.stringify(contextJson)
      };
      const res = await ApiClient.post(LEADS_ENDPOINTS.GET_LEADS_COUNT, payload);
      const result = res?.data?.data?.[0]?.result || {};
      setLeadsCount(result);
    } catch (err) {
      console.log('Error fetching lead count:', err);
      setLeadsCount(0);
    }
  }, [apiContext, controlUnitId, searchQuery,showMyLeads]);

  const handleRefresh = () => {
    setRefreshing(true);
    setCurrentPage(1);
    initializeAppData();
    fetchLeadsCount();
  };

  const loadMore = () => {
    if (loading || !hasMoreData || loadMoreTimeoutRef.current) return;

    loadMoreTimeoutRef.current = setTimeout(() => {
      loadMoreTimeoutRef.current = null;
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      initializeAppData(nextPage, true);
    }, 300);
  };

  const confirmDelete = async () => {
    if (!leadToDelete) return;

    try {
      const [schemaName, accountId, workspaceId, userId] = await Promise.all([
        getSchemaName(),
        getAccountId(),
        getSelectedWorkspaceId(),
        getUserId()
      ]);
      const payload = {
        schemaName,
        contextJson: {
          account_id: accountId,
          workspace_id: workspaceId,
          control_unit_id: controlUnitId,
          user_id: userId,
        },
        inputJson: {
          org_ids: [leadToDelete],
        },
      };
      const res = await ApiClient.post(LEADS_ENDPOINTS.DELETE_LEAD, payload);
      if (res?.data?.statusCode === 200) {
        showToast.success('Lead deleted successfully');
        initializeAppData();
        fetchLeadsCount(); 
      } else {
        showToast.error(res?.data?.message || 'Failed to delete lead');
      }
    } catch (error) {
      console.error('Delete lead error:', error);
      showToast.error('Error deleting lead');
    } finally {
      setLeadToDelete(null);
      setShowDeleteModal(false);
    }
  };


  const renderLead = ({ item }: { item: any }) => (
    <LeadHeaderCard
      company={item.company}
      email={item.lead?.email}
      contactName={item.contactName}
      category={item.category}
      leadState={item.type}
      leadGrade={item.typeGrade}
      onMoveToProspect={() => {
        navigation.navigate('LeadForm', {
          lead: item.lead,
          fromTab: activeTab,
          formMode: 'prospect',
          setActiveTabName
        });
      }}
      totalEnquiries={item.totalEnquiries}
      totalQuotes={item.totalQuotes}
      totalJobs={item.totalJobs}
      lead={item.lead}
      activeTab={activeTab}
      setActiveTabName={setActiveTabName}
      onDeleteLead={() => {
        setLeadToDelete(item?.lead?.orgId);
        setShowDeleteModal(true);
      }}
    />
  );

  const renderFooter = () => {
    if (leads.length === 0) return null;

    if (hasMoreData && currentPage > 1) {
      return (
        <View style={{ paddingVertical: verticalScale(16) }}>
          <ActivityIndicator size="small" color="#2563EB" />
        </View>
      );
    }

    // if (!hasMoreData) {
    //   return (
    //     <View style={{ paddingVertical: verticalScale(16), alignItems: 'center' }}>
    //       <Text style={{ color: '#9CA3AF', fontSize: 12 }}>No more leads to load</Text>
    //     </View>
    //   );
    // }

    return null;
  };

  useEffect(() => {
    const loadApiContext = async () => {
      try {
        const [schemaName, accountId, workspaceId] = await Promise.all([
          getSchemaName(),
          getAccountId(),
          getSelectedWorkspaceId(),
        ]);
        setApiContext({ schemaName, accountId, workspaceId });
      } catch (err) {
        console.error("Error loading API context:", err);
      }
    };
    loadApiContext();
    return () => {
      if (loadMoreTimeoutRef.current) {
        clearTimeout(loadMoreTimeoutRef.current);
      }
    };
  }, []);

  useFocusEffect(
    
      useCallback(() => {
      if (!apiContext || !controlUnitId) return;
      if(showMyLeads && activeTab === 'Suspects'){setActiveTabName('All Leads');}

      setCurrentPage(1);
      setHasMoreData(true);
      setLoading(true);
      initializeAppData(1, false, searchQuery);
      fetchLeadsCount();
    }, [activeTab, controlUnitId, apiContext, showMyLeads])
  )

  useEffect(() => {
    const handler = setTimeout(() => {
      if (!apiContext || !controlUnitId) return;
      setCurrentPage(1);
      setHasMoreData(true);
      initializeAppData(1, false, searchQuery);
    }, 500);
    return () => clearTimeout(handler);
  }, [searchQuery, apiContext, controlUnitId]);

  useEffect(() => {
    if (!apiContext || !controlUnitId) return;
    setCurrentPage(1);
    setHasMoreData(true);
    setLoading(true);
    initializeAppData(1, false, searchQuery);
    fetchLeadsCount();
  }, [showMyLeads]);

  return (
    <View style={styles.container}>
      <AppHeader
        title="Leads"
        menuVisible={false}
        showMenu={false}
        customHeaderStyles={{ backgroundColor: '#FFFFFF' }}
        showControlUnit={true}
        showPeopleHirerachy={false}
        onControlUnitsPress={handleControlUnits}
        selectedControlUnit={selectedControlUnitData}
        onPeopleHirerachyPress={handlePeopleHirerachy}
        selectedPeopleHirerachy={peopleHirerachySelectedData}
        rightElement={
          <View style={{ alignItems: 'center', justifyContent: 'center', flexDirection: 'row' }}>
            {/* <Text style={{ color: '#000', fontSize: 11 }} variant="bold">
              {showMyLeads ? 'Show ' : 'Show'}
            </Text> */}

            <Switch
              value={showMyLeads}
              onValueChange={setShowMyLeads}
              trackColor={{ false: '#aaa', true: '#3377ff' }}
              thumbColor={showMyLeads ? '#fff' : '#fff'}
              style={{ transform: [{ scaleX: 0.7 }, { scaleY: 0.7 }] }}
            />
            <View style={{alignItems: 'center', justifyContent: 'center'}}>
              <Text
                style={{
                  color: '#000',
                  fontSize: moderateScale(10),
                  // textAlign: 'center',
                  // lineHeight: moderateScale(14),
                }}
                variant="semiBold"
              >
                My
              </Text>
              <Text style={{ color: '#000', fontSize: moderateScale(10) }} variant="semiBold">
                Leads
              </Text>
            </View>
          </View>
        }
      />
      <View style={{ marginBottom: moderateScale(8) }}>
        <Tabs
          activeTab={activeTab}
          tabs={tabs}
          onTabPress={setActiveTab}
          labelKey="label"
          valueKey="label"
          showCounts={true}
          countKey="count"
        />
      </View>
      <View
        style={{ marginHorizontal: moderateScale(16), backgroundColor: theme.colors.background }}
      >
        <View style={[styles.headerRow]}>
          <SearchBar
            placeholder="Search Leads..."
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={{ flex: 1 }}
          />
          <TouchableOpacity
            style={styles.filterIconWrap}
            activeOpacity={0.7}
            onPress={() => navigation.navigate('LeadForm', { fromTab: activeTab })}
          >
            <Plus color="white" size={moderateScale(18)} strokeWidth={2.5} />
            <Text style={{
              color: "#fff",
              fontSize: moderateScale(13)
            }} variant='semiBold'>Lead</Text>
          </TouchableOpacity>
        </View>
      </View>
      {loading ? (
        <AppLoader message="Loading data..." />
      ) : (
        <>
          <FlatList
            data={leads}
            keyExtractor={(item, index) => `${item.id}-${index}`}
            showsVerticalScrollIndicator={false}
            renderItem={renderLead}
            contentContainerStyle={
              leads.length === 0
                ? { flex: 1, justifyContent: 'center', alignItems: 'center' }
                : { paddingBottom: 100 }
            }
            refreshing={refreshing}
            onRefresh={handleRefresh}
            onEndReached={loadMore}
            onEndReachedThreshold={0.5}
            ListFooterComponent={renderFooter}
            style={{ marginHorizontal: moderateScale(16) }}
            ListEmptyComponent={
              !loading && (
                <View style={{ alignItems: 'center', flex: 1, justifyContent: 'center' }}>
                  <Text style={{ color: '#9CA3AF', fontSize: 14 }}>{`No leads found`}</Text>
                </View>
              )
            }
            bounces={false}
          />
        </>
      )}
      <ControlUnitModal
        visible={showControlUnitsModal}
        onDismiss={() => setShowControlUnitsModal(false)}
        controlUnits={controlUnits || []}
        onSelect={handleControlUnitSelect}
      />
      <CustomAlert
        visible={showDeleteModal}
        title="Delete Lead ?"
        message="Are you sure you want to delete this lead? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        showCancel={true}
        onCancel={() => {
          setLeadToDelete(null);
          setShowDeleteModal(false);
        }}
        onConfirm={confirmDelete}
        button2Styles={{ backgroundColor: '#dc2626' }}
      />
    </View>
  );
};

export default LeadsScreenMain;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabBar: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  tabItem: {
    paddingVertical: 8,
    paddingHorizontal: moderateScale(7),
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
    marginRight: moderateScale(2),
  },
  activeTabItem: {
    borderBottomColor: '#2563EB',
  },
  tabText: {
    fontSize: moderateScale(11),
    color: '#6B7280',
  },
  activeTabText: {
    color: '#2563EB',
    fontWeight: '600',
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(8),
    marginBottom: verticalScale(5),
  },
  filterIconWrap: {
    height: verticalScale(28),
    borderRadius: 5,
    backgroundColor: '#3377ff',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: "row",
    paddingHorizontal: scale(9),
    gap: scale(3)
  },
  fab: {
    backgroundColor: '#2563EB',
  },
  fabContainer: {
    position: 'absolute',
    right: 0,
    bottom: -verticalScale(40),
    zIndex: 999,
    elevation: 10,
  },
});