import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Platform,
  ActivityIndicator,
  KeyboardAvoidingView,
} from 'react-native';
import { Text, Button } from 'react-native-paper';
import { verticalScale, scale, moderateScale } from 'react-native-size-matters';
import AppHeader from '../../../../Components/AppHeader';
import { DropdownField } from '../../../../Components/UI/Menu/DropdownModal';
import { Star, Check } from 'lucide-react-native';
import { fontFamily } from '../../../../Common/Theme/typography';
import CustomAlert from '../../../../Components/CustomAlert/CustomAlert';
import { MasterOption, useLeadForm } from './useLeadForm';
import { CountryButton, CountryPicker } from 'react-native-country-codes-picker';
import CustomInput from '../../../../Components/UI/TextInput';
import { TextInput as PaperTextInput } from 'react-native-paper';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import EmailInput from '../../../../Components/UI/Menu/EmailInput';

export default function LeadForm({ navigation, route }: any) {
  const { formMode = 'suspect', lead, fromTab,setActiveTabName } = route.params || {};
  const [mode, setMode] = useState<'suspect' | 'prospect'>(formMode);
  const props = useLeadForm(mode, navigation, lead, fromTab, setMode,setActiveTabName);

  const getColor = (value: string) => {
    const match = value?.match(/Type\s+([A-C])/i);
    const letter = match?.[1]?.toUpperCase();
    switch (letter) {
      case 'A':
        return '#2563eb';
      case 'B':
        return '#fbbf24';
      case 'C':
        return '#ef4444';
      default:
        return '#ccc';
    }
  };

  if (props.isSubmitting || props.loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#3377FF" />
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#fff' }}>
      <AppHeader
        title={mode === 'prospect' ? 
          props.form.organizationName // 'Move to Prospect' 
          : lead ? 'Update Suspect' : 'New Suspect'}
        customHeaderStyles={{ backgroundColor: '#fff' }}
      />
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        // behavior={ 'height'}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        //  keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
      >
        <ScrollView
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          bounces={false}
        >
          {/* <KeyboardAwareScrollView 
            contentContainerStyle={styles.scrollContent} 
            // behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
          > */}
          {mode === 'prospect' ? (
            <>
              <Text style={styles.label} variant="semiBold">
                Prospect Type <Text style={styles.asterisk}>*</Text>
              </Text>
              <View
                style={{
                  flexDirection: 'row',
                  gap: scale(8),
                  marginTop: verticalScale(5),
                  marginBottom: verticalScale(8),
                }}
              >
                {props.prospectTypes.map((item) => {
                  const selected =
                    props.form.prospectType?.prospect_type_id === item.prospect_type_id;
                  const color = getColor(item.prospect_type_name);
                  return (
                    <TouchableOpacity
                      key={item.prospect_type_id}
                      onPress={() => props.handleChange('prospectType', item)}
                      style={[
                        {
                          flex: 1,
                          borderWidth: 1,
                          borderColor: selected ? color : '#D1D5DB',
                          borderRadius: 8,
                          padding: scale(10),
                          alignItems: 'center',
                        },
                      ]}
                    >
                      <Star
                        fill={selected ? color : 'transparent'}
                        color={selected ? 'white' : color}
                        size={20}
                      />
                      <Text style={{ color: selected ? color : '#111827', fontWeight: '600' }}>
                        {item.prospect_type_name}
                      </Text>
                      <Text style={{ fontSize: 12, color: '#6B7280', textAlign: 'center' }}>
                        {item.prospect_type_description}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </View>
              {props.errors?.prospectType && (
                <Text style={styles.errorText}>{props.errors.prospectType}</Text>
              )}
              <Text style={styles.label} variant="semiBold">
                Job Type <Text style={styles.asterisk}>*</Text>
              </Text>
              <View
                style={{
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  gap: scale(10),
                  marginTop: verticalScale(5),
                  marginBottom: verticalScale(8),
                }}
              >
                {props.jobTypes.map((item) => {
                  const selected = !!props.form.jobTypes.find(
                    (j: MasterOption) => j.Value === item.Value
                  );
                  return (
                    <TouchableOpacity
                      key={item.Value}
                      onPress={() => props.toggleJobType(item)}
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: scale(6),
                        width: '48%',
                      }}
                    >
                      <View
                        style={{
                          width: 20,
                          height: 20,
                          borderRadius: 4,
                          borderWidth: 1,
                          borderColor: '#D1D5DB',
                          justifyContent: 'center',
                          alignItems: 'center',
                          backgroundColor: selected ? '#2563eb' : '#fff',
                        }}
                      >
                        {selected && <Check size={12} color="#fff" />}
                      </View>
                      <Text
                        style={{
                          fontFamily: fontFamily.regular,
                          fontSize: moderateScale(13),
                          color: '#111827',
                          width: '80%',
                        }}
                      >
                        {item.Text}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </View>
              {props.errors?.jobTypes && (
                <Text style={styles.errorText}>{props.errors.jobTypes}</Text>
              )}
              <DropdownField
                label="Control Unit"
                value={props.form.controlUnit}
                data={props.controlUnits}
                keyField="id"
                valueField="controlUnitName"
                onSelect={(item) => props.handleChange('controlUnit', item)}
                placeholder="Select control unit"
              />
              {props.errors?.controlUnit && (
                <Text style={styles.errorText}>{props.errors.controlUnit}</Text>
              )}
              <DropdownField
                label="Assigned To"
                value={props.form.assignedTo}
                data={props.assignedUsers}
                labelField={'label'}
                toPascal={false}
                keyField="id"
                valueField="name"
                onSelect={(item) => props.handleChange('assignedTo', item)}
                placeholder="Select user"
              />
              {props.errors?.assignedTo && (
                <Text style={styles.errorText}>{props.errors.assignedTo}</Text>
              )}
              <DropdownField
                label="Area"
                value={props.form.area}
                data={props.areaOptions}
                keyField="Value"
                valueField="Text"
                onSelect={(item) => props.handleChange('area', item)}
                placeholder="Select area"
              />
              {props.errors?.area && <Text style={styles.errorText}>{props.errors.area}</Text>}
              <Text style={styles.label} variant="semiBold">
                Comments
              </Text>
              <PaperTextInput
                value={props.form.comments}
                onChangeText={(text) => props.handleChange('comments', text)}
                mode="outlined"
                multiline
                placeholder="Add comments..."
                style={styles.textArea}
                contentStyle={{
                  fontSize: moderateScale(12),
                  fontFamily: fontFamily.regular,
                  textAlignVertical: 'top',
                  paddingVertical: verticalScale(0),
                }}
                outlineColor="#D1D5DB"
                activeOutlineColor="#2563EB"
                placeholderTextColor={'#ccc'}
              />
            </>
          ) : (
            <>
              <CustomInput
                label="Organization Name"
                value={props.form.organizationName}
                onChangeText={(text) => props.handleChange('organizationName', text)}
                errorText={props.formErrors?.organizationName}
                isMandatory={true}
                placeholder="Enter organization name"
              />
              <DropdownField
                label="Industry"
                value={props.form.industry}
                data={props.industryOptions}
                keyField="Value"
                valueField="Text"
                onSelect={(item) => props.handleChange('industry', item)}
                placeholder="Select industry"
                enableSearch
                showAsterisk={false}
              />

              <DropdownField
                label="Source"
                value={props.form.source}
                data={props.sourceOptions}
                keyField="Value"
                valueField="Text"
                onSelect={(item) => props.handleChange('source', item)}
                placeholder="Select source"
                enableSearch
                type="radio"
                radioOptionContainerStyle={true}
              />
              {props.formErrors?.source && (
                <Text style={styles.errorText}>{props.formErrors.source}</Text>
              )}

              {props.form.source?.Text === 'External' && (
                <CustomInput
                  label="External Person"
                  value={props.form.externalPerson}
                  onChangeText={(text) => props.handleChange('externalPerson', text)}
                  placeholder="Enter external person"
                  errorText={props.formErrors?.externalPerson}
                  isMandatory={true}
                />
              )}
              {props.form.source?.Text === 'Internal' && (
                <>
                  <DropdownField
                    label="Internal User"
                    value={props.form.selectedInternalUser}
                    data={props.internalUsers}
                    keyField="id"
                    valueField="name"
                    onSelect={(item) => props.handleChange('selectedInternalUser', item)}
                    placeholder="Select internal user"
                    enableSearch
                    toPascal={false}
                    labelField={'label'}
                  />
                  {props.form.source?.Text === 'Internal' &&
                    props.formErrors?.selectedInternalUser && (
                      <Text style={styles.errorText}>{props.formErrors.selectedInternalUser}</Text>
                    )}
                </>
              )}
              <CustomInput
                label="Contact Name"
                value={props.form.contactName}
                onChangeText={(text) => props.handleChange('contactName', text)}
                placeholder="Enter contact name"
                errorText={props.formErrors.contactName}
                isMandatory={true}
              />
              {/* <CustomInput
                label="Email"
                value={props.form.email}
                onChangeText={(text) => props.handleChange('email', text)}
                placeholder="Enter email address"
                keyboardType="email-address"
                errorText={props.formErrors?.email}
                isMandatory={true}
              /> */}
              <EmailInput
                label="Email"
                value={props.form.email}
                onChangeText={(text) => props.handleChange('email', text)}
                placeholder="Enter your email"
                isMandatory={true}
                errorText={props.formErrors?.email}
                onValidationChange={(valid) => {props.handleChange("emailValidation", valid)}}
              />
              <Text style={styles.label} variant="semiBold">
                Phone Number
                <Text style={{ color: 'red' }} variant="semiBold">
                  {' '} * 
                </Text>
              </Text>
              <View style={styles.phoneRow_new}>
                <TouchableOpacity
                  style={[
                    styles.countryCodeBox_new,
                    {
                      width: scale(75),
                    },
                  ]}
                  onPress={() => props.setShow(true)}
                >
                  <Text style={styles.flag_new}>{props.form?.countryFlag}</Text>
                  <Text style={styles.countryCode_new}>{props.form.countryCode}</Text>
                </TouchableOpacity>

                <CustomInput
                  value={props.form.phone}
                  onChangeText={(text) => props.handleChange('phone', text)}
                  placeholder="Enter phone number"
                  keyboardType="phone-pad"
                  errorText={''}
                  label={''}
                  style={{ marginBottom: 0, flex: 1, width: scale(235) }}
                />
              </View>
              { props.formErrors?.phone && (
                <Text style={[styles.errorText,{marginTop:-5,marginBottom:5}]}>{props.formErrors?.phone}</Text>
              )}

              <Text style={styles.label} variant="semiBold">
                Notes
              </Text>
              <PaperTextInput
                value={props.form.notes}
                onChangeText={(text) => props.handleChange('notes', text)}
                mode="outlined"
                multiline
                placeholder="Add notes"
                style={styles.textArea}
                contentStyle={{
                  fontSize: moderateScale(12),
                  fontFamily: fontFamily.regular,
                  textAlignVertical: 'top',
                  paddingVertical: verticalScale(0),
                }}
                outlineColor="#D1D5DB"
                activeOutlineColor="#2563EB"
                placeholderTextColor={'#ccc'}
                scrollEnabled={Platform.OS === 'ios' ? false : true}
              />
            </>
          )}
          {/* </KeyboardAwareScrollView> */}
        </ScrollView>
      </KeyboardAvoidingView>

      <View style={styles.actionRow}>
        <Button
          mode="contained"
          style={[styles.closeButton, mode === 'suspect' && lead && styles.submitButton]}
          textColor={mode === 'suspect' && lead ? '#fff' : '#000'}
          onPress={mode === 'prospect' ? props.handleClose : props.handleLeftAction}
          labelStyle={{ fontSize: moderateScale(12) }}
        >
          {mode === 'prospect' ? 'Close' : lead ? 'Update' : 'Cancel'}
        </Button>
        <Button
          mode="contained"
          style={styles.submitButton}
          textColor="#fff"
          loading={props.isSubmitting}
          disabled={props.isSubmitting}
          labelStyle={{ fontSize: moderateScale(12) }}
          onPress={() => {
            if (mode === 'prospect') {
              props.submitForm();
            } else {
              const modified =
                JSON.stringify(props.form) !== JSON.stringify(props.initialFormState);
              if (!lead) {
                props.submitLeadForm();
              } else {
                if (!modified) {
                  setMode('prospect');
                } else {
                  props.setShowAlert(true);
                }
              }
            }
          }}
        >
          {mode === 'prospect' ? 'Move to Prospect' : lead ? 'Next' : 'Save'}
        </Button>
      </View>

      {mode === 'suspect' && (
        <CustomAlert
          visible={props.showAlert}
          title="Unsaved Changes"
          message="You have modified the form. Do you want to update before proceeding?"
          showCancel={true}
          cancelText="Discard"
          confirmText="Next"
          onCancel={() => props.setShowAlert(false)}
          onConfirm={() => {
            props.setIsSubmitting(true);
            props.submitForm(true);
            props.setShowAlert(false)
          }}
          button2Styles={{ backgroundColor: '#3377FF' }}
        />
      )}
      

      <CountryPicker
        show={props.show}
        style={{
          modal: {
            flex:1,
            marginTop:'50%'
          },
        }}
        pickerButtonOnPress={(item) => {
          props.setShow(false);
          props.handleChange('countryCode', item?.dial_code);
          props.handleChange('countryFlag', item?.flag);
        }}
        onBackdropPress={() => props.setShow(false)}
        lang="en"
        popularCountries={['AE', 'QA', 'OM', 'KW', 'SA', 'BH', 'IN']}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  scrollContent: {
    paddingHorizontal: scale(16),
  },
  sectionLabel: {
    fontFamily: fontFamily.semiBold,
    fontSize: moderateScale(14),
    marginTop: verticalScale(10),
    marginBottom: verticalScale(5),
  },
  asterisk: { color: 'red' },
  errorText: {
    color: '#DC2626',
    fontSize: moderateScale(11),
    marginTop: verticalScale(0),
    marginBottom: verticalScale(8),
    fontFamily: fontFamily.medium,
  },
  input: { flex: 1, height: 48, backgroundColor: 'white' },

  countryCodeBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f3f4f6',
    paddingHorizontal: scale(8),
    paddingVertical: verticalScale(6),
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  flag: { fontSize: moderateScale(16), marginRight: 4 },
  countryCode: { fontSize: moderateScale(13), color: '#111827' },
  phoneRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(8),
  },
  actionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    padding: 16,
    gap: scale(10),
    marginBottom: Platform.OS === 'ios' ? verticalScale(10) : 0,
  },
  closeButton: { flex: 1, borderRadius: 4, backgroundColor: '#E5E7EB' },
  submitButton: { flex: 1, borderRadius: 4, backgroundColor: 'rgba(51, 119, 255, 1)' },
  phoneRow_new: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: scale(8),
    marginBottom: verticalScale(10),
  },
  countryCodeBox_new: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: scale(8),
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    height: verticalScale(28),
  },
  flag_new: {
    fontSize: moderateScale(16),
    marginRight: scale(4),
  },
  countryCode_new: {
    fontSize: moderateScale(13),
    color: '#111827',
  },
  label: {
    fontSize: moderateScale(12),
    marginVertical: 4,
    color: '#111827',
    marginBottom: verticalScale(5),
  },
  textArea: {
    borderRadius: 6,
    backgroundColor: '#fff',
    fontFamily: fontFamily.regular,
    height: verticalScale(100),
  },
});
