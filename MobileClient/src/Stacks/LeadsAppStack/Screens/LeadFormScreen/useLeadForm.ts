import { useEffect, useRef, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { useSelector } from 'react-redux';
import ApiClient from '../../../../Common/API/ApiClient';
import { encryptPayload } from '../../../../Utils/payloadEncryption';
import {
  getAccountId,
  getSchemaId,
  getSchemaName,
  getSelectedWorkspaceId,
  getUserId,
} from '../../../../Common/Utils/Storage';
import { COMMON_ENDPOINTS, LEADS_ENDPOINTS } from '../../../../Common/API/ApiEndpoints';
import { showToast } from '../../../../Components/AppToaster/AppToaster';
import { RootState } from '../../../../State/Store';
import { countryCodes } from '../../../../../node_modules/react-native-country-codes-picker/constants/countryCodes';

export type MasterOption = { Text: string; Value: string };
type ProspectType = {
  prospect_type_id: number;
  prospect_type_name: string;
  prospect_type_description: string;
};
type JobType = { Text: string; Value: string };
type Area = MasterOption;
type ControlUnit = { id: string; controlUnitName: string };
type AssignedUser = { id: string; name: string };

export const useLeadForm = (
  mode: 'prospect' | 'suspect',
  navigation: any,
  leadData: any,
  fromTab: string = '',
  setMode: any,
  setActiveTabName:any
) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const controlUnitId = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedControlUnit?.data?.id
  );

  const [form, setForm] = useState<any>({
    organizationName: '',
    contactName: '',
    email: '',
    industry: null,
    source: null,
    phone: '',
    countryFlag : '🇮🇳',
    countryCode : '+91',
    notes: '',
    externalPerson: '',
    selectedInternalUser: null,
    prospectType: null,
    jobTypes: [],
    controlUnit: null,
    assignedTo: null,
    area: null,
    comments: '',
  });

  const [industryOptions, setIndustryOptions] = useState<MasterOption[]>([]);
  const [sourceOptions, setSourceOptions] = useState<MasterOption[]>([]);
  const [internalUsers, setInternalUsers] = useState<any[]>([]);
  const [formErrors, setFormErrors] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const [showAlert, setShowAlert] = useState(false);
  const initialFormState = useRef<any>(null);
  const [prospectTypes, setProspectTypes] = useState<ProspectType[]>([]);
  const [jobTypes, setJobTypes] = useState<JobType[]>([]);
  const [areaOptions, setAreaOptions] = useState<Area[]>([]);
  const [controlUnits, setControlUnits] = useState<ControlUnit[]>([]);
  const [assignedUsers, setAssignedUsers] = useState<AssignedUser[]>([]);
  const [errors, setErrors] = useState<any>({});
  const [show, setShow] = useState(false);
  const [savedLeadIds, setSavedLeadIds] = useState<{ orgId: string; contactId: string } | null>(null);
  const controlUnitLevelId = useSelector((state: any) => state.appData?.controlUnitLevelId);
  const userDetails = useSelector(
    (state: RootState) => state.UserInfo.userDetailsInRedux,
  );
   const getCountryFlag = (dialCode: string): string => {
      const match = countryCodes?.find((c) => c.dial_code === dialCode);
      return match?.flag || '🌐';
    };

  useEffect(() => {
    setLoading(true);
    fetchMasters();
  }, [mode]);

  useEffect(() => {
    if (!leadData) {
      setLoading(false);
      return;
    }
    const patchForm = async () => {
      if (mode === 'suspect') {
        const internalUser =
          internalUsers.find(
            (user) =>
              user.id === leadData.personCategoryText || user.name === leadData.personCategoryText
          ) || null;
        const mappedForm = {
          ...form,
          organizationName: leadData.organizationName || '',
          contactName: leadData.contactName || '',
          email: leadData.email || '',
          industry: industryOptions.find((i) => i.Text === leadData.industryName) || null,
          source: sourceOptions.find((i) => i.Text === leadData.sourceName) || null,
          phone: leadData.phoneNo?.split(' ')[1]|| '',
          notes: leadData.notes || '',
          externalPerson: leadData.sourceName === 'External' ? leadData.personCategoryText : '',
          selectedInternalUser: leadData.sourceName === 'Internal' ? internalUser : null,
          emailValidation:true,
          countryCode:leadData.phoneNo?.split(' ')[0]|| '+91',
          countryFlag: leadData.phoneNo?.split(' ')[0] ? getCountryFlag(leadData.phoneNo?.split(' ')[0]) : '🇮🇳',
        };
        setForm(mappedForm);
        initialFormState.current = mappedForm;
      } else {
        const userId = await getUserId();
        const internalUser =
          internalUsers.find(
            (user) =>
              user.id === leadData.personCategoryText || user.name === leadData.personCategoryText
          ) || null;
        const mappedForm = {
          ...form,
          organizationName: leadData.organizationName || '',
          contactName: leadData.contactName || '',
          email: leadData.email || '',
          industry: industryOptions.find((i) => i.Text === leadData.industryName) || null,
          source: sourceOptions.find((i) => i.Text === leadData.sourceName) || null,
          phone: leadData.phoneNo?.replace('+91', '') || '',
          notes: leadData.notes || '',
          externalPerson: leadData.sourceName === 'External' ? leadData.contactName : '',
          selectedInternalUser: leadData.sourceName === 'Internal' ? internalUser : null,
        };
        setForm(mappedForm);
        initialFormState.current = mappedForm;
        setForm((prev: any) => ({
          ...prev,
          controlUnit: controlUnits.find((cu) => cu.id === leadData.controlUnitId) || null,
          assignedTo: 
          form.assignedTo ? form.assignedTo :
            assignedUsers.find((u) =>  u.id === leadData.assignedTo  || u.id === userId) || null,
          area: areaOptions.find((a) => a.Value === leadData.area) || null,
          comments: leadData.comments === 'null' ? '' : leadData.comments || '',
        }));
      }
    };
    if (
      ( 
        industryOptions.length &&
        sourceOptions.length &&
        internalUsers.length)
    ) {
      patchForm();
    }
  }, [industryOptions, sourceOptions, internalUsers, controlUnits, assignedUsers, areaOptions]);

  const fetchMasters = async () => {
    try {
      const schemaName = await getSchemaName();
      const userId = await getUserId();
      const schemaId = await getSchemaId();
      const [industryRes, sourceRes, internalUserRes] = await Promise.all([
        ApiClient.get(
          `getMastersDropDown/${schemaName}/crm_industry_master?columns=industry_id,industry_name`
        ),
        ApiClient.get(
          `getMastersDropDown/${schemaName}/crm_source_master?columns=source_id,source_name`
        ),
        ApiClient.post(COMMON_ENDPOINTS.SCHEMA_USERS, { schemaName, schemaId }),
      ]);
      setIndustryOptions(industryRes.data || []);
      setSourceOptions(sourceRes.data || []);
      const [areaRes, prospectRes, jobRes, controlUnitRes] = await Promise.all([
        ApiClient.get(`getMastersDropDown/${schemaName}/crm_area?columns=area_id,area_name`),
        ApiClient.get(
          `getMastersDropDown/${schemaName}/crm_prospect_type_master?columns=prospect_type_id,prospect_type_name,prospect_type_description`
        ),
        ApiClient.get(
          `getMastersDropDown/${schemaName}/crm_job_type_master?columns=job_type_id,job_type_name`
        ),
        ApiClient.post(COMMON_ENDPOINTS.CONTROL_UNIT, { schemaName, controlUnitLevelId, userId }),
      ]);
      setAreaOptions(areaRes.data || []);
      setProspectTypes(prospectRes.data || []);
      setJobTypes(jobRes.data || []);
      setControlUnits(controlUnitRes.data?.data || []);
      const modifiedInternalUserRes = internalUserRes.data?.data?.map((user) => {
        return user.id === userDetails.sub ? {...user,label:`${user.name} (You)`} : {...user,label:user.name}
      })
      setInternalUsers(modifiedInternalUserRes|| []);
      setAssignedUsers(modifiedInternalUserRes|| []);
    } catch (err) {
      console.log('Error loading masters:', err);
      showToast.error('Error loading masters');
    } finally {
      setLoading(false);
      setIsSubmitting(false);
    }
  };

  const handleChange = (key: string, value: any) => {
    setForm((prev: any) => ({ ...prev, [key]: value }));

    setFormErrors((prev: any) => ({
      ...prev,
      [key]: undefined
    }));

    if (mode === 'prospect') {
      setErrors((prev: any) => ({ ...prev, [key]: undefined }));
    }
  };

  const toggleJobType = (item: JobType) => {
    const exists = form.jobTypes.find((j: any) => j.Value === item.Value);
    const updated = exists
      ? form.jobTypes.filter((j: any) => j.Value !== item.Value)
      : [...form.jobTypes, item];
    setForm((prev: any) => ({ ...prev, jobTypes: updated }));
    setErrors((prev: any) => ({ ...prev, jobTypes: undefined }));
  };

  const validateProspectForm = () => {
    const newErrors: any = {};
    if (!form.prospectType) newErrors.prospectType = 'Prospect type is required.';
    if (form.jobTypes.length === 0) newErrors.jobTypes = 'At least one job type must be selected.';
    if (!form.controlUnit) newErrors.controlUnit = 'Control unit is required.';
    if (!form.assignedTo) newErrors.assignedTo = 'Assigned user is required.';
    if (!form.area) newErrors.area = 'Area is required.';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateSuspectForm = () => {
    const errors: any = {};
    if (!form.organizationName.trim()) errors.organizationName = 'Organization name is required';
    if (!form.contactName.trim() && form.source?.Text !== 'Internal')
      errors.contactName = 'Contact name is required';
    if (!form.source) errors.source = 'Source is required';
    if (!form.email.trim()) errors.email = 'Email is required';
    else if (!form.emailValidation) errors.email = 'Invalid email format';
    if (!form.phone.trim()) {
      errors.phone = 'Phone number is required';
    }
    if (form.source?.Text === 'External' && !form.externalPerson.trim())
      errors.externalPerson = 'External person name is required';
    if (form.source?.Text === 'Internal' && !form.selectedInternalUser)
      errors.selectedInternalUser = 'Internal user selection is required';
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const submitForm = async (toProspect = false) => {
    const isValid = mode === 'prospect' ? validateProspectForm() : validateSuspectForm();
    const orgId = savedLeadIds?.orgId ?? leadData?.orgId ?? uuidv4();
    const contactId = savedLeadIds?.contactId ?? leadData?.contactId ?? uuidv4();
    if (!isValid) {
      setIsSubmitting(false)
      return
    };
    setIsSubmitting(true);
    try {
      const [accountId, workspaceId, userId, schemaName] = await Promise.all([
        getAccountId(),
        getSelectedWorkspaceId(),
        getUserId(),
        getSchemaName(),
      ]);
      const payload: any = {
        org_id: orgId,
        contact_id: contactId,
        organization_name: form.organizationName ?? leadData?.organizationName,
        contact_name: form.contactName ?? leadData?.contactName,
        email: form.email ?? leadData?.email,
        phone_no: form.phone ? `${form.countryCode} ${form.phone}` : leadData?.phoneNo,
        account_id: accountId,
        workspace_id: workspaceId,
        control_unit_id: leadData ? form.controlUnit?.id ?? leadData?.controlUnitId : controlUnitId,
        updated_by: leadData ? userId : null,
        created_by: leadData?.createdBy ?? userId,
        org_status: 1,
        lead_id: mode === 'prospect' ? 2 : 1,
        
        
        
        person_category_text:
          form.source?.Text === 'Internal'
            ? form.selectedInternalUser?.id
            : form.externalPerson ?? leadData?.personCategoryText,

        schemaName,
      };

      if (mode === 'suspect') {
        payload.source_id = form.source?.Value ?? null;
        if(form.industry?.Value){payload.industry_id = form.industry?.Value ?? null}
        if(form.notes){payload.notes = form.notes ?? leadData?.notes}
      } 
      if(mode === 'prospect'){
        payload.area = form.area?.Value ?? null
        payload.assigned_to = form.assignedTo?.id ?? null
         if(form.comments){ payload.comments = leadData ? form.comments ?? '' : null}
        payload.job_type_json = 
        form.jobTypes.length > 0
        ? form.jobTypes.map((j: any) => ({ id: j.Value, job_type_name: j.Text }))
        : null
        payload.prospect_type_id = form.prospectType?.prospect_type_id?.toString() ?? null
      }
      const encrypted = await encryptPayload(JSON.stringify(payload));
      const message = await ApiClient.post(LEADS_ENDPOINTS.CREATE_LEAD, encrypted);
      // if (message?.data?.message === 'Lead created successfully') {
      if (message?.data?.statusCode === 200 || message?.data?.statusCode === 201) {
          const newOrgId = payload.org_id;
          const newContactId = payload.contact_id;
          setSavedLeadIds({ orgId: newOrgId, contactId: newContactId });
        if (leadData || mode === "prospect") {
            if (toProspect) {
               setForm((prev: any) => ({
                ...prev,
                controlUnit: controlUnits.find((c) => c.id === payload.control_unit_id) || null,
                assignedTo: assignedUsers.find((u) => u.id === payload.person_category_text) || null,
              }));
              setMode('prospect');
            } else if(mode !== "prospect"){
              showToast.success('Suspect updated successfully');
              setIsSubmitting(false);
              navigation.goBack();
            } else {
              showToast.success('Moved To Prospect');
              setIsSubmitting(false);
              navigation.goBack();
            }
        } else {
            showToast.success('Suspect Created Successfully');
             setForm((prev: any) => ({
                ...prev,
                controlUnit: controlUnits.find((c) => c.id === payload.control_unit_id) || null,
                assignedTo: assignedUsers.find((u) => u.id === payload.person_category_text) || null,
              }));
            setMode('prospect');
        }
      } else {
            showToast.error('Submission failed!');
      }
    } catch (err) {
      showToast.error('Submission failed!');
    }
  };

  const handleLeftAction = () => {
    const modified = JSON.stringify(form) !== JSON.stringify(initialFormState);
    if (leadData) {
      submitForm();
    } else {
      navigation.goBack();
    }
  };

  const handleNextAction = () => {
    if (!leadData) {
      submitForm();
      return;
    }
    const modified = JSON.stringify(form) !== JSON.stringify(initialFormState.current);
    if (!modified) navigation.navigate('LeadForm', { lead: leadData, fromTab });
    else setShowAlert(true);
  };



  return {
    mode,
    form,
    handleChange,
    toggleJobType,
    isSubmitting,
    loading,
    submitForm,
    controlUnits,
    assignedUsers,
    areaOptions,
    jobTypes,
    prospectTypes,
    industryOptions,
    sourceOptions,
    internalUsers,
    errors,
    formErrors,
    showAlert,
    setShowAlert,
    lead: leadData,
    handleLeftAction,
    handleNextAction,
    handleClose: () => navigation.goBack(),
    submitLeadForm: submitForm,
    initialFormState: initialFormState.current,
    setIsSubmitting,
    show,
    setShow,
    userDetails,
  };
};
