import React, { useEffect, useState, useCallback } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, StatusBar } from 'react-native';
import { ActivityIndicator, Card, Chip, Divider, Text, } from 'react-native-paper';
import { moderateScale, verticalScale, scale } from 'react-native-size-matters';
import { fontFamily } from '../../../../Common/Theme/typography';
import ApiClient from '../../../../Common/API/ApiClient';
import { encryptPayload } from '../../../../Utils/payloadEncryption';
import { getSchemaName, getSelectedWorkspaceId } from '../../../../Common/Utils/Storage';
import { RouteProp, useFocusEffect, useNavigation } from '@react-navigation/native';
import AppHeader from '../../../../Components/AppHeader';
import { Pencil, Trash2, TrendingUp, MapPin, DollarSign, Package, Clock, CheckCircle } from 'lucide-react-native';
import CustomAlert from '../../../../Components/CustomAlert/CustomAlert';

type RootStackParamList = {
  CompetitiveIntelligenceDetail: { competitorId: string; orgId: string };
  EditCompetitiveIntelligence: { competitorId: string; orgId: string; detail: any };
};

interface Props {
  route: RouteProp<RootStackParamList, 'CompetitiveIntelligenceDetail'>;
}

const CompetitiveIntelligenceDetail = ({ route }: Props) => {
  const { competitorId, orgId } = route.params;
  const [schemaName, setSchemaName] = useState('');
  const [workspaceId, setWorkspaceId] = useState('');
  const [loading, setLoading] = useState(true);
  const [detail, setDetail] = useState<any>(null);

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');

  const navigation = useNavigation<any>();

  const fetchDetail = useCallback(async () => {
    if (!schemaName || !workspaceId) return;

    try {
      if (!detail) setLoading(true);
      setLoading(true);
      const payload = {
        schemaName,
        metaData: { workspace_id: workspaceId },
        data: { org_id: orgId, competitor_id: competitorId },
      };
      const encryptedPayload = await encryptPayload(JSON.stringify(payload));
      const response = await ApiClient.post('/getCompetitiveIntelligenceDetail', encryptedPayload);

      if (response?.status === 200) {
        const data = response?.data?.data?.[0]?.result?.data;
        setDetail(data);
      }
    } catch (err) {
      setErrorMsg('Failed to fetch details');
    } finally {
      setLoading(false);
    }
  }, [schemaName, workspaceId, competitorId, orgId]);

  useEffect(() => {
    const loadCommonData = async () => {
      const sName = (await getSchemaName()) ?? '';
      const wId = (await getSelectedWorkspaceId()) ?? '';
      setSchemaName(sName);
      setWorkspaceId(wId);
    };
    loadCommonData();
  }, []);

  useFocusEffect(
    useCallback(() => {
      if (schemaName && workspaceId) {
        fetchDetail();
      }
    }, [schemaName, workspaceId, competitorId, orgId])
  );

  const handleEdit = () => {
    navigation.navigate('CompetitiveIntelligenceForm', {
      competitorId,
      orgId,
      detail,
    });
  };

  const handleDelete = () => {
    setShowDeleteModal(true);
  };

  const onCancelDelete = () => {
    setShowDeleteModal(false);
  };

  const onConfirmDelete = async () => {
    setShowDeleteModal(false);
    try {
      const payload = {
        schemaName,
        metaData: { workspace_id: workspaceId },
        data: { competitor_id: competitorId, org_id: orgId },
      };
      const encryptedPayload = await encryptPayload(JSON.stringify(payload));
      const response = await ApiClient.post(
        '/deleteCompetitiveIntelligence',
        encryptedPayload
      );

      if (response?.status === 200) {
        setShowSuccessModal(true);
      } else {
        setErrorMsg('Failed to delete the record');
      }
    } catch (err) {
      setErrorMsg('Error occurred while deleting');
    }
  };

  const closeSuccessModal = () => {
    setShowSuccessModal(false);
    navigation.goBack();
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2563EB" />
        <Text style={styles.loadingText}>Loading details...</Text>
      </View>
    );
  }

  if (!detail) {
    return (
      <View style={styles.emptyContainer}>
        <Package size={64} color="#9CA3AF" />
        <Text style={styles.emptyTitle}>No Details Found</Text>
        <Text style={styles.emptySubtitle}>Unable to load competitor information</Text>
      </View>
    );
  }

  const InfoRow = ({ icon, label, value, accent = false }: { 
    icon: React.ReactNode; 
    label: string; 
    value: string; 
    accent?: boolean;
  }) => (
    <View style={styles.infoRow}>
      <View style={styles.infoLeft}>
        <View style={[styles.iconContainer, accent && styles.accentIconContainer]}>
          {icon}
        </View>
        <Text style={styles.infoLabel} variant='medium'>{label}</Text>
      </View>
      <Text style={[styles.infoValue, accent && styles.accentValue]} variant='medium'>{value}</Text>
    </View>
  );

  const MetricCard = ({ icon, title, value, subtitle, color }: {
    icon: React.ReactNode;
    title: string;
    value: string;
    subtitle?: string;
    color: string;
  }) => (
    <View style={[styles.metricCard, { borderLeftColor: color }]}>
      <View style={styles.metricHeader}>
        <View style={[styles.metricIcon, { backgroundColor: color + '20' }]}>
          {React.cloneElement(icon as React.ReactElement, { color, size: moderateScale(18) })}
        </View>
        <Text style={styles.metricTitle} variant='medium'>{title}</Text>
      </View>
      <Text style={styles.metricValue} variant='semiBold'>{value}</Text>
      {subtitle && <Text style={styles.metricSubtitle} variant='medium'>{subtitle}</Text>}
    </View>
  );

  return (
      <View style={styles.container}>
        <AppHeader
          title="Competitive Intelligence"
          menuVisible={false}
          showMenu={false}
          showPeopleHirerachy={false}
          showControlUnit={false}
          rightElement={
            <View style={styles.actionContainer}>
              <TouchableOpacity onPress={handleEdit} style={[styles.actionBtn, styles.editBtn]}>
                <Pencil size={moderateScale(18)} color="#2563EB" />
              </TouchableOpacity>
              <TouchableOpacity onPress={handleDelete} style={[styles.actionBtn, styles.deleteBtn]}>
                <Trash2 size={moderateScale(18)} color="#EF4444" />
              </TouchableOpacity>
            </View>
          }
        />

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false} bounces={false}>
          {/* Header Card with Company Name and Status */}
          <Card style={styles.headerCard}>
            <Card.Content style={styles.headerContent}>
              <View style={styles.headerTop}>
                <Text style={styles.competitorName} variant={'semiBold'} >{detail.competitor_name}</Text>
                <Text style={[styles.statusText,styles.activeStatusText]} variant='medium'>{detail.status_id === 1 ? 'Active' : 'Inactive'}</Text>
              </View>
              <Text style={styles.jobTypeText} variant='medium'>{detail.job_type_name || 'N/A'}</Text>
            </Card.Content>
          </Card>

          {/* Key Metrics Row */}
          <View style={styles.metricsRow}>
            <MetricCard
              icon={<TrendingUp />}
              title="Market Share"
              value={`${detail.competitor_market_share_percent}%`}
              color="#059669"
            />
            <MetricCard
              icon={<Package />}
              title="Weekly Capacity"
              value={String(detail.capacity_teu_per_week)}
              subtitle="TEUs"
              color="#2563EB"
            />
          </View>

          {/* Basic Information */}
          <Card style={styles.infoCard}>
            <Card.Content>
              <Text style={styles.sectionTitle} variant='semiBold'>Basic Information</Text>
              <InfoRow
                icon={<MapPin size={moderateScale(16)} color="#6B7280" />}
                label="Geographical Coverage"
                value={detail.crm_geographical_coverage_master?.name || 'N/A'}
              />
            </Card.Content>
          </Card>

          {/* Pricing & Operations */}
          <Card style={styles.infoCard}>
            <Card.Content>
              <Text style={styles.sectionTitle} variant='semiBold'>Pricing & Operations</Text>
              <InfoRow
                icon={<TrendingUp size={moderateScale(16)} color="#6B7280" />}
                label="Pricing Strategy"
                value={detail.crm_pricing_strategy_master?.name || 'N/A'}
              />
              <Divider style={styles.divider} />
              <InfoRow
                icon={<DollarSign size={moderateScale(16)} color="#EF4444" />}
                label="Cost of Transport"
                value={`$${detail.cost_of_transport}`}
                accent={true}
              />
            </Card.Content>
          </Card>

          {/* System Information */}
          <Card style={styles.infoCard}>
            <Card.Content>
              <Text style={styles.sectionTitle} variant='semiBold'>System Information</Text>
              <InfoRow
                icon={<Clock size={moderateScale(15)} color="#6B7280" />}
                label="Created At"
                value={new Date(detail.created_at).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric'
                })}
              />
              <Divider style={styles.divider} />
              <InfoRow
                icon={<Clock size={moderateScale(16)} color="#6B7280" />}
                label="Last Updated"
                value={new Date(detail.updated_at).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric'
                })}
              />
            </Card.Content>
          </Card>

          <View style={styles.bottomPadding} />
        </ScrollView>

        {/* Delete Confirmation */}
        <CustomAlert
          visible={showDeleteModal}
          title="Delete Record?"
          message="Are you sure you want to delete this record? This action cannot be undone."
          confirmText="Delete"
          cancelText="Cancel"
          showCancel={true}
          onCancel={onCancelDelete}
          onConfirm={onConfirmDelete}
          button2Styles={{ backgroundColor: '#dc2626' }}
        />

        {/* Success Modal */}
        <CustomAlert
          visible={showSuccessModal}
          title="Success"
          message="Record deleted successfully."
          confirmText="OK"
          showCancel={false}
          onConfirm={closeSuccessModal}
        />

        {/* Error Modal */}
        <CustomAlert
          visible={!!errorMsg}
          title="Error"
          message={errorMsg}
          confirmText="OK"
          showCancel={false}
          onConfirm={() => setErrorMsg('')}
        />
      </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: '#FFFFFF',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: moderateScale(16),
  },
  actionContainer: {
    flexDirection: 'row',
  },
  actionBtn: {
    width: moderateScale(30),
    height: moderateScale(30),
    borderRadius: moderateScale(20),
    alignItems: 'center',
    justifyContent: 'center',
  },
  editBtn: {
    // backgroundColor: '#EBF4FF',
  },
  deleteBtn: {
    // backgroundColor: '#FEF2F2',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8FAFC',
  },
  loadingText: {
    marginTop: verticalScale(12),
    fontSize: scale(16),
    color: '#6B7280',
    fontFamily: fontFamily.medium,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8FAFC',
    paddingHorizontal: moderateScale(32),
  },
  emptyTitle: {
    fontSize: scale(18),
    fontFamily: fontFamily.bold,
    color: '#374151',
    marginTop: verticalScale(16),
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: scale(14),
    fontFamily: fontFamily.regular,
    color: '#6B7280',
    marginTop: verticalScale(8),
    textAlign: 'center',
  },
  headerCard: {
    marginTop: verticalScale(16),
    borderRadius: moderateScale(16),
    elevation: 2,
    backgroundColor: '#FFFFFF',
  },
  headerContent: {
    paddingVertical: verticalScale(15),
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  competitorName: {
    fontSize: scale(15),
    color: '#111827',
    flex: 1,
    marginRight: moderateScale(12),
  },
  statusChip: {
    backgroundColor: '#F3F4F6',
    borderColor: '#D1D5DB',
  },
  activeChip: {
    backgroundColor: '#ECFDF5',
    borderColor: '#10B981',
  },
  statusText: {
    fontSize: scale(11),
    color: '#6B7280',
  },
  activeStatusText: {
    color: '#059669',
  },
  jobTypeText: {
    fontSize: scale(12),
    color: '#6B7280',
  },
  metricsRow: {
    flexDirection: 'row',
    gap: moderateScale(12),
    marginTop: verticalScale(10),
  },
  metricCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: moderateScale(12),
    padding: moderateScale(16),
    borderLeftWidth: moderateScale(4),
    elevation: 2,
    paddingVertical:verticalScale(10)
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(4),
  },
  metricIcon: {
    width: moderateScale(20),
    height: moderateScale(20),
    borderRadius: moderateScale(16),
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: moderateScale(5),
  },
  metricTitle: {
    fontSize: scale(12),
    color: '#6B7280',
    flex: 1,
  },
  metricValue: {
    fontSize: scale(15),
    color: '#111827',
    marginBottom: verticalScale(2),
  },
  metricSubtitle: {
    fontSize: scale(11),
    color: '#9CA3AF',
  },
  infoCard: {
    marginTop: verticalScale(10),
    borderRadius: moderateScale(16),
    elevation: 2,
    backgroundColor: '#FFFFFF',
  },
  sectionTitle: {
    fontSize: scale(13),
    color: '#111827',
    marginBottom: verticalScale(16),
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  infoLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: moderateScale(22),
    height: moderateScale(22),
    borderRadius: moderateScale(16),
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: moderateScale(5),
  },
  accentIconContainer: {
    backgroundColor: '#FEF2F2',
  },
  infoLabel: {
    fontSize: scale(12),
    color: '#374151',
    flex: 1,
  },
  infoValue: {
    fontSize: scale(12),
    color: '#111827',
    textAlign: 'right',
  },
  accentValue: {
    color: '#EF4444',
  },
  divider: {
    backgroundColor: '#F3F4F6',
    height: 1,
    marginVertical: verticalScale(5),
  },
  bottomPadding: {
    height: verticalScale(32),
  },
});

export default CompetitiveIntelligenceDetail;
