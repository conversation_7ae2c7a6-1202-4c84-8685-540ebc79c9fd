import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  findNodeHandle,
} from 'react-native';
import { useCompetitiveIntelligenceForm } from './useCompetitiveIntelligenceForm';
import AppHeader from '../../../../Components/AppHeader';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { fontFamily } from '../../../../Common/Theme/typography';
import { Button, ActivityIndicator, Card } from 'react-native-paper';
import CustomInput from '../../../../Components/UI/TextInput';
import { DropdownField } from '../../../../Components/UI/Menu/DropdownModal';
import { Plus, Trash2 } from 'lucide-react-native';

interface DropdownOption {
  Value: string | number;
  Text: string;
}

export const CompetitiveIntelligenceForm: React.FC<{ route?: any }> = ({ route }) => {
  const {
    form,
    handleChange,
    handleRouteChange,
    removeRoute,
    serviceOfferings,
    geographicCoverage,
    pricingStrategies,
    addRoute,
    submitForm,
    resetForm,
    loading,
  } = useCompetitiveIntelligenceForm(route?.params);
  const isEdit = !!route?.params?.detail;
  const scrollViewRef = React.useRef<ScrollView>(null);
  const fieldRefs = React.useRef<Record<string, View | null>>({});

  if (loading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="small" />
      </View>
    );
  }

  const scrollToError = (firstErrorKey: string) => {
    const ref = fieldRefs.current[firstErrorKey];
    if (ref && scrollViewRef.current) {
      const scrollResponder = scrollViewRef.current.getScrollResponder();
      const nodeHandle = findNodeHandle(ref);

      if (nodeHandle && scrollResponder) {
        scrollResponder.scrollResponderScrollNativeHandleToKeyboard(
          nodeHandle,
          100,
          true
        );
      }
    }
  };

  const handleSubmit = async () => {
    const result = await submitForm();
    if (!result?.success && result?.firstErrorKey) {
      scrollToError(result.firstErrorKey);
    }
  };

  const renderBlockHeader = (title: string, onPress: () => void, isMandatory?: boolean) => (
    <View style={styles.blockHeader}>
      <Text style={styles.sectionTitle}>
        {title}
        {isMandatory ? <Text style={{ color: 'red' }}> *</Text> : ''}
      </Text>
      <TouchableOpacity onPress={onPress}>
        <Plus size={18} color="#2563EB" />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#fff' }}>
      {/* Header */}
      <AppHeader
       title={isEdit ? "Edit Competitive Intelligence" : "Create Competitive Intelligence"}
        customHeaderStyles={{ backgroundColor: '#fff' }}
      />

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          ref={scrollViewRef}
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={styles.container}
          showsVerticalScrollIndicator={false}
          bounces={false}
        >
          {/* Basic Info */}
          <Text style={[styles.sectionTitle]}>
            Basic Info
          </Text>
          <View ref={(ref) => (fieldRefs.current['competitor_name'] = ref)}>
            <CustomInput
              label="Competitor Name"
              value={form.competitor_name}
              onChangeText={(text: string) => handleChange('competitor_name', text)}
              placeholder="Enter competitor name"
              isMandatory
              errorText={form.errors?.competitor_name}
            />
          </View>

          <View ref={(ref) => (fieldRefs.current['market_share'] = ref)}>
            <CustomInput
              label="Market Share (%)"
              value={form.market_share}
              onChangeText={(text: string) => handleChange('market_share', text)}
              placeholder="e.g., 25.5"
              keyboardType="numeric"
              isMandatory
              errorText={form.errors?.market_share}
            />
          </View>

          <View ref={(ref) => (fieldRefs.current['service_offerings'] = ref)}>
            <DropdownField
              label="Service Offerings"
              value={form.service_offerings?.Text}
              data={serviceOfferings}
              keyField="Value"
              valueField="Text"
              onSelect={(item: DropdownOption) => handleChange('service_offerings', item)}
              placeholder="Select service offerings"
              showAsterisk
              errorText={form.errors?.service_offerings}
            />
          </View>

          <View ref={(ref) => (fieldRefs.current['geographic_coverage'] = ref)}>
            <DropdownField
              label="Geographic Coverage"
              value={form.geographic_coverage?.Text}
              data={geographicCoverage}
              keyField="Value"
              valueField="Text"
              onSelect={(item: DropdownOption) => handleChange('geographic_coverage', item)}
              placeholder="Select geographic coverage"
              showAsterisk
              errorText={form.errors?.geographic_coverage}
            />
          </View>

          {/* Pricing & Capacity */}
          <Text style={[styles.sectionTitle, { marginTop: verticalScale(10) }]}>
            Pricing & Capacity
          </Text>

          <View ref={(ref) => (fieldRefs.current['pricing_strategy'] = ref)}>
            <DropdownField
              label="Pricing Strategies"
              value={form.pricing_strategy?.Text}
              data={pricingStrategies}
              keyField="Value"
              valueField="Text"
              onSelect={(item: DropdownOption) => handleChange('pricing_strategy', item)}
              placeholder="Select pricing strategies"
              showAsterisk
              errorText={form.errors?.pricing_strategy}
            />
          </View>

          <View ref={(ref) => (fieldRefs.current['capacity'] = ref)}>
            <CustomInput
              label="Capacity (TEUs per week)"
              value={form.capacity}
              onChangeText={(text: string) => handleChange('capacity', text)}
              placeholder="Enter capacity"
              keyboardType="numeric"
              isMandatory
              errorText={form.errors?.capacity}
            />
          </View>

          <View ref={(ref) => (fieldRefs.current['cost_of_transport'] = ref)}>
            <CustomInput
              label="Cost of Transport ($)"
              value={form.cost_of_transport}
              onChangeText={(text: string) => handleChange('cost_of_transport', text)}
              placeholder="Enter cost of transport"
              keyboardType="numeric"
              isMandatory
              errorText={form.errors?.cost_of_transport}
            />
          </View>

          {/* Routes */}
          {renderBlockHeader('Route Performance & Transit Time', () => addRoute(), false)}
          {form.routes.map((routeItem, idx) => (
            <Card key={idx} style={styles.routeBox}>
              <View ref={(ref) => (fieldRefs.current[`route_${idx}_route_name`] = ref)}>
                <CustomInput
                  label="Route Name"
                  value={routeItem.route_name}
                  onChangeText={(text: string) => handleRouteChange(idx, 'route_name', text)}
                  placeholder="Enter route name"
                  isMandatory
                  errorText={form.errors?.[`route_${idx}_route_name`]}
                />
              </View>

              <View ref={(ref) => (fieldRefs.current[`route_${idx}_transit_time`] = ref)}>
                <CustomInput
                  label="Transit Time"
                  value={routeItem.transit_time}
                  onChangeText={(text: string) => handleRouteChange(idx, 'transit_time', text)}
                  placeholder="e.g., 5 days"
                  isMandatory
                  errorText={form.errors?.[`route_${idx}_transit_time`]}
                />
              </View>

              <View>
                <CustomInput
                  label="Origin"
                  value={routeItem.origin ?? ''}
                  onChangeText={(text: string) => handleRouteChange(idx, 'origin', text)}
                  placeholder="Search ports..."
                />
              </View>

              <View>
                <CustomInput
                  label="Destination"
                  value={routeItem.destination ?? ''}
                  onChangeText={(text: string) => handleRouteChange(idx, 'destination', text)}
                  placeholder="Search ports..."
                />
              </View>

              {idx > 0 && (
                <TouchableOpacity style={styles.deleteIcon} onPress={() => removeRoute(idx)}>
                  <Trash2 size={18} color="#DC2626" />
                </TouchableOpacity>
              )}
            </Card>
          ))}
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Action Buttons */}
      <SafeAreaView>
        <View style={styles.actionRow}>
          <Button mode="contained" style={styles.cancelButton} textColor="#000" onPress={resetForm}>
            Reset
          </Button>
          <Button
            mode="contained"
            style={styles.saveButton}
            textColor="#fff"
            onPress={handleSubmit}
          >
            {isEdit ? "Update" : "Create"}
          </Button>
        </View>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  loaderContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  container: { padding: moderateScale(16) },
  sectionTitle: {
    fontSize: scale(16),
    fontFamily: fontFamily.medium,
    marginBottom: verticalScale(10),
  },
  blockHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: verticalScale(10),
    marginBottom: verticalScale(10),
  },
  routeBox: {
    marginBottom: verticalScale(15),
    padding: moderateScale(12),
    borderRadius: 10,
    elevation: 2,
    backgroundColor: '#fff',
  },
  deleteIcon: { position: 'absolute', top: 10, right: 10 },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: moderateScale(16),
  },
  cancelButton: {
    flex: 1,
    marginRight: moderateScale(8),
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  saveButton: {
    flex: 1,
    marginLeft: moderateScale(8),
    borderRadius: 8,
    backgroundColor: '#2563EB',
  },
});
