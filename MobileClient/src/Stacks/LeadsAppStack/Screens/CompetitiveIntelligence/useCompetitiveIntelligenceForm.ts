import { useEffect, useState } from 'react';
import {
  getAccountId,
  getSchemaId,
  getSchemaName,
  getSelectedWorkspaceId,
  getUserId,
} from '../../../../Common/Utils/Storage';
import { encryptPayload } from '../../../../Utils/payloadEncryption';
import ApiClient from '../../../../Common/API/ApiClient';
import { useNavigation } from '@react-navigation/native';
import { showToast } from '../../../../Components/AppToaster/AppToaster';
import { v4 as uuidv4 } from 'uuid';
import { useSelector } from 'react-redux';
import { LEADS_ENDPOINTS } from '../../../../Common/API/ApiEndpoints';

export interface DropdownOption {
  Value: number;
  Text: string;
}

export interface RouteItem {
  route_name: string;
  transit_time: string;
  origin: string | null;
  destination: string | null;
}

export interface FormErrors {
  [key: string]: string | undefined;
}

export interface CompetitiveIntelligenceFormType {
  competitor_name: string;
  market_share: string;
  service_offerings: DropdownOption | null;
  geographic_coverage: DropdownOption | null;
  pricing_strategy: DropdownOption | null;
  capacity: string;
  cost_of_transport: string;
  routes: RouteItem[];
  errors: FormErrors;
}

const emptyForm: CompetitiveIntelligenceFormType = {
  competitor_name: '',
  market_share: '',
  service_offerings: null,
  geographic_coverage: null,
  pricing_strategy: null,
  capacity: '',
  cost_of_transport: '',
  routes: [{ route_name: '', transit_time: '', origin: '', destination: '' }],
  errors: {},
};

export const useCompetitiveIntelligenceForm = (params?: { orgId?: string; detail?: any }) => {
  const navigation = useNavigation<any>();
  const controlUnitId = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedControlUnit?.data?.id
  );

  const [form, setForm] = useState<CompetitiveIntelligenceFormType>({ ...emptyForm });
  const [serviceOfferings, setServiceOfferings] = useState<DropdownOption[]>([]);
  const [geographicCoverage, setGeographicCoverage] = useState<DropdownOption[]>([]);
  const [pricingStrategies, setPricingStrategies] = useState<DropdownOption[]>([]);
  const [loading, setLoading] = useState(true);

  const [context, setContext] = useState({
    schemaName: '',
    schemaId: '',
    userId: '',
    accountId: '',
    workspaceId: '',
  });

  const handleChange = (field: keyof CompetitiveIntelligenceFormType, value: any) => {
    setForm((prev) => {
      const updatedErrors = { ...prev.errors };
      if (updatedErrors[field]) delete updatedErrors[field];
      return { ...prev, [field]: value, errors: updatedErrors };
    });
  };

  const handleRouteChange = (idx: number, field: keyof RouteItem, value: string) => {
    const updatedRoutes = [...form.routes];
    updatedRoutes[idx][field] = value;
    setForm((prev) => {
      const updatedErrors = { ...prev.errors };
      const routeErrorKey = `route_${idx}_${field}`;
      if (updatedErrors[routeErrorKey]) delete updatedErrors[routeErrorKey];
      return { ...prev, routes: updatedRoutes, errors: updatedErrors };
    });
  };

  const addRoute = () => {
    setForm((prev) => ({
      ...prev,
      routes: [...prev.routes, { route_name: '', transit_time: '', origin: '', destination: '' }],
    }));
  };

  const removeRoute = (idx: number) => {
    setForm((prev) => ({
      ...prev,
      routes: prev.routes.filter((_, index) => index !== idx),
    }));
  };

  const resetForm = () => setForm({ ...emptyForm });

  const validateForm = (data: CompetitiveIntelligenceFormType): FormErrors => {
    const errors: FormErrors = {};
    if (!data.competitor_name) errors.competitor_name = 'Competitor Name is required';
    if (!data.market_share || Number(data.market_share) <= 0)
      errors.market_share = 'Market Share must be greater than 0';
    if (!data.service_offerings) errors.service_offerings = 'Service Offerings is required';
    if (!data.geographic_coverage) errors.geographic_coverage = 'Geographic Coverage is required';
    if (!data.pricing_strategy) errors.pricing_strategy = 'Pricing Strategy is required';
    if (!data.capacity || Number(data.capacity) <= 0)
      errors.capacity = 'Capacity must be greater than 0';
    if (!data.cost_of_transport || Number(data.cost_of_transport) <= 0)
      errors.cost_of_transport = 'Cost of Transport must be greater than 0';

    data.routes.forEach((route, idx) => {
      if (!route.route_name) errors[`route_${idx}_route_name`] = 'Route Name is required';
      if (!route.transit_time) errors[`route_${idx}_transit_time`] = 'Transit Time is required';
    });

    return errors;
  };

  const submitForm = async () => {
    const errors = validateForm(form);
    if (Object.keys(errors).length > 0) {
      setForm((prev) => ({ ...prev, errors }));
      return { success: false, firstErrorKey: Object.keys(errors)[0] };
    }

    try {
      const isEdit = !!params?.detail;

      const payload: any = {
        schemaName: context.schemaName,
        metaData: {
          account_id: context.accountId,
          workspace_id: context.workspaceId,
          control_unit_id: controlUnitId,
          ...(isEdit ? { updated_by: context.userId } : { created_by: context.userId }),
        },
        data: {
          competitor_id: isEdit ? params.detail.competitor_id : uuidv4(),
          competitor_name: form.competitor_name,
          currency_id: 1,
          org_id: params?.orgId,
          status_id: 1,
          job_type_id: Number(form.service_offerings?.Value),
          competitor_market_share_percent: Number(form.market_share),
          geographical_coverage_id: Number(form.geographic_coverage?.Value),
          pricing_strategy_id: Number(form.pricing_strategy?.Value),
          capacity_teu_per_week: Number(form.capacity),
          cost_of_transport: Number(form.cost_of_transport),
          shipping_routes: form.routes.map((r, idx) => {
            const existing = params?.detail?.shipping_routes?.[idx];
            return {
              shipping_route_id: existing?.shipping_route_id || uuidv4(),
              route_name: r.route_name,
              transit_time: r.transit_time,
              port_of_loading_id: r.origin ? Number(r.origin) : null,
              port_of_destination_id: r.destination ? Number(r.destination) : null,
            };
          }),
          delete_routes: [],
        },
      };

      const encryptedPayload = await encryptPayload(payload);
      const apiEndpoint = isEdit
        ? LEADS_ENDPOINTS.UPDATE_COMPETITIVE_FORM
        : LEADS_ENDPOINTS.CREATE_COMPETITIVE_FORM;

      const response = await ApiClient.post(apiEndpoint, encryptedPayload);

      if (response?.status === 200) {
        showToast.success(
          isEdit
            ? 'Competitive Intelligence updated successfully!'
            : 'Competitive Intelligence created successfully!'
        );
        navigation.goBack();
        return { success: true };
      } else {
        showToast.error(response?.data ?? 'Failed to submit record');
        return { success: false };
      }
    } catch (err) {
      showToast.error('Something went wrong');
      return { success: false };
    }
  };

  const fetchDropdowns = async () => {
    try {
      const [servicesRes, geoRes, pricingRes] = await Promise.all([
        ApiClient.get(
          `/getMastersDropDown/${context.schemaName}/crm_job_type_master?columns=job_type_id,job_type_name`
        ),
        ApiClient.get(
          `/getMastersDropDown/${context.schemaName}/crm_geographical_coverage_master?columns=geographical_coverage_id,geographical_coverage_name`
        ),
        ApiClient.get(
          `/getMastersDropDown/${context.schemaName}/crm_pricing_strategy_master?columns=pricing_strategy_id,pricing_strategy_name`
        ),
      ]);

      setServiceOfferings(
        servicesRes?.data
      );
      setGeographicCoverage(
        geoRes?.data
      );
      setPricingStrategies(
        pricingRes?.data
      );
    } catch (err) {
      showToast.error('Dropdown fetch error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const loadContext = async () => {
      const [schemaName, schemaId, userId, accountId, workspaceId] = await Promise.all([
        getSchemaName(),
        getSchemaId(),
        getUserId(),
        getAccountId(),
        getSelectedWorkspaceId(),
      ]);
      setContext({
        schemaName: schemaName || '',
        schemaId: schemaId || '',
        userId: userId || '',
        accountId: accountId || '',
        workspaceId: workspaceId || '',
      });
    };
    loadContext();
  }, []);

  useEffect(() => {
    if (context.schemaName) fetchDropdowns();
  }, [context.schemaName]);

  useEffect(() => {
    if (params?.detail && !loading) {
      const d = params.detail;
      setForm((prev) => ({
        ...prev,
        competitor_name: d.competitor_name ?? '',
        market_share: d.competitor_market_share_percent?.toString() ?? '',
        capacity: d.capacity_teu_per_week?.toString() ?? '',
        cost_of_transport: d.cost_of_transport?.toString() ?? '',
        routes:
          d.shipping_routes && d.shipping_routes.length > 0
            ? d.shipping_routes.map((r: any) => ({
                route_name: r.route_name ?? '',
                transit_time: r.transit_time ?? '',
                origin: r.port_of_loading_id?.toString() ?? '',
                destination: r.port_of_destination_id?.toString() ?? '',
              }))
            : [{ route_name: '', transit_time: '', origin: '', destination: '' }],
      }));
    }
  }, [params?.detail, loading]);

  useEffect(() => {
    if (params?.detail && !loading) {
      const d = params.detail;
      setForm((prev) => ({
        ...prev,
        service_offerings:
          serviceOfferings.find((s) => s.Value == Number(d.job_type_id)) ||
          (d.job_type_id && d.job_type_name
            ? { Value: Number(d.job_type_id), Text: d.job_type_name }
            : null),
        geographic_coverage:
          geographicCoverage.find((g) => g.Value == Number(d.geographical_coverage_id)) ||
          (d.crm_geographical_coverage_master?.id
            ? {
                Value: Number(d.crm_geographical_coverage_master.id),
                Text: d.crm_geographical_coverage_master.name,
              }
            : null),
        pricing_strategy:
          pricingStrategies.find((p) => p.Value == Number(d.pricing_strategy_id)) ||
          (d.crm_pricing_strategy_master?.id
            ? {
                Value: Number(d.crm_pricing_strategy_master.id),
                Text: d.crm_pricing_strategy_master.name,
              }
            : null),
      }));
    }
  }, [serviceOfferings, geographicCoverage, pricingStrategies, params?.detail, loading]);

  return {
    form,
    handleChange,
    handleRouteChange,
    removeRoute,
    serviceOfferings,
    geographicCoverage,
    pricingStrategies,
    addRoute,
    submitForm,
    resetForm,
    loading,
    context,
  };
};
