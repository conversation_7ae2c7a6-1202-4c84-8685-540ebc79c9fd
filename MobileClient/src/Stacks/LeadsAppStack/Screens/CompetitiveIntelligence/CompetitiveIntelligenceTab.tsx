import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import { ActivityIndicator } from 'react-native-paper';
import { moderateScale, verticalScale, scale } from 'react-native-size-matters';
import { fontFamily } from '../../../../Common/Theme/typography';
import ApiClient from '../../../../Common/API/ApiClient';
import { encryptPayload } from '../../../../Utils/payloadEncryption';
import {
  getSchemaName,
  getSelectedWorkspaceId,
  getAccountId,
} from '../../../../Common/Utils/Storage';
import {
  NavigationProp,
  ParamListBase,
  useFocusEffect,
} from '@react-navigation/native';
import SearchBar from '../../../../Components/SearchInput';
import { Plus } from 'lucide-react-native';

const PAGE_LIMIT = 10;

interface Props {
  orgId: string;
  navigation: NavigationProp<ParamListBase>;
}

const CompetitiveIntelligenceTab = ({ orgId, navigation }: Props) => {
  const [schemaName, setSchemaName] = useState('');
  const [workspaceId, setWorkspaceId] = useState('');
  const [accountId, setAccountId] = useState('');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [list, setList] = useState<any[]>([]);
  const [offset, setOffset] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  const fetchList = useCallback(
    async (reset = false, search = '') => {
      if (!schemaName || !workspaceId || !accountId) return;

      try {
        if (reset) {
          setRefreshing(true);
          setOffset(0);
          setHasMore(true);
        } else {
          setLoading(true);
        }

        const payload = {
          schemaName,
          metaData: { workspace_id: workspaceId, account_id: accountId },
          data: {
            limit: PAGE_LIMIT,
            offset: reset ? 0 : offset,
            search,
            org_id: orgId,
          },
        };

        const encryptedPayload = await encryptPayload(JSON.stringify(payload));
        const response = await ApiClient.post(
          '/getCompetitiveIntelligenceList',
          encryptedPayload
        );

        if (response?.status === 200) {
          const newData = response?.data?.data?.[0]?.result?.data || [];
          const pagination =
            response?.data?.data?.[0]?.result?.pagination || {};
          setHasMore(pagination?.has_more);

          if (reset) {
            setList(newData);
            setOffset(PAGE_LIMIT);
          } else {
            setList((prev) => [...prev, ...newData]);
            setOffset((prev) => prev + PAGE_LIMIT);
          }
        }
      } catch (err) {
        console.error('Fetch list error:', err);
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    },
    [schemaName, workspaceId, accountId, offset, orgId]
  );

  useEffect(() => {
    const loadCommonData = async () => {
      const sName = (await getSchemaName()) ?? '';
      const wId = (await getSelectedWorkspaceId()) ?? '';
      const aId = (await getAccountId()) ?? '';
      setSchemaName(sName);
      setWorkspaceId(wId);
      setAccountId(aId);
    };
    loadCommonData();
  }, []);

  useFocusEffect(
    useCallback(() => {
      if (schemaName && workspaceId && accountId) {
        fetchList(true, searchQuery);
      }
    }, [schemaName, workspaceId, accountId, searchQuery])
  );

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    fetchList(true, text); // refresh list with search filter
  };

  const renderItem = ({ item, index }) => (
  <TouchableOpacity
    style={styles.compactCard}
    onPress={() =>
      navigation.navigate('CompetitiveIntelligenceDetail', {
        competitorId: item.competitor_id,
        orgId: orgId,
      })
    }
  >
    <View style={styles.gradientHeader}>
      <View style={styles.compactHeaderContent}>
        <View style={styles.compactTitleRow}>
          <Text style={styles.compactIndex}>#{index + 1}</Text>
          <Text style={styles.compactTitle} numberOfLines={1}>
            {item.competitor_name}
          </Text>
        </View>
        <View style={styles.compactStatus}>
          <Text style={styles.compactStatusText}>
            {item.status_id === 1 ? '✓' : '✗'}
          </Text>
        </View>
      </View>
    </View>
    
    <View style={styles.compactBody}>
      <View style={styles.compactRow}>
        <Text style={styles.compactLabel}>Pricing Strategy:</Text>
        <Text style={styles.compactValue} numberOfLines={1}>
          {item.crm_pricing_strategy_master?.name || 'N/A'}
        </Text>
      </View>
      <View style={styles.compactRow}>
        <Text style={styles.compactLabel}>Coverage:</Text>
        <Text style={styles.compactValue} numberOfLines={1}>
          {item.crm_geographical_coverage_master?.name || 'N/A'}
        </Text>
      </View>
      <View style={styles.compactFooter}>
        <Text style={styles.compactDate}>
          {new Date(item.created_at).toLocaleDateString()}
        </Text>
      </View>
    </View>
  </TouchableOpacity>
);

  // const renderItem = ({ item, index }) => (
  //   <TouchableOpacity
  //     style={styles.card}
  //     onPress={() =>
  //       navigation.navigate('CompetitiveIntelligenceDetail', {
  //         competitorId: item.competitor_id,
  //         orgId: orgId,
  //       })
  //     }
  //   >
  //     {/* Header */}
  //     <View style={styles.headerRow}>
  //       <View style={styles.headerRow}>
  //         <Text style={styles.idText}>#{index + 1}</Text>
  //         <Text style={styles.title}>{item.competitor_name}</Text>
  //       </View>
  //       <View style={styles.statusPill}>
  //         {/* <View style={styles.dot} /> */}
  //         <Text style={styles.statusText}>
  //           {item.status_id === 1 ? 'Active' : 'Inactive'}
  //         </Text>
  //       </View>
  //     </View>

  //     {/* Pricing Strategy */}
  //     <View style={styles.row}>
  //       <Text style={styles.label}>Pricing Strategy: </Text>
  //       <View style={styles.tag}>
  //         <Text style={styles.tagText}>
  //           {item.crm_pricing_strategy_master?.name || 'N/A'}
  //         </Text>
  //       </View>
  //     </View>

  //     {/* Coverage */}
  //     <View style={styles.row}>
  //       <Text style={styles.label}>Coverage: </Text>
  //       <Text style={styles.value}>
  //         {item.crm_geographical_coverage_master?.name || 'N/A'}
  //       </Text>
  //     </View>

  //     {/* Created */}
  //     <View style={styles.row}>
  //       <Text style={styles.label}>Created: </Text>
  //       <Text style={styles.value}>
  //         {new Date(item.created_at).toLocaleDateString()}
  //       </Text>
  //     </View>
  //   </TouchableOpacity>
  // );

  const handleLoadMore = () => {
    if (hasMore && !loading) {
      fetchList(false, searchQuery);
    }
  };

  return (
    <View style={styles.container}>
      {/* Search + Add Button */}
      <View style={styles.topBar}>
        <SearchBar
          placeholder="Search competitor..."
          value={searchQuery}
          onChangeText={handleSearch}
          style={{ flex: 1 }}
        />
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('CompetitiveIntelligenceForm',{orgId})}
        >
          <Plus size={moderateScale(22)} color="#FFF" />
        </TouchableOpacity>
      </View>

      {loading && list.length === 0 ? (
        <ActivityIndicator size="small" />
      ) : (
        <FlatList
          data={list}
          renderItem={renderItem}
          keyExtractor={(item, idx) =>
            item.competitor_id || idx.toString()
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.3}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={() => fetchList(true, searchQuery)}
            />
          }
          ListEmptyComponent={
            <Text style={styles.emptyText}>No records found</Text>
          }
          bounces={false}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#fff', padding: moderateScale(10) },
  topBar: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(12),
  },
  addButton: {
    backgroundColor: '#3377FF',
    borderRadius: 5,
    height: verticalScale(32),
    width: verticalScale(32),
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: scale(8),
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    paddingBottom: 0,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 0.5,
    borderColor: '#3377ff90',
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: verticalScale(6),
  },
  idText: {
    fontFamily: fontFamily.medium,
    fontSize: scale(12),
    color: '#888',
  },
  statusPill: {
    flexDirection: 'row',
    alignItems: 'center',
    // backgroundColor: '#E6F9EE',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#2ECC71',
    marginRight: 4,
  },
  statusText: {
    fontFamily: fontFamily.medium,
    fontSize: scale(12),
    color: '#2ECC71',
  },
  title: {
    fontSize: scale(16),
    fontFamily: fontFamily.medium,
    color: '#000',
    marginLeft: scale(5),
  },
  row: { flexDirection: 'row', marginBottom: verticalScale(6) },
  label: { fontFamily: fontFamily.medium, fontSize: scale(13), color: '#555' },
  value: { fontFamily: fontFamily.regular, fontSize: scale(13), color: '#000' },
  tag: {
    backgroundColor: '#F4F5F7',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  tagText: {
    fontSize: scale(12),
    color: '#555',
    fontFamily: fontFamily.regular,
  },
  emptyText: {
    textAlign: 'center',
    marginTop: verticalScale(20),
    color: '#999',
  },
  compactCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: moderateScale(12),
    marginBottom: verticalScale(12),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 3,
    overflow: 'hidden',
    borderWidth:1,
    borderColor: '#3377ff90',
  },
  gradientHeader: {
    backgroundColor: '#fff',
    // backgroundColor: '#3B82F6',
    padding: moderateScale(12),
    borderBottomWidth:1,
    borderBottomColor:'#BFDBFE',
  },
  compactHeaderContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  compactTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  compactIndex: {
    fontSize: scale(11),
    color: '#3377ff90',
    // color: '#BFDBFE',
    marginRight: moderateScale(8),
  },
  compactTitle: {
    fontSize: scale(16),
    // fontFamily: fontFamily.semiBold,
    color: '#000',
    flex: 1,
  },
  compactStatus: {
    width: moderateScale(24),
    height: moderateScale(24),
    borderRadius: moderateScale(12),
    backgroundColor: '#2ECC71',
    // backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  compactStatusText: {
    fontSize: scale(14),
    color: '#FFFFFF',
  },
  compactBody: {
    padding: moderateScale(12),
  },
  compactRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(8),
  },
  compactLabel: {
    fontSize: scale(12),
    fontFamily: fontFamily.medium,
    color: '#64748B',
  },
  compactValue: {
    fontSize: scale(13),
    fontFamily: fontFamily.regular,
    color: '#0F172A',
    flex: 1,
    textAlign: 'right',
    marginLeft: moderateScale(8),
  },
  compactFooter: {
    borderTopWidth: 1,
    borderTopColor: '#F1F5F9',
    paddingTop: verticalScale(8),
    marginTop: verticalScale(4),
  },
  compactDate: {
    fontSize: scale(11),
    fontFamily: fontFamily.regular,
    color: '#94A3B8',
    textAlign: 'center',
  },
});

export default CompetitiveIntelligenceTab;
