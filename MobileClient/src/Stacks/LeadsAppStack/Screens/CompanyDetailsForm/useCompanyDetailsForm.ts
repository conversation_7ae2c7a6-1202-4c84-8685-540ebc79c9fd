import { useEffect, useState } from 'react';
import ApiClient from '../../../../Common/API/ApiClient';
import {
  getAccountId,
  getSchemaName,
  getSelectedWorkspaceId,
  getUserId,
} from '../../../../Common/Utils/Storage';
import { showToast } from '../../../../Components/AppToaster/AppToaster';
import { uploadImageToS3Global } from '../../../../Utils/imageUploader';
import { v4 as uuidv4 } from 'uuid';
import { encryptPayload } from '../../../../Utils/payloadEncryption';
import { countryCodes } from '../../../../../node_modules/react-native-country-codes-picker/constants/countryCodes';

export interface Option {
  Text: string;
  Value: string;
}

export const useCompanyDetailsForm = ({ orgDetails, navigation, fetchProspectDetails }: any) => {
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [industryOptions, setIndustryOptions] = useState([]);
  const [companySizeOptions, setCompanySizeOptions] = useState([]);
  const [orgTypeOptions, setOrgTypeOptions] = useState([]);
  const [currencyOptions, setCurrencyOptions] = useState([]);
  const [areaOptions, setAreaOptions] = useState([]);
  const [addressTypeOptions, setAddressTypeOptions] = useState([]);
  const [countryOptions, setCountryOptions] = useState([]);
  const [cityOptions, setCityOptions] = useState<{ [index: number | "all"]: any[] }>({});
  const [phoneTypeOptions, setPhoneTypeOptions] = useState([]);
  const [emailTypeOptions, setEmailTypeOptions] = useState([]);
  const [socialTypeOptions, setSocialTypeOptions] = useState([]);
  const [showCountryPicker, setShowCountryPicker] = useState(false);
  const [countryPickerIndex, setCountryPickerIndex] = useState<number | null>(null);
  const [logo, setLogo] = useState<string | null>(orgDetails?.org_logo_url || null);
  const [deletedEmailIds, setDeletedEmailIds] = useState<string[]>([]);
  const [deletedPhoneIds, setDeletedPhoneIds] = useState<string[]>([]);
  const [deletedAddressIds, setDeletedAddressIds] = useState<string[]>([]);
  const [deletedSocialIds, setDeletedSocialIds] = useState<string[]>([]);
  const [emailValidations, setEmailValidations] = useState<boolean[]>(() =>
    form?.emails.map(() => false)
  );
    const getCountryFlag = (dialCode: string): string => {
    const match = countryCodes?.find((c) => c.dial_code === dialCode);
    return match?.flag || '🌐';
  };
const [form, setForm] = useState(() => mapOrgDetailsToForm(orgDetails));
  function mapOrgDetailsToForm (orgDetails:any) {
    const currencyObj = currencyOptions.find(c => c.currency_id === orgDetails.currency_id);
    return ({
    ...orgDetails,
    year_founded: orgDetails?.year_founded || "2025",
    annual_revenue: orgDetails?.annual_revenue || 1,
    annual_growth_rate: orgDetails?.annual_growth_rate || 1,
    expected_business_value: orgDetails?.expected_business_value || 1,
    decision_timeline: orgDetails?.decision_timeline || 1,
    org_logo_url: orgDetails?.org_logo_url || null,
    company_name: orgDetails?.company_name || '',
    industry_id: orgDetails?.industry_id
      ? { Value: orgDetails.industry_id, Text: orgDetails.industry_name }
      : null,
    company_size_id: orgDetails?.company_size_id
      ? { Value: orgDetails.company_size_id, Text: orgDetails.company_size }
      : null,
    // currency_id: orgDetails?.currency_id ?
    //  { Value: orgDetails.currency_id, Text: orgDetails.currency_name } : null,
    currency_id: currencyObj || (orgDetails?.currency_id
      ? { Value: orgDetails.currency_id, Text: orgDetails.currency_name }
      : null),
    area: orgDetails?.area ? { Value: orgDetails.area, Text: orgDetails.area_name } : null,
    org_types: orgDetails?.org_types?.map((t) => String(t.org_type_id)) ||["9"],
    customer_type: orgDetails?.customer_type || "credit",
    category: orgDetails?.category || "internal",
    tax_vat_id: orgDetails?.tax_vat_id || '',
    credit_limit: orgDetails?.credit_limit || 0,
    credit_days: orgDetails?.credit_days || 0,
    addresses: orgDetails?.addresses?.length
      ? orgDetails.addresses.map((a, index) => ({
          index,
          address_id: a.address_id,
          address: a.address ?? '',
          address_type_id: {
            Value: a.address_type_id ?? '',
            Text: a.address_type_name ?? '',
          },
          country_id: {
            Value: a.country_id ?? '',
            Text: a.country_name ?? '',
          },
          city_id: {
            Value: a.city_id ?? '',
            Text: a.city_name ?? '',
          },
          postal_code: a.postal_code ?? '',
        }))
      : [
          {
            address: '',
            address_type_id: addressTypeOptions[0],
            country_id: null,
            city_id: null,
            postal_code: '',
          },
        ],
    phone_numbers: orgDetails?.phone_numbers?.length
      ? orgDetails.phone_numbers.map((p, index) => {
        // const flag = getCountryFlag(p?.country_code || "+91")
        return ({
          index,
          phone_number_id: p.phone_number_id,
          contact_phone_number: p.contact_phone_number ?? p.phone_number ?? '',
          contact_phone_number_type_id: {
            Value: p.contact_phone_number_type_id ?? p.phone_number_type_id ?? '',
            Text: p.contact_phone_number_type_name ?? p.phone_number_type_name ?? '',
          },
          is_on_whatsapp: Boolean(p.is_on_whatsapp),
          is_on_telegram: Boolean(p.is_on_telegram),
          country_code: p.country_code || '+91',
          countryFlag: getCountryFlag(p?.country_code || "+91")
        })})
      : [
          {
            contact_phone_number_type_id: {
              Value: phoneTypeOptions?.[0]?.Value ?? '',
              Text: phoneTypeOptions?.[0]?.Text ?? '',
            },
            contact_phone_number: '',
            is_on_whatsapp: false,
            is_on_telegram: false,
            country_code: '+91',
            countryFlag: getCountryFlag('+91'),
          },
        ],
    emails: orgDetails?.emails?.length
      ? orgDetails?.emails?.map((e, index) => ({
          email_id: e.email_id,
          email: e.email,
          index,
          email_type_id: {
            Value: e.email_type_id,
            Text: e.email_type_name,
          },
        }))
      : [{
          email_type_id: {
            Value: emailTypeOptions?.[0]?.Value ?? '',
            Text: emailTypeOptions?.[0]?.Text ?? '',
          },
          email: ''
        }],
    social_medias: orgDetails?.social_medias?.length
      ? orgDetails.social_medias.map((s, index) => ({
          index,
          social_media_id: s.social_media_id,
          social_media_handle: s.social_media_handle ?? '',
          social_media_type_id: {
            Value: s.social_media_type_id ?? '',
            Text: s.social_media_type_name ?? '',
          },
        }))
      : [{
          social_media_type_id: {
            Value: socialTypeOptions?.[0]?.Value ?? '',
            Text: socialTypeOptions?.[0]?.Text ?? '',
          },
          social_media_handle: '',
        }],
  })
}

  const handleEmailValidationChange = (index: number, isValid: boolean) => {
    setEmailValidations((prev=[]) => {
      const updated = [...prev];
      updated[index] = isValid;
      return updated;
    });
  };

  const restButton = () => {
    const resetForm = mapOrgDetailsToForm(orgDetails);
    setForm(resetForm);
    setErrors({});
    setLogo(orgDetails?.org_logo_url || null);
    setDeletedEmailIds([]);
    setDeletedPhoneIds([]);
    setDeletedAddressIds([]);
    setDeletedSocialIds([]);
  };
  const removeEmail = (index: number) => {
    setForm((prev: any) => {
      const updated = [...prev.emails];
      const removed = updated.splice(index, 1)[0];
      if (removed?.email_id) {
        setDeletedEmailIds((prevIds) => [...prevIds, removed.email_id]);
      }
      return { ...prev, emails: updated };
    });
  };

  const removePhone = (index: number) => {
    setForm((prev: any) => {
      const updated = [...prev.phone_numbers];
      const removed = updated.splice(index, 1)[0];
      if (removed?.phone_number_id) {
        setDeletedPhoneIds((prevIds) => [...prevIds, removed.phone_number_id]);
      }
      return { ...prev, phone_numbers: updated };
    });
  };

  const removeAddress = (index: number) => {
    setForm((prev: any) => {
      const updated = [...prev.addresses];
      const removed = updated.splice(index, 1)[0];
      if (removed?.address_id) {
        setDeletedAddressIds((prevIds) => [...prevIds, removed.address_id]);
      }
      return { ...prev, addresses: updated };
    });
  };

  const removeSocial = (index: number) => {
    setForm((prev: any) => {
      const updated = [...prev.social_medias];
      const removed = updated.splice(index, 1)[0];
      if (removed?.social_media_id) {
        setDeletedSocialIds((prevIds) => [...prevIds, removed.social_media_id]);
      }
      return { ...prev, social_medias: updated };
    });
  };

  const handleChange = (key: string, value: any) => {
    setForm((prev: any) => {
      const keys = key.split('.');
      const newForm = { ...prev };
      let current = newForm;
      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) current[keys[i]] = {};
        current = current[keys[i]];
      }
      current[keys[keys.length - 1]] = value;
      return newForm;
    });

    setErrors(prevErrors => {
      const updatedErrors = { ...prevErrors };
      if (updatedErrors[key]) {
        delete updatedErrors[key];
      }
      return updatedErrors;
    });
  };
  const handleEmailChange = (index: number, value: string) => {
    const updatedEmails = [...form.emails];
    updatedEmails[index].email = value;

    setForm(prev => ({ ...prev, emails: updatedEmails }));

    setErrors(prev => {
      const updatedErrors = { ...prev };
      if (updatedErrors.emails?.[index]?.email) {
        updatedErrors.emails[index] = { ...updatedErrors.emails[index], email: "" };
      }
      return updatedErrors;
    });
  };
    
  const handlePhoneChange = (index: number, value: any) => {
    const updatedPhones = [...form.phone_numbers];
    updatedPhones[index].contact_phone_number = value;

    setForm(prev => ({ ...prev, contact_phone_number: updatedPhones }));

    setErrors(prev => {
      const updatedErrors = { ...prev };
      if (updatedErrors.phone_numbers?.[index]?.contact_phone_number) {
        updatedErrors.phone_numbers[index] = {
          ...updatedErrors.phone_numbers[index],
          contact_phone_number: ""
        };
      }
      return updatedErrors;
    });
  };

  const addPhoneOption = () => {
    handleChange('phone_numbers', [
      ...form.phone_numbers,
      {
        contact_phone_number_type_id: phoneTypeOptions[0],
        contact_phone_number: '',
        is_on_whatsapp: false,
        is_on_telegram: false,
        country_code: '+91',
        countryFlag: getCountryFlag('+91'),
      },
    ]);
  }

  const handleAddressChange = (index: number, key: string, value: any) => {
    const updatedAddresses = [...form.addresses];
    updatedAddresses[index][key] = value;

    setForm(prev => ({ ...prev, addresses: updatedAddresses }));

    setErrors(prev => {
      const updatedErrors = { ...prev };
      if (updatedErrors.addresses?.[index]?.[key]) {
        updatedErrors.addresses[index] = {
          ...updatedErrors.addresses[index],
          [key]: ""
        };
      }
      return updatedErrors;
    });
  };

  const validate = () => {
    const newErrors: { [key: string]: string } = {};
    if (!form.company_name?.trim()) {
      newErrors.company_name = 'Company name is required';
    }
    if (!form.org_types || form.org_types.length === 0) {
      newErrors.org_types = 'Select at least one organization type';
    }
    if (!form.currency_id) {
      newErrors.currency_id = 'Currency is required';
    }
    if (!form.area) {
      newErrors.area = 'Area is required';
    }
    newErrors.phone_numbers = form.phone_numbers.map((phone: any) => {
      const phoneErrors: any = {};
      if (!phone.contact_phone_number_type_id) {
        phoneErrors.contact_phone_number_type_id = 'Phone type is required';
      }
      if (!phone.contact_phone_number?.trim()) {
        phoneErrors.contact_phone_number = 'Phone number is required';
      }
      return Object.keys(phoneErrors).length ? phoneErrors : null;
    });
    if (!newErrors.phone_numbers.some((entry: any) => entry)) {
      delete newErrors.phone_numbers;
    }

    newErrors.emails = form.emails.map((email: any, index: number) => {
      const emailErrors: any = {};
      if (!email.email_type_id || !email.email_type_id.Value) {
        emailErrors.email_type_id = 'Email type is required';
      }

      if (!email.email?.trim()) {
        emailErrors.email = 'Email is required';
      } else if (emailValidations[index] === false) {
        emailErrors.email = 'Invalid email address';
      }

      return Object.keys(emailErrors).length ? emailErrors : null;
    });

    newErrors.addresses = form.addresses.map((addr: any, index: number) => {
      const addressErrors: any = {};

      if (!addr.address?.trim()) {
        addressErrors.address = 'Address is required';
      }
      if (!addr.address_type_id?.Value) {
        addressErrors.address_type_id = 'Address type is required';
      }
      if (!addr.country_id?.Value) {
        addressErrors.country_id = 'Country is required';
      }
      if (!addr.city_id?.Value) {
        addressErrors.city_id = 'City is required';
      }
      if (!addr.postal_code?.trim()) {
        addressErrors.postal_code = 'Postal code is required';
      }

      return Object.keys(addressErrors).length ? addressErrors : null;
    });


    if (!newErrors.emails.some((entry: any) => entry)) {
      delete newErrors.emails;
    }
    if (!newErrors.phone_numbers?.some((entry: any) => entry)) {
      delete newErrors.phone_numbers;
    }
    if (!newErrors.addresses.some((entry: any) => entry)) {
      delete newErrors.addresses;
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validate()) {
      showToast.error('Please fill all the required details');
      return;
    }

    try {
      const schemaName = await getSchemaName();
      const account_id = await getAccountId();
      const workspace_id = await getSelectedWorkspaceId();
      const user_id = await getUserId();
      let updatedLogo = form.org_logo_url;
      if (updatedLogo && updatedLogo.startsWith('file')) {
        updatedLogo = await uploadImageToS3Global({ uri: updatedLogo });
        handleChange('org_logo_url', updatedLogo);
      }

      const inputParamsJson: any = {
        org_id: form.org_id,
        company_name: form.company_name,
        org_code: form.org_code,
        org_logo_url: updatedLogo || '',
        industry_id: form.industry_id?.Value ? String(form.industry_id.Value) : null,
        year_founded: form.year_founded ? Number(form.year_founded) : null,
        org_type_id: form.org_types.map((id: any) => String(id)),
        annual_revenue: form.annual_revenue || 0,
        annual_growth_rate: form.annual_growth_rate || 0,
        area: form.area?.Value ? Number(form.area.Value) : null,
        category: form.category || 'internal',
        customer_type: form.customer_type || 'credit',
        expected_business_value: form.expected_business_value || 0,
        decision_timeline: form.decision_timeline || 0,
        pain_points: form.pain_points || "",
        key_decision_makers: form.key_decision_makers || "",
        decision_criteria: form.decision_criteria || "",
        currency_id: form.currency_id?.Value ? String(form.currency_id.Value) : null,
        tax_vat_id:form.tax_vat_id || '',
        credit_limit: form?.credit_limit || 0,
        credit_days: form?.credit_days || 0,
        addresses: form.addresses.map((a: any) => ({
          address_id: a.address_id || uuidv4(),
          address: a.address,
          address_type_id: Number(a.address_type_id?.Value) ?? null,
          country_id: Number(a.country_id?.Value) ?? null,
          city_id: Number(a.city_id?.Value) ?? null,
          postal_code: a.postal_code,
        })),
        deleteable_address_ids: deletedAddressIds,
        emails: [...form.emails]
          .sort((a, b) => {
            const order = orgDetails?.emails?.map((e) => e.email_id);
            return order.indexOf(a.email_id) - order.indexOf(b.email_id);
          })
          .map((e: any) => ({
            email_id: e.email_id || uuidv4(),
            email: e.email,
            email_type_id: e.email_type_id?.Value ? String(e.email_type_id.Value) : null,
          })),
        deleteable_email_ids: deletedEmailIds,
        phone_numbers: [...form.phone_numbers]
          .sort((a, b) => (a.index ?? 0) - (b.index ?? 0))
          .map((p: any) => ({
            phone_number_id: p.phone_number_id || uuidv4(),
            contact_phone_number: p.contact_phone_number || p.phone_number,
            contact_phone_number_type_id: String(p.contact_phone_number_type_id?.Value) ?? null,
            is_on_whatsapp: p.is_on_whatsapp,
            is_on_telegram: p.is_on_telegram,
            country_code: p.country_code || '',
            org_id: orgDetails.org_id,
          })),
        deleteable_phone_number_ids: deletedPhoneIds,
        social_medias: [...form.social_medias]
          .sort((a, b) => (a.index ?? 0) - (b.index ?? 0))
          .map((s: any) => ({
            social_media_id: s.social_media_id || uuidv4(),
            social_media_type_id: String(s.social_media_type_id?.Value) ?? null,
            social_media_handle: s.social_media_handle,
            org_id: orgDetails.org_id,
          })),

        deleteable_social_media_ids: deletedSocialIds,

        deleteable_org_type_ids: [],
      };
      if (form.company_size_id?.Value) 
         inputParamsJson.company_size_id = form.company_size_id?.Value ? String(form.company_size_id.Value) : null

      const contextJson = JSON.stringify({ account_id, workspace_id, user_id });
      const inputParamsJsonString = JSON.stringify(inputParamsJson);

      const payloadToEncrypt = {
        schemaName,
        contextJson,
        inputParamsJson: inputParamsJsonString,
      };

      const finalPayload = await encryptPayload(JSON.stringify(payloadToEncrypt));
      const response = await ApiClient.post('/updateCompanyInformation', finalPayload);

      if (response.data?.success) {
        showToast.success('Company details updated successfully');
        fetchProspectDetails();
        navigation.goBack();
      } else {
        showToast.error(response.data?.message || 'Update failed');
      }
    } catch (error) {
      console.error('Submit Error:', error);
      showToast.error('Something went wrong');
    }
  };

  const fetchMasters = async () => {
    const schemaName = await getSchemaName();
    const endpoints = [
      {
        key: 'industryOptions',
        url: `/getMastersDropDown/${schemaName}/crm_industry_master?columns=industry_id,industry_name`,
      },
      {
        key: 'companySizeOptions',
        url: `/getMastersDropDown/${schemaName}/crm_company_size_master?columns=company_size_id,company_size`,
      },
      {
        key: 'orgTypeOptions',
        url: `/getMastersDropDown/${schemaName}/crm_organization_type_master?columns=org_type_id,organization_type_name`,
      },
      {
        key: 'currencyOptions',
        url: `/getMastersDropDown/${schemaName}/currency_master?columns=currency_id,currency_name,currency_symbol,currency_code,exchange_rate`,
      },
      {
        key: 'areaOptions',
        url: `/getMastersDropDown/${schemaName}/crm_area?columns=area_id,area_name`,
      },
      {
        key: 'addressTypeOptions',
        url: `/getMastersDropDown/${schemaName}/crm_address_type_master?columns=address_type_id,address_type_name`,
      },
      {
        key: 'countryOptions',
        url: `/getMastersDropDown/${schemaName}/country_master?columns=country_id,country_name`,
      },
      {
        key: 'cityOptions',
        url: `/getMastersDropDown/${schemaName}/city_master?columns=city_id,city_name&limit=100`,
      },
      {
        key: 'phoneTypeOptions',
        url: `/getMastersDropDown/${schemaName}/crm_phone_number_type_master?columns=phone_number_type_id,phone_number_type_name`,
      },
      {
        key: 'emailTypeOptions',
        url: `/getMastersDropDown/${schemaName}/crm_email_type_master?columns=email_type_id,email_type_name`,
      },
      {
        key: 'socialTypeOptions',
        url: `/getMastersDropDown/${schemaName}/crm_social_media_type_master?columns=social_media_type_id,social_media_type_name`,
      },
    ];

    const results = await Promise.allSettled(endpoints.map(({ url }) => ApiClient.get(url)));
    results.forEach((res, idx) => {
      const key = endpoints[idx].key;
      if (res.status === 'fulfilled') {
        const data = res.value?.data || [];
        switch (key) {
          case 'currencyOptions':
            const formatted = data.map((d: any) => ({
              ...d,
              Text: `${d.currency_symbol} ${d.currency_code}`,
              Value: d.currency_id,
              searchText: `${d.currency_name} ${d.currency_symbol} ${d.currency_code}`,
            }));
            setCurrencyOptions(formatted);
            break;
          case 'industryOptions':
            setIndustryOptions(data);
            break;
          case 'companySizeOptions':
            setCompanySizeOptions(data);
            break;
          case 'orgTypeOptions':
            const modifiedData = data?.filter((type:{Text:string}) => ['Shipper','Consignee', 'Customer', 'notify'].includes(type.Text))
            setOrgTypeOptions(modifiedData);
            break;
          case 'areaOptions':
            setAreaOptions(data);
            break;
          case 'addressTypeOptions':
            setAddressTypeOptions(data);
            break;
          case 'countryOptions':
            setCountryOptions(data);
            break;
          case 'cityOptions':
            setCityOptions({all:data});
            break;
          case 'phoneTypeOptions':
            setPhoneTypeOptions(data);
            break;
          case 'emailTypeOptions':
            setEmailTypeOptions(data);
            break;
          case 'socialTypeOptions':
            setSocialTypeOptions(data);
            break;
        }
      } else {
        console.error(`${key} failed`, res.reason);
        showToast.error(`Failed to load ${key}`);
      }
    });
  };

  const searchCities = async (searchText: string, index: number) => {
    try {
      const schemaName = await getSchemaName();
      const url = `/getMastersDropDown/${schemaName}/city_master?columns=city_id,city_name&limit=50&search=${encodeURIComponent(searchText)}`;
      const res = await ApiClient.get(url);
      if (res.data) {
        setCityOptions((prev) => ({
          ...prev,
          [index]: res.data,
        }));
      }
    } catch (error) {
      console.error('City search failed:', error);
      showToast.error('Failed to search cities');
    }
  }

  useEffect(() => {
    fetchMasters();
  }, []);

  useEffect(() => {
    if (currencyOptions.length && orgDetails?.currency_id) {
      const match = currencyOptions.find((c) => c.currency_id === orgDetails.currency_id);
      if (match) {
        setForm((prev: any) => ({ ...prev, currency_id: match }));
      }
    }
  }, [currencyOptions, orgDetails?.currency_id]);

  useEffect(() => {
    if (
      phoneTypeOptions.length &&
      emailTypeOptions.length &&
      socialTypeOptions.length && 
      currencyOptions.length
    ) {
      const defaultedForm = mapOrgDetailsToForm(orgDetails);
      setForm(defaultedForm);
    }
  }, [phoneTypeOptions, emailTypeOptions, socialTypeOptions,currencyOptions]);

  return {
    form,
    errors,
    handleChange,
    handleSubmit,
    industryOptions,
    companySizeOptions,
    orgTypeOptions,
    currencyOptions,
    areaOptions,
    addressTypeOptions,
    countryOptions,
    cityOptions,
    phoneTypeOptions,
    emailTypeOptions,
    socialTypeOptions,
    showCountryPicker,
    setShowCountryPicker,
    countryPickerIndex,
    setCountryPickerIndex,
    logo,
    setLogo,
    removeEmail,
    removePhone,
    removeAddress,
    removeSocial,
    restButton,
    handleEmailValidationChange,
    handleEmailChange,
    searchCities,
    handleAddressChange,
    handlePhoneChange,
    addPhoneOption,
  };
};
