import React from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { Text, Button, TextInput } from 'react-native-paper';
import { verticalScale, scale, moderateScale } from 'react-native-size-matters';
import AppHeader from '../../../../Components/AppHeader';
import { DropdownField } from '../../../../Components/UI/Menu/DropdownModal';
import { useLeadProspectForm } from './useLeadProspectForm';
import { Star, Check } from 'lucide-react-native';
import { fontFamily } from '../../../../Common/Theme/typography';

export default function LeadProspectForm({ navigation, route }: any) {
  const leadData = route.params?.lead;
  const { fromTab } = route.params ?? {};
  const {
    form,
    handleChange,
    toggleJobType,
    isSubmitting,
    submitForm,
    controlUnits,
    assignedUsers,
    areaOptions,
    prospectTypes,
    jobTypes,
    errors,
    isLoading,
  } = useLeadProspectForm(leadData, navigation);

  const getColor = (value: string) => {
    const match = value?.match(/Type\s+([A-C])/i);
    const letter = match?.[1]?.toUpperCase();
    switch (letter) {
      case 'A':
        return '#2563eb';
      case 'B':
        return '#fbbf24';
      case 'C':
        return '#ef4444';
      default:
        return '#ccc';
    }
  };

  return (
    <View style={styles.container}>
      <AppHeader title="Move to Prospect" customHeaderStyles={{ backgroundColor: '#fff' }} />

      {isLoading || isSubmitting ? (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <ActivityIndicator size="large" color="#3377FF" />
        </View>
      ) : (
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          bounces={false}
        >
          <Text style={styles.sectionLabel}>
            Prospect Type <Text style={styles.asterisk}>*</Text>
          </Text>
          <View style={styles.prospectTypeRow}>
            {prospectTypes.map((item) => {
              const selected = form.prospectType?.prospect_type_id === item.prospect_type_id;
              const color = getColor(item.prospect_type_name);
              return (
                <TouchableOpacity
                  key={item.prospect_type_id}
                  onPress={() => handleChange('prospectType', item)}
                  style={[styles.prospectTypeCard, selected && { borderColor: color }]}
                >
                  <Star
                    fill={selected ? color : 'transparent'}
                    color={selected ? 'white' : color}
                    size={20}
                    style={styles.iconWrap}
                  />
                  <Text style={[styles.prospectTypeTitle, selected && { color }]}>
                    {item.prospect_type_name}
                  </Text>
                  <Text style={styles.prospectTypeSubtitle}>{item.prospect_type_description}</Text>
                </TouchableOpacity>
              );
            })}
          </View>
          {errors.prospectType && <Text style={styles.errorText}>{errors.prospectType}</Text>}

          <Text style={styles.sectionLabel}>
            Job Type <Text style={styles.asterisk}>*</Text>
          </Text>
          <View style={styles.jobTypeGrid}>
            {jobTypes.map((item) => {
              const selected = !!form.jobTypes.find((j) => j.Value === item.Value);
              return (
                <TouchableOpacity
                  key={item.Value}
                  onPress={() => toggleJobType(item)}
                  style={styles.jobTypeRow}
                >
                  <View style={[styles.checkbox, selected && styles.checkboxSelected]}>
                    {selected && <Check size={12} color="#fff" />}
                  </View>
                  <Text style={styles.jobTypeLabel}>{item.Text}</Text>
                </TouchableOpacity>
              );
            })}
          </View>
          {errors.jobTypes && <Text style={styles.errorText}>{errors.jobTypes}</Text>}

          <DropdownField
            label="Control Unit"
            value={form.controlUnit}
            data={controlUnits}
            keyField="id"
            valueField="controlUnitName"
            onSelect={(item) => handleChange('controlUnit', item)}
            placeholder="Select control unit"
          />
          {errors.controlUnit && (
            <Text style={[styles.errorText, { marginTop: verticalScale(5) }]}>
              {errors.controlUnit}
            </Text>
          )}

          <DropdownField
            label="Assigned To"
            value={form.assignedTo}
            data={assignedUsers}
            keyField="id"
            valueField="name"
            onSelect={(item) => handleChange('assignedTo', item)}
            placeholder="Select user"
          />
          {errors.assignedTo && (
            <Text style={[styles.errorText, { marginTop: verticalScale(5) }]}>
              {errors.assignedTo}
            </Text>
          )}

          <DropdownField
            label="Area"
            value={form.area}
            data={areaOptions}
            keyField="Value"
            valueField="Text"
            onSelect={(item) => handleChange('area', item)}
            placeholder="Select area"
          />
          {errors.area && (
            <Text style={[styles.errorText, { marginTop: verticalScale(5) }]}>{errors.area}</Text>
          )}

          <Text style={styles.sectionLabel}>Comments</Text>
          <TextInput
            value={form.comments}
            onChangeText={(text) => handleChange('comments', text)}
            mode="outlined"
            multiline
            numberOfLines={4}
            placeholder="Add comments..."
            style={styles.textArea}
          />
        </ScrollView>
      )}

      <View style={styles.actionRow}>
        <Button
          mode="contained"
          style={styles.closeButton}
          textColor="#000"
          onPress={() => {
            navigation.navigate('BottomTabStack', {
              screen: 'Home',
              params: {
                screen: 'LeadsStack',
                params: {
                  screen: 'LeadsScreenMain',
                  params: {
                    tab: fromTab ?? 'All Leads',
                  },
                },
              },
            });
          }}
          disabled={isSubmitting}
        >
          Close
        </Button>
        <Button
          mode="contained"
          style={styles.submitButton}
          textColor="#fff"
          loading={isSubmitting}
          disabled={isSubmitting}
          onPress={submitForm}
        >
          Move to Prospect
        </Button>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#fff' },
  loaderContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  scrollContent: { padding: scale(16) },
  sectionLabel: {
    fontFamily: fontFamily.semiBold,
    fontSize: moderateScale(14),
    marginTop: verticalScale(10),
    marginBottom: verticalScale(5),
  },
  asterisk: { color: 'red' },
  errorText: {
    color: 'red',
    fontSize: scale(12),
    marginTop: -8,
    marginBottom: 8,
    fontFamily: fontFamily.regular,
  },
  textArea: {
    backgroundColor: '#fff',
    textAlignVertical: 'top',
    height: verticalScale(100),
    fontSize: moderateScale(13),
    fontFamily: fontFamily.regular,
    marginBottom: verticalScale(20),
  },
  actionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    padding: 16,
    gap: scale(10),
    marginBottom: Platform.OS === 'ios' ? verticalScale(10) : 0,
  },
  closeButton: {
    flex: 1,
    borderRadius: 4,
    backgroundColor: '#E5E7EB',
  },
  submitButton: {
    flex: 1,
    borderRadius: 4,
    backgroundColor: 'rgba(51, 119, 255, 1)',
  },
  prospectTypeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: scale(8),
    marginBottom: verticalScale(12),
  },
  prospectTypeCard: {
    flex: 1,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    padding: scale(10),
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  iconWrap: { marginBottom: 4 },
  prospectTypeTitle: {
    fontWeight: '600',
    fontSize: scale(14),
  },
  prospectTypeSubtitle: {
    fontSize: scale(12),
    color: '#6B7280',
    marginTop: 2,
    textAlign: 'center',
  },
  jobTypeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  jobTypeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '48%',
    marginBottom: verticalScale(12),
    backgroundColor: '#fff',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    marginRight: scale(8),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  checkboxSelected: {
    backgroundColor: '#2563eb',
    borderColor: '#2563eb',
  },
  jobTypeLabel: {
    fontFamily: fontFamily.regular,
    fontSize: moderateScale(13),
    color: '#111827',
    width: '80%',
  },
});
