import { useEffect, useState } from 'react';
import { getSchemaId, getSchemaName, getUserId } from '../../../../Common/Utils/Storage';
import { encryptPayload } from '../../../../Utils/payloadEncryption';
import ApiClient from '../../../../Common/API/ApiClient';
import { useNavigation } from '@react-navigation/native';
import { showToast } from '../../../../Components/AppToaster/AppToaster';
import { COMMON_ENDPOINTS, LEADS_ENDPOINTS } from '../../../../Common/API/ApiEndpoints';
export const useAccountDetailsForm = (accountDataFromRoute) => {
  const navigation = useNavigation();
  const [contactProfiles, setContactProfiles] = useState([]);
  const [salesPersons, setSalesPersons] = useState([]);
  const [paymentTerms, setPaymentTerms] = useState([]);
  const orgId = accountDataFromRoute.org_id || "";
  const [arForm, setArForm] = useState({ ...emptyForm });
  const [apForm, setApForm] = useState({ ...emptyForm });
  const [loading, setLoading] = useState(true);
  const [schemaName, setSchemaName] = useState('');
  const [schemaId, setSchemaId] = useState('');
  const [userId, setUserId] = useState('');
  const emptyForm = {
    contact_profile: null,
    sales_person: null,
    payment_term: null,
    credit_limit: '',
    account_code: '',
    unvouched_account: '',
    discount_code: '',
    errors: {},
  };

  const handleChange = (section, field, value) => {
    const setter = section === 'ar' ? setArForm : setApForm;
    setter((prev) => ({ ...prev, [field]: value }));
  };

  const fetchContactProfiles = async (org_id) => {
    const user_id = userId;
    const payload = {
      schemaName,
      contextJson: { user_id },
      inputParamsJson: { org_id },
    };
    const encryptedPayload = await encryptPayload(payload);
    const res = await ApiClient.post(LEADS_ENDPOINTS.GET_CRM_CONTACTS, encryptedPayload);
    setContactProfiles(res?.data?.data || []);
  };

  const fetchSalesPersons = async () => {
    const res = await ApiClient.post(COMMON_ENDPOINTS.SCHEMA_USERS, {
      schemaName,
      schemaId,
    });
    setSalesPersons(res?.data?.data || []);
  };

  const fetchPaymentTerms = async () => {
    const res = await ApiClient.get(
      `/getMastersDropDown/${schemaName}/crm_payment_term_master?columns=payment_term_id,payment_term_name`
    );
    setPaymentTerms(res?.data || []);
  };

  const initializeForms = (accountData) => {
    const mapContact = (id) => contactProfiles.find((c) => c.contact_id === id) || null;
    const mapSales = (id) => salesPersons.find((s) => s.id === id) || null;
    const mapTerms = (id) => paymentTerms.find((p) => p.Value === String(id)) || null;

    setArForm({
      contact_profile: mapContact(accountData.ar_contact_profile_id),
      sales_person: mapSales(accountData.ar_sales_person_id),
      payment_term: mapTerms(accountData.ar_payment_term_id),
      credit_limit: accountData.ar_credit_limit?.toString() || '',
      account_code: accountData.ar_account_code || '',
      unvouched_account: accountData.ar_unvouched_account || '',
      discount_code: accountData.ar_discount_code || '',
    });

    setApForm({
      contact_profile: mapContact(accountData.ap_contact_profile_id),
      sales_person: mapSales(accountData.ap_sales_person_id),
      payment_term: mapTerms(accountData.ap_payment_term_id),
      credit_limit: accountData.ap_credit_limit?.toString() || '',
      account_code: accountData.ap_account_code || '',
      unvouched_account: accountData.ap_unvouched_account || '',
      discount_code: accountData.ap_discount_code || '',
    });
    setLoading(false);
  };

  const validateForm = (form) => {
    const errors: any = {};

    if (!form.contact_profile) {
      errors.contact_profile = 'Contact Profile is required';
    }

    if (!form.sales_person) {
      errors.sales_person = 'Sales Person is required';
    }

    if (!form.payment_term) {
      errors.payment_term = 'Payment term is required';
    }

    if (!form.credit_limit || Number(form.credit_limit) <= 0) {
      errors.credit_limit = 'Credit limit must be greater than 0';
    }

    return errors;
  };

  const submitForm = async () => {
    const arErrors = validateForm(arForm);
    const apErrors = validateForm(apForm);

    if (Object.keys(arErrors).length > 0 || Object.keys(apErrors).length > 0) {
      setArForm((prev) => ({ ...prev, errors: arErrors }));
      setApForm((prev) => ({ ...prev, errors: apErrors }));
      return;
    }
    try {
      const user_id = userId
      const payload = {
        schemaName,
        contextJson: { user_id },
        inputParamsJson: {
          org_id: orgId,

          ar_contact_profile_id: arForm.contact_profile?.contact_id,
          ar_sales_person_id: arForm.sales_person?.id,
          ar_payment_term_id: arForm.payment_term?.Value,
          ar_credit_limit: Number(arForm.credit_limit),
          ar_account_code: arForm.account_code,
          ar_unvouched_account: arForm.unvouched_account,
          ar_discount_code: arForm.discount_code,

          ap_contact_profile_id: apForm.contact_profile?.contact_id,
          ap_sales_person_id: apForm.sales_person?.id,
          ap_payment_term_id: apForm.payment_term?.Value,
          ap_credit_limit: Number(apForm.credit_limit),
          ap_account_code: apForm.account_code,
          ap_unvouched_account: apForm.unvouched_account,
          ap_discount_code: apForm.discount_code,
        },
      };

      const encryptedPayload = await encryptPayload(payload);
      const response = await ApiClient.post(LEADS_ENDPOINTS.UPDATE_CRM_ORG_ACC, encryptedPayload);
      if (response?.status === 200) {
        showToast.success('Account details updated successfully!');
        accountDataFromRoute?.fetchAccountDetails(orgId)
        navigation.goBack();
      } else {
        showToast.error('Failed to update account details');
      }
    } catch (err) {
      console.error('Submit error:', err);
      showToast.error('Something went wrong');
    }
  };

  useEffect(() => {
    // if (!accountDataFromRoute?.accountData) return;
    if (!accountDataFromRoute?.accountData || !schemaName || !userId || !schemaId) return;
    const org_id = accountDataFromRoute.org_id;

    const fetchAllDropdowns = async () => {
      setLoading(true);
      try {
        await Promise.all([fetchContactProfiles(org_id), fetchSalesPersons(), fetchPaymentTerms()]);
      } catch (error) {
        console.error('Error loading dropdowns:', error);
      }
    };

    fetchAllDropdowns();
  }, [accountDataFromRoute, schemaName, schemaId, userId]);

  useEffect(() => {
    if (
      accountDataFromRoute?.accountData &&
      contactProfiles.length > 0 &&
      salesPersons.length > 0 &&
      paymentTerms.length > 0
    ) {
      initializeForms(accountDataFromRoute.accountData);
    }
  }, [contactProfiles, salesPersons, paymentTerms]);

  useEffect(() => {
    const loadSchemaContext = async () => {
      const sName = (await getSchemaName()) ?? '';
      const sId = (await getSchemaId()) ?? '';
      const uId = (await getUserId()) ?? '';

      setSchemaName(sName);
      setSchemaId(sId);
      setUserId(uId);
    };

    loadSchemaContext();
  }, []);

  return {
    arForm,
    apForm,
    handleChange,
    contactProfiles,
    salesPersons,
    paymentTerms,
    submitForm,
    loading,
  };
};
