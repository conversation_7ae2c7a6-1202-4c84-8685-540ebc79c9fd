import React from 'react';
import { View, Text, ScrollView, StyleSheet, SafeAreaView, KeyboardAvoidingView, Platform } from 'react-native';
import { useAccountDetailsForm } from './useAccountDetailsForm';
import CustomInput from '../../../../Components/UI/TextInput';
import { DropdownField } from '../../../../Components/UI/Menu/DropdownModal';
import AppHeader from '../../../../Components/AppHeader';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { fontFamily } from '../../../../Common/Theme/typography';
import { Button,ActivityIndicator } from 'react-native-paper';

export const AccountDetailsForm = ({route}:any) => {
  const {
    arForm,
    apForm,
    handleChange,
    contactProfiles,
    salesPersons,
    paymentTerms,
    submitForm,
    loading
    } = useAccountDetailsForm(route.params);

  const renderSection = (form:any, sectionKey:"ar" | "ap") => (
    <>
      <DropdownField
        label="Contact Profile"
        value={form.contact_profile?.contact_name}
        data={contactProfiles}
        keyField="contact_id"
        valueField="contact_name"
        onSelect={(item) => handleChange(sectionKey, 'contact_profile', item)}
        placeholder="Select contact profile"
        showAsterisk
        errorText={form.errors?.contact_profile}
      />
      <DropdownField
        label="Sales Person"
        value={form.sales_person?.name}
        data={salesPersons}
        keyField="id"
        valueField="name"
        onSelect={(item) => handleChange(sectionKey, 'sales_person', item)}
        placeholder="Select sales person"
        showAsterisk
        errorText={form.errors?.sales_person}
      />
      <DropdownField
        label="Payment Terms"
        value={form.payment_term?.Text}
        data={paymentTerms}
        keyField="Value"
        valueField="Text"
        onSelect={(item) => handleChange(sectionKey, 'payment_term', item)}
        placeholder="Select payment terms"
        showAsterisk
        errorText={form.errors?.payment_term}
      />
      <CustomInput
        label="Credit Limit"
        value={form.credit_limit}
        onChangeText={(text:string) => handleChange(sectionKey, 'credit_limit', text)}
        keyboardType="numeric"
        placeholder="Enter credit limit"
        isMandatory
        errorText={form.errors?.credit_limit}
      />
      <CustomInput
        label="Account Code"
        value={form.account_code}
        onChangeText={(text:string) => handleChange(sectionKey, 'account_code', text)}
        placeholder="Enter account code"
      />
      <CustomInput
        label="Unvouched Account"
        value={form.unvouched_account}
        onChangeText={(text:string) => handleChange(sectionKey, 'unvouched_account', text)}
        placeholder="Enter unvouched account notes"
      />
      <CustomInput
        label="Discount Code"
        value={form.discount_code}
        onChangeText={(text:string) => handleChange(sectionKey, 'discount_code', text)}
        placeholder="Enter discount code"
      />
    </>
  );


  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
      </View>
    );
  }


  return (
    <View style={{ flex: 1, backgroundColor: '#fff' }}>
      <AppHeader title="Edit Account Details" customHeaderStyles={{ backgroundColor: '#fff' }} />
      <KeyboardAvoidingView  
              style={{ flex: 1 }}           
              behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            > 
            <ScrollView  
            keyboardShouldPersistTaps="handled"
             contentContainerStyle={styles.container} 
             showsVerticalScrollIndicator={false} bounces={false}>
      {/* <ScrollView contentContainerStyle={styles.container}> */}
        <Text style={styles.sectionTitle}>AR (Accounts Receivable) Details</Text>
        {renderSection(arForm, 'ar')}

        <Text style={[styles.sectionTitle, { marginTop: verticalScale(20) }]}>
          AP (Accounts Payable) Details
        </Text>
        {renderSection(apForm, 'ap')}
      </ScrollView>
    </KeyboardAvoidingView>
      <SafeAreaView>
        <View style={styles.actionRow}>
          <Button mode="contained" style={styles.cancelButton} textColor="#000" onPress={() => {}}>
            Reset
          </Button>
          <Button mode="contained" style={styles.saveButton} textColor="#fff" onPress={submitForm}>
            Save Changes
          </Button>
        </View>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: scale(16),
    paddingBottom: verticalScale(30),
    backgroundColor: '#fff',
    paddingTop: 0,
  },
  sectionTitle: {
    fontSize: moderateScale(15),
    fontFamily: fontFamily.semiBold,
    marginBottom: verticalScale(8),
    color: '#1F2937',
  },
  card: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 6,
    padding: moderateScale(12),
    marginBottom: verticalScale(12),
    backgroundColor: '#F9FAFB',
    position: 'relative',
  },
  deleteIcon: {
    position: 'absolute',
    top: scale(6),
    right: scale(6),
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(10),
  },
  radioLabel: {
    fontFamily: fontFamily.medium,
    fontSize: moderateScale(14),
    marginVertical: verticalScale(8),
  },
  blockHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: verticalScale(20),
  },
  rowHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sectionHeader: {
    fontSize: moderateScale(15),
    fontFamily: fontFamily.semiBold,
    color: '#1F2937',
    marginVertical: verticalScale(10),
  },
  countryCodeBox: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 4,
    paddingHorizontal: scale(8),
    paddingVertical: verticalScale(6),
    marginRight: scale(8),
    backgroundColor: '#F3F4F6',
    width: scale(80),
  },
  flag: {
    fontSize: moderateScale(16),
    marginRight: scale(4),
  },
  countryCode: {
    fontSize: moderateScale(14),
    color: '#1F2937',
    fontFamily: fontFamily.medium,
  },
  phoneInputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(10),
    marginBottom: verticalScale(10),
  },
  phoneRow: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 6,
    padding: moderateScale(12),
    marginBottom: verticalScale(12),
    backgroundColor: '#F9FAFB',
    position: 'relative',
  },
  checkRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(10),
    marginTop: verticalScale(6),
  },
  socialRow: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 6,
    padding: moderateScale(12),
    marginBottom: verticalScale(12),
    backgroundColor: '#F9FAFB',
    position: 'relative',
  },
  label: {
    fontFamily: fontFamily.medium,
    fontSize: moderateScale(13),
    marginBottom: verticalScale(4),
    color: '#374151',
  },
  actionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    gap: scale(10),
    paddingVertical: verticalScale(10),
  },
  cancelButton: {
    flex: 1,
    borderRadius: 4,
    backgroundColor: '#E5E7EB',
  },
  saveButton: {
    flex: 1,
    borderRadius: 4,
    backgroundColor: '#2563EB',
  },
  checkboxGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: verticalScale(10),
  },
  checkboxColumn: {
    width: '48%',
    marginBottom: verticalScale(6),
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkboxLabel: {
    fontSize: moderateScale(11),
    fontFamily: fontFamily.regular,
    flexShrink: 1,
  },
  helperText: {
    fontSize: moderateScale(10),
    color: '#6B7280',
    marginBottom: verticalScale(6),
  },
  asterisk: {
    color: 'red',
  },
  sectionLabel: {
    fontFamily: fontFamily.semiBold,
    fontSize: moderateScale(13),
    marginBottom: verticalScale(5),
    marginTop: verticalScale(10),
  },
});
