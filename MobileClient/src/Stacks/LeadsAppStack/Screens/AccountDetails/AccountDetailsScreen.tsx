import React, { useEffect, useState } from 'react';
import { View, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import { Text, Button, Divider } from 'react-native-paper';
import ApiClient from '../../../../Common/API/ApiClient';
import { getUserId, getAccountId, getSelectedWorkspaceId, getSchemaName } from '../../../../Common/Utils/Storage';
import { useSelector } from 'react-redux';
import { encryptPayload } from '../../../../Utils/payloadEncryption';
import { String } from 'aws-sdk/clients/cloudwatchevents';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { fontFamily } from '../../../../Common/Theme/typography';
import { Pencil } from 'lucide-react-native';

const AccountDetailsScreen = ({org_id,navigation}:{org_id:String,navigation:any}) => {
  const [accountData, setAccountData] = useState<any>(null);
  const controlUnitId = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedControlUnit?.data?.id
  );

  useEffect(() => {
    if (org_id) {
      fetchAccountDetails();
    }
  }, [org_id]);

  const fetchAccountDetails = async () => {
    try {
      const user_id = await getUserId();
      const account_id = await getAccountId();
      const workspace_id = await getSelectedWorkspaceId();
      const schemaName = await getSchemaName();

      const payload = {
        schemaName,
        contextJson: {
          user_id,
          account_id,
          workspace_id,
          control_unit_id: controlUnitId,
        },
        inputParamsJson: {
          org_id,
        },
      };
      const encryptedPayload = await encryptPayload(payload);

      const response = await ApiClient.post('/getAccountDetailsData', encryptedPayload);
      if (response?.data?.success) {
        const result = response?.data?.data?.[0];
        setAccountData(result);
      } else {
        console.error('Failed to fetch account details');
      }
    } catch (error) {
      console.error('Error fetching account details:', error);
    }
  };

  const renderRow = (label: string, value?: string | null) => (
    <View style={styles.row}>
      <Text style={styles.label}>{label}</Text>
      <Text style={styles.value}>{value || '--'}</Text>
    </View>
  );

  return (
    <View style={{flex:1}} >
      <View style={styles.companyInfoRow}>
        <Text style={styles.companyInfo}>Account Details</Text>
        <TouchableOpacity onPress={() => {
            navigation.navigate("AccountDetailsForm",{accountData,org_id,fetchAccountDetails})
        }} style={styles.editButton}>
          <Pencil size={moderateScale(16)} color="#2563EB" />
        </TouchableOpacity>
      </View>

    <ScrollView contentContainerStyle={styles.container} bounces={false}>
      <Text style={styles.sectionTitle}>AR (Accounts Receivable) Contact Details</Text>
      {renderRow('Contact Name', accountData?.ar_contact_profile_name)}
      {renderRow('Sales Person', accountData?.ar_sales_person_name)}
      {renderRow('Payment Terms', accountData?.ar_payment_term_name)}
      {renderRow('Credit Limit', accountData?.ar_credit_limit)}
      {renderRow('Account Code', accountData?.ar_account_code)}
      {renderRow('Unvouched Account', accountData?.ar_unvouched_account)}
      {renderRow('Discount Code', accountData?.ar_discount_code)}

      <Divider style={{ marginVertical: 16 }} />

      <Text style={styles.sectionTitle}>AP (Accounts Payable) Contact Details</Text>
      {renderRow('Contact Name', accountData?.ap_contact_profile_name)}
      {renderRow('Sales Person', accountData?.ap_sales_person_name)}
      {renderRow('Payment Terms', accountData?.ap_payment_term_name)}
      {renderRow('Credit Limit', accountData?.ap_credit_limit)}
      {renderRow('Account Code', accountData?.ap_account_code)}
      {renderRow('Unvouched Account', accountData?.ap_unvouched_account)}
      {renderRow('Discount Code', accountData?.ap_discount_code)}
    </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconCircle: {
    backgroundColor: '#e0f0ff',
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  iconText: {
    color: '#007aff',
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 12,
  },
  row: {
    marginBottom: 12,
  },
  label: {
    fontSize: 13,
    color: '#555',
  },
  value: {
    fontSize: 14,
    color: '#000',
  },
  companyInfoRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginHorizontal: scale(16),
      marginTop: verticalScale(10),
    },
      companyInfo: {
        color: '#000',
        fontSize: moderateScale(16),
        fontFamily: fontFamily.medium,
      },
       editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(6),
    paddingVertical: scale(6),
    backgroundColor: '#EFF6FF',
    borderRadius: 30,
  },
});

export default AccountDetailsScreen;
