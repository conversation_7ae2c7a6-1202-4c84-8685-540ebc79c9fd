// components/CategorySelector.tsx
import React from 'react';
import { View, TouchableOpacity, TextInput, ScrollView, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import { Search, X, Package } from 'lucide-react-native';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { HSCodeCategory } from '../types/category.types';
import { getHSCodeDisplayInfo } from '../utils/helpers';

interface CategorySelectorProps {
  categories: HSCodeCategory[];
  selectedHSCode: HSCodeCategory | null;
  showDropdown: boolean;
  searchText: string;
  onToggleDropdown: () => void;
  onCategorySelect: (category: HSCodeCategory) => void;
  onSearchTextChange: (text: string) => void;
  onClearSelection: () => void;
  loading?: boolean;
  updateFormField: () => {};
}

export const CategorySelector: React.FC<CategorySelectorProps> = ({
  categories,
  hs_code_category_name,
  category_hs_code,
  hs_code_sub_category_name,
  isMainCategory,
  sub_category_hs_code,
  showDropdown,
  searchText,
  onToggleDropdown,
  onCategorySelect,
  onSearchTextChange,
  onClearSelection,
  loading = false,
  updateFormField,
}) => {
  // Filter categories based on search text
  const filteredCategories = categories.filter(
    (category) =>
      category.hs_code_category_name.toLowerCase().includes(searchText.toLowerCase()) ||
      category.category_hs_code.includes(searchText)
  );

  const finalHSCode = {
    isMainCategory,
    hs_code_category_name,
    category_hs_code,
    hs_code_sub_category_name,
    sub_category_hs_code,
  };
  const selectedDisplay = getHSCodeDisplayInfo(finalHSCode);
  // console.log("selectedDisplay", finalHSCode)
  if (!selectedDisplay?.name) {
    return (
      <View style={styles.section}>
        <TouchableOpacity
          style={styles.categorySelector}
          onPress={onToggleDropdown}
          disabled={loading}
        >
          <Text style={styles.placeholderText}>
            {loading ? 'Loading categories...' : 'Select a category'}
          </Text>
          <View style={styles.dropdownIcon}>
            <Text style={styles.chevron}>▼</Text>
          </View>
        </TouchableOpacity>

        {showDropdown && (
          <CategoryDropdown
            categories={filteredCategories}
            searchText={searchText}
            onSearchTextChange={onSearchTextChange}
            onCategorySelect={(value) => {
              // console.log('category data', value);
              updateFormField('objectDataWithKeys', {
                category_hs_code: value?.category_hs_code ?? '',
                hs_code_category_id: value?.hs_code_category_id ?? 0,
                hs_code_category_name: value?.hs_code_category_name ?? '',
                isMainCategory: value?.isMainCategory ?? true,
                subCodes: value?.subCodes ?? [],
              });
            }}
          />
        )}
      </View>
    );
  }

  return (
    <View style={styles.section}>
      <View style={styles.selectedCategoryContainer}>
        <View style={styles.selectedCategory}>
          <View style={styles.categoryIcon}>
            <Package size={20} color="#6B7280" />
          </View>
          <View style={styles.categoryInfo}>
            <Text style={styles.categoryName}>{selectedDisplay?.name}</Text>
          </View>
          <Text style={styles.categoryCode}>{selectedDisplay?.code}</Text>
          <TouchableOpacity onPress={onClearSelection} style={styles.clearButton}>
            <X size={20} color="#6B7280" />
          </TouchableOpacity>
          <TouchableOpacity onPress={onToggleDropdown}>
            <Text style={styles.chevron}>▼</Text>
          </TouchableOpacity>
        </View>
      </View>

      {showDropdown && (
        <CategoryDropdown
          categories={filteredCategories}
          searchText={searchText}
          onSearchTextChange={onSearchTextChange}
          onCategorySelect={(value) => {
            updateFormField('objectDataWithKeys', {
              category_hs_code: value?.category_hs_code ?? '',
              hs_code_category_id: value?.hs_code_category_id ?? 0,
              hs_code_category_name: value?.hs_code_category_name ?? '',
              isMainCategory: value?.isMainCategory ?? false,
              subCodes: value?.subCodes ?? [],
            });
          }}
        />
      )}
    </View>
  );
};

interface CategoryDropdownProps {
  categories: HSCodeCategory[];
  searchText: string;
  onSearchTextChange: (text: string) => void;
  onCategorySelect: (category: HSCodeCategory) => void;
}

const CategoryDropdown: React.FC<CategoryDropdownProps> = ({
  categories,
  searchText,
  onSearchTextChange,
  onCategorySelect,
}) => {
  return (
    <View style={styles.dropdown}>
      {/* Search Input */}
      <View style={styles.searchContainer}>
        <Search size={16} color="#6B7280" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search categories..."
          value={searchText}
          onChangeText={onSearchTextChange}
          placeholderTextColor="#9CA3AF"
        />
      </View>

      {/* Categories List */}
      <ScrollView style={styles.categoriesList} nestedScrollEnabled={true} bounces={false}>
        {categories.map((category) => (
          <TouchableOpacity
            key={category.hs_code_category_id}
            style={styles.categoryItem}
            onPress={() => onCategorySelect(category)}
          >
            <Text style={styles.categoryItemName} numberOfLines={2}>
              {category.hs_code_category_name}
            </Text>
            <Text style={styles.categoryItemCode}>{category.category_hs_code}</Text>
          </TouchableOpacity>
        ))}

        {categories.length === 0 && (
          <View style={styles.noResults}>
            <Text style={styles.noResultsText}>No categories found</Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: verticalScale(10),
  },
  categorySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFF',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: scale(10),
    paddingVertical: verticalScale(5),
  },
  placeholderText: {
    fontSize: moderateScale(12),
    color: '#9CA3AF',
  },
  dropdownIcon: {
    padding: scale(4),
  },
  chevron: {
    fontSize: moderateScale(11),
    color: '#6B7280',
  },
  selectedCategoryContainer: {
    marginBottom: verticalScale(8),
  },
  selectedCategory: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: scale(16),
    paddingVertical: verticalScale(5),

    // paddingVertical: verticalScale(12),
  },
  categoryIcon: {
    marginRight: scale(12),
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: moderateScale(12),
    fontWeight: '500',
    color: '#111827',
  },
  categoryCode: {
    fontSize: moderateScale(11),
    color: '#6B7280',
    fontWeight: '600',
    marginRight: scale(12),
  },
  clearButton: {
    padding: scale(4),
    marginRight: scale(8),
  },
  dropdown: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    marginTop: verticalScale(4),
    maxHeight: verticalScale(300),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    paddingVertical: verticalScale(12),
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  searchIcon: {
    marginRight: scale(8),
  },
  searchInput: {
    flex: 1,
    fontSize: moderateScale(12),
    color: '#111827',
    paddingVertical: 0,
  },
  categoriesList: {
    maxHeight: verticalScale(200),
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: scale(16),
    paddingVertical: verticalScale(12),
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  categoryItemName: {
    flex: 1,
    fontSize: moderateScale(12),
    color: '#111827',
    marginRight: scale(12),
  },
  categoryItemCode: {
    fontSize: moderateScale(11),
    color: '#6B7280',
    fontWeight: '600',
    minWidth: scale(30),
    textAlign: 'right',
  },
  noResults: {
    padding: scale(16),
    alignItems: 'center',
  },
  noResultsText: {
    fontSize: moderateScale(12),
    color: '#6B7280',
  },
});
