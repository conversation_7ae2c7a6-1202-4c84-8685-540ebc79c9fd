// components/AdditionalFieldsRenderer.tsx
import React, { useState } from 'react';
import { View, TouchableOpacity, StyleSheet, Modal, FlatList } from 'react-native';
import { Text, Button, TextInput, IconButton, Menu } from 'react-native-paper';
import { ChevronDown, Upload } from 'lucide-react-native';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import CustomInput from '../../../../../Components/UI/TextInput';
import { DropdownField } from '../../../../../Components/UI/Menu/DropdownModal';
import { getFieldValue, handleUpdateField } from '../utils/helpers';
import FileViewer from 'react-native-file-viewer';
import DocumentPicker from 'react-native-document-picker';
import { Eye, Trash2 } from 'lucide-react-native';
interface HazardousCargoFieldsProps {
  formState: CategoryFormState;
  onFieldUpdate: (field: string, value: any) => void;
  onOpenImoModal: () => void;
}
import {
  CargoType,
  CategoryFormState,
  ImoClass,
  ImoSubClass,
  Dimensions,
} from '../types/category.types';
import {
  requiresHazardousFields,
  requiresTemperatureFields,
  requiresDimensionFields,
  getImoDisplayText,
  requiresPerishableFields,
} from '../utils/helpers';
import { TEMPERATURE_UNITS, DIMENSION_UNITS } from '../utils/constants';
import { updateFormField } from '../../../utils/EnquiryFormUpdateUtils';
import { showToast } from '../../../../../Components/AppToaster/AppToaster';

interface AdditionalFieldsRendererProps {
  selectedCargoTypes: number[];
  cargoTypes: CargoType[];
  formData: CategoryFormState;
  onFieldUpdate: (field: string, value: any) => void;
  onDimensionsUpdate: (dimensions: Dimensions) => void;
  onOpenImoModal: () => void;
}

export const AdditionalFieldsRenderer: React.FC<AdditionalFieldsRendererProps> = ({
  cargoType,
  cargo_type_id,
  cargoTypes,
  formData,
  onFieldUpdate,
  onDimensionsUpdate,
  onOpenImoModal,
  updateFormField,
}) => {
  // if (selectedCargoTypes?.length === 0) return null;

  // const selectedCargoTypeId = selectedCargoTypes[0];
  // const selectedCargoType = cargoTypes.find((ct) => ct.cargo_type_id === selectedCargoTypeId);
  // console.log('cargoType >>>', cargoType);
  if (!cargo_type_id) return null;

  // const cargoTypeName = selectedCargoType.cargo_type_name;

  // Render hazardous cargo fields
  if (requiresHazardousFields(cargoType)) {
    return (
      <View style={styles.additionalFields}>
        <HazardousCargoFields
          formData={formData}
          onFieldUpdate={onFieldUpdate}
          onOpenImoModal={onOpenImoModal}
          updateFormField={updateFormField}
        />
      </View>
    );
  }

  // Render temperature/perishable cargo fields
  if (requiresTemperatureFields(cargoType)) {
    return (
      <View style={styles.additionalFields}>
        <TemperatureCargoFields
          formData={formData}
          onFieldUpdate={onFieldUpdate}
          updateFormField={updateFormField}
          type={'temperatureFeild'}
        />
      </View>
    );
  }

  if (requiresPerishableFields(cargoType)) {
    return (
      <View style={styles.additionalFields}>
        <TemperatureCargoFields
          formData={formData}
          onFieldUpdate={onFieldUpdate}
          updateFormField={updateFormField}
          type={'perishable'}
        />
      </View>
    );
  }

  // Render oversized cargo fields
  if (requiresDimensionFields(cargoType)) {
    return (
      <View style={styles.additionalFields}>
        <OversizedCargoFields
          formData={formData}
          onDimensionsUpdate={onDimensionsUpdate}
          onFieldUpdate={onFieldUpdate}
          updateFormField={updateFormField}
        />
      </View>
    );
  }

  return null;
};

interface HazardousCargoFieldsProps {
  formData: CategoryFormState;
  onFieldUpdate: (field: string, value: any) => void;
  onOpenImoModal: () => void;
}

interface TemperatureCargoFieldsProps {
  formState: CategoryFormState;
  onFieldUpdate: (field: string, value: any) => void;
}
const HazardousCargoFields: React.FC<HazardousCargoFieldsProps> = ({
  formData,
  onFieldUpdate,
  onOpenImoModal,
  updateFormField,
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [documentName, setDocumentName] = useState('');
  const [selectedFiles, setSelectedFiles] = useState<any[]>([]); // Changed to array
  const [uploadedDocs, setUploadedDocs] = useState<any[]>(formData?.uploadedDocs || []);
  const [showError, setShowError] = useState(false);


  const canUpload = true;

  // Updated file picker for multiple files
  const pickDocuments = async () => {
    try {
      const results = await DocumentPicker.pick({
        type: [DocumentPicker.types.allFiles],
        allowMultiSelection: true, // Enable multiple selection
      });
      console.log('results', results);
      setSelectedFiles(results);
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        // User cancelled the picker
      } else {
        console.error('DocumentPicker Error: ', err);
      }
    }
  };

  // Updated add documents function
  const addDocuments = () => {
    if (selectedFiles.length === 0) return;

    const newDocs = selectedFiles.map((file, index) => ({
      id: `${Date.now()}_${index}`,
      name: documentName?.trim() ? `${documentName.trim()}_${file?.name}` : file?.name ,
        // selectedFiles.length === 1 ? documentName.trim() : `${documentName.trim()}_${index + 1}`,
      size: (file.size / 1024 / 1024).toFixed(2),
      file: file,
      originalFileName: file.name,
    }));

    const newDocsArray = [...uploadedDocs, ...newDocs];
    console.log(newDocsArray, 'nwewe data');
    setUploadedDocs(newDocsArray);
    onFieldUpdate('uploadedDocs', newDocsArray); // <--- here you update the form data
    setDocumentName('');
    setSelectedFiles([]);
    setShowError(false);
    setModalVisible(false);
  };

  // Remove selected file from modal
  const removeSelectedFile = (index: number) => {
    const filtered = selectedFiles.filter((_, i) => i !== index);
    setSelectedFiles(filtered);
  };

  // Delete doc from uploaded list (unchanged)
  const deleteDocument = (id: string) => {
    const filtered = uploadedDocs.filter((doc) => doc.id !== id);
    setUploadedDocs(filtered);
    onFieldUpdate('uploadedDocs', filtered);
  };
  const deleteExistFile = (id: string) => {
    const filtered = formData?.enquiryDocuments?.filter((doc) => doc.document_id !== id);
    console.log('deleteExistFile', filtered);
    // setUploadedDocs(filtered);
    onFieldUpdate('enquiryDocuments', filtered);
  };

  const validateDocs = () => {
    if (canUpload && uploadedDocs.length === 0) {
      setShowError(true);
    } else {
      setShowError(false);
    }
  };
  return (
    <View>
      <View style={styles.fieldRow}>
        <View style={[{ flex: 1, marginRight: scale(16) }]}>
          <Text style={styles.fieldLabel} variant="semiBold">
            IMO Class
          </Text>
          <TouchableOpacity style={styles.imoSelector} onPress={onOpenImoModal}>
            <Text
              style={[
                styles.imoSelectorText,
                formData.imo_sub_class_name && styles.imoSelectorTextSelected,
              ]}
              numberOfLines={1}
            >
              {formData.imo_sub_class_name || 'Select IMO Class'}
            </Text>
            <ChevronDown size={16} color="#6B7280" />
          </TouchableOpacity>
        </View>
        <View style={[{ flex: 1 }]}>
          <CustomInput
            label="UN Number"
            placeholder="0"
            value={formData.unNumber}
            onChangeText={(text: string) => updateFormField('unNumber', text)}
            keyboardType="numeric"
            isMandatory={true}
            leftIcon={undefined}
            rightIcon={undefined}
            style={{ textAlign: 'right' }}
            onFocus={undefined}
            onBlur={undefined}
            errorText={undefined}
            maxLength={undefined}
          />
        </View>
      </View>
      {formData?.selectedMode?.toLowerCase() === 'air' && (
        <View style={[styles.fieldRow]}>
          <CustomInput
            label="SR Number"
            placeholder="0"
            value={formData.srNumber}
            onChangeText={(text: string) => updateFormField('srNumber', text)}
            keyboardType="numeric"
            isMandatory={true}
            leftIcon={undefined}
            rightIcon={undefined}
            style={{ marginRight: 13, textAlign: 'right' }}
            onFocus={undefined}
            onBlur={undefined}
            errorText={undefined}
            maxLength={undefined}
          />
          <CustomInput
            label="RND"
            placeholder="0"
            value={formData.Rnd}
            onChangeText={(text: string) => updateFormField('Rnd', text)}
            keyboardType="numeric"
            isMandatory={true}
            leftIcon={undefined}
            rightIcon={undefined}
            style={{ textAlign: 'right' }}
            onFocus={undefined}
            onBlur={undefined}
            errorText={undefined}
            maxLength={undefined}
          />
          <CustomInput
            label="RFL"
            placeholder="0"
            value={formData.Rfl}
            onChangeText={(text: string) => updateFormField('Rfl', text)}
            keyboardType="numeric"
            isMandatory={true}
            leftIcon={undefined}
            rightIcon={undefined}
            style={{ textAlign: 'right' }}
            onFocus={undefined}
            onBlur={undefined}
            errorText={undefined}
            maxLength={undefined}
          />
        </View>
      )}
      <View style={{ marginTop: verticalScale(4) }}>
        <Text style={styles.fieldLabel} variant="semiBold">
          Upload Documents
          <Text style={{ color: '#FF3B30' }}> *</Text>
        </Text>
        {canUpload && (
          <TouchableOpacity
            style={styles.modernUploadButton}
            onPress={() => setModalVisible(true)}
            activeOpacity={0.7}
          >
            <Upload size={20} color="#007AFF" style={{ marginRight: scale(8) }} />
            <Text style={styles.modernUploadButtonText}>Upload Documents</Text>
          </TouchableOpacity>
        )}

        {formData?.enquiryDocuments?.length > 0 && (
          <>
            <Text style={{ color: '#6B7280', marginBottom: 6 }}>Old uploaded documents:</Text>
            <View>
              {formData?.enquiryDocuments?.map((item, index) => (
                <View
                  key={`${item?.document_id} ${index}`}
                  style={[styles.uploadedDocRow, { paddingVertical: 1 }]}
                >
                  <View style={{ flex: 1 }}>
                    {item?.document_name && (
                      <Text style={{ fontSize: moderateScale(12), fontWeight: '600' }}>
                        {item.document_name}
                      </Text>
                    )}
                  </View>
                  <View style={{ flexDirection: 'row' }}>
                    {/* <IconButton
                      icon="eye"
                      size={20}
                      onPress={() => {
                       
                      }}
                    /> */}
                    <IconButton
                      icon="delete"
                      size={20}
                      onPress={() => deleteExistFile(item.document_id)}
                    />
                  </View>
                </View>
              ))}
            </View>
          </>
        )}

        {/* Uploaded documents list - unchanged */}
        {uploadedDocs.length > 0 && (
          <>
            <Text style={{ color: '#6B7280', marginBottom: 6 }}>Newly uploaded documents:</Text>
            <View>
              {uploadedDocs.map((item) => (
                <View key={item.id} style={styles.uploadedDocRow}>
                  <View style={{ flex: 1 }}>
                    <Text style={{ fontWeight: '600' }}>{item?.name || item.originalFileName}</Text>
                    <Text style={{ color: '#6B7280', fontSize: moderateScale(12) }}>
                      ({item.size} MB)
                    </Text>
                    {/* {item.originalFileName && (
                      <Text style={{ color: '#6B7280', fontSize: moderateScale(10) }}>
                        {item.originalFileName}
                      </Text>
                    )} */}
                  </View>
                  <View style={{ flexDirection: 'row' }}>
                    <IconButton
                      icon="eye"
                      size={20}
                      onPress={() => {
                        FileViewer.open(item.file.uri,{ showOpenWithDialog: true })
                          .then(() => {
                            console.log('File opened successfully');
                          })
                          .catch((error) => {
                            console.error('Error opening file:', error);
                            showToast.error('Unable to open file');
                          });
                      }}
                    />
                    <IconButton icon="delete" size={20} onPress={() => deleteDocument(item.id)} />
                  </View>
                </View>
              ))}
            </View>
          </>
        )}
      </View>

      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle} variant="bold">
                Upload Documents
              </Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Text style={styles.closeButton}>✕</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              {/* <View style={{height:moderateScale(60)}}> */}
              <CustomInput
                label="Document Name"
                placeholder="Enter document name"
                value={documentName}
                onChangeText={setDocumentName}
                // style={styles.documentNameInput}
                mode="outlined"
                // containerStyle={{ flex: 0 }}
                // autoFocus
              />
              {/* </View> */}
              <View></View>
              <Text variant="medium" style={styles.sectionLabel}>
                Select Files
              </Text>
              <TouchableOpacity
                style={styles.filePickerButton}
                onPress={pickDocuments}
                activeOpacity={0.7}
              >
                <Upload
                  size={moderateScale(16)}
                  strokeWidth={2.6}
                  color="#007AFF"
                  style={styles.uploadIcon}
                />
                <Text style={styles.filePickerText} variant="semiBold">
                  Upload
                </Text>
              </TouchableOpacity>

              {/* Selected files list */}
              {selectedFiles.length > 0 && (
                <View style={styles.selectedFilesContainer}>
                  <Text style={styles.selectedFilesTitle}>
                    Selected Files ({selectedFiles.length})
                  </Text>
                  {selectedFiles.map((file, index) => (
                    <View key={index} style={styles.selectedFileRow}>
                      <View style={{ flex: 1 }}>
                        <Text style={styles.selectedFileName}>{file.name}</Text>
                        <Text style={styles.selectedFileSize}>
                          {(file.size / 1024 / 1024).toFixed(2)} MB
                        </Text>
                      </View>
                      <TouchableOpacity onPress={() => removeSelectedFile(index)}>
                        <Text style={styles.removeFileButton}>✕</Text>
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              )}

              <View style={styles.modalActions}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => {
                    setModalVisible(false);
                    setSelectedFiles([]);
                    setDocumentName('');
                  }}
                  activeOpacity={0.7}
                >
                  <Text style={styles.cancelButtonText} variant="medium">
                    Cancel
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.addButton, selectedFiles.length === 0 && styles.disabledButton]}
                  onPress={addDocuments}
                  disabled={selectedFiles.length === 0}
                  activeOpacity={0.7}
                >
                  <Text
                    style={[
                      styles.addButtonText,
                      selectedFiles.length === 0 && styles.disabledButtonText,
                    ]}
                    variant="medium"
                  >
                    Add Documents ({selectedFiles.length})
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

interface TemperatureCargoFieldsProps {
  formData: CategoryFormState;
  onFieldUpdate: (field: string, value: any) => void;
}

const TemperatureCargoFields: React.FC<TemperatureCargoFieldsProps> = ({
  formData,
  onFieldUpdate,
  updateFormField,
  type,
}) => {
  // console.log("type data ", formData.temperatureUnit)
  const [isMenuVisible, setIsMenuVisible] = useState(false);

  return (
    <View>
      {/* Temperature Field */}
      <View
        style={[
          styles.fieldContainer,
          { flex: 1, marginRight: scale(16), flexDirection: 'row', gap: 10 },
        ]}
      >
        <View style={styles.temperatureContainer}>
          <View style={{ flex: 2 }}>
            <CustomInput
              label="Temperature Regime"
              value={getFieldValue(formData, type, 'temperature')}
              onChangeText={(text) => handleUpdateField(type, 'temperature', text, updateFormField)}
              keyboardType="numeric"
              containerStyle={styles.temperatureInput}
              style={{ textAlign: 'right' }}
            />
          </View>
          <View>
            {/* <DropdownField
              data={TEMPERATURE_UNITS}
              label=" "
              // type = 'radio'
              showAsterisk={false}
              valueField="label"
              labelField="label"
              value={getFieldValue(formData, type, 'units')}
              onSelect={(value) => {
                console.log('value selected in perishable carge temp unit', value);
                handleUpdateField(type, 'units', value, updateFormField);
              }}
              dropDownButtonStyles={{ paddingRight: 0 }}
              containerStyle={styles.unitDropdown}
            /> */}
            <Text></Text>
            <Menu
              visible={isMenuVisible}
              onDismiss={() => setIsMenuVisible(false)}
              contentStyle={{ backgroundColor: '#fff',marginTop:50, }}
              // style={{ backgroundColor: '#fff' }}
              
              anchor={
                <View style={{flexDirection:"row",minWidth:scale(60),alignItems:"center",justifyContent:"center",backgroundColor:"#fff",borderRadius:4,borderWidth:1,borderColor:"#ccc",marginTop:10,height:verticalScale(28),paddingHorizontal:scale(5)}}>
                  <Text style={ styles.unitDropdownText}>
                    {getFieldValue(formData, type, 'units')}
                  </Text>
                  <IconButton
                    icon="chevron-down"
                    size={20}
                    color="#888"
                    style={{ margin: 0 }}
                    onPress={() => setIsMenuVisible(true)}
                  />
                </View>
              }
            >
              {TEMPERATURE_UNITS.map((unit) => (
                <Menu.Item
                  key={unit.value}
                  title={unit.label}
                  onPress={() => {
                    handleUpdateField(type, 'units', unit, updateFormField);
                    setIsMenuVisible(false);
                  }}
                  titleStyle={styles.menuItemTitle}
                  style={styles.menuItem}
                  trailingIcon={getFieldValue(formData, type, 'units') === unit.label ? 'check' : ''}
                  
                
                />
              ))}
            </Menu> 
          </View>
        </View>
        <View style={styles.humidityContainer}>
          <View style={{ flex: 1 }}>
            <CustomInput
              label={'Humidity ( % )'}
              value={getFieldValue(formData, type, 'humidity')}
              onChangeText={(text) => handleUpdateField(type, 'humidity', text, updateFormField)}
              keyboardType="numeric"
              containerStyle={styles.humidityInput}
              style={{ textAlign: 'right' }}
            />
          </View>
        </View>
      </View>

      {/* Humidity Field */}
      <View style={[styles.fieldContainer]}></View>
    </View>
  );
};

interface OversizedCargoFieldsProps {
  formData: CategoryFormState;
  onDimensionsUpdate: (dimensions: Dimensions) => void;
  onFieldUpdate: (field: string, value: any) => void;
}

const OversizedCargoFields: React.FC<OversizedCargoFieldsProps> = ({
  formData,
  onDimensionsUpdate,
  onFieldUpdate,
  updateFormField,
}) => {
  const [hasShownHeightWarning, setHasShownHeightWarning] = useState(false);
  console.log('hasShownHeightWarning >>>>', hasShownHeightWarning);
  return (
    <View>
      <View style={styles.dimensionsContainer}>
        <View>
          <Text style={styles.fieldLabel} variant="semiBold">
            Length
          </Text>
          <CustomInput
            placeholder="0"
            value={formData.oversizedCargoDimensions.length}
            onChangeText={(text: string) =>
              updateFormField('oversizedCargoDimensions', {
                length: text,
              })
            }
            keyboardType="numeric"
            containerStyle={styles.dimensionInput}
            leftIcon={undefined}
            rightIcon={undefined}
            style={{ textAlign: 'right' }}
            onFocus={undefined}
            onBlur={undefined}
            errorText={undefined}
            maxLength={undefined}
          />
        </View>
        <Text style={styles.dimensionSeparator}>×</Text>
        <View>
          <Text style={styles.fieldLabel} variant="semiBold">
            Breadth
          </Text>
          <CustomInput
            //   label="Breadth"
            placeholder="0"
            value={formData.oversizedCargoDimensions.breadth}
            onChangeText={(text: string) =>
              updateFormField('oversizedCargoDimensions', {
                breadth: text,
              })
            }
            keyboardType="numeric"
            containerStyle={styles.dimensionInput}
            leftIcon={undefined}
            rightIcon={undefined}
            style={{ textAlign: 'right' }}
            onFocus={undefined}
            onBlur={undefined}
            errorText={undefined}
            maxLength={undefined}
          />
        </View>
        <Text style={styles.dimensionSeparator}>×</Text>
        <View>
          <Text style={styles.fieldLabel} variant="semiBold">
            Height
          </Text>
          <CustomInput
            //   label="Height"
            placeholder="0"
            value={formData.oversizedCargoDimensions.height}
            onChangeText={(text: string) => {
              const num = parseInt(text);
              //need to maintain global state i think
              if (num > 150) {
                if (!hasShownHeightWarning) {
                  showToast.warning('You are entering a height value greater than 150');
                  setHasShownHeightWarning(true);
                }
              } 
              updateFormField('oversizedCargoDimensions', {
                height: text,
              });
            }}
            keyboardType="numeric"
            containerStyle={styles.dimensionInput}
            leftIcon={undefined}
            rightIcon={undefined}
            style={{ textAlign: 'right' }}
            onFocus={undefined}
            onBlur={undefined}
            errorText={undefined}
            maxLength={undefined}
          />
        </View>
        <View style={{ flex: 1, marginTop: verticalScale(18) }}>
          <DropdownField
            data={DIMENSION_UNITS}
            valueField="value"
            labelField="label"
            value={formData.OversizeddimensionUnitValue}
            onSelect={(value) => {
              console.log('dimension unit selected value', value);
              updateFormField('objectDataWithKeys', {
                OversizeddimensionUnitLabel: value?.label,
                OversizeddimensionUnitValue: value?.value,
              });
            }}
            containerStyle={styles.unitDropdown}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  additionalFields: {
    marginTop: verticalScale(16),
  },
  fieldRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // marginBottom: verticalScale(5),
  },
  fieldContainer: {
    marginBottom: verticalScale(8),
  },

  imoSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFF',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 4,
    paddingHorizontal: scale(16),
    paddingVertical: verticalScale(4),
    // minHeight: verticalScale(28),

    // marginVertical: 4,
    // borderRadius: moderateScale(5),
    height: verticalScale(28),

    // backgroundColor: '#FFF',
    // marginBottom: verticalScale(10),
  },
  imoSelectorText: {
    fontSize: moderateScale(12),
    flex: 1,
    color: '#6B7280',
  },
  imoSelectorTextSelected: {
    color: '#111827',
  },
  temperatureContainer: {
    flex: 2,
    flexDirection: 'row',
    // alignItems: 'center',
  },
  temperatureInput: {
    flex: 1,
    marginRight: scale(8),
  },
  humidityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  humidityInput: {
    flex: 1,
  },
  percentageText: {
    fontSize: moderateScale(12),
    // color: '#6B7280',
    // marginLeft: scale(8),
  },
  dimensionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(8),
  },
  dimensionInput: {
    flex: 1,
  },
  dimensionSeparator: {
    fontSize: moderateScale(12),
    color: '#6B7280',
  },
  unitDropdown: {
    minWidth: scale(60),
  },
  unitDropdownText: {
    fontSize: moderateScale(12),
    color: '#111827',
    flex: 1,
  },
  unitsContainer: {
    backgroundColor: '#f1f5f9',
    minHeight: verticalScale(28),
    marginBottom: verticalScale(10),
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 5,
    borderBottomRightRadius: 5,
    borderTopRightRadius: 5,
  },
  uploadedDocRow: {
    backgroundColor: '#F3F6F9',
    borderRadius: 6,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 12,
    marginBottom: verticalScale(6),
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  modalContent: {
    backgroundColor: '#fff',
    borderRadius: moderateScale(12),

    width: '90%',
    maxWidth: 400,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },

  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    // paddingBottom: 10,
  },

  modalTitle: {
    fontSize: moderateScale(14),
    color: '#000',
    flex: 1,
  },

  closeButton: {
    fontSize: moderateScale(13),
    color: '#666',
    padding: 4,
  },

  modalBody: {
    padding: 20,

    // paddingTop: 10,
  },

  sectionLabel: {
    fontSize: moderateScale(12),

    color: '#000',
    marginBottom: verticalScale(8),
    marginTop: verticalScale(8),
  },

  documentNameInput: {
    marginBottom: verticalScale(16),
    backgroundColor: '#fff',
  },

  filePickerButton: {
    // marginBottom: verticalScale(16),
    flexDirection: 'row',
    borderColor: '#e0e0e0',
    borderWidth: 1,

    backgroundColor: 'white',
    paddingVertical: verticalScale(8),
    marginBottom: verticalScale(8),
    paddingHorizontal: scale(10),
    borderRadius: 5,
    gap: 10,

    // alignSelf: 'flex-start',
  },
  filePickerText: {
    color: '#3377ff',
    textAlign: 'center',
  },

  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: scale(12),
  },

  cancelButton: {
    // Styling for cancel button
  },
  buttonText: {
    fontSize: moderateScale(11),
  },

  addButton: {
    backgroundColor: '#007AFF',
  },
  selectedFilesContainer: {
    marginTop: verticalScale(12),
    marginBottom: verticalScale(16),
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    padding: 12,
  },

  selectedFilesTitle: {
    fontSize: moderateScale(11),
    marginBottom: verticalScale(8),
    color: '#333',
  },

  selectedFileRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },

  selectedFileName: {
    fontSize: moderateScale(11),
    fontWeight: '500',
    color: '#333',
  },

  selectedFileSize: {
    fontSize: moderateScale(11),
    color: '#6B7280',
    marginTop: 2,
  },

  removeFileButton: {
    fontSize: 16,
    color: '#EF4444',
    padding: 4,
    fontWeight: 'bold',
  },
  uploadButton: {
    backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    marginTop: verticalScale(8),
    marginBottom: verticalScale(12),
    paddingVertical: verticalScale(6),
    paddingHorizontal: scale(16),
    // alignSelf: 'flex-start',

    // minWidth: scale(140),
    // Shadow for iOS
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    // Elevation for Android
    elevation: 2,
  },

  uploadButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  uploadIconContainer: {
    marginRight: scale(8),
  },

  uploadIcon: {
    fontSize: moderateScale(12),
  },

  uploadButtonText: {
    fontSize: moderateScale(12),
    color: '#374151',
  },

  // Updated field label to match the theme
  fieldLabel: {
    fontSize: moderateScale(12),
    color: '#1F2937',
    marginBottom: verticalScale(5),
  },
  modernUploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0F8FF',
    borderWidth: 1,
    borderColor: '#007AFF',
    borderRadius: moderateScale(12),
    paddingVertical: verticalScale(6),
    paddingHorizontal: scale(16),
    marginTop: verticalScale(8),
    marginBottom: verticalScale(12),
    alignSelf: 'flex-start',
  },

  modernUploadButtonText: {
    fontSize: moderateScale(12),
    fontWeight: '600',
    color: '#007AFF',
  },
  cancelButton: {
    paddingVertical: verticalScale(12),
    paddingHorizontal: scale(20),
    borderRadius: 8,
    backgroundColor: 'transparent',
  },

  cancelButtonText: {
    color: '#666',
    textAlign: 'center',
  },

  addButton: {
    backgroundColor: '#007AFF',
    paddingVertical: verticalScale(6),
    paddingHorizontal: scale(20),
    borderRadius: 8,
    minWidth: scale(140),
  },

  addButtonText: {
    color: '#ffffff',

    textAlign: 'center',
  },

  disabledButton: {
    backgroundColor: '#E5E7EB',
    opacity: 0.6,
  },

  disabledButtonText: {
    color: '#9CA3AF',
  },

  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: scale(12),
    alignItems: 'center',
  },
  menuItemTitle: {
    fontSize: moderateScale(12),
    color: '#374151',
  },
  menuItem: {
    height: 30,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: scale(10),
  


  },
});
