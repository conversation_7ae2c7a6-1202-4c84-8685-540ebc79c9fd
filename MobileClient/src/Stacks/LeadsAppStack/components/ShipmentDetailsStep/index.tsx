import React from 'react';
import { View, ActivityIndicator, ScrollView, Alert } from 'react-native';
import { Text, useTheme } from 'react-native-paper';
import { EnquiryFormData } from '../../types';
import ActionButtons from '../ActionButtons';
import { useShipmentData } from './hooks/useShipmentData';
import { useLocationValidation } from './hooks/useLocationValidation';
import { useModeSpecificData } from './hooks/useModeSpecificData';
import { useShipmentValidation } from './hooks/useShipmentValidation';
import { ModeSelector } from '../ModeSelector';
import { DirectionSelector } from '../DirectionSelector';
import { LocationSection } from '../LocationSection';
import { ModeSpecificSections } from './components/ModeSpecificSections';
import { layoutStyles } from './styles';
import { ShipmentStepProps } from './types/shipment.types';
import { isValidLocation, isValidTransit } from './utils/validation';
import ShipmentCategory from '../ShipmentCategoryStep';
import { DropdownField } from '../../../../Components/UI/Menu/DropdownModal';
import { AddressInput } from '../Shared/AddressInput';
import ContainerDetailsStep from '../ContainerDetailsStep';
import { useFormValidation } from '../ContainerDetailsStep/hooks/useContainerDetails';
import AppLoader from '../../../../Components/Loader/AppLoader/InitialLoaderWithText';
import EmptyorErrorComponent from '../../../../Components/EmptyOrError/EmptyOrErrorComponent';
import { Ship } from 'lucide-react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { convertFirstCharacterCapital } from '../../../../Utils';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { getModeIcon } from './utils/transformers';
import LocationAddressSearch from '../../../../Components/LocationAddressSearch';
export default function ShipmentDetailsStep({
  formData,
  updateFormData,
  currentStep,
  totalSteps,
  onNext,
  onPrevious,
  initialFormState,
}: ShipmentStepProps) {
  const theme = useTheme();

  const {
    shipmentOptions,
    directionOptions,
    incoterms,
    borderChargesOptions,
    packageTypes,
    loading,
    error,
    handleAddress,
  } = useShipmentData(formData);

  const {
    handleModeChange,
    handleDirectionChange,
    handleIncotermSelect,
    handleTransshipmentToggle,
    handleBorderChargesToggle,
    handleAddressStage,
  } = useModeSpecificData(updateFormData, formData);

  const { errors, isValid } = useShipmentValidation(formData);
  const { isValid: iscontainerValidated } = useFormValidation(formData);
  console.log('isValid data', !iscontainerValidated() && !isValid);

  const onModeSelect = (mode: any) => {
    handleModeChange(mode, updateFormData, initialFormState);
  };

  const onDirectionSelect = (direction: any) => {
    handleDirectionChange(direction, updateFormData);
  };
  const IconComponent = getModeIcon(formData.selectedMode);
  return (
    <View style={[layoutStyles.container, { backgroundColor: theme.colors.background }]}>
      {loading ? (
        <AppLoader message="Loading shipment options..." />
      ) : error ? (
        <EmptyorErrorComponent message="" />
      ) : (
        <ScrollView
          style={layoutStyles.scrollContent}
          automaticallyAdjustKeyboardInsets
          showsVerticalScrollIndicator={false}
          keyboardDismissMode="on-drag"
          bounces={false}
        >
          {formData?.type === 'New' ? (
            <ModeSelector
              shipmentOptions={shipmentOptions}
              selectedMode={formData.selectedMode}
              onModeSelect={onModeSelect}
            />
          ) : (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginBottom: moderateScale(12),
              }}
            >
              <IconComponent
                size={25}
                color={'#3b82f6'}
                style={{ marginRight: moderateScale(6) }}
              />
              <Text style={{ fontSize: moderateScale(16), color: '#3b82f6' }} variant="medium">
                {convertFirstCharacterCapital(formData.selectedMode)}
              </Text>
            </View>
          )}

          <DirectionSelector
            directionOptions={directionOptions}
            selectedDirection={formData.selectedDirection}
            onDirectionSelect={onDirectionSelect}
          />
          <DropdownField
            label="Incoterms"
            data={incoterms}
            valueField="incoterm_name"
            placeholder="Select incoterm"
            value={formData.incoterm_name}
            onSelect={(value) => handleIncotermSelect(value)}
            isMandatory={true}
          />
          {formData?.incoterm_name == 'Ex Works' && (
            // <AddressInput
            //   label="Ex Works Address"
            //   value={formData.exWorksAddress || ''}
            //   onChangeText={(text) => handleAddressStage('exWorksAddress', text)}
            //   placeholder="Enter Exworks Address"
            // />
            <View style={{ marginBottom: verticalScale(10) }}>
              <LocationAddressSearch
                label="Ex Works Address"
                value={formData.exWorksAddress || ''}
                onAddressChange={(text) => handleAddressStage('exWorksAddress', text)}
                placeholder="Ex Works Address"
              />
            </View>
          )}

          <ContainerDetailsStep formData={formData} updateFormData={updateFormData} />
          <View style={{ flex: 1, justifyContent: 'flex-end', alignItems: 'flex-end' }}>
            <ActionButtons
              currentStep={currentStep}
              totalSteps={totalSteps}
              onNext={onNext}
              onPrevious={onPrevious}
              isNextDisabled={!(iscontainerValidated() && isValid)}
              // isNextDisabled={!true}
            />
          </View>
        </ScrollView>
      )}

      {/* <View style={layoutStyles.actionButtonsContainer}>
      </View> */}
    </View>
  );
}
