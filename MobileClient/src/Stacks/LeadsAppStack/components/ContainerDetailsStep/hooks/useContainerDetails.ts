// hooks/useContainerDetails.ts
import { useState, useEffect } from 'react';
import ApiClient from '../../../../../Common/API/ApiClient';
import { getSchemaName } from '../../../../../Common/Utils/Storage';
import { COMMON_ENDPOINTS, DSREndPoints } from '../../../../../Common/API/ApiEndpoints';
import { TransportationOption, DropdownOption } from '../types/containerDetailsTypes';
import {
  getJobCategory,
  transformApiDataToDropdown,
  transformApiDataToDropdownWithId,
} from '../utils/containerDetailsUtils';

export const useTransportationData = (formData: any, updateFormData: any) => {
  const [transportationOptions, setTransportationOptions] = useState<TransportationOption[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [schemaName, setSchemaName] = useState<string | null>(null);
  const modeOfShipment = formData?.selectedMode;
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const schema = await getSchemaName();
        setSchemaName(schema);

        if (schema && modeOfShipment) {
          const jobCategory = getJobCategory(modeOfShipment);
          const response = await ApiClient.get(
            `${DSREndPoints.GET_MASTERS_DROPDOWN}/${schema}/crm_transportation_by_master?columns=transportation_type_id,job_category,transportation_by_code,transportation_by_name&job_category=${jobCategory}`
          );
          const firstOption = response?.data?.[0];
          setTransportationOptions(
            response?.data?.map((opt) => ({
              ...opt,
              transportation_by_code: opt.transportation_by_code?.trim()
                ? opt.transportation_by_code
                : opt.transportation_by_name,
            })) || []
          );
          if (formData?.transportationBy === '') {
            console.log('i am in update form data in options', formData?.transportationBy);

            updateFormData({
              transportationBy: firstOption.transportation_by_name,
              transportationTypeid: firstOption.transportation_type_id,
              transportationByCode: firstOption.transportation_by_code,
            });
          }
        }
      } catch (err) {
        setError('Error fetching transportation options');
        console.error('Error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [modeOfShipment]);

  return { transportationOptions, loading, error, schemaName };
};

export const useDropdownData = () => {
  const [containerTypes, setContainerTypes] = useState<DropdownOption[]>([]);
  const [packageTypes, setPackageTypes] = useState<any[]>([]);
  const [stackabilityTypes, setStackabilityTypes] = useState<DropdownOption[]>([]);
  const [shipTypes, setShipTypes] = useState<DropdownOption[]>([]);
  const [truckTypes, setTruckTypes] = useState<any[]>([]);
  const [uldContainerTypes, setUldContainerTypes] = useState<any[]>([]);

  const fetchDropdownData = async (endpoint: string, transformer?: (data: any[]) => any[]) => {
    try {
      const response = await ApiClient.get(endpoint);
      return transformer ? transformer(response?.data || []) : response?.data || [];
    } catch (error) {
      console.error(`Error fetching data from ${endpoint}:`, error);
      return [];
    }
  };

  useEffect(() => {
    const fetchAllDropdownData = async () => {
      const schema = await getSchemaName();
      const API_ENDPOINTS = {
        CONTAINER_TYPES: `${COMMON_ENDPOINTS.GET_MASTERS_DROPDOWN}/${schema}/crm_fcl_container_type_master?columns=container_type_id,container_type_name`,
        PACKAGE_TYPES: `${COMMON_ENDPOINTS.GET_MASTERS_DROPDOWN}/${schema}/crm_package_type_master?columns=package_type_id,package_type_name`,
        STACKABILITY_TYPES: `${COMMON_ENDPOINTS.GET_MASTERS_DROPDOWN}/${schema}/crm_stackability_master?columns=stackability_id,stackability_name`,
        SHIP_TYPES: `${COMMON_ENDPOINTS.GET_MASTERS_DROPDOWN}/${schema}/crm_ship_type_master?columns=ship_type_id,ship_type_name`,
        TRUCK_TYPES: `${COMMON_ENDPOINTS.GET_MASTERS_DROPDOWN}/${schema}/crm_truck_type_master?columns=truck_type_id,truck_type_name`,
        ULD_CONTAINER_TYPES: `${COMMON_ENDPOINTS.GET_MASTERS_DROPDOWN}/${schema}/crm_uld_container_type_master?columns=container_type_id,container_type_name`,
      };

      const [
        containerTypesData,
        packageTypesData,
        stackabilityTypesData,
        shipTypesData,
        truckTypesData,
        uldContainerTypesData,
      ] = await Promise.all([
        fetchDropdownData(API_ENDPOINTS.CONTAINER_TYPES, transformApiDataToDropdown),
        fetchDropdownData(API_ENDPOINTS.PACKAGE_TYPES),
        fetchDropdownData(API_ENDPOINTS.STACKABILITY_TYPES, transformApiDataToDropdownWithId),
        fetchDropdownData(API_ENDPOINTS.SHIP_TYPES, transformApiDataToDropdownWithId),
        fetchDropdownData(API_ENDPOINTS.TRUCK_TYPES),
        fetchDropdownData(API_ENDPOINTS.ULD_CONTAINER_TYPES),
      ]);
      setContainerTypes(containerTypesData);
      setPackageTypes(packageTypesData);
      
      setStackabilityTypes(stackabilityTypesData);
      setShipTypes(shipTypesData);
      setTruckTypes(truckTypesData);
      setUldContainerTypes(uldContainerTypesData);
    };

    fetchAllDropdownData();
  }, []);

  return {
    containerTypes,
    packageTypes,
    stackabilityTypes,
    shipTypes,
    truckTypes,
    uldContainerTypes,
  };
};

export const useFormValidation = (formData: any) => {
  const isValid = () => {
    // console.log("formdata>>>", formData)
    const errors: Record<string, boolean> = {};
    const jobType = formData?.selectedMode?.toLowerCase();
    // const transportationBy = selectedTransportationType?.transportation_by_name;

    const {
      containerOptions = [],
      lclOptions = [],
      shipOptions = [],
      standardCargoOptions = [],
      uldOptions = [],
      truckOptions = [],
      ltlOptions = [],
      transportationBy,
      unitSystem,
    } = formData;
    console.log('transportationBy >>>', JSON.stringify(truckOptions, null, 2));

    const calculateShipmentTotal = (lotsToCalculate: any) => {
      let totalVolumeCm3 = 0;
      let totalVolumeM3 = 0;
      let totalWeightKg = 0;
      let totalWeightMt = 0;

      // Track if we have any items with dimensions in meters
      let hasMeterDimensions = false;
      // Track if we have any weights in metric tons
      let hasMetricTonWeights = false;

      lotsToCalculate.forEach((lot: any) => {
        const length = Number.parseFloat(lot.dimensions.length) || 0;
        const width = Number.parseFloat(lot.dimensions.width) || 0;
        const height = Number.parseFloat(lot.dimensions.height) || 0;
        const quantity = Number.parseInt(lot.quantity) || 0;
        const weight = Number.parseFloat(lot.weight) || 0;

        // Calculate volume based on dimensions
        const itemVolume = length * width * height;

        // Process volume based on unit
        if (lot.dimensions.unit === 'm' || lot.dimensions.unit === 'ft') {
          hasMeterDimensions = true;
          // Convert m³ to cm³ for internal calculations
          totalVolumeCm3 += itemVolume * 1000000 * quantity;
          // Also track m³ directly
          totalVolumeM3 += itemVolume * quantity;
        } else {
          // cm or in
          const conversionFactor = lot.dimensions.unit === 'in' ? 16.387064 : 1; // 1 cubic inch = 16.387064 cubic cm
          totalVolumeCm3 += itemVolume * conversionFactor * quantity;
          totalVolumeM3 += ((itemVolume * conversionFactor) / 1000000) * quantity;
        }

        // Process weight based on unit
        if (lot.weightUnit === 'mt' || lot.weightUnit === 'lb') {
          hasMetricTonWeights = true;
          if (lot.weightUnit === 'lb') {
            totalWeightKg += weight * 0.453592; // Convert lb to kg
            totalWeightMt += weight * 0.000453592; // Convert lb to mt
          } else {
            totalWeightKg += weight * 1000;
            totalWeightMt += weight;
          }
        } else {
          // kg or st
          if (lot.weightUnit === 'st') {
            totalWeightKg += weight * 6.35029; // Convert stone to kg
            totalWeightMt += weight * 0.00635029; // Convert stone to mt
          } else {
            totalWeightKg += weight;
            totalWeightMt += weight / 1000;
          }
        }
      });

      // Determine display units based on what's in the lots
      const volumeUnit = hasMeterDimensions
        ? unitSystem === 'SI'
          ? 'm³'
          : 'ft³'
        : unitSystem === 'SI'
        ? 'cm³'
        : 'in³';
      const weightUnit = hasMetricTonWeights
        ? unitSystem === 'SI'
          ? 'mt'
          : 'lb'
        : unitSystem === 'SI'
        ? 'kg'
        : 'st';

      // Return formatted values with appropriate units
      return {
        volume: hasMeterDimensions ? totalVolumeM3.toFixed(2) : totalVolumeCm3.toFixed(2),
        volumeUnit,
        weight: hasMetricTonWeights ? totalWeightMt.toFixed(2) : totalWeightKg.toFixed(2),
        weightUnit,
        // Also provide raw values for other calculations if needed
        rawVolumeM3: totalVolumeM3,
        rawVolumeCm3: totalVolumeCm3,
        rawWeightKg: totalWeightKg,
        rawWeightMt: totalWeightMt,
      };
    };
    // console.log("containerOptions", containerOptions[0]?.fcl_enquiry_items)

    if (jobType === 'sea' || jobType === 'cross') {
      if (transportationBy === 'Full container load') {
        let hasFclError = false;
        console.log(containerOptions, 'yooooo');
        containerOptions.forEach((option: any) => {
          if (!option.name_fcl_option?.trim()) {
            console.log('i am in name_fcl_option');
            hasFclError = true;
          }

          option.fcl_enquiry_items?.forEach((container: any) => {
            console.log('i am in containerQuantity', JSON.stringify(container, null, 2));
            if (
              !container.containerTypeId ||
              parseInt(container.containerQuantity) <= 0 ||
              parseInt(container.weight) <= 0
            ) {
              hasFclError = true;
            }
          });
        });

        if (hasFclError) errors.fcl = true;
      } else if (transportationBy === 'Less container load') {
        let hasLclError = false;

        lclOptions.forEach((option: any) => {
          if (!option.name_lcl_option?.trim()) {
            hasLclError = true;
          }

          const shipment = option.lcl_enquiry_items?.[0];
          const lots = shipment?.lots || [];
          const { weight, volume } = calculateShipmentTotal(lots);
          console.log('shipment?.byUnits', weight, volume);
          if (shipment?.byUnits) {
            if (weight === '0.00' || volume === '0.00') {
              hasLclError = true;
            }

            lots.forEach((lot: any) => {
              if (!lot.package_type_item_id || !lot.type_package_id) {
                hasLclError = true;
              }
            });
          } else {
            const weightVolumeEntries = shipment?.weightVolumeEntries || [];

            if (weightVolumeEntries.length === 0) {
              hasLclError = true;
            }

            weightVolumeEntries.forEach((entry: any) => {
              const hasWeightError = !entry.weight || entry.weight === '0' || entry.weight === '';
              const hasVolumeError = !entry.volume || entry.volume === '0' || entry.volume === '';

              if (hasWeightError || hasVolumeError) {
                hasLclError = true;
              }
            });
          }
        });

        if (hasLclError) errors.lcl = true;
      } else if (transportationBy === 'Bulk') {
        let hasBulkError = false;

        shipOptions.forEach((option: any) => {
          const nameMissing = !option.name_bulk_option?.trim();

          if (nameMissing) {
            hasBulkError = true;
          }

          option.bulk_enquiry_items.forEach((ship: any) => {
            const missingShipType = !ship.shipTypeId?.trim();

            const grossWeightValue = parseFloat(ship.grossWeight || '');
            const missingGrossWeight =
              !ship.grossWeight?.trim() || isNaN(grossWeightValue) || grossWeightValue <= 0;

            if (missingShipType || missingGrossWeight) {
              hasBulkError = true;
            }
          });
        });

        if (hasBulkError) {
          errors.bulk = true;
        }
      }
    } else if (jobType === 'air') {
      if (transportationBy === 'Standard cargo') {
        let hasStandardError = false;

        standardCargoOptions.forEach((option: any) => {
          if (!option.name_standard_cargo_option?.trim()) {
            hasStandardError = true;
          }

          const shipment = option.standard_cargo_enquiry_items?.[0];
          const lots = shipment?.lots || [];
          const { weight, volume } = calculateShipmentTotal(lots);

          if (shipment?.byUnits) {
            // If byUnits is true → check weight/volume & lot fields
            if (weight === '0.00' || volume === '0.00') {
              hasStandardError = true;
            }
            lots.forEach((lot: any) => {
              console.log("lot.package_type_item_id", lot?.type_package_id)
              if (!lot.package_type_item_id || !lot.type_package_id) {
                hasStandardError = true;
              }
            });
          } else {
            // If byUnits is false → check weightVolumeEntries
            const entries = shipment?.weightVolumeEntries || [];

            if (entries.length === 0) {
              hasStandardError = true;
            }

            entries.forEach((entry: any) => {
              const weightMissing = !entry.weight || entry.weight === '0' || entry.weight === '';
              const volumeMissing = !entry.volume || entry.volume === '0' || entry.volume === '';

              if (weightMissing || volumeMissing) {
                hasStandardError = true;
              }
            });
          }
        });

        if (hasStandardError) {
          errors.standardCargo = true;
        }
      } else if (transportationBy === 'ULD') {
        let hasUldError = false;

        uldOptions.forEach((option: any) => {
          if (!option.name_uld_option?.trim()) {
            hasUldError = true;
          }

          option.uld_enquiry_items?.forEach((uld: any) => {
            const missingType = !uld.uldTypeId;
            const missingWeight = !uld.weight || parseFloat(uld.weight) <= 0;

            if (missingType || missingWeight) {
              hasUldError = true;
            }
          });
        });

        if (hasUldError) {
          console.log(' any errors in uld?');
          errors.uld = true;
        }
      }
    } else if (jobType === 'road') {
      if (transportationBy === 'Full truck load') {
        let hasFtlError = false;

        truckOptions.forEach((option: any) => {
          if (!option.name_ftl_option?.trim()) {
            hasFtlError = true;
          }

          option.ftl_enquiry_items?.forEach((truck: any) => {
            const missingType = !truck.truckTypeId;
            const missingQuantity = !truck.truckQuantity || parseInt(truck.truckQuantity) <= 0;
            const missingWeight = !truck.weight || parseFloat(truck.weight) <= 0;

            if (missingType || missingQuantity || missingWeight) {
              hasFtlError = true;
            }
          });
        });

        if (hasFtlError) {
          errors.ftl = true;
        }
      } else if (transportationBy === 'Less truck load') {
        let hasLtlError = false;

        ltlOptions.forEach((option: any) => {
          if (!option.name_ltl_option?.trim()) {
            hasLtlError = true;
          }

          const shipment = option.ltl_enquiry_items?.[0];
          const lots = shipment?.lots || [];
          const { weight, volume } = calculateShipmentTotal(lots);
          if (shipment?.byUnits) {
            // Validate total weight and volume
            if (weight === '0.00' || volume === '0.00') {
              hasLtlError = true;
            }

            // Validate each lot
            lots.forEach((lot: any) => {
              if (!lot.package_type_item_id || !lot.type_package_id) {
                hasLtlError = true;
              }
            });
          } else {
            // Validate weight/volume entries
            const weightVolumeEntries = shipment?.weightVolumeEntries || [];

            if (weightVolumeEntries.length === 0) {
              hasLtlError = true;
            }

            weightVolumeEntries.forEach((entry: any) => {
              const weightInvalid = !entry.weight || entry.weight === '' || entry.weight === '0';
              const volumeInvalid = !entry.volume || entry.volume === '' || entry.volume === '0';

              if (weightInvalid || volumeInvalid) {
                hasLtlError = true;
              }
            });
          }
        });

        if (hasLtlError) {
          errors.ltl = true;
        }
      }
    }
    // console.log('errors in fcl', errors);
    return Object.keys(errors).length === 0;
  };

  return { isValid };
};
