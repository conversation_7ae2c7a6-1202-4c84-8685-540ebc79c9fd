// OptionForm.tsx

import React, { useState } from 'react';
import { View, TouchableOpacity, Alert } from 'react-native';
import { Text } from 'react-native-paper';
import { AlignCenter, Plus, Trash2, X } from 'lucide-react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';


import { DropdownField } from '../../../../../Components/UI/Menu/DropdownModal';
import CustomInput from '../../../../../Components/UI/TextInput';
import { containerDetailsStyles as styles } from '../styles/containerDetailsStyles'; // adjust the import!
import { generateUniqueId } from '../../../../../Utils/util';
import { showToast } from '../../../../../Components/AppToaster/AppToaster';
const SectionHeader = ({ title, onAdd, addLabel = 'Add Option' }) => (
  <View style={styles.sectionHeader}>
    <Text style={styles.sectionTitle} variant="semiBold">
      {title}
    </Text>
    <TouchableOpacity style={styles.addButton} onPress={onAdd}>
      <Plus size={16} color="#3377FF" />
      <Text style={styles.addButtonText} variant="semiBold">
        {addLabel}
      </Text>
    </TouchableOpacity>
  </View>
);

// Subcomponent for the simple Weight/Volume entry
const WeightVolumeEntry = ({
  entry,
  entryIndex,
  optionIndex,
  weightUnits,
  volumeUnits,
  onChange,
  onRemove,
  canRemove = false,
  title,
}) => {
  const grossWeight = parseFloat(entry.weight) || 0;
  const volumeWeight = parseFloat(entry.volume) || 0;
  const volumeWeightDivided = volumeWeight / 6000;
  const chargeableWeight = Math.max(grossWeight, volumeWeightDivided).toFixed(2);
   const chargeableUnit = (grossWeight > volumeWeightDivided) ? entry.weightUnit : entry.volumeUnit

  const unitOfMeasure = grossWeight > volumeWeightDivided ? 'Weight' : 'Volume';
  return (
    <View style={styles.weightVolumeEntry}>
      {canRemove && (
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Text variant="semiBold" style={{ fontSize: moderateScale(12) }}>
            Units #{entryIndex + 1}
          </Text>
          <TouchableOpacity
            style={styles.removeWeightVolumeButton}
            onPress={() => onRemove(optionIndex, entryIndex)}
          >
            <Trash2 size={16} color="#EF4444" />
          </TouchableOpacity>
        </View>
      )}
      {/* Weight Row */}
      <View style={styles.row}>
        <View style={[styles.inputContainer, { flex: 1, marginRight: 8 }]}>
          <View style={styles.weightVolumeRow}>
            <View style={{ flex: 1 }}>
              <CustomInput
                label="Weight"
                isMandatory={true}
                placeholder="0"
                value={entry.weight?.toString() || ''}
                onChangeText={(text) => onChange(optionIndex, entryIndex, 'weight', text)}
                keyboardType="numeric"
                style={[styles.weightVolumeInput, { textAlign: 'right' }]}
              />
            </View>
            <View style={{ flex: 1 }}>
              <DropdownField
                data={weightUnits}
                valueField="value"
                labelField="label"
                placeholder="Unit"
                label=" "
                showAsterisk={false}
                value={entry.weightUnit}
                onSelect={(selected) =>
                  onChange(optionIndex, entryIndex, 'weightUnit', selected.value)
                }
              />
            </View>
          </View>
        </View>
      </View>
      {/* Volume Row */}
      <View style={[styles.inputContainer, { flex: 1, marginRight: 8 }]}>
        <View style={styles.weightVolumeRow}>
          <View style={{ flex: 1 }}>
            <CustomInput
              label="Volume"
              isMandatory={true}
              placeholder="Enter volume"
              value={entry.volume?.toString() || ''}
              onChangeText={(text) => onChange(optionIndex, entryIndex, 'volume', text)}
              keyboardType="numeric"
              style={[styles.weightVolumeInput, { textAlign: 'right' }]}
            />
          </View>
          <View style={{ flex: 1 }}>
            <DropdownField
              data={volumeUnits}
              valueField="value"
              labelField="label"
              placeholder="Unit"
              label=" "
              showAsterisk={false}
              value={entry.volumeUnit}
              onSelect={(selected) =>
                onChange(optionIndex, entryIndex, 'volumeUnit', selected.value)
              }
            />
          </View>
        </View>
      </View>
      {title === 'Standard Cargo Options' && (
        <View style={[styles.inputContainer]}>
          <View style={styles.weightContainer}>
            <View style={{ flex: 1, marginRight: 7 }}>
              <CustomInput
                label="Chargeable wt"
                value={chargeableWeight?.toString() || '0'}
                onChangeText={() => {}}
                keyboardType="numeric"
                disabled={true}
                units={chargeableUnit}
              />
            </View>
            <View style={{ flex: 1 }}>
              <CustomInput
                label="Unit of Measure"
                value={unitOfMeasure?.toString() || '0'}
                onChangeText={() => {}}
                keyboardType="numeric"
                disabled={true}
                // units={shipmentTotal.weightUnit}
              />
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

// Subcomponent for the package lot (by-units) form
const PackageLot = ({
  lot,
  lotIdx,
  optionIndex,
  packageTypes,
  stackabilityTypes,
  weightUnits,
  dimensionUnits,
  unitSystem,
  onUpdateLot,
  onRemoveLot,
  lotsLength,
  shipmentTotal,
  title = '',
}: any) => {
  const [hasShownHeightWarning, setHasShownHeightWarning] = useState(false);
  console.log('hasShownHeightWarning >>>>', lot);
  const quantity = parseFloat(lot.quantity) || 0;
  const weight = parseFloat(lot.weight) || 0;

  const length = parseFloat(lot.dimensions?.length) || 0;
  const width = parseFloat(lot.dimensions?.width) || 0;
  const height = parseFloat(lot.dimensions?.height) || 0;

  const perUnitWeight = quantity > 0 ? (weight / quantity).toFixed(2) : 0;
  const volumetric = length * width * height * (quantity || 1); // fallback quantity to 1 for volume

  const volumetricWeight = parseFloat(volumetric) || 0;
  const volumetricWeightDivided = volumetricWeight / 6000;
  const chargeableWeight = Math.max(weight, volumetricWeightDivided).toFixed(2);

  // Determine unit of measure (which is higher - weight or volume)
  const unitOfMeasure = weight > volumetricWeightDivided ? 'Weight' : 'Volume';
  // Alert.alert(JSON.stringify(volumetric))
  return (
    <View key={lotIdx} style={styles.packageRow}>
      {/* Header row */}
      <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
        <Text variant="semiBold" style={styles.optionLabels}>
          Package #{lotIdx + 1}
        </Text>
        {lotsLength > 1 && (
          <TouchableOpacity
            style={{
              padding: 4,
              borderRadius: 18,
              backgroundColor: '#FEF2F2',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            hitSlop={1}
            onPress={() => onRemoveLot(optionIndex, lotIdx)}
          >
            <Trash2 size={12} color="#EF4444" />
          </TouchableOpacity>
        )}
      </View>
      {/* Package & Type */}
      <View>
        <View style={[styles.inputContainer, { flex: 0.5, marginRight: 8 }]}>
          <CustomInput
            label="Package"
            value={lot.package?.toString() || '1'}
            onChangeText={(text) => onUpdateLot(optionIndex, lotIdx, 'package', text)}
            keyboardType="numeric"
            disabled={true}
          />
        </View>
        <View style={[styles.inputContainer, { flex: 1, marginRight: 8 }]}>
          <DropdownField
            data={packageTypes}
            valueField="Text"
            labelField="Text"
            label="Package Type"
            placeholder="Select Package..."
            value={lot.packageType}
            onSelect={async (selected) => {
              await onUpdateLot(optionIndex, lotIdx, 'objectTypeData', {
                packageType: selected?.Text,
                package_type_item_id: selected?.Value,
              });
            }}
            isMandatory={true}
          />
        </View>
        <View style={[styles.inputContainer, { flex: 1, marginRight: 8 }]}>
          <DropdownField
            data={stackabilityTypes}
            type="radio"
            valueField="id"
            labelField="label"
            label="Stackability"
            placeholder="Select Type..."
            value={lot.type_package_id?.toString()}
            onSelect={async (value) => {
              await onUpdateLot(optionIndex, lotIdx, 'objectTypeData', {
                type_package_id: value?.id,
                stackableTypeId: value?.id,
                stackableValue: value?.value,
                stackability: value?.value,
              });
            }}
            isMandatory={true}
          />
        </View>
      </View>
      {/* Dimensions */}
      <View style={[styles.inputContainer, { flex: 2 }]}>
        <Text style={{ fontSize: moderateScale(12) }} variant="semiBold">
          Dimensions<Text style={{ color: 'red' }}>*</Text>
        </Text>
        <View style={[styles.dimensionsContainer]}>
          <View style={{ marginRight: 4, width: '20%' }}>
            <CustomInput
              placeholder="Length"
              value={lot.dimensions?.length?.toString() || ''}
              onChangeText={(text) => onUpdateLot(optionIndex, lotIdx, 'dimensions.length', text)}
              keyboardType="numeric"
              style={{ textAlign: 'right' }}
            />
          </View>
          <X size={moderateScale(12)} style={{ marginRight: 4, color: '#6B7280' }} />
          <View style={{ marginRight: 4, width: '20%' }}>
            <CustomInput
              placeholder="Width"
              value={lot.dimensions?.width?.toString() || ''}
              onChangeText={(text) => onUpdateLot(optionIndex, lotIdx, 'dimensions.width', text)}
              keyboardType="numeric"
              style={{ textAlign: 'right' }}
            />
          </View>
          <X size={moderateScale(12)} style={{ marginRight: 2, color: '#6B7280' }} />
          <View style={{ flexDirection: 'row', width: '20%' }}>
            <CustomInput
              placeholder="Height"
              value={lot.dimensions?.height?.toString() || ''}
              onChangeText={(text) => {
                const num = parseInt(text);
                if (num > 150) {
                  if (!hasShownHeightWarning) {
                    showToast.warning('You are entering a height value greater than 150');
                    setHasShownHeightWarning(true);
                  }
                }
                onUpdateLot(optionIndex, lotIdx, 'dimensions.height', text);
              }}
              keyboardType="numeric"
              style={{ textAlign: 'right' }}
            />
          </View>
          <View style={{ flex: 1 }}>
            <DropdownField
              data={dimensionUnits}
              valueField="value"
              labelField="label"
              value={lot.dimensions?.unit || (unitSystem === 'SI' ? 'cm' : 'in')}
              onSelect={(selected) =>
                onUpdateLot(optionIndex, lotIdx, 'dimensions.unit', selected.value)
              }
              placeholder="Unit"
            />
          </View>
        </View>
      </View>
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <View style={{ flex: 1, marginRight: 8 }}>
          <CustomInput
            label="QTY"
            value={lot.quantity?.toString() || '1'}
            onChangeText={(text) => onUpdateLot(optionIndex, lotIdx, 'quantity', text)}
            keyboardType="numeric"
            style={{ textAlign: 'right' }}
          />
        </View>
        <View style={{ flex: 1 }}>
          <CustomInput
            label="Per Unit Weight"
            value={perUnitWeight?.toString() || '0'}
            onChangeText={(text) => onUpdateLot(optionIndex, lotIdx, 'perUnitWeight', text)}
            keyboardType="numeric"
            disabled={true}
            units={shipmentTotal.weightUnit}
            style={{ textAlign: 'right' }}
          />
        </View>
      </View>

      <View>
        <View style={[styles.inputContainer]}>
          <View style={styles.weightContainer}>
            <View style={{ flex: 1.5 }}>
              <CustomInput
                label="Gross Weight"
                isMandatory={true}
                value={weight?.toString() || '0'}
                onChangeText={(text) => onUpdateLot(optionIndex, lotIdx, 'weight', text)}
                keyboardType="numeric"
                style={{ textAlign: 'right' }}
              />
            </View>
            {/* <View style={{ flex: 0.5 , marginRight: 8 }}> */}
            <DropdownField
              data={weightUnits}
              label=""
              valueField="value"
              labelField="label"
              value={lot.weightUnit || (unitSystem === 'SI' ? 'kg' : 'st')}
              onSelect={(selected) =>
                onUpdateLot(optionIndex, lotIdx, 'weightUnit', selected.value)
              }
              placeholder="Unit"
              dropDownButtonStyles={{}}
            />
            {/* </View> */}
            <View style={{ flex: 1, marginLeft: 7 }}>
              <CustomInput
                label="Volumetric"
                value={volumetric?.toString() || '0'}
                onChangeText={(text) => onUpdateLot(optionIndex, lotIdx, 'volumetric', text)}
                keyboardType="numeric"
                disabled={true}
                units={shipmentTotal.volumeUnit}
                style={{ textAlign: 'right' }}
              />
            </View>
            {/* </View> */}
          </View>
        </View>
        {title === 'Standard Cargo Options' && (
          <View style={[styles.inputContainer]}>
            <View style={styles.weightContainer}>
              <View style={{ flex: 1, marginRight: 7 }}>
                <CustomInput
                  label="Chargeable wt"
                  value={chargeableWeight?.toString() || '0'}
                  onChangeText={(text) => onUpdateLot(optionIndex, lotIdx, 'volumetric', text)}
                  keyboardType="numeric"
                  disabled={true}
                  units={shipmentTotal.weightUnit}
                />
              </View>
              <View style={{ flex: 1 }}>
                <CustomInput
                  label="Unit of Measure"
                  value={unitOfMeasure?.toString() || '0'}
                  onChangeText={(text) => onUpdateLot(optionIndex, lotIdx, 'volumetric', text)}
                  keyboardType="numeric"
                  disabled={true}
                  // units={shipmentTotal.weightUnit}
                />
              </View>
            </View>
          </View>
        )}
      </View>
    </View>
  );
};

export const OptionForm = ({
  title,
  options,
  optionKey,
  itemKey,
  nameKey,
  packageTypes,
  stackabilityTypes,
  weightUnits,
  volumeUnits,
  dimensionUnits,
  unitSystem,
  onAddOption,
  onUpdateOption,
  onRemoveOption,
  onAddLot,
  onUpdateLot,
  onRemoveLot,
  onToggleByUnits,
  onAddWeightVolumeEntry,
  onUpdateWeightVolumeEntry,
  onRemoveWeightVolumeEntry,
  calculatePackageTotalsData,
}: any) => {

  return  (
  <View style={styles.section}>
    <SectionHeader title={title} onAdd={onAddOption} />
    {options.map((option, optionIndex) => {
      const entries = option[itemKey]?.[0]?.weightVolumeEntries || [
        { id: Date.now().toString(), weight: '', volume: '', weightUnit: 'lb', volumeUnit: 'ft³' },
      ];
      const lots = option[itemKey]?.[0]?.lots || [];
      const byUnits = option[itemKey]?.[0]?.byUnits;

      // For shipment totals
      const shipmentTotal = calculatePackageTotalsData
        ? calculatePackageTotalsData(byUnits ? lots : [], unitSystem)
        : { weight: '', weightUnit: '', volume: '', volumeUnit: '' };

      return (
        <View key={option[optionKey]} style={styles.optionRow}>
          {/* Option Header and Remove */}
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <Text variant="semiBold" style={{ fontSize: moderateScale(12) }}>
              Option #{optionIndex + 1}
            </Text>
            {options.length > 1 && (
              <TouchableOpacity
                style={{
                  padding: 4,
                  borderRadius: 18,
                  backgroundColor: '#FEF2F2',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginLeft: 12,
                }}
                hitSlop={1}
                onPress={() => onRemoveOption(optionIndex)}
              >
                <Trash2 size={12} color="#EF4444" />
              </TouchableOpacity>
            )}
          </View>
          {/* Option fields */}
          <View style={[styles.inputContainer, { flex: 1 }]}>
            <CustomInput
              label="Option Name"
              isMandatory={true}
              value={ option[nameKey] 
              }
              onChangeText={(text) =>
                onUpdateOption(
                  optionIndex,
                 nameKey,
                  text
                )
              }
            />
          </View>
          <View style={[styles.inputContainer, { flex: 2 }]}>
            <CustomInput
              label="Description"
              placeholder="Enter option description"
              value={option.description}
              onChangeText={(text) => onUpdateOption(optionIndex, 'description', text)}
            />
          </View>
          {/* By Units Toggle & Add */}
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <TouchableOpacity
              style={styles.checkboxContainer}
              onPress={() => onToggleByUnits(optionIndex, !byUnits)}
            >
              <View style={[styles.checkbox, byUnits && styles.checkboxSelected]}>
                {byUnits && <Text style={styles.checkboxText}>✓</Text>}
              </View>
              <Text style={styles.checkboxLabel}>By units</Text>
            </TouchableOpacity>
            {byUnits ? (
              <TouchableOpacity
                style={[styles.addPackageButton, {}]}
                onPress={() => onAddLot(optionIndex)}
              >
                <Plus size={16} color="#3377FF" />
                <Text style={styles.addPackageText} variant="semiBold">
                  Add Package
                </Text>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={styles.addPackageButton}
                onPress={() => onAddWeightVolumeEntry(optionIndex)}
              >
                <Plus size={16} color="#3377FF" />
                <Text style={styles.addButtonText} variant="semiBold">
                  Add Weight/Volume
                </Text>
              </TouchableOpacity>
            )}
          </View>
          {/* By Units Mode */}
          {byUnits ? (
            <>
              {lots.map((lot, lotIdx) => (
                <PackageLot
                  key={lotIdx}
                  lot={lot}
                  lotIdx={lotIdx}
                  lotsLength={lots.length}
                  optionIndex={optionIndex}
                  packageTypes={packageTypes}
                  stackabilityTypes={stackabilityTypes}
                  weightUnits={weightUnits}
                  dimensionUnits={dimensionUnits}
                  unitSystem={unitSystem}
                  onUpdateLot={onUpdateLot}
                  onRemoveLot={onRemoveLot}
                  shipmentTotal={shipmentTotal}
                  title={title}
                />
              ))}
              <View style={styles.shipmentTotals}>
                <Text style={styles.totalsText}>
                  Shipment total: {shipmentTotal.volume} {shipmentTotal.volumeUnit}{' '}
                  {shipmentTotal.weight} {shipmentTotal.weightUnit}
                </Text>
              </View>
            </>
          ) : (
            <View style={styles.simpleFormContainer}>
              {entries.map((entry, entryIndex) => (
                <WeightVolumeEntry
                  key={entry.id || entryIndex}
                  entry={entry}
                  entryIndex={entryIndex}
                  optionIndex={optionIndex}
                  weightUnits={weightUnits}
                  volumeUnits={volumeUnits}
                  onChange={onUpdateWeightVolumeEntry}
                  onRemove={onRemoveWeightVolumeEntry}
                  canRemove={entries.length > 1}
                  title={title}
                />
              ))}
            </View>
          )}
        </View>
      );
    })}
  </View>
);
}
  
  
 
