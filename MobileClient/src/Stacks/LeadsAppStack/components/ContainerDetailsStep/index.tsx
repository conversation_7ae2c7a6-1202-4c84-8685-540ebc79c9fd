// ContainerDetailsStep.tsx - Complete Refactored Main Component
import React, { useState, useEffect } from 'react';
import { View, ActivityIndicator, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { Text, useTheme } from 'react-native-paper';
import { DropdownField } from '../../../../Components/UI/Menu/DropdownModal';
import ActionButtons from '../ActionButtons';
import UnitSelector from '../ShipmentDetailsStep/components/UnitSelector';
import { OptionForm } from './components/OptionForm';
import { FCLForm } from './components/FCLForm';
import { LCLForm } from './components/LCLForm';
import { BulkForm } from './components/BulkForm';
import { ULDForm } from './components/ULDForm';
import { FTLForm } from './components/FTLForm';
import { LTLForm } from './components/LTLForm';
import { StandardCargoForm } from './components/StandardCargoForm';
import { calculatePackageTotalsData } from './utils/containerDetailsUtils';
import { containerDetailsStyles as styles } from './styles/containerDetailsStyles';
import { ContainerDetailsStepProps } from './types/containerDetailsTypes';
import {
  useTransportationData,
  useDropdownData,
  useFormValidation,
} from './hooks/useContainerDetails';
import { ContainerDetailsActions } from './actions/containerDetailsActions';
import { getFormTypeForTransportation } from './utils/containerDetailsUtils';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { OptionWithItemsForm } from './components/OptionsWithItemsForm';
import AppLoader from '../../../../Components/Loader/AppLoader/InitialLoaderWithText';
import EmptyorErrorComponent from '../../../../Components/EmptyOrError/EmptyOrErrorComponent';
export default function ContainerDetailsStep({
  formData,
  updateFormData,
  currentStep,
  totalSteps,
  onNext,
  onPrevious,
}: ContainerDetailsStepProps) {
  const theme = useTheme();
  const { transportationOptions, loading, error } = useTransportationData(formData, updateFormData);
  const {
    containerTypes,
    packageTypes,
    stackabilityTypes,
    shipTypes,
    truckTypes,
    uldContainerTypes,
  } = useDropdownData();

  const actions = new ContainerDetailsActions(formData, updateFormData);
  const weightUnits = {
    SI: [
      { value: 'kg', label: 'kg' },
      { value: 'mt', label: 'mt' },
    ],
    Imperial: [
      { value: 'st', label: 'st' },
      { value: 'lb', label: 'lb' },
    ],
  };

  const volumeUnits = {
    Imperial: [
      { value: 'ft³', label: 'ft³' },
      { value: 'in³', label: 'in³' },
    ],
    SI: [
      { value: 'm³', label: 'm³' },
      { value: 'cm³', label: 'cm³' },
    ],
  };
  const DimensionUnits = {
    SI: [
      { value: 'cm', label: 'cm' },
      { value: 'm', label: 'm' },
    ],
    Imperial: [
      { value: 'in', label: 'in' },
      { value: 'ft', label: 'ft' },
    ],
  };
  useEffect(() => {
    actions.initializeDefaultOptions();
  }, []);
  useEffect(() => {
    if (transportationOptions.length > 0) {
      const firstOption = transportationOptions[0];
      // console.log('transportationOptions', transportationOptions);
      // updateFormData({
      //   transportationBy: firstOption.transportation_by_name,
      //   transportationTypeid: firstOption.transportation_type_id,
      //   transportationByCode: firstOption.transportation_by_code,
      // });
    }
  }, [transportationOptions]);

  const handleTransportationSelect = (selectedTransportation: any) => {
    // console.log('selectedTransportation >>>', selectedTransportation);
    const transportationOption = transportationOptions.find(
      (option) => option.transportation_by_name === selectedTransportation.transportation_by_name
    );

    if (transportationOption) {
      updateFormData({
        transportationBy: selectedTransportation.transportation_by_name,
        transportationTypeid: selectedTransportation.transportation_type_id,
        transportationByCode: selectedTransportation.transportation_by_code,
      });
    }
  };

  const renderTransportationSpecificForm = () => {
    if (!formData?.transportationBy) return null;

    const formType = getFormTypeForTransportation(formData?.transportationBy);

    switch (formType) {
      case 'fcl':
        return (
          <OptionWithItemsForm
            title="Container Options"
            options={formData.containerOptions}
            optionKey="fcl_container_option_id"
            nameKey="name_fcl_option"
            itemKey="fcl_enquiry_items"
            itemTypeLabel="Container Type"
            itemTypeDataField="containerType"
            itemTypeDataFieldId="containerTypeId"
            itemTypeDropdownValueField="label"
            itemQuantityField="containerQuantity"
            onAddOption={actions.addFCLOption}
            onUpdateOption={actions.updateFCLOption}
            onRemoveOption={actions.removeFCLOption}
            onAddItem={actions.addFCLContainer}
            onUpdateItem={actions.updateFCLContainer}
            onRemoveItem={actions.removeFCLContainer}
            itemTypesDropdownData={containerTypes}
            formData={formData}
          />
        );

      case 'lcl':
        return (
          <OptionForm
            title="LCL Options"
            options={formData.lclOptions}
            optionKey="lcl_option_id"
            itemKey="lcl_enquiry_items"
            nameKey="name_lcl_option"
            packageTypes={packageTypes}
            stackabilityTypes={stackabilityTypes}
            weightUnits={weightUnits[formData.unitSystem]}
            volumeUnits={volumeUnits[formData.unitSystem]}
            dimensionUnits={DimensionUnits[formData.unitSystem]}
            unitSystem={formData.unitSystem}
            onAddOption={actions.addLCLOption}
            onUpdateOption={actions.updateLCLOption}
            onRemoveOption={actions.removeLCLOption}
            onAddLot={actions.addLCLLot}
            onUpdateLot={actions.updateLCLLot}
            onRemoveLot={actions.removeLCLLot}
            onToggleByUnits={actions.toggleLCLByUnits}
            onAddWeightVolumeEntry={actions.addLCLWeightVolumeEntry}
            onUpdateWeightVolumeEntry={actions.updateLCLWeightVolumeEntry}
            onRemoveWeightVolumeEntry={actions.removeLCLWeightVolumeEntry}
            calculatePackageTotalsData={calculatePackageTotalsData}
          />
        );

      case 'bulk':
        return (
          <BulkForm
            formData={formData}
            shipTypes={shipTypes}
            onAddOption={actions.addBulkOption}
            onUpdateOption={actions.updateBulkOption}
            onRemoveOption={actions.removeBulkOption}
            onAddShipment={actions.addBulkShipment}
            onUpdateShipment={actions.updateBulkShipment}
            onRemoveShipment={actions.removeBulkShipment}
          />
        );

      case 'uld':
        return (
          <OptionWithItemsForm
            title="ULD Options"
            options={formData.uldOptions}
            optionKey="uld_option_id"
            itemKey="uld_enquiry_items"
            nameKey="name_uld_option"
            itemTypeLabel="Container Type"
            itemTypeDataField="uldType"
            itemTypeDataFieldId="uldTypeId"
            itemTypeDropdownValueField="Text"
            itemQuantityField="uldQuantity"
            onAddOption={actions.addULDOption}
            onUpdateOption={actions.updateULDOption}
            onRemoveOption={actions.removeULDOption}
            onAddItem={actions.addULDItem}
            onUpdateItem={actions.updateULDItem}
            onRemoveItem={actions.removeULDItem}
            itemTypesDropdownData={uldContainerTypes}
            formData={formData}
          />
        );

      case 'standardCargo':
        return (
          <OptionForm
            title="Standard Cargo Options"
            options={formData.standardCargoOptions}
            optionKey="standard_cargo_option_id"
            itemKey="standard_cargo_enquiry_items"
            nameKey="name_standard_cargo_option"
            packageTypes={packageTypes}
            stackabilityTypes={stackabilityTypes}
            weightUnits={weightUnits[formData.unitSystem]}
            volumeUnits={volumeUnits[formData.unitSystem]}
            dimensionUnits={DimensionUnits[formData.unitSystem]}
            unitSystem={formData.unitSystem}
            onAddOption={actions.addStandardCargoOption}
            onUpdateOption={actions.updateStandardCargoOption}
            onRemoveOption={actions.removeStandardCargoOption}
            onAddLot={actions.addStandardCargoLot}
            onUpdateLot={actions.updateStandardCargoLot}
            onRemoveLot={actions.removeStandardCargoLot}
            onToggleByUnits={actions.toggleStandardCargoByUnits}
            onAddWeightVolumeEntry={actions.addStandardCargoWeightVolumeEntry}
            onUpdateWeightVolumeEntry={actions.updateStandardCargoWeightVolumeEntry}
            onRemoveWeightVolumeEntry={actions.removeStandardCargoWeightVolumeEntry}
            calculatePackageTotalsData={calculatePackageTotalsData}
          />
        );

      case 'ftl':
        return (
          <OptionWithItemsForm
            title="Truck Options"
            options={formData.truckOptions}
            optionKey="ftl_option_id"
            itemKey="ftl_enquiry_items"
            nameKey="name_ftl_option"
            itemTypeLabel="Truck type"
            itemTypeDataField="truckType"
            itemTypeDataFieldId="truckTypeId"
            itemTypeDropdownValueField="Text"
            itemQuantityField="truckQuantity"
            onAddOption={actions.addFTLOption}
            onUpdateOption={actions.updateFTLOption}
            onRemoveOption={actions.removeFTLOption}
            onAddItem={actions.addFTLTruck}
            onUpdateItem={actions.updateFTLTruck}
            onRemoveItem={actions.removeFTLTruck}
            itemTypesDropdownData={truckTypes}
            formData={formData}
          />
        );

      case 'ltl':
        return (
          <OptionForm
            title="LTL Options"
            options={formData.ltlOptions}
            optionKey="ltl_option_id"
            itemKey="ltl_enquiry_items"
            nameKey="name_ltl_option"
            packageTypes={packageTypes}
            stackabilityTypes={stackabilityTypes}
            weightUnits={weightUnits[formData.unitSystem]}
            volumeUnits={volumeUnits[formData.unitSystem]}
            dimensionUnits={DimensionUnits[formData.unitSystem]}
            unitSystem={formData.unitSystem}
            onAddOption={actions.addLTLOption}
            onUpdateOption={actions.updateLTLOption}
            onRemoveOption={actions.removeLTLOption}
            onAddLot={actions.addLTLLot}
            onUpdateLot={actions.updateLTLLot}
            onRemoveLot={actions.removeLTLLot}
            onToggleByUnits={actions.toggleLTLByUnits}
            onAddWeightVolumeEntry={actions.addLTLWeightVolumeEntry}
            onUpdateWeightVolumeEntry={actions.updateLTLWeightVolumeEntry}
            onRemoveWeightVolumeEntry={actions.removeLTLWeightVolumeEntry}
            calculatePackageTotalsData={calculatePackageTotalsData}
          />
        );

      default:
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {loading ? (
        <AppLoader message="Loading transportation options.." />
      ) : error ? (
        <EmptyorErrorComponent message="Something went wrong" />
      ) : (
          // <ScrollView
          //   showsVerticalScrollIndicator={false}
          //   keyboardShouldPersistTaps="handled"
          //   // contentContainerStyle={{ flexGrow: 1 }}
          // >
            <View style = {{flex : 1}}>
            <View style={styles.section}>
              <DropdownField
                label="Transportation By"
                onSelect={handleTransportationSelect}
                data={transportationOptions}
                valueField="transportation_by_name"
                labelField="transportation_by_code"
                value={formData.transportationBy}
                placeholder="Select transportation"
                isMandatory={true}
                type="radio"
              />
            </View>

            {formData?.transportationBy && renderTransportationSpecificForm()}

            {/* <ActionButtons
          currentStep={currentStep}
          totalSteps={totalSteps}
          onNext={onNext}
          onPrevious={onPrevious}
          isNextDisabled={!isValid()}
          // isNextDisabled={false}
        /> */}
        </View>
          // </ScrollView>
  //       <KeyboardAwareScrollView
  //         showsVerticalScrollIndicator={false}
  //         keyboardShouldPersistTaps="handled"
  //         enableOnAndroid={true}
  //         // extraScrollHeight={80} // adjust to move content above keyboard
  //         contentContainerStyle={{ flexGrow: 1 }} // keep full scroll area
  //       >
  //         <View style={styles.section}>
  //           <DropdownField
  //             label="Transportation By"
  //             onSelect={handleTransportationSelect}
  //             data={transportationOptions}
  //             valueField="transportation_by_name"
  //             labelField="transportation_by_code"
  //             value={formData.transportationBy}
  //             placeholder="Select transportation"
  //             isMandatory={true}
  //             type="radio"
  //           />
  //         </View>

  //         {formData?.transportationBy && renderTransportationSpecificForm()}

  //         {/* <ActionButtons
  //   currentStep={currentStep}
  //   totalSteps={totalSteps}
  //   onNext={onNext}
  //   onPrevious={onPrevious}
  //   isNextDisabled={!isValid()}
  //   // isNextDisabled={false}
  // /> */}
  //       </KeyboardAwareScrollView>
      )}
    </View>
  );
}
