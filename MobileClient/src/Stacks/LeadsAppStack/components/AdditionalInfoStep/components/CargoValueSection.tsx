// components/CargoValueSection.tsx
import React from 'react';
import { Alert, View } from 'react-native';
import { Text } from 'react-native-paper';
import { DropdownField } from '../../../../../Components/UI/Menu/DropdownModal';
import CustomInput from '../../../../../Components/UI/TextInput';
import { additionalInfoStyles as styles } from '../styles/additionalInfoStyles';
import { Currency } from '../types/additionalInfoTypes';

interface CargoValueSectionProps {
  show: boolean;
  formData: any;
  currencies: Currency[];
  onCargoValueChange: (text: string) => void;
  onCurrencySelect: (value: string) => void;
}

export const CargoValueSection: React.FC<CargoValueSectionProps> = ({
  show,
  formData,
  currencies,
  onCargoValueChange,
  onCurrencySelect,
}) => {
  if (!show) return null;

  return (
    <View style={styles.cargoValueSection}>
      <View style={styles.cargoValueContainer}>
        <View style={{ flex: 2 }}>
          <CustomInput
            placeholder="0"
            label="Cargo Value"
            isMandatory={true}
            value={formData.cargoValueAmount}
            onChangeText={onCargoValueChange}
            keyboardType="numeric"
            style={{textAlign :"right"}}
          />
        </View>
        <View style={{ flex: 1 }}>
          <DropdownField
            label=" "
            data={currencies}
            valueField="displayData"
            keyField={currencies?.currency_id}
            // labelField="displayData"
            value={formData?.displayData}
            showAsterisk={false}
            onSelect={(value) => {
              // Alert.alert(JSON.stringify(value))
              // console.log("value data", value)
              onCurrencySelect(value);
            }}
            containerStyle={styles.currencyDropdown}
          />
        </View>
      </View>
    </View>
  );
};
