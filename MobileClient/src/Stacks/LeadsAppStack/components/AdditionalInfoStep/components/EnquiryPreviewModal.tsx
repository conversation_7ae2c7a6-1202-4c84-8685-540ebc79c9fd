import React, { useEffect, useState } from 'react';
import {
  Modal,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { scale, verticalScale, moderateScale } from 'react-native-size-matters';
import { transformToFinalPayload } from '../../../utils/finalEnquiryPayloadForAll';
import { fontFamily } from '../../../../../Common/Theme/typography';
import AppLoader from '../../../../../Components/Loader/AppLoader/InitialLoaderWithText';

const EnquiryPreviewModal = ({ visible, onClose, onSubmit, formData }) => {
  const [data, setData] = useState(null);

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const transformedData = await transformToFinalPayload(formData, {});
        const finalJson = transformedData?.data || {};
        console.log('finalJson >>>', JSON.stringify(transformedData?.enquiryDocuments, null, 2));
        setData(finalJson);

        console.error('Error in loadData:', error);
      } finally {
        setLoading(false);
      }
    };

    if (visible && formData) {
      loadData();
    }
  }, [visible, formData]);
  const formatDate = (dateString) => {
    if (!dateString || dateString == 'Invalid date') return 'Not specified';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const DetailRow = ({ label, value }) => {
    // if(value &&
    // Object.keys(value).length !== 0  ){
    //     return null
    // }
    if (!value || value == 'null') return null;
    return (
      <View style={styles.detailRow}>
        <Text style={styles.label}>{label}</Text>
        <Text style={styles.value}>{value || 'Not specified'}</Text>
      </View>
    );
  };

  const SectionHeader = ({ title }) => <Text style={styles.sectionHeader}>{title}</Text>;

  const DocumentList = ({ title, documents, isOld }) => {
    if (!documents || documents.length === 0) return null;
    console.log('documents', documents);
    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{title}</Text>
        <View>
          {documents.map((item, index) => {
            const key = item.id || item.document_id || index.toString();
            const displayName = isOld ? item.document_name : item?.name || item.originalFileName;

            return (
              <View key={key} style={styles.docRow}>
                <View style={styles.uploadedDocRow}>
                  {displayName && <Text style={styles.docName}>{displayName}</Text>}
                  {!isOld && item.size && <Text style={styles.docSize}>({item.size} MB)</Text>}
                </View>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  const renderArrayItems = (items, title) => {
    if (!items || items.length === 0) return null;

    return (
      <View style={styles.arrayContainer}>
        <Text style={styles.arrayTitle}>{title}</Text>
        {items.map((item, index) => {
          return (
            <Text key={index} style={styles.arrayItem}>
              • {item?.organization_name}
            </Text>
          );
        })}
      </View>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      {loading ? (
        <AppLoader />
      ) : (
        <SafeAreaView style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Enquiry Preview</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false} bounces={false}>
            {/* Basic Information */}
            <View style={styles.section}>
              <SectionHeader title="Basic Information" />
              {/* <DetailRow label="Enquiry ID" value={data?.json_data?.enquiryId} /> */}
              <DetailRow label="Mode of Shipment" value={data?.json_data?.modeOfShimpent} />
              <DetailRow label="Direction" value={data?.json_data?.direction} />
              <DetailRow label="Shipment Type" value={data?.json_data?.shipmentType} />
              {/* <DetailRow label="Organization ID" value={data?.json_data?.orgnization_id} /> */}
              <DetailRow label="Country Of Origin" value={formData?.countryOfOriginName} />
              <DetailRow label="Unit System" value={data?.json_data?.unitSystem} />
              {/* <DetailRow label="Selected Currency" value={data?.json_data?.selectedCurrency} /> */}
              {/* <DetailRow label="Direction ID" value={data?.json_data?.direction_id} /> */}
            </View>

            {/* Route Information */}
            <View style={styles.section}>
              <SectionHeader title="Route Information" />
              <DetailRow
                label="From Location"
                value={`${data?.json_data.fromLocation?.name} (${data?.json_data?.fromLocation?.type})`}
              />
              <DetailRow
                label="From Address"
                value={data?.json_data?.fromAddress || data?.from_address}
              />
              <DetailRow
                label="To Location"
                value={`${data?.json_data?.toLocation?.name} (${data?.json_data?.toLocation?.type})`}
              />
              <DetailRow
                label="To Address"
                value={data?.json_data?.toAddress || data?.to_address}
              />

              {/* Transshipment Information */}
              {data?.json_data?.is_transshipment && (
                <>
                  <DetailRow
                    label="Is Transshipment"
                    value={data?.json_data?.is_transshipment ? 'Yes' : 'No'}
                  />
                  <DetailRow
                    label="Transit Location"
                    value={`${data?.json_data?.transitLocation?.name} (${data?.json_data?.transitLocation?.type})`}
                  />
                  <DetailRow
                    label="Shipment Method"
                    value={data?.json_data?.shipmentMethod ? 'Yes' : 'No'}
                  />
                  <DetailRow label="Remarks" value={data?.json_data?.remarks} />
                </>
              )}
            </View>

            {/* Cargo Information */}
            <View style={styles.section}>
              <SectionHeader title="Cargo Information" />
              <DetailRow label="Cargo Type" value={data?.json_data?.cargoType} />
              <DetailRow label="Transportation Type" value={data?.json_data?.transportationType} />

              <DetailRow label="Incoterm" value={data?.json_data?.incoterm} />
              <DetailRow label="ExWorks Add" value={data?.ex_works_address || formData?.exWorksAddress} /> 
              <DetailRow label="Cargo Value" value={data?.json_data?.invoiceAmount} />

              {/* Package Information */}
              {data?.json_data?.packageType && (
                <DetailRow label="Package Type" value={data?.json_data?.packageType} />
              )}

              {/* HS Code Information */}
              {data?.json_data?.selectedCategory && (
                <DetailRow label="Selected Category" value={data?.json_data?.selectedCategory} />
              )}

              {data?.json_data?.selectedSubCategory && (
                <DetailRow
                  label="Selected Sub Category"
                  value={data?.json_data?.selectedSubCategory}
                />
              )}

              {/* Temperature and Humidity */}
              {data?.temperature_regime && (
                <DetailRow
                  label="Temperature"
                  value={`${data?.temperature_regime}${data?.json_data?.temperatureUnit || '°C'}`}
                />
              )}
              {data?.humidity && <DetailRow label="Humidity" value={`${data?.humidity}%`} />}
            </View>

            {/* Cargo Type Specific Information */}
            {data?.json_data?.cargoType === 'hazardous cargo' && (
              <View style={styles.section}>
                <SectionHeader title="Hazardous Cargo Details" />
                <DetailRow label="IMO Class" value={data?.json_data?.imoClass} />
                <DetailRow label="UN Number" value={data?.json_data?.unNumber} />
                {data?.json_data?.jobType === 'Air' && (
                  <>
                    <DetailRow label="SR Number" value={data?.json_data?.srNumber} />
                    <DetailRow label="RnD" value={data?.json_data?.Rnd} />
                    <DetailRow label="RfL" value={data?.json_data?.Rfl} />
                  </>
                )}
              </View>
            )}

            {data?.json_data?.cargoType === 'perishable cargo' && (
              <View style={styles.section}>
                <SectionHeader title="Perishable Cargo Details" />
                <DetailRow label="Temperature" value={data?.json_data?.temperature} />
                <DetailRow label="Temperature Unit" value={data?.json_data?.temperatureUnit} />
                <DetailRow label="Humidity" value={data?.json_data?.humidity} />
              </View>
            )}

            {data?.json_data?.cargoType === 'oversized cargo' && (
              <View style={styles.section}>
                <SectionHeader title="Oversized Cargo Details" />
                <DetailRow label="Length" value={data?.json_data?.length} />
                <DetailRow label="Breadth" value={data?.json_data?.breadth} />
                <DetailRow label="Height" value={data?.json_data?.height} />
                {data?.json_data?.dimensions && (
                  <DetailRow
                    label="Dimensions"
                    value={`${data?.json_data?.dimensions.length} x ${data?.json_data?.dimensions.width} x ${data?.json_data?.dimensions.height}`}
                  />
                )}
              </View>
            )}

            {data?.json_data?.cargoType === 'temperature cargo' && (
              <View style={styles.section}>
                <SectionHeader title="Temperature Cargo Details" />
                <DetailRow
                  label="Temperature Regime"
                  value={data?.json_data?.tempRegimeOfTemperatureCargo}
                />
                <DetailRow
                  label="Temperature Units"
                  value={data?.json_data?.tempRegimeOfTemperatureCargoUnits}
                />
                <DetailRow label="Humidity" value={data?.json_data?.humidityOfTemperatureCargo} />
              </View>
            )}

            {/* Carrier/Airlines Information */}
            {data?.json_data?.selectedAirLine && data?.json_data?.selectedAirLine.length > 0 && (
              <View style={styles.section}>
                <SectionHeader title="Selected Airlines" />
                {renderArrayItems(data?.json_data?.selectedAirLine, 'Airlines')}
              </View>
            )}

            {data?.json_data?.selectedCarrier && data?.json_data?.selectedCarrier.length > 0 && (
              <View style={styles.section}>
                <SectionHeader title="Selected Carriers" />
                {renderArrayItems(data?.json_data?.selectedCarrier, 'Carriers')}
              </View>
            )}

            {/* Services Information */}
            {data?.json_data?.services && Object.keys(data?.json_data?.services).length > 0 && (
              <View style={styles.section}>
                <SectionHeader title="Services" />
                {Object.entries(data?.json_data?.services).map(([key, value]) =>
                  value ? <DetailRow key={key} label={key} value={'Yes'} /> : null
                )}
              </View>
            )}

            {/* Border Charges */}
            {data?.json_data?.is_border_charges && (
              <View style={styles.section}>
                <SectionHeader title="Border Charges" />
                <DetailRow
                  label="Border Charges"
                  value={data?.json_data?.is_border_charges ? 'Yes' : 'No'}
                />
                <DetailRow label="Selected Charges" value={data?.json_data?.selectedCharges} />
                <DetailRow label="Border Charges ID" value={data?.json_data?.border_charges_id} />
              </View>
            )}

            {/* FCL Container Options */}
            {data?.json_data?.fclOptions && data?.json_data?.fclOptions.length > 0 && (
              <View style={styles.section}>
                <SectionHeader title="FCL Container Options" />
                {data?.json_data?.fclOptions.map((option, index) => (
                  <View key={option.optionId} style={styles.optionContainer}>
                    <Text style={styles.optionTitle}>{option.optionName}</Text>
                    <DetailRow label="Description" value={option.description} />
                    {option.containers?.map((container, containerIndex) => (
                      <View key={container.id} style={styles.itemContainer}>
                        <DetailRow label="Container Type" value={container.containerType} />
                        <DetailRow label="Quantity" value={container.containerQuantity} />
                        <DetailRow label="Weight" value={`${container.weight} kg`} />
                      </View>
                    ))}
                  </View>
                ))}
              </View>
            )}

            {/* LCL Options */}
            {data?.json_data?.lclOptions && data?.json_data?.lclOptions.length > 0 && (
              <View style={styles.section}>
                <SectionHeader title="LCL Options" />
                {data?.json_data?.lclOptions.map((option, index) => (
                  <View key={option.optionId} style={styles.optionContainer}>
                    <Text style={styles.optionTitle}>{option.optionName}</Text>
                    <DetailRow label="Description" value={option.description} />

                    {option.lclItems?.map((item, itemIndex) => (
                      <View key={itemIndex} style={styles.itemContainer}>
                        <DetailRow label="By Units" value={item.byUnits ? 'Yes' : 'No'} />
                        <DetailRow label="Weight" value={item.weight} />
                        <DetailRow label="Volume" value={item.volume} />

                        {item.byUnits
                          ? // Render Lots
                            item.lots?.map((lot) => (
                              <View key={lot.id} style={styles.subItemContainer}>
                                <DetailRow label="Quantity" value={lot.quantity} />
                                <DetailRow
                                  label="Weight"
                                  value={`${lot.weight} ${lot.weightUnit}`}
                                />
                                {/* <DetailRow label="Per Unit Weight" value={lot.perUnitWeight} /> */}
                                {/* <DetailRow
                                  label="Volume Metric Weight"
                                  value={lot.volumeMetricWeight}
                                /> */}
                                <DetailRow
                                  label="Dimensions"
                                  value={`${lot.dimensions.length}(l) x ${lot.dimensions.width}(b) x ${lot.dimensions.height}(h) ${lot.dimensions.unit}`}
                                />
                                {/* <DetailRow
                                  label="Package Type ID"
                                  value={lot.package_type_item_id}
                                />
                                <DetailRow label="Type Package ID" value={lot.type_package_id} /> */}
                              </View>
                            ))
                          : // Render WeightVolumeEntries
                            item.weightVolumeEntries?.map((entry) => (
                              <View key={entry.id} style={styles.subItemContainer}>
                                <DetailRow
                                  label="Weight"
                                  value={`${entry.weight} ${entry.weightUnit}`}
                                />
                                <DetailRow
                                  label="Volume"
                                  value={`${entry.volume} ${entry.volumeUnit}`}
                                />
                              </View>
                            ))}
                      </View>
                    ))}
                  </View>
                ))}
              </View>
            )}

            {/* Bulk Options */}
            {data?.json_data?.bulkOptions && data?.json_data?.bulkOptions.length > 0 && (
              <View style={styles.section}>
                <SectionHeader title="Bulk Options" />
                {data?.json_data?.bulkOptions.map((option, index) => (
                  <View key={option.optionId} style={styles.optionContainer}>
                    <Text style={styles.optionTitle}>{option.optionName}</Text>
                    <DetailRow label="Description" value={option.description} />
                    {option.ships?.map((ship, shipIndex) => (
                      <View key={ship.id} style={styles.itemContainer}>
                        <DetailRow label="Ship Type" value={ship.shipType} />
                        <DetailRow label="Ship Type ID" value={ship.shipTypeId} />
                        <DetailRow label="Gross Weight" value={ship.grossWeight} />
                        <DetailRow label="Loading Rate" value={ship.loadingRate} />
                        <DetailRow label="Discharging Rate" value={ship.dischargingRate} />
                      </View>
                    ))}
                  </View>
                ))}
              </View>
            )}

            {/* standardCargoOptions Options */}
            {data?.json_data?.standardCargoOptions &&
              data?.json_data?.standardCargoOptions.length > 0 && (
                <View style={styles.section}>
                  <SectionHeader title="Standard Cargo Options" />
                  {data?.json_data?.standardCargoOptions.map((option, index) => (
                    <View key={option.optionId} style={styles.optionContainer}>
                      <Text style={styles.optionTitle}>{option.optionName}</Text>
                      <DetailRow label="Description" value={option.description} />

                      {option.standardCargoItems?.map((item, itemIndex) => (
                        <View key={itemIndex} style={styles.itemContainer}>
                          <DetailRow label="By Units" value={item.byUnits ? 'Yes' : 'No'} />
                          <DetailRow label="Weight" value={item.weight} />
                          <DetailRow label="Volume" value={item.volume} />

                          {item.byUnits
                            ? // Render Lots
                              item.lots?.map((lot) => (
                                <View key={lot.id} style={styles.subItemContainer}>
                                  <DetailRow label="Quantity" value={lot.quantity} />
                                  <DetailRow
                                    label="Weight"
                                    value={`${lot.weight} ${lot.weightUnit}`}
                                  />
                                  {/* <DetailRow label="Per Unit Weight" value={lot.perUnitWeight} /> */}
                                  {/* <DetailRow
                                  label="Volume Metric Weight"
                                  value={lot.volumeMetricWeight}
                                /> */}
                                  <DetailRow
                                    label="Dimensions"
                                    value={`${lot.dimensions.length}(l) x ${lot.dimensions.width}(b) x ${lot.dimensions.height}(h) ${lot.dimensions.unit}`}
                                  />
                                  {/* <DetailRow
                                  label="Package Type ID"
                                  value={lot.package_type_item_id}
                                />
                                <DetailRow label="Type Package ID" value={lot.type_package_id} /> */}
                                </View>
                              ))
                            : // Render WeightVolumeEntries
                              item.weightVolumeEntries?.map((entry) => (
                                <View key={entry.id} style={styles.subItemContainer}>
                                  <DetailRow
                                    label="Weight"
                                    value={`${entry.weight} ${entry.weightUnit}`}
                                  />

                                  <DetailRow
                                    label="Volume"
                                    value={`${entry.volume} ${entry.volumeUnit}`}
                                  />
                                </View>
                              ))}
                        </View>
                      ))}
                    </View>
                  ))}
                </View>
              )}

            {/* ULD Options */}
            {data?.json_data?.uldOptions && data?.json_data?.uldOptions.length > 0 && (
              <View style={styles.section}>
                <SectionHeader title="ULD Options" />
                {data?.json_data?.uldOptions.map((option, index) => (
                  <View key={option.optionId} style={styles.optionContainer}>
                    <Text style={styles.optionTitle}>{option.optionName}</Text>
                    <DetailRow label="Description" value={option.description} />
                    {option.ulds?.map((uld, uldIndex) => (
                      <View key={uld.id} style={styles.itemContainer}>
                        <DetailRow label="ULD Type" value={uld.uldType} />
                        <DetailRow label="ULD Type ID" value={uld.uldTypeId} />
                        <DetailRow label="Quantity" value={uld.uldQuantity} />
                        <DetailRow label="Weight" value={`${uld.weight} kg`} />
                        {/* <DetailRow label="Per Unit Weight" value={`${uld.perUnitWeight} kg`} /> */}
                      </View>
                    ))}
                  </View>
                ))}
              </View>
            )}

            {/* FTL Truck Options */}
            {data?.json_data?.ftlOptions && data?.json_data?.ftlOptions.length > 0 && (
              <View style={styles.section}>
                <SectionHeader title="FTL Truck Options" />
                {data?.json_data?.ftlOptions.map((option, index) => (
                  <View key={option.optionId} style={styles.optionContainer}>
                    <Text style={styles.optionTitle}>{option.optionName}</Text>
                    <DetailRow label="Description" value={option.description} />
                    {option.trucks?.map((truck, truckIndex) => (
                      <View key={truck.id} style={styles.itemContainer}>
                        <DetailRow label="Truck Type" value={truck.truckType} />
                        <DetailRow label="Truck Type ID" value={truck.truckTypeId} />
                        <DetailRow label="Quantity" value={truck.truckQuantity} />
                        <DetailRow label="Weight" value={`${truck.weight} kg`} />
                      </View>
                    ))}
                  </View>
                ))}
              </View>
            )}
            {/* LTL Truck Options */}
            {data?.json_data?.ltlOptions && data?.json_data?.ltlOptions.length > 0 && (
              <View style={styles.section}>
                <SectionHeader title="LTL Options" />
                {data?.json_data?.ltlOptions.map((option, index) => (
                  <View key={option.optionId} style={styles.optionContainer}>
                    <Text style={styles.optionTitle}>{option.optionName}</Text>
                    <DetailRow label="Description" value={option.description} />

                    {option.ltlItems?.map((item, itemIndex) => (
                      <View key={itemIndex} style={styles.itemContainer}>
                        <DetailRow label="By Units" value={item.byUnits ? 'Yes' : 'No'} />
                        <DetailRow label="Weight" value={item.weight} />
                        <DetailRow label="Volume" value={item.volume} />

                        {item.byUnits
                          ? // Render Lots
                            item.lots?.map((lot) => (
                              <View key={lot.id} style={styles.subItemContainer}>
                                <DetailRow label="Quantity" value={lot.quantity} />
                                <DetailRow
                                  label="Weight"
                                  value={`${lot.weight} ${lot.weightUnit}`}
                                />
                                {/* <DetailRow label="Per Unit Weight" value={lot.perUnitWeight} /> */}
                                {/* <DetailRow
                                  label="Volume Metric Weight"
                                  value={lot.volumeMetricWeight}
                                /> */}
                                <DetailRow
                                  label="Dimensions"
                                  value={`${lot.dimensions.length}(l) x ${lot.dimensions.width}(b) x ${lot.dimensions.height}(h) ${lot.dimensions.unit}`}
                                />
                                {/* <DetailRow
                                  label="Package Type ID"
                                  value={lot.package_type_item_id}
                                />
                                <DetailRow label="Type Package ID" value={lot.type_package_id} /> */}
                              </View>
                            ))
                          : // Render WeightVolumeEntries
                            item.weightVolumeEntries?.map((entry) => (
                              <View key={entry.id} style={styles.subItemContainer}>
                                <DetailRow
                                  label="Weight"
                                  value={`${entry.weight} ${entry.weightUnit}`}
                                />
                                <DetailRow
                                  label="Volume"
                                  value={`${entry.volume} ${entry.volumeUnit}`}
                                />
                              </View>
                            ))}
                      </View>
                    ))}
                  </View>
                ))}
              </View>
            )}

            {/* Schedule Information */}
            <View style={styles.section}>
              <SectionHeader title="Schedule Information" />
              <DetailRow
                label="Ready to Load"
                value={formatDate(data?.ready_to_load || data?.json_data?.formattedDate)}
              />
              <DetailRow
                label="Transit Time"
                value={data?.transit_time ? `${data?.transit_time} days` : 'Not specified'}
              />
              <DetailRow
                label="Free Time"
                value={data?.free_time ? `${data?.free_time} days` : 'Not specified'}
              />
            </View>

            {/* Additional Information */}
            {(data?.additional_information || data?.json_data?.additionalInfo) && (
              <View style={styles.section}>
                <SectionHeader title="Additional Information" />
                <Text style={styles.additionalInfo}>
                  {data?.additional_information || data?.json_data?.additionalInfo}
                </Text>
              </View>
            )}
            <DocumentList
              title="Old uploaded documents:"
              documents={formData?.enquiryDocuments || []}
              isOld
            />

            <DocumentList
              title="Newly uploaded documents:"
              documents={formData?.uploadedDocs || []}
            />
            <View style={styles.spacer} />
          </ScrollView>

          {/* Action Buttons */}
          {/* <View style={styles.footer}>
            <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
              <Text style={styles.cancelButtonText}>Edit Details</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.submitButton} onPress={onSubmit}>
              <Text style={styles.submitButtonText}>Submit Enquiry</Text>
            </TouchableOpacity>
          </View> */}
        </SafeAreaView>
      )}
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: scale(20),
    paddingVertical: verticalScale(15),
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: moderateScale(18),
    fontFamily: fontFamily.semiBold,
    color: '#2c3e50',
  },
  closeButton: {
    width: scale(30),
    height: scale(30),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: scale(15),
  },
  closeButtonText: {
    fontSize: moderateScale(16),
    color: '#666',
    fontFamily: fontFamily.semiBold,
  },
  content: {
    flex: 1,
    paddingHorizontal: scale(10),
  },
  section: {
    backgroundColor: '#f7f9fb',
    marginTop: verticalScale(6),
    paddingHorizontal: scale(16),
    paddingVertical: verticalScale(16),
    borderRadius: scale(12),
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 1,
    // },
    // shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  sectionHeader: {
    fontSize: moderateScale(16),
    fontFamily: fontFamily.semiBold,
    color: '#2c3e50',
    marginBottom: verticalScale(12),
    paddingBottom: verticalScale(8),
    borderBottomWidth: 1,
    borderBottomColor: '#e8ecf4',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: verticalScale(6),
    // borderBottomWidth: 0.5,
    // borderBottomColor: '#f0f0f0',
  },
  label: {
    fontSize: moderateScale(13),
    color: '#7f8c8d',
    fontFamily: fontFamily.semiBold,
    flex: 1,
    marginRight: scale(10),
    textTransform: 'capitalize',
  },
  value: {
    fontSize: moderateScale(13),
    color: '#2c3e50',
    fontFamily: fontFamily.semiBold,
    flex: 1.5,
    textAlign: 'right',
  },
  optionContainer: {
    // marginBottom: verticalScale(12),
    // paddingTop: verticalScale(8),
    // borderTopWidth: 1,
    // borderTopColor: '#f0f0f0',
  },
  optionTitle: {
    fontSize: moderateScale(14),
    fontFamily: fontFamily.semiBold,
    color: '#34495e',
    // marginBottom: verticalScale(8),
  },
  itemContainer: {
    backgroundColor: '#f8f9fb',
    paddingHorizontal: scale(12),
    paddingVertical: verticalScale(8),
    borderRadius: scale(8),
    // marginBottom: verticalScale(6),
  },
  arrayContainer: {
    marginTop: verticalScale(8),
  },
  arrayTitle: {
    fontSize: moderateScale(13),
    fontFamily: fontFamily.semiBold,
    color: '#34495e',
    marginBottom: verticalScale(4),
  },
  arrayItem: {
    fontSize: moderateScale(12),
    color: '#2c3e50',
    fontFamily: fontFamily.regular,
    marginLeft: scale(8),
  },
  additionalInfo: {
    fontSize: moderateScale(13),
    color: '#2c3e50',
    lineHeight: verticalScale(18),
    fontStyle: 'italic',
    fontFamily: fontFamily.semiBold,
  },
  spacer: {
    height: verticalScale(20),
  },
  footer: {
    flexDirection: 'row',
    paddingHorizontal: scale(20),
    paddingVertical: verticalScale(15),
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    gap: scale(12),
  },
  cancelButton: {
    flex: 1,
    paddingVertical: verticalScale(12),
    backgroundColor: '#ecf0f1',
    borderRadius: scale(8),
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButtonText: {
    fontSize: moderateScale(14),
    fontFamily: fontFamily.semiBold,
    color: '#7f8c8d',
  },
  submitButton: {
    flex: 1,
    paddingVertical: verticalScale(8),
    backgroundColor: '#3b82f6',
    borderRadius: scale(8),
    alignItems: 'center',
    justifyContent: 'center',
  },
  submitButtonText: {
    fontSize: moderateScale(14),
    fontFamily: fontFamily.semiBold,
    color: '#fff',
  },

  sectionTitle: {
    color: '#6B7280',
    marginBottom: moderateScale(6),
    fontSize: moderateScale(13),
    fontFamily: fontFamily.semiBold,
  },
  docRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: moderateScale(4),
  },
  docInfo: {
    flex: 1,
  },
  docName: {
    fontSize: moderateScale(12),
    fontFamily: fontFamily.semiBold,
    color: '#111827',
  },
  docSize: {
    color: '#6B7280',
    fontSize: moderateScale(11),
  },
  docActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  uploadedDocRow: {
    backgroundColor: '#fff',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: verticalScale(7),
  },
});

export default EnquiryPreviewModal;
