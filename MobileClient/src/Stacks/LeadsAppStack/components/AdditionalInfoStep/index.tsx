import React, { useEffect, useState } from 'react';
import { View, ActivityIndicator, ScrollView, Platform, KeyboardAvoidingView } from 'react-native';
import { Text, useTheme } from 'react-native-paper';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { DatePickerSection } from './components/DatePickerSection';
import { TransportProviderDropdown } from './components/TransportProviderDropDown';
import { TimeInputs } from './components/TimeInputs';
import { AssociatedServicesSection } from './components/AssociatedServicesSection';
import { CargoValueSection } from './components/CargoValueSection';
import { AdditionalInfoTextArea } from './components/AdditionalInfoTextArea';
import { DropdownField } from '../../../../Components/UI/Menu/DropdownModal';
import ActionButtons from '../ActionButtons';
import { additionalInfoStyles as styles } from './styles/additionalInfoStyles';
import { AdditionalInfoStepProps } from './types/additionalInfoTypes';
import {
  useAdditionalInfoData,
  useDatePicker,
  useFormSubmission,
} from './hooks/additionalInfoHooks';
import { AdditionalInfoActions } from './actions/additionalInfoActions';
import { validateForm } from './utils/additionalInfoUtils';
import EmptyorErrorComponent from '../../../../Components/EmptyOrError/EmptyOrErrorComponent';
import AppLoader from '../../../../Components/Loader/AppLoader/InitialLoaderWithText';
import { AddressInput } from '../Shared/AddressInput';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import EnquiryPreviewModal from './components/EnquiryPreviewModal';
import ShipmentCategory from '../ShipmentCategoryStep';
import { LocationSection } from '../LocationSection';
import { ModeSpecificSections } from '../ShipmentDetailsStep/components/ModeSpecificSections';
import useEnquiryFormHooks from '../../Screens/EnquiryScreens/EnquiryForm.hooks';
import { useModeSpecificData } from '../ShipmentDetailsStep/hooks/useModeSpecificData';
import { EnquiryFormData } from '../../types';
import { isValidLocation } from '../ShipmentDetailsStep/utils/validation';
import { useLocationValidation } from '../ShipmentDetailsStep/hooks/useLocationValidation';
import DropdownMenu from '../../../../Components/UI/Menu/DropdownMenu';
const AdditionalInfoStep: React.FC<AdditionalInfoStepProps> = ({
  formData,
  updateFormData,
  currentStep,
  totalSteps,
  onNext,
  onPrevious,
  setActiveTabName,
}) => {
  const [showPreview, setShowPreview] = useState(false);

  const theme = useTheme();

  const actions = new AdditionalInfoActions(formData, updateFormData);
  const {
    carriers,
    airlines,
    currencies,
    associatedServices,
    countries,
    loading,
    servicesLoading,
    error,
    handleCountryOfOrigin,
  } = useAdditionalInfoData(formData.selectedMode, formData, actions, updateFormData);

  const {
    handleModeChange,
    handleDirectionChange,
    handleIncotermSelect,
    handleTransshipmentToggle,
    handleBorderChargesToggle,
  } = useModeSpecificData(updateFormData, formData);

  const { showReadyToLoadPicker, openPicker, closePicker } = useDatePicker();
  const { submitting, handleComplete } = useFormSubmission(setActiveTabName);
  const {
    shipmentOptions,
    directionOptions,

    borderChargesOptions,
    packageTypes,

    isAddressEnabled,
    handleAddress,
  } = useEnquiryFormHooks(formData);

  const onPreview = () => {
    setShowPreview(true);
  };

  const onCompleteForm = async () => {
    try {
      await handleComplete(formData);
    } catch (error) {
      console.error('Form completion failed:', error);
    }
  };

  const handleDateConfirm = (params: any) => {
    closePicker();
    actions.handleReadyToLoadDateChange(params);
  };

  const isValid = () => validateForm(formData);
  const { handleLocationSelect } = useLocationValidation(formData);

  const onLocationSelect = (type: 'from' | 'to', location: any) => {
    return handleLocationSelect(type, location, updateFormData, handleAddress);
  };
  // console.log('direciton', formData.direction);
  const onAddressChange = (type: 'from' | 'to', address: string) => {
    const updates: Partial<EnquiryFormData> = {};

    if (type === 'from') {
      updates.fromAddress = address;
    } else {
      updates.toAddress = address;
    }
    updateFormData(updates);
  };

  const onTransshipmentToggle = () => {
    handleTransshipmentToggle(formData, updateFormData);
  };

  const onBorderChargesToggle = () => {
    handleBorderChargesToggle(formData, updateFormData);
  };

  const onTransitLocationSelect = (location: any) => {
    console.log('location >> in transit', location);
    if (!isValidLocation(location, 'transit', formData)) {
      return;
    }

    updateFormData({
      transitLocation: {
        id: location.id,
        name: location.name,
        type: location.type,
      },
      transshipment_port_id: location.id,
    });
  };

  const onBorderChargeSelect = (value: string) => {
    console.log('value selected in bordercharges', value);
    updateFormData({
      selectedBorderCharges: value.Text,
      selectedBorderChargesId: value.Value,
    });
  };

  const onRemarksChange = (text: string) => {
    updateFormData({ remarks: text });
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {loading ? (
        <AppLoader message="Loading data..." />
      ) : error ? (
        <EmptyorErrorComponent message="Failed to Fetch Data" />
      ) : (
        <ScrollView
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          automaticallyAdjustKeyboardInsets
          keyboardDismissMode="on-drag"
          bounces={false}
        >
          <DropdownField
            label="Country of Origin"
            data={countries}
            valueField="Text"
            labelField="Text"
            keyField={'Value'}
            placeholder="Select a Country"
            value={formData?.countryOfOriginName}
            onSelect={handleCountryOfOrigin}
            showAsterisk={true}
          />
          <LocationSection
            formData={formData}
            onLocationSelect={onLocationSelect}
            onAddressChange={onAddressChange}
          />
          <ModeSpecificSections
            formData={formData}
            borderChargesOptions={borderChargesOptions}
            onTransshipmentToggle={onTransshipmentToggle}
            onBorderChargesToggle={onBorderChargesToggle}
            onTransitLocationSelect={onTransitLocationSelect}
            onBorderChargeSelect={onBorderChargeSelect}
            onRemarksChange={onRemarksChange}
          />
          <ShipmentCategory
            formData={formData}
            updateFormData={updateFormData}
            currentStep={currentStep}
            // totalSteps={steps?.length}
            // onNext={goToNextStep}
            // onPrevious={goToPreviousStep}
          />

          {/* need to check where it will place */}
          {/* {formData?.shipmentType && (
            <View
              style={{
                marginBottom: moderateScale(4),
              }}
            >
              <Text
                style={{
                  fontSize: moderateScale(11),
                }}
                variant="semiBold"
              >
                {formData?.shipmentType}
              </Text>
            </View>
          )} */}
          <DatePickerSection
            value={formData.readyToLoadDate}
            onConfirm={handleDateConfirm}
            showPicker={showReadyToLoadPicker}
            onShowPicker={openPicker}
            onHidePicker={closePicker}
          />

          <View style={[styles.row, { marginBottom: verticalScale(7) }]}>
            <View style={[styles.fieldContainer, { flex: 1 }]}>
              <TransportProviderDropdown
                jobType={formData?.selectedMode}
                carriers={carriers}
                airlines={airlines}
                selectedCarrier={formData.selectedSeaCarrierName}
                selectedAirline={formData.selectedAirlineName}
                onSelect={(value) =>
                  actions.handleTransportProviderSelect(value, carriers, airlines)
                }
              />
            </View>
          </View>

          <TimeInputs
            transitTime={formData.transitDays}
            freeTime={formData.freeDays}
            onTransitTimeChange={actions.handleTransitTimeChange}
            onFreeTimeChange={actions.handleFreeTimeChange}
          />
          <AssociatedServicesSection
            services={associatedServices}
            selectedServiceIds={formData.selectedServiceIds || []}
            loading={servicesLoading}
            onServiceToggle={(value) => actions.handleServiceToggle(value, associatedServices)}
          />
          <CargoValueSection
            show={actions.isInsuranceSelected()}
            formData={formData}
            currencies={currencies}
            onCargoValueChange={actions.handleCargoValueChange}
            onCurrencySelect={actions.handleCurrencySelect}
          />
          <AdditionalInfoTextArea
            value={formData.additionalInfo}
            onChangeText={actions.handleAdditionalInfoChange}
          />
          <ActionButtons
            formData={formData}
            currentStep={currentStep}
            totalSteps={totalSteps}
            onNext={onNext}
            onPrevious={onPrevious}
            isNextDisabled={!isValid()}
            // isNextDisabled={false}
            onPreview={onPreview}
            onComplete={onCompleteForm}
            submitting={submitting}
          />

          <EnquiryPreviewModal
            visible={showPreview}
            onClose={() => setShowPreview(false)}
            onSubmit={() => {}}
            formData={formData}
          />
        </ScrollView>
      )}
    </View>
  );
};

export default AdditionalInfoStep;
