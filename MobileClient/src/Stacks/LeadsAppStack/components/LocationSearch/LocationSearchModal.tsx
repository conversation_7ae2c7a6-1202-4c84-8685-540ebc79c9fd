// LocationSeachModal.tsx
import React from 'react';
import { View, TouchableOpacity, StyleSheet, Modal } from 'react-native';
import { verticalScale } from 'react-native-size-matters';

export function LocationSeachModal({
  visible,
  onClose,
  children,
}: {
  visible: boolean;
  onClose: () => void;
  children?: React.ReactNode;
}) {
  return (
    <Modal
      visible={visible}
      animationType="slide"
      onRequestClose={onClose}
      transparent
    >
      <View style={styles.overlay}>
        <TouchableOpacity
          style={styles.backdrop}
          onPress={onClose}
          activeOpacity={1}
        />
        <View style={styles.content}>{children}</View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  backdrop: {
    flex: 1,
    height: '100%', 
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  content: {
    // flex: 1,
    height : '80%',
    
    backgroundColor: '#fff',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    overflow: 'hidden',
  },
});
