import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { Text, ActivityIndicator } from 'react-native-paper';
import { ChevronLeft, ChevronRight, Send, Eye } from 'lucide-react-native';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';

interface ActionButtonsProps {
  formData: any;
  currentStep: number;
  totalSteps: number;
  onNext: () => void;
  onPrevious: () => void;
  isNextDisabled?: boolean;
  isLastStep?: boolean;
  onComplete?: () => void;
  submitting?: boolean;
  onPreview?: () => void;
}

export default function ActionButtons({
  formData,
  currentStep,
  totalSteps,
  onNext,
  onPrevious,
  isNextDisabled = false,
  isLastStep = false,
  onComplete,
  submitting,
  onPreview,
}: ActionButtonsProps) {
  return (
    <View style={styles.container}>
      {currentStep > 1 && (
        <TouchableOpacity style={[styles.button, styles.secondaryButton]} onPress={onPrevious}>
          {/* <ChevronLeft size={20} color="#6B7280" /> */}
          <Text style={styles.secondaryButtonText} variant="medium">Previous</Text>
        </TouchableOpacity>
      )}

      <View style={styles.spacer} />

      {currentStep === totalSteps ? (
        <>
          <TouchableOpacity
            style={[styles.button, styles.previewButton, submitting && styles.disabledButton]}
            onPress={onPreview}
            disabled={submitting}
          >
            <Text style={styles.previewButtonText} variant="medium">
              Preview
            </Text>
            {/* <Eye color="#3377ff" size={moderateScale(16)} /> */}
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.button,
              styles.primaryButton,
              (submitting || isNextDisabled) && styles.disabledButton,
            ]}
            onPress={onComplete}
            disabled={submitting || isNextDisabled}
          >
            <>
              <Text
                style={[
                  styles.primaryButtonText,
                  (submitting || isNextDisabled) && styles.disabledButtonText,
                ]}
                variant="medium"
              >
                {formData?.type === 'Detail' ? 'Update' : 'Submit'}
              </Text>
              {submitting && (
                <ActivityIndicator
                  color="#9CA3AF"
                  size={moderateScale(10)}
                  style={{ marginLeft: 5 }}
                />
              )}
              {/* <Send size={20} color={isNextDisabled ? '#9CA3AF' : '#FFFFFF'} /> */}
            </>
          </TouchableOpacity>
        </>
      ) : (
        <TouchableOpacity
          style={[styles.button, styles.primaryButton, isNextDisabled && styles.disabledButton]}
          onPress={onNext}
          disabled={isNextDisabled}
        >
          <Text
            style={[styles.primaryButtonText, isNextDisabled && styles.disabledButtonText]}
            variant="medium"
          >
            Next
          </Text>
          {/* <ChevronRight size={20} color={isNextDisabled ? '#9CA3AF' : '#FFFFFF'} /> */}
        </TouchableOpacity>
      )}
    </View>
  );
}

// const styles = StyleSheet.create({
//   container: {
//     flexDirection: 'row',
//     alignItems: 'flex-end',
//     // flex:1,
//     // paddingTop: 24,
//     paddingBottom: 62,
//   },
//   button: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     paddingHorizontal: moderateScale(12),
//     paddingVertical: verticalScale(4),
//     borderRadius: 4,
//   },
//   primaryButton: {
//     backgroundColor: '#3b82f6',
//      borderWidth: 1,
//          borderColor: '#3b82f6',
//   },
//   secondaryButton: {
//     backgroundColor: '#FFFFFF',
//     borderWidth: 1,
//     borderColor: '#D1D5DB',
//   },
//   disabledButton: {
//     backgroundColor: '#F3F4F6',
//   },
//   primaryButtonText: {
//     fontSize: moderateScale(12),
//     // fontWeight: '600',
//     color: '#FFFFFF',
//     // marginRight: 8,
//     justifyContent:"center",
//     alignItems:'center',
//   },
//   secondaryButtonText: {
//     fontSize: moderateScale(12),
//     color: '#6B7280',
//     // marginLeft: 8,
//   },
//   disabledButtonText: {
//     color: '#9CA3AF',
//   },
//   spacer: {
//     flex: 1,
//   },
//   previewButton: {
//     backgroundColor: '#f1f5f9',
//     borderWidth: 1,
//     borderColor: '#94a3b8',
//     paddingHorizontal: moderateScale(14),
//     paddingVertical: verticalScale(8),
//     borderRadius: 6,
//   },
//   previewButtonText: {
//     fontSize: moderateScale(13),
//     color: '#334155',
//     fontWeight: '600',
//     textAlign: 'center',
//   },
// });

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingBottom: 62,
  },
  spacer: {
    flex: 1,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: moderateScale(14),
    paddingVertical: verticalScale(6),
    borderRadius: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
    // elevation: 2,
    // shadowColor: '#000',
    // shadowOpacity: 0.08,
    // shadowRadius: 4,
    // shadowOffset: { width: 0, height: 2 },
  },
  primaryButton: {
    backgroundColor: '#3377ff',
    // borderWidth: 1,
    borderColor: '#3377ff',
    marginRight: 10,
    // shadowColor: '#3377ff',
    shadowOffset: { width: 2, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  primaryButtonText: {
    fontSize: moderateScale(12),
    color: '#fff',
    textAlign: 'center',
  },
  secondaryButton: {
    backgroundColor: '#fff',
    // borderWidth: 1,
    borderColor: '#3377ff',
  },
  secondaryButtonText: {
    fontSize: moderateScale(12),
    color: '#3377ff',
    textAlign: 'center',
  },
  disabledButton: {
    backgroundColor: '#f3f4f6',
    borderColor: '#f3f4f6',
  },
  disabledButtonText: {
    color: '#9ca3af',
  },
  previewButton: {
    backgroundColor: '#fff',
    // borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,

    // borderWidth: 1,
    borderColor: '#3377ff',
    paddingHorizontal: moderateScale(14),
    paddingVertical: verticalScale(6),
    borderRadius: 4,
    marginRight: 10,
  },
  previewButtonText: {
    fontSize: moderateScale(12),
    color: '#3377ff',
    textAlign: 'center',
    // marginRight: 5,
  },
});
