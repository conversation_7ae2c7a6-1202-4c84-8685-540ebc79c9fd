// components/shared/SearchableModal.tsx
import React from 'react';
import {
  Modal,
  View,
  TouchableOpacity,
  TextInput,
  ScrollView,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { Text } from 'react-native-paper';
import { Search, X, ChevronDown, ChevronUp } from 'lucide-react-native';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { fontFamily } from '../../../../Common/Theme/typography';
import SearchBar from '../../../../Components/SearchInput';

interface SearchableModalProps {
  visible: boolean;
  title: string;
  searchText: string;
  loading: boolean;
  error: string | null;
  onClose: () => void;
  onSearchTextChange: (text: string) => void;
  onConfirm: () => void;
  children: React.ReactNode;
  confirmDisabled?: boolean;
}

export const SearchableModal: React.FC<SearchableModalProps> = ({
  visible,
  title,
  searchText,
  loading,
  error,
  onClose,
  onSearchTextChange,
  onConfirm,
  children,
  confirmDisabled = false,
}) => {
  return (
    <Modal visible={visible} animationType="slide" transparent={true}>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{title}</Text>
            <TouchableOpacity onPress={onClose}>
              <X size={moderateScale(22)} color="#6B7280" />
            </TouchableOpacity>
          </View>

          <View style={styles.modalSearchContainer}>
            <Search size={16} color="#6B7280" style={styles.searchIcon} />
            <TextInput
              style={styles.modalSearchInput}
              placeholder={`Search ${title.toLowerCase()}...`}
              value={searchText}
              onChangeText={onSearchTextChange}
              placeholderTextColor="#9CA3AF"
            />
          </View>
          {/* <SearchBar
          placeholder={`Search ${title.toLowerCase()}...`}
          onChangeText={onSearchTextChange}
          value={searchText}
          style={{  height: verticalScale(28),}}
        /> */}

          <ScrollView style={styles.contentList}
          showsVerticalScrollIndicator = {false} bounces={false}>
            {loading ? (
              <View style={styles.centered}>
                <ActivityIndicator size="large" color="#4F46E5" />
                <Text style={styles.loadingText}>Loading {title.toLowerCase()}...</Text>
              </View>
            ) : error ? (
              <View style={styles.centered}>
                <Text style={styles.errorText}>{error}</Text>
              </View>
            ) : (
              children
            )}
          </ScrollView>

          <View style={styles.modalFooter}>
            {/* <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity> */}
            <TouchableOpacity
              style={[styles.selectButton, confirmDisabled && styles.selectButtonDisabled]}
              onPress={onConfirm}
              disabled={confirmDisabled}
            >
              <Text
                style={[
                  styles.selectButtonText,
                  confirmDisabled && styles.selectButtonTextDisabled,
                ]}
              >
                Select
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

interface ExpandableItemProps {
  title: string;
  subtitle?: string;
  index : number;
  code?: string;
  isSelected: boolean;
  isExpanded?: boolean;
  hasChildren?: boolean;
  loading?: boolean;
  onPress: () => void;
  onToggleExpansion?: () => void;
  children?: React.ReactNode;
}

export const ExpandableItem: React.FC<ExpandableItemProps> = ({
  title,
  subtitle,
  code,
  isSelected,
  isExpanded = false,
  hasChildren = false,
  loading = false,
  index,
  onPress,
  onToggleExpansion,
  children,
}) => {
  return (
    <View>
      <TouchableOpacity
        style={[styles.itemContainer, isSelected && styles.itemSelected, (isSelected && index === 0 )&& {marginTop : verticalScale(7)}]}
        onPress={onPress}
      >
        <View style={styles.itemContent}>
          <Text
            style={[styles.itemTitle, isSelected && styles.itemTitleSelected]}
            numberOfLines={2}
          >
            {title}
          </Text>
          {subtitle && (
            <Text
              style={[styles.itemSubtitle, isSelected && styles.itemSubtitleSelected]}
              numberOfLines={1}
            >
              {subtitle}
            </Text>
          )}
        </View>

        <View style={styles.itemRight}>
          {code && (
            <Text style={[styles.itemCode, isSelected && styles.itemCodeSelected]}>{code}</Text>
          )}
          {hasChildren && onToggleExpansion && (
            <TouchableOpacity onPress={onToggleExpansion} style={styles.expandButton}>
              {loading ? (
                <ActivityIndicator size="small" color="#6B7280" />
              ) : isExpanded ? (
                <ChevronUp size={16} color="#6B7280" />
              ) : (
                <ChevronDown size={16} color="#6B7280" />
              )}
            </TouchableOpacity>
          )}
        </View>
      </TouchableOpacity>

      {isExpanded && children && <View style={styles.childrenContainer}>{children}</View>}
    </View>
  );
};

interface SubItemProps {
  title: string;
  code?: string;
  isSelected: boolean;
  onPress: () => void;
}

export const SubItem: React.FC<SubItemProps> = ({ title, code, isSelected, onPress }) => {
  return (
    <TouchableOpacity
      style={[styles.subItemContainer, isSelected && styles.itemSelected]}
      onPress={onPress}
    >
      <Text style={[styles.subItemTitle, isSelected && styles.itemTitleSelected]} numberOfLines={2}>
        {title}
      </Text>
      {code && (
        <Text style={[styles.subItemCode, isSelected && styles.subItemCodeSelected]}>{code}</Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: scale(12),
    width: '90%',
    maxHeight: '80%',
    paddingVertical: verticalScale(20),
    paddingHorizontal : moderateScale(20)
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // paddingHorizontal: scale(20),
    marginBottom: verticalScale(10),
  },
  modalTitle: {
    fontSize: moderateScale(12),
    fontFamily : fontFamily.semiBold,
    // fontWeight: '600',
    color: '#111827',
  },
  modalSearchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    // marginHorizontal: scale(16),
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    paddingHorizontal: moderateScale(12),
    paddingVertical: verticalScale(8),
    // marginBottom: verticalScale(10),
  },
  searchIcon: {
    marginRight: scale(8),
  },
  modalSearchInput: {
    flex: 1,
    fontSize: moderateScale(12),
    color: '#111827',
    paddingVertical: 0,
  },
  contentList: {
    maxHeight: verticalScale(400),
    // paddingHorizontal: scale(20),
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: scale(20),
  },
  loadingText: {
    marginTop: verticalScale(8),
    fontSize: moderateScale(12),
    color: '#6B7280',
  },
  errorText: {
    fontSize: moderateScale(12),
    color: '#EF4444',
    textAlign: 'center',
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: verticalScale(7),
    paddingLeft: scale(16),
    borderRadius: 8,
    // marginBottom: verticalScale(4),
  },
  itemSelected: {
    backgroundColor: '#3B82F6',
    // marginTop : verticalScale(10)
  },
  itemContent: {
    flex: 1,
    marginRight: scale(12),
  },
  itemTitle: {
    fontSize: moderateScale(12),
    color: '#111827',
    fontFamily : fontFamily.regular,

  },
  itemTitleSelected: {
    color: '#FFFFFF',
  },
  itemSubtitle: {
    fontSize: moderateScale(11),
    color: '#6B7280',
    marginTop: verticalScale(2),
  },
  itemSubtitleSelected: {
    color: '#E5E7EB',
  },
  itemRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(8),
  },
  itemCode: {
    fontSize: moderateScale(11),
    color: '#6B7280',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: scale(8),
    paddingVertical: verticalScale(4),
    borderRadius: 12,
    fontFamily : fontFamily.regular,
    // fontWeight: '600',
    minWidth: scale(40),
    textAlign: 'center',
  },
  itemCodeSelected: {
    color: '#3B82F6',
    backgroundColor: '#FFFFFF',
  },
  expandButton: {
    padding: scale(4),
  },
  childrenContainer: {
    marginLeft: scale(20),
    borderLeftWidth: 1,
    borderLeftColor: '#E5E7EB',
    paddingLeft: scale(16),
  },
  subItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: verticalScale(5),
    paddingHorizontal: scale(12),
    borderRadius: 6,
    marginBottom: verticalScale(2),
  },
  subItemTitle: {
    flex: 1,
    fontSize: moderateScale(11),
    color: '#374151',
    fontFamily : fontFamily.regular,
    marginRight: scale(12),
  },
  subItemCode: {
    fontSize: moderateScale(11),
    color: '#6B7280',
    backgroundColor: '#F9FAFB',
    paddingHorizontal: scale(6),
    paddingVertical: verticalScale(2),
    borderRadius: 8,
    fontFamily : fontFamily.regular,
    // fontWeight: '600',
    minWidth: scale(60),
    textAlign: 'center',
  },
  subItemCodeSelected: {
    color: '#3B82F6',
    backgroundColor: '#FFFFFF',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    // paddingHorizontal: scale(20),
    paddingTop: verticalScale(16),
    gap: scale(12),
  },
  cancelButton: {
    paddingHorizontal: scale(16),
    paddingVertical: verticalScale(8),
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  cancelButtonText: {
    fontSize: moderateScale(12),
    color: '#6B7280',
    fontWeight: '500',
  },
  selectButton: {
    paddingHorizontal: scale(16),
    paddingVertical: verticalScale(8),
    borderRadius: 6,
    backgroundColor: '#3B82F6',
  },
  selectButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  selectButtonText: {
    fontSize: moderateScale(12),
    color: '#FFFFFF',
    fontWeight: '600',
  },
  selectButtonTextDisabled: {
    color: '#D1D5DB',
  },
});
