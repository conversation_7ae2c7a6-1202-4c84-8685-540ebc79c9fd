// components/LocationSection.tsx
import React from 'react';
import { View } from 'react-native';
import SearchLocation from '../../../Components/SearchLocation';
import { AddressInput } from './Shared/AddressInput';
import { EnquiryFormData } from '../types';
import { LocationData } from './ShipmentDetailsStep/types/shipment.types';
import LocationAddressSearch from '../../../Components/LocationAddressSearch';
import { verticalScale } from 'react-native-size-matters';
interface LocationSectionProps {
  formData: EnquiryFormData;
  isAddressEnabled: {
    from: boolean;
    to: boolean;
  };
  onLocationSelect: (type: 'from' | 'to', location: LocationData) => boolean;
  onAddressChange: (type: 'from' | 'to', address: string) => void;
}

export const LocationSection: React.FC<LocationSectionProps> = ({
  formData,
  onLocationSelect,
  onAddressChange,
}) => {
  return (
    <View>
      {/* From Location */}
      <SearchLocation
        value={formData.fromLocation.name}
        modeOfShipment={formData.selectedMode}
        onLocationSelect={(location) => {
          // console.log("from location", location)
          onLocationSelect('from', location);
        }}
        testID="from-location-search"
      />

      {/* From Address */}
      {formData?.isFromLocationAddress && (
        // <AddressInput
        //   label="From Address"
        //   value={formData.fromAddress || ''}
        //   onChangeText={(text) => onAddressChange('from', text)}
        //   placeholder="Enter From Address"
        //   testID="from-address-input"
        // />
        <View style={{ marginBottom: verticalScale(10) }}>
          <LocationAddressSearch
            label="From Address"
            value={formData.fromAddress || ''}
            onAddressChange={(text) => onAddressChange('from', text)}
            placeholder="From Address"
            testID="from-address-input"
          />
        </View>
      )}

      {/* To Location */}
      <SearchLocation
        label="To"
        value={formData.toLocation.name}
        modeOfShipment={formData.selectedMode}
        onLocationSelect={(location) => {
          console.log('to location select', location);
          onLocationSelect('to', location);
        }}
        //  placeholder="Enter To Address"
        testID="to-location-search"
      />

      {/* To Address */}
      {formData?.isToLocationAddress && (
        // <AddressInput
        //   label="To Address"
        //   value={formData.toAddress || ''}
        //   onChangeText={(text) => onAddressChange('to', text)}
        //   placeholder="Enter To Address"
        //   testID="to-address-input"
        // />
        <View style={{ marginBottom: verticalScale(10) }}>
          <LocationAddressSearch
            label="To Address"
            value={formData.toAddress || ''}
            onAddressChange={(text) => onAddressChange('to', text)}
            placeholder="To Address"
            testID="to-address-input"
          />
        </View>
      )}
    </View>
  );
};
