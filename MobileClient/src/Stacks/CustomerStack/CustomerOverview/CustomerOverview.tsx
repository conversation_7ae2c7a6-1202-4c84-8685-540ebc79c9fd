import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ActivityIndicator, ScrollView, Image } from 'react-native';
import { Badge, Text } from 'react-native-paper';
import { moderateScale, scale } from 'react-native-size-matters';
import { useSelector } from 'react-redux';
import { PrimraryCard } from './Cards';
import SecondaryCard, { CardHeading } from './InnerCard';
import {
  ArrowDown,
  ArrowDownUp,
  ArrowUp,
  Briefcase,
  BriefcaseIcon,
  CalendarDays,
  DollarSign,
  FileText,
  MoveDown,
  MoveRight,
  Phone,
  Plane,
  Search,
  Ship,
  Target,
  Truck,
  Users,
} from 'lucide-react-native';

import {
  CustomerOverviewProps,
  Stats,
  Invoice,
  Job,
  Contact,
  SalesActivity,
  RecentEnquiry,
} from './types';

import {
  getDateConvert,
  getStatusStyle,
  fetchOrganizationStatics,
  fetchInvoiceList,
  fetchAllJobs,
  fetchOrgContacts,
  fetchAllActivityEvents,
  fetchAllEnquiries,
} from './CustomerOver.functions';

const CustomerOverView: React.FC<CustomerOverviewProps> = ({ org_id, type }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [stats, setStats] = useState<Stats>({});
  const [invoiceList, setInvoiceList] = useState<Invoice[]>([]);
  const [jobs, setJobs] = useState<Job[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [saleActivityTypes, setSalesActivityTypes] = useState<SalesActivity[]>([]);
  const [invoicePage, setInvoicePage] = useState<number>(1);
  const [jobsPage, setJobsPage] = useState<number>(1);
  const [hasMoreInvoices, setHasMoreInvoices] = useState<boolean>(true);
  const [hasMoreJobs, setHasMoreJobs] = useState<boolean>(true);
  const [recentQueries, setRecentQueries] = useState<RecentEnquiry[]>([]);

  const controlUnitsData = useSelector(
    (state: any) => state?.GlobalAppStateData?.selectedControlUnit?.data
  );

  const getIcons = (item: string) => {
    const iconProps = { size: moderateScale(20), style: styles.modeIcon };
    switch (item) {
      case 'SEA':
        return <Ship {...iconProps} color="#2563eb" />;
      case 'AIR':
        return <Plane {...iconProps} color="#9333ea" />;
      default:
        return <Truck {...iconProps} color="#d97706" />;
    }
  };

  const loadMoreInvoices = async () => {
    if (hasMoreInvoices && !loading) {
      try {
        setLoading(true);
        const result = await fetchInvoiceList(
          org_id,
          controlUnitsData?.id || null,
          invoicePage + 1
        );
        setInvoiceList((prev) => [...prev, ...result.invoices]);
        setHasMoreInvoices(result.hasMore);
        setInvoicePage(invoicePage + 1);
      } catch (error) {
        console.log('Error loading more invoices:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  const loadMoreJobs = async () => {
    if (hasMoreJobs && !loading) {
      try {
        setLoading(true);
        const result = await fetchAllJobs(org_id, controlUnitsData?.id || null, jobsPage + 1);
        setJobs((prev) => [...prev, ...result.jobs]);
        setHasMoreJobs(result.hasMore);
        setJobsPage(jobsPage + 1);
      } catch (error) {
        console.log('Error loading more jobs:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  const loadData = async () => {
    try {
      setLoading(true);
      const controlUnitId = controlUnitsData?.id || null;

      const [statsData, invoiceData, jobsData, contactsData, activitiesData, enquiriesData] =
        await Promise.all([
          fetchOrganizationStatics(org_id, controlUnitId),
          fetchInvoiceList(org_id, controlUnitId),
          fetchAllJobs(org_id, controlUnitId),
          fetchOrgContacts(org_id, controlUnitId),
          fetchAllActivityEvents(org_id, controlUnitId),
          fetchAllEnquiries(org_id, controlUnitId, type),
        ]);

      setStats(statsData);
      setInvoiceList(invoiceData.invoices);
      setHasMoreInvoices(invoiceData.hasMore);
      setJobs(jobsData.jobs);
      setHasMoreJobs(jobsData.hasMore);
      setContacts(contactsData);
      setSalesActivityTypes(activitiesData);
      setRecentQueries(enquiriesData);
    } catch (error) {
      console.log('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (org_id) {
      loadData();
    }
  }, [org_id]);

  const renderDashboardStats = () => {
    if (!stats || Object.keys(stats).length === 0) return null;
    
    const statsData = [
      {
        title: 'Total Amount Receivable',
        value: `${stats?.currency_code || ''} ${stats?.acc_receivables_amount || '0'}`,
        icon: <DollarSign size={moderateScale(20)} color="#16a34a" />,
        color: '#16a34a'
      },
      {
        title: 'Total Open Jobs',
        value: stats?.total_jobs || '0',
        icon: <Briefcase size={moderateScale(20)} color="#2563eb" />,
        color: '#2563eb'
      },
      {
        title: 'Win Ratio',
        value: `${stats?.win_ratio_percentage || '0'}%`,
        icon: <Target size={moderateScale(20)} color="#ea580c" />,
        color: '#ea580c'
      }
    ];

    return (
      <View style={styles.statsContainer}>
        {statsData.map((stat, index) => (
          <View key={index} style={styles.statCard}>
            <SecondaryCard styles={styles.statisticsCard}>
              <View style={styles.statHeader}>
                <View style={styles.statTitleContainer}>
                  <Text style={styles.statTitle} numberOfLines={2}>{stat.title}</Text>
                </View>
                {stat.icon}
              </View>
              <View style={styles.divider} />
              <Text variant="semiBold" style={styles.statValue}>
                {stat.value}
              </Text>
            </SecondaryCard>
          </View>
        ))}
      </View>
    );
  };

 const renderRecentEnquiries = () => {
 if (!recentQueries?.length) return null;

 return (
   <PrimraryCard>
     <View style={styles.sectionHeader}>
       <Search size={moderateScale(20)} color="#6b7280" />
       <CardHeading title="Recent Enquiries" />
     </View>
     {recentQueries.map((item, index) => (
       <View key={item.enquiry_id || `enquiry-${index}`} style={styles.cardMargin}>
         <SecondaryCard>
           <View style={styles.enquiryCard}>
             <View style={styles.enquiryHeader}>
               <View style={styles.enquiryTypeContainer}>
                 {getIcons(item.enquiry_type || '')}
                 <Text variant="semiBold" style={styles.enquiryType}>
                   {item.enquiry_type}
                 </Text>
               </View>
               <View style={[
                 styles.statusBadge,
                 { backgroundColor: item.status_name === 'Quoted' ? '#fef3c7' : '#f3f4f6' }
               ]}>
                 <Text style={[
                   styles.statusText,
                   { color: item.status_name === 'Quoted' ? '#92400e' : '#374151' }
                 ]}>
                   {item.status_name}
                 </Text>
               </View>
             </View>
             
             <Text variant="semiBold" style={styles.referenceNumber}>
               {item.reference_number}
             </Text>
             
             <View style={styles.routeContainer}>
               <View style={styles.locationCard}>
                 <Text style={styles.routeLabel}>From:</Text>
                 <View style={styles.locationDetails}>
                   <Image
                     style={styles.flagImage}
                     source={{ uri: item.from_country_flag }}
                     resizeMode="cover"
                   />
                   <Text numberOfLines={2} style={styles.locationText}>
                     {item.from_port_name || item.from_country_name}
                   </Text>
                 </View>
               </View>
               
               <View style={styles.routeArrow}>
                 <MoveRight size={moderateScale(16)} color="#6b7280" />
               </View>
               
               <View style={styles.locationCard}>
                 <Text style={styles.routeLabel}>To:</Text>
                 <View style={styles.locationDetails}>
                   <Image
                     style={styles.flagImage}
                     source={{ uri: item.to_country_flag }}
                     resizeMode="cover"
                   />
                   <Text numberOfLines={2} style={styles.locationText}>
                     {item.to_port_name || item.to_country_name}
                   </Text>
                 </View>
               </View>
             </View>
           </View>
         </SecondaryCard>
       </View>
     ))}
   </PrimraryCard>
 );
};

  const renderInvoiceList = () => {
    if (!invoiceList?.length) return null;

    return (
      <PrimraryCard>
        <View style={styles.sectionHeader}>
          <FileText size={moderateScale(20)} color="#3b82f6" />
          <CardHeading title="Customer Invoices" />
        </View>
        {invoiceList.map((item, index) => (
          <View key={item?.reference_number || `invoice-${index}`} style={styles.cardMargin}>
            <SecondaryCard>
              <View style={styles.invoiceCard}>
                <View style={styles.invoiceHeader}>
                  <Text variant="semiBold" style={styles.invoiceReference}>
                    {item?.reference_number || 'No Reference'}
                  </Text>
                  <View style={[
                    styles.invoiceStatusBadge,
                    { backgroundColor: item?.invoice_status_name === 'sent' ? '#3b82f6' : '#f59e0b' }
                  ]}>
                    <Text style={styles.invoiceStatusText}>
                      {item?.invoice_status_name || 'Unknown'}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.invoiceContent}>
                  <Text style={styles.customerName}>
                    {item?.customer_name || 'Unknown Customer'}
                  </Text>
                  <View style={styles.invoiceDateContainer}>
                    <CalendarDays size={moderateScale(14)} color="#6b7280" />
                    <Text style={styles.invoiceDate}>
                      {item?.invoice_date || 'No Date'}
                    </Text>
                  </View>
                  <Text variant="semiBold" style={styles.invoiceAmount}>
                    {item?.currency_code || ''} {item?.invoice_amount || '0'}
                  </Text>
                </View>
              </View>
            </SecondaryCard>
          </View>
        ))}
        {hasMoreInvoices && (
          <View style={styles.loadMoreContainer}>
            <Text style={styles.loadMoreText} onPress={loadMoreInvoices}>
              Load More Invoices
            </Text>
          </View>
        )}
      </PrimraryCard>
    );
  };

  const renderJobs = () => {
    if (!jobs?.length) return null;

    return (
      <PrimraryCard>
        <View style={styles.sectionHeader}>
          <BriefcaseIcon size={moderateScale(20)} color="#ea580c" />
          <CardHeading title="Recent Jobs" />
        </View>
        {jobs.map((item, index) => (
          <View key={item?.job_id || `job-${index}`} style={styles.cardMargin}>
            <SecondaryCard>
              <View style={styles.jobCard}>
                <View style={styles.jobHeader}>
                  <View style={styles.jobModeContainer}>
                    {getIcons(item?.mode_of_shipment)}
                     {item?.direction_name === "Import" ? (
  <ArrowDown size={18} color="green" />
) : item?.direction_name === "Export" ? (
  <ArrowUp size={18} color="red" />
) : item?.direction_name === "Crosstrade" ? (
  <ArrowDownUp size={18} color="green" />
) : null}

                  </View>
                  <View style={[
                    styles.jobStatusBadge,
                    { backgroundColor: item?.job_status_name === 'Draft' ? '#fef3c7' : '#f3f4f6' }
                  ]}>
                    <Text style={[
                      styles.jobStatusText,
                      { color: item?.job_status_name === 'Draft' ? '#92400e' : '#374151' }
                    ]}>
                      {item?.job_status_name || 'Unknown Status'}
                    </Text>
                  </View>
                </View>
                
                <Text variant="semiBold" style={styles.jobReference}>
                  {item?.reference_number || 'No Reference'}
                </Text>
                
               
                <View style={styles.routeContainer}>
               <View style={styles.locationCard}>
                 <Text style={styles.routeLabel}>From:</Text>
                 <View style={styles.locationDetails}>
                   <Image
                     style={styles.flagImage}
                     source={{ uri: item.from_country_flag }}
                     resizeMode="cover"
                   />
                   <Text numberOfLines={2} style={styles.locationText}>
                     {item.from_city_name ||  item.from_port_name||  'N/A'}
                   </Text>
                 </View>
               </View>
               
               <View style={styles.routeArrow}>
                 <MoveRight size={moderateScale(16)} color="#6b7280" />
               </View>
               
               <View style={styles.locationCard}>
                 <Text style={styles.routeLabel}>To:</Text>
                 <View style={styles.locationDetails}>
                   <Image
                     style={styles.flagImage}
                     source={{ uri: item.to_country_flag }}
                     resizeMode="cover"
                   />
                   <Text numberOfLines={2} style={styles.locationText}>
                     {item.to_city_name ||  item.to_port_name ||   'N/A'}
                   </Text>
                 </View>
               </View>
             </View>
                
                <View style={styles.jobDetails}>
                  <Text style={styles.jobDetailText}>
                    {item?.incoterm_name || 'No Incoterm'}
                  </Text>
                  <Text style={styles.jobDetailText}>
                    {item?.organization_name || 'No Organization'}
                  </Text>
                </View>
              </View>
            </SecondaryCard>
          </View>
        ))}
        {hasMoreJobs && (
          <View style={styles.loadMoreContainer}>
            <Text style={styles.loadMoreText} onPress={loadMoreJobs}>
              Load More Jobs
            </Text>
          </View>
        )}
      </PrimraryCard>
    );
  };

  const renderContacts = () => {
    if (!contacts?.length) return null;

    return (
      <PrimraryCard>
        <View style={styles.sectionHeader}>
          <Users size={moderateScale(20)} color="#4f46e5" />
          <CardHeading title="Key Contacts" />
        </View>
        {contacts.map((item, index) => (
          <View key={item?.contact_id || `contact-${index}`} style={styles.cardMargin}>
            <SecondaryCard>
              <View style={styles.contactCard}>
                <View style={styles.contactAvatar}>
                  <Text variant="semiBold" style={styles.contactAvatarText}>
                    {item?.contact_name ? item.contact_name.charAt(0).toUpperCase() : '?'}
                  </Text>
                </View>
                
                <View style={styles.contactInfo}>
                  <View style={styles.contactHeader}>
                    <Text variant="semiBold" style={styles.contactName} numberOfLines={1}>
                      {item?.contact_name || 'Unnamed Contact'}
                    </Text>
                    <View style={[
                      styles.contactStatusBadge,
                      {
                        backgroundColor: item?.status_id === 1 ? '#3b82f6' : '#f3f4f6',
                      }
                    ]}>
                      <Text style={[
                        styles.contactStatusText,
                        { color: item?.status_id === 1 ? '#ffffff' : '#374151' }
                      ]}>
                        {item?.status_id === 1 ? 'Active' : 'Inactive'}
                      </Text>
                    </View>
                  </View>
                  
                  {item?.is_primary === 1 && (
                    <View style={styles.primaryBadgeContainer}>
                      <Text style={styles.primaryBadge}>Primary</Text>
                    </View>
                  )}
                  
                  <Text style={styles.contactEmail} numberOfLines={1}>
                    {item?.emails?.[0]?.email || 'No Email'}
                  </Text>
                  <Text style={styles.contactEmail} numberOfLines={1}>
                   <Phone size={12}/>  {item?.phone_numbers?.[0]?.contact_phone_number || 'No Email'}
                  </Text>
                </View>
              </View>
            </SecondaryCard>
          </View>
        ))}
      </PrimraryCard>
    );
  };

  const renderSalesActivities = () => {
    if (!saleActivityTypes?.length) return null;

    return (
      <PrimraryCard>
        <View style={styles.sectionHeader}>
          <Users size={moderateScale(20)} color="#6b7280" />
          <CardHeading title="Recent Activities" />
        </View>
        {saleActivityTypes.map((item, index) => (
          <View key={item?.sales_activity_id || `activity-${index}`} style={styles.cardMargin}>
            <SecondaryCard>
              <View style={styles.activityCard}>
                <View style={styles.activityHeader}>
                  <Text variant="semiBold" style={styles.activityTitle} numberOfLines={1}>
                    {item?.sales_activity_title || 'No Title'}
                  </Text>
                  <View style={styles.activityStateBadge}>
                    <Text style={styles.activityStateText}>
                      {item?.sales_activity_state_name || 'No State'}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.activityMeta}>
                  <Text style={styles.activityDate}>
                    {getDateConvert(item?.start_date_time || '')}
                  </Text>
                  <Text style={styles.activityType}>
                    {item?.sales_activity_type_name || 'No Type'}
                  </Text>
                </View>
                
                <View style={styles.activityFooter}>
                  <View style={styles.activityPriorityBadge}>
                    <Text style={styles.activityPriorityText}>
                      {item?.sales_activity_priority_name || 'No Priority'}
                    </Text>
                  </View>
                  <Text style={styles.assignedUser}>
                    {item?.assigned_users?.[0]?.first_name || 'Unassigned'}
                  </Text>
                </View>
                
                <Text style={styles.activityOrganization}>
                  {item?.organization_name || 'No Organization'}
                </Text>
              </View>
            </SecondaryCard>
          </View>
        ))}
      </PrimraryCard>
    );
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false} bounces={false}>
      {renderDashboardStats()}
      {renderInvoiceList()}
      {renderJobs()}
      {renderContacts()}
      {renderSalesActivities()}
      {renderRecentEnquiries()}
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3b82f6" />
        </View>
      )}
    </ScrollView>
  );
};

export default CustomerOverView;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  locationDetails: {
  flexDirection: 'row',
  alignItems: 'center',
  gap: moderateScale(6),
},
locationCard: {
  flex: 1,
  gap: moderateScale(4),
  width:'33%',
  
},

  cardMargin: {
    marginVertical: moderateScale(4),
  },

  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(8),
    marginBottom: moderateScale(4),
  },

  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: moderateScale(4),
    marginVertical: moderateScale(8),
    // paddingVertical:moderateScale(6),
    paddingHorizontal:moderateScale(6)
  },
  statCard: {
    width: '50%',
    paddingHorizontal: moderateScale(8),
    paddingVertical:moderateScale(6)
  },
  statisticsCard: {
    backgroundColor: '#ffffff',
    padding: moderateScale(12),
    // borderRadius: moderateScale(12),
    borderWidth: 1,
    borderColor: '#e2e8f0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
     paddingVertical:moderateScale(8),
    paddingHorizontal:moderateScale(12)
  },
  statHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
   
  },
  statTitleContainer: {
    flex: 1,
    marginRight: moderateScale(8),
  },
  statTitle: {
    fontSize: moderateScale(10),
    color: '#64748b',
    lineHeight: moderateScale(12),
  },
  divider: {
    // height: 1,
    backgroundColor: '#e2e8f0',
    marginVertical: moderateScale(8),
  },
  statValue: {
    fontSize: moderateScale(12),
    color: '#1e293b',
  },


  enquiryCard: {
    padding: moderateScale(12),
    gap: moderateScale(8),
  },
  enquiryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  enquiryTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(6),
    flex: 1,
  },
  modeIcon: {
    marginRight: moderateScale(2),
  },
  enquiryType: {
    fontSize: moderateScale(11),
    color: '#374151',
  },
  statusBadge: {
    paddingHorizontal: moderateScale(8),
    paddingVertical: moderateScale(4),
    borderRadius: moderateScale(12),
  },
  statusText: {
    fontSize: moderateScale(10),
    fontWeight: '600',
  },
  referenceNumber: {
    fontSize: moderateScale(11),
    color: '#1e293b',
  },


  routeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: moderateScale(4),
    
  },
  routeSection: {
    flex: 1,
    alignItems: 'center',
    gap: moderateScale(2),
  },
  routeLabel: {
    fontSize: moderateScale(10),
    color: '#6b7280',
    fontWeight: '500',
  },
  routeCode: {
    fontSize: moderateScale(10),
    color: '#374151',
    fontWeight: '600',
  },
  routeArrow: {
    paddingHorizontal: moderateScale(8),
  width:'33%',

  },
  flagImage: {
    width: moderateScale(24),
    height: moderateScale(16),
    borderRadius: moderateScale(2),
  },
  locationText: {
    fontSize: moderateScale(10),
    color: '#374151',
    flex: 1,
  },invoiceCard: {
    padding: moderateScale(12),
    gap: moderateScale(8),
  },
  invoiceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  invoiceReference: {
    fontSize: moderateScale(11),
    color: '#1e293b',
    flex: 1,
    marginRight: moderateScale(8),
  },
  invoiceStatusBadge: {
    paddingHorizontal: moderateScale(8),
    paddingVertical: moderateScale(4),
    borderRadius: moderateScale(6),
  },
  invoiceStatusText: {
    fontSize: moderateScale(10),
    color: '#ffffff',
    fontWeight: '600',
  },
  invoiceContent: {
    gap: moderateScale(4),
  },
  customerName: {
    fontSize: moderateScale(10),
    color: '#374151',
  },
  invoiceDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(4),
  },
  invoiceDate: {
    fontSize: moderateScale(10),
    color: '#6b7280',
  },
  invoiceAmount: {
    fontSize: moderateScale(11),
    color: '#1e293b',
  },

  jobCard: {
    padding: moderateScale(12),
    gap: moderateScale(8),
  },
  jobHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  jobModeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    // gap: moderateScale(2),
    flex: 1,
  },
  jobMode: {
    fontSize: moderateScale(11),
    color: '#374151',
  },
  jobStatusBadge: {
    paddingHorizontal: moderateScale(8),
    paddingVertical: moderateScale(4),
    borderRadius: moderateScale(6),
  },
  jobStatusText: {
    fontSize: moderateScale(10),
    fontWeight: '600',
  },
  jobReference: {
    fontSize: moderateScale(11),
    color: '#1e293b',
  },
  jobDetails: {
    gap: moderateScale(2),
  },
  jobDetailText: {
    fontSize: moderateScale(10),
    color: '#6b7280',
  },

  contactCard: {
    padding: moderateScale(12),
    flexDirection: 'row',
    gap: moderateScale(10),
  },
  contactAvatar: {
    width: moderateScale(36),
    height: moderateScale(36),
    borderRadius: moderateScale(18),
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  contactAvatarText: {
    fontSize: moderateScale(12),
    color: '#ffffff',
  },
  contactInfo: {
    flex: 1,
    gap: moderateScale(4),
  },
  contactHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  contactName: {
    fontSize: moderateScale(11),
    color: '#1e293b',
    flex: 1,
    marginRight: moderateScale(8),
  },
  contactStatusBadge: {
    paddingHorizontal: moderateScale(6),
    paddingVertical: moderateScale(2),
    borderRadius: moderateScale(4),
  },
  contactStatusText: {
    fontSize: moderateScale(10),
    fontWeight: '600',
  },
  primaryBadgeContainer: {
    alignSelf: 'flex-start',
  },
  primaryBadge: {
    fontSize: moderateScale(10),
    color: '#059669',
    fontWeight: '600',
  },
  contactEmail: {
    fontSize: moderateScale(10),
    color: '#6b7280',
  },

  activityCard: {
    padding: moderateScale(12),
    gap: moderateScale(6),
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  activityTitle: {
    fontSize: moderateScale(11),
    color: '#1e293b',
    flex: 1,
    marginRight: moderateScale(8),
  },
  activityStateBadge: {
    backgroundColor: '#dbeafe',
    paddingHorizontal: moderateScale(8),
    paddingVertical: moderateScale(2),
    borderRadius: moderateScale(6),
  },
  activityStateText: {
    fontSize: moderateScale(10),
    color: '#1e40af',
    fontWeight: '600',
  },
  activityMeta: {
    flexDirection: 'row',
    gap: moderateScale(12),
  },
  activityDate: {
    fontSize: moderateScale(10),
    color: '#6b7280',
  },
  activityType: {
    fontSize: moderateScale(10),
    color: '#6b7280',
  },
  activityFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  activityPriorityBadge: {
    backgroundColor: '#fef3c7',
    paddingHorizontal: moderateScale(6),
    paddingVertical: moderateScale(2),
    borderRadius: moderateScale(4),
  },
  activityPriorityText: {
    fontSize: moderateScale(10),
    color: '#92400e',
    fontWeight: '600',
  },
  assignedUser: {
    fontSize: moderateScale(10),
    color: '#374151',
  },
  activityOrganization: {
    fontSize: moderateScale(10),
    color: '#6b7280',
  },

  loadMoreContainer: {
    alignItems: 'center',
    paddingVertical: moderateScale(12),
    marginTop: moderateScale(8),
  },
  loadMoreText: {
    fontSize: moderateScale(11),
    color: '#3b82f6',
    fontWeight: '600',
  },
  loadingContainer: {
    paddingVertical: moderateScale(20),
    alignItems: 'center',
    justifyContent: 'center',
  },
});