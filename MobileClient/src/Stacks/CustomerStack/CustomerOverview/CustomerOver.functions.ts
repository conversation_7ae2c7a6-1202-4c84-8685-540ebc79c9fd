// utils/CustomerOverview.utils.ts

import moment from "moment";
import { moderateScale } from "react-native-size-matters";
import {
  getAccountId,
  getSchemaName,
  getSelectedWorkspaceId,
  getUserId,
} from "../../../Common/Utils/Storage";
import ApiClient from "../../../Common/API/ApiClient";
import { LEADS_ENDPOINTS } from "../../../Common/API/ApiEndpoints";
import { ContextPayload, StatusStyle } from "./types";

// export const formatNumber = (num: number | string): string => {
//   const numValue = typeof num === 'string' ? parseInt(num) : num;
//   return (numValue < 1 && numValue > 0) ? `0${numValue}` : `${numValue}`;
// };

export const getDateConvert = (item: string): string => {
  if (!item) return "No Date";
  try {
    return moment.utc(item).local().format("MMM DD, hh:mm A");
  } catch (error) {
    return "Invalid Date";
  }
};

export const getStatusStyle = (status: string): StatusStyle => {
  const normalizedStatus = status?.toLowerCase() || '';
  const baseContainer = {
    borderRadius: moderateScale(8),
    paddingHorizontal: moderateScale(8),
    paddingVertical: moderateScale(4),
    borderWidth: moderateScale(0.5),
    borderColor: 'gray'
  };

  switch (normalizedStatus) {
    case "sent":
      return {
        container: { ...baseContainer, backgroundColor: "black" },
        text: { color: "white" }
      };
    case "draft":
      return {
        container: { ...baseContainer, backgroundColor: "transparent" },
        text: { color: "#0a0a0a" }
      };
    default:
      return {
        container: { ...baseContainer, backgroundColor: "#e2e8f0" },
        text: { color: "black" }
      };
  }
};

export const buildContextPayload = async (org_id: string): Promise<ContextPayload | null> => {
  try {
    const user_id = await getUserId();
    const schemaName = await getSchemaName();
    const workspace_id = await getSelectedWorkspaceId();
    const account_id = await getAccountId();

    return {
      schemaName,
      contextJson: {
        account_id: account_id,
        workspace_id: workspace_id,
        control_unit_id: null,
        org_id: org_id,
        account_user_id: user_id,
      },
      transportationType: null,
      operationType: "BOTH",
    };
  } catch (error) {
    console.error("Error building context payload:", error);
    return null;
  }
};

export const fetchOrganizationStatics = async (org_id: string, controlUnitId: string | null) => {
  const payload = await buildContextPayload(org_id);
  if (!payload) return {};

  payload.contextJson.control_unit_id = controlUnitId;
const finalPayload = {
  schemaName: payload.schemaName,
    contextJson: payload.contextJson,
      transportationType: payload.transportationType,
      operationType: payload.operationType,
    // below is out dev
    payloadJson: {
      transportation_type: payload.transportationType,
      operation_type: payload.operationType,
    },
  };


  const res = await ApiClient.post(
    LEADS_ENDPOINTS.GET_CUSTOMER_ORGANISATATION_STATICS,
    {
      encryptedPayload: JSON.stringify(finalPayload),
      iv: "dev-mode",
    },
  );
console.log('dsfgafd',res)
  return res?.data?.data?.[0] || {};
};

export const fetchInvoiceList = async (org_id: string, controlUnitId: string | null, page: number = 1) => {
  const payload = await buildContextPayload(org_id);
  if (!payload) return { invoices: [], hasMore: false };

  payload.contextJson.control_unit_id = controlUnitId;

  const encryptedPayload = JSON.stringify({
    schemaName: payload.schemaName,
    contextJson: {
      account_id: payload.contextJson.account_id,
      workspace_id: payload.contextJson.workspace_id,
      control_unit_id: payload.contextJson.control_unit_id,
    },
    payloadJson: {
      customer_id: org_id,
      page_number: page,
      page_size: 20,
    },
  });

  const response = await ApiClient.post(
    LEADS_ENDPOINTS.GET_INVOICE_LIST,
    { encryptedPayload, iv: "dev-mode" }
  );

  const invoicesData = response?.data?.data?.[0]?.result?.invoices || [];
  return {
    invoices: invoicesData,
    hasMore: invoicesData.length === 20
  };
};

export const fetchAllJobs = async (org_id: string, controlUnitId: string | null, page: number = 1) => {
  const payload = await buildContextPayload(org_id);
  if (!payload) return { jobs: [], hasMore: false };

  payload.contextJson.control_unit_id = controlUnitId;

  const encryptedPayload = JSON.stringify({
    schemaName: payload.schemaName,
    contextJson: {
      account_id: payload.contextJson.account_id,
      workspace_id: payload.contextJson.workspace_id,
      control_unit_id: payload.contextJson.control_unit_id,
    },
    payloadJson: {
      page_number: page,
      limit: 5,
      org_id: org_id,
    },
  });

  const response = await ApiClient.post(LEADS_ENDPOINTS.GET_ALL_JOBS, {
    encryptedPayload,
    iv: "dev-mode",
  });

  const allJobs = response?.data?.data?.[1] || [];
  return {
    jobs: allJobs,
    hasMore: allJobs.length === 10
  };
};

export const fetchOrgContacts = async (org_id: string, controlUnitId: string | null) => {
  const payload = await buildContextPayload(org_id);
  if (!payload) return [];

  payload.contextJson.control_unit_id = controlUnitId;

  const requestBody = {
    payload: JSON.stringify({
      schemaName: payload.schemaName,
      contextJson: {
        account_user_id: payload.contextJson.account_user_id,
        account_id: payload.contextJson.account_id,
        workspace_id: payload.contextJson.workspace_id,
        control_unit_id: payload.contextJson.control_unit_id,
      },
      inputParamsJson: {
        org_id: org_id,
      },
    }),
    schemaName: payload.schemaName,
  };

  const response = await ApiClient.post(
    LEADS_ENDPOINTS.GET_ORG_CONTACTS,
    requestBody,
    { headers: { "Content-Type": "application/json" } }
  );

  return response?.data?.data?.[0]?.contacts_json || [];
};

export const fetchAllActivityEvents = async (org_id: string, controlUnitId: string | null) => {
  const payload = await buildContextPayload(org_id);
  if (!payload) return [];

  payload.contextJson.control_unit_id = controlUnitId;

  const encryptedPayload = JSON.stringify({
    schemaName: payload.schemaName,
    contextJson: {
      account_id: payload.contextJson.account_id,
      workspace_id: payload.contextJson.workspace_id,
      control_unit_id: payload.contextJson.control_unit_id,
    },
    inputParamsJson: {
      org_id: org_id,
      activity_type: 'All'
    }
  });

  const res = await ApiClient.post(LEADS_ENDPOINTS.GET_ACTIVITY_BASED_ON_TYPE, {
    encryptedPayload,
    iv: 'dev-mode'
  });

  return res?.data?.data || [];
};

export const fetchAllEnquiries = async (org_id: string, controlUnitId: string | null, type: string) => {
  const payload = await buildContextPayload(org_id);
  if (!payload) return [];

  payload.contextJson.control_unit_id = controlUnitId;

  const inputPayload = {
    schemaName: payload?.schemaName,
    contextJson: JSON.stringify({
      account_id: payload?.contextJson.account_id,
      workspace_id: payload?.contextJson.workspace_id,
      control_unit_id: payload?.contextJson.control_unit_id,
      org_id: org_id,
      page_number: 1,
      limit: 5,
      search_text: '',
      enquiry_state_id: type,
    }),
    payloadJson: {
      enquiry_type: "ALL",
    },
  };

  const response = await ApiClient.post(
    LEADS_ENDPOINTS.GET_ALL_ENQUIRIES,
    inputPayload
  );

  return response.data.data[1] || [];
};