import React, { Children } from "react";
import { View, Platform, StyleSheet } from "react-native";
import { moderateScale, scale } from "react-native-size-matters";

export const  PrimraryCard = ({ children }:any) => {
  return (
    <View style={styles.card}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: "#fff", 
    paddingVertical: moderateScale(12),
    paddingHorizontal: moderateScale(16),
    marginVertical: moderateScale(12),
    marginHorizontal: moderateScale(12),
    borderWidth: moderateScale(1),
    borderColor: "#e2e8f0",
    borderRadius: moderateScale(12),
    // gap: scale(8),


    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,


    elevation: 4,

   
  },
  
});











