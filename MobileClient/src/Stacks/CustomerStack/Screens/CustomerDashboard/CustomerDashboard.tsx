import { ScrollView, StyleSheet,  View } from 'react-native'
import React from 'react'
import { Text } from 'react-native-paper';
import AppHeader from '../../../../Components/AppHeader'
import { TouchableOpacity } from 'react-native';
import { useState } from 'react';
import { AlertTriangle, ChevronDown, ChevronUp } from 'lucide-react-native';
const summaryCards = [
  { icon: 'cube-outline', label: 'Active shipment', value: 234, color: '#F9A825' },
  { icon: 'calendar', label: 'Pending Quotes', value: 23, color: '#F57C00' },
  { icon: 'dollar-sign', label: 'Expenditure', value: 234, color: '#8E24AA' },
  { icon: 'check-circle', label: 'Delivered', value: 23, color: '#43A047' },
];

const shipmentStatus = [
  {
    id: 'FF-2025-001',
    route: 'Dubai → Mumbai · Air Freight',
    status: 'Delivered',
    statusColor: '#43A047',
    eta: '27-02-2025',
    percent: 100,
  },
  {
    id: 'FF-2025-002',
    route: 'Shanghai, China → Los Angeles, USA',
    status: 'in transit',
    statusColor: '#1976D2',
    eta: '20-02-2025',
    percent: 65,
  },
  {
    id: 'FF-2025-003',
    route: 'Hamburg → New York',
    status: 'Customs',
    statusColor: '#FBC02D',
    eta: '18-02-2025',
    percent: 85,
  },
];

const recentActivity = [
  {
    icon: 'file-text',
    text: 'New quote received from Global Shipping Co.',
    time: '2 hours ago',
    color: '#1976D2',
  },
  {
    icon: 'check-circle',
    text: 'Shipment SH-2024-001 passed customs clearance',
    time: '4 hours ago',
    color: '#43A047',
  },
  {
    icon: 'alert-circle',
    text: 'Document upload required for SH-2024-001',
    time: '6 hours ago',
    color: '#F9A825',
  },
  {
    icon: 'dollar-sign',
    text: 'Invoice INV-2024-001 payment processed',
    time: '1 day ago',
    color: '#8E24AA',
  },
];

import { Package, Calendar, DollarSign, CheckCircle, AlertCircle, FileText } from 'lucide-react-native';
import { moderateScale } from 'react-native-size-matters';

const CustomerDashboard = () => {
  const [actionsCollapsed, setActionsCollapsed] = useState(false);
  const [statusCollapsed, setStatusCollapsed] = useState(false);
  const [recentCollapsed, setRecentCollapsed] = useState(false);
  return (
    <View style={{ flex: 1, backgroundColor: '#F6F7FB' }}>
      <AppHeader isLogoVisible={true} showDrawer={true} hideBackAction={false}/>
      <ScrollView bounces={false}>
        {/* Summary Cards */}
        <Text variant="semiBold" style={{ fontSize: moderateScale(18), marginLeft: 16 }}>
          Dashboard
        </Text>
        <View
          style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            justifyContent: 'space-between',
            margin: 16,
          }}
        >
          {summaryCards.map((card, idx) => {
            const Icon =
              card.icon === 'cube-outline'
                ? Package
                : card.icon === 'calendar'
                ? Calendar
                : card.icon === 'dollar-sign'
                ? DollarSign
                : card.icon === 'check-circle'
                ? CheckCircle
                : Package;
            return (
              <View
                key={card.label}
                style={{
                  width: '48%',
                  backgroundColor: '#fff',
                  borderRadius: 16,
                  padding: 16,
                  marginBottom: 12,
                  shadowColor: '#000',
                  shadowOpacity: 0.06,
                  shadowRadius: 8,
                  elevation: 2,
                  // alignItems:"center"
                }}
              >
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'flex-start',
                    // marginBottom: 8,
                    justifyContent: 'flex-start',
                  }}
                >
                  <Icon size={moderateScale(21)} color={card.color} style={{ marginRight: 8 }} />
                  <View
                    style={{ flexDirection: 'column', justifyContent: 'space-between', gap: 5 }}
                  >
                    <Text
                      variant="regular"
                      style={{ fontSize: moderateScale(13), color: '#64748B' }}
                    >
                      {card.label}
                    </Text>
                    <Text variant="semiBold" style={{ fontSize: moderateScale(24) }}>
                      {card.value}
                    </Text>
                  </View>
                </View>
              </View>
            );
          })}
        </View>

        {/* Actions Required */}
        <View
          style={{
            backgroundColor: '#fff',
            borderRadius: 16,
            marginHorizontal: 16,
            marginBottom: 16,
            shadowColor: '#000',
            shadowOpacity: 0.06,
            shadowRadius: 8,
            elevation: 2,
          }}
        >
          <TouchableOpacity
            style={{ flexDirection: 'row', alignItems: 'center', padding: 16 }}
            activeOpacity={0.7}
            onPress={() => setActionsCollapsed((prev) => !prev)}
          >
            <View
              style={{
                backgroundColor: '#EA580C',
                borderRadius: 99,
                padding: moderateScale(10),
                marginRight: 8,
              }}
            >
              <AlertTriangle size={18} color="#fff" />
            </View>
            <Text
              style={{ color: '#222', flex: 1, fontSize: moderateScale(16) }}
              variant="semiBold"
            >
              Actions Required
            </Text>
            {actionsCollapsed ? (
              <ChevronDown size={20} color="#222" />
            ) : (
              <ChevronUp size={20} color="#222" />
            )}
          </TouchableOpacity>
          {!actionsCollapsed && (
            <View
              style={{
                backgroundColor: '#FFF7ED',
                borderRadius: 12,
                marginHorizontal: 12,
                marginBottom: 12,
                padding: 12,
              }}
            >
              <Text variant="medium" style={{ color: '#222', marginBottom: 4 }}>
                Submit Commercial Invoice
              </Text>
              <Text variant="regular" style={{ color: '#888', marginBottom: 4 }}>
                Upload commercial invoice for shipment SH-2025-001
              </Text>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <Text variant="regular" style={{ color: '#EA580C' }}>
                  Due: 10-02-2025
                </Text>
                <TouchableOpacity
                  style={{
                    alignSelf: 'flex-end',
                    backgroundColor: '#fff',
                    borderRadius: 6,
                    paddingHorizontal: 16,
                    paddingVertical: 6,
                  }}
                >
                  <Text variant="regular">Complete</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>

        {/* Shipment Status */}
        <View
          style={{
            backgroundColor: '#fff',
            borderRadius: 16,
            marginHorizontal: 16,
            marginBottom: 16,
            shadowColor: '#000',
            shadowOpacity: 0.06,
            shadowRadius: 8,
            elevation: 2,
          }}
        >
          <TouchableOpacity
            style={{ flexDirection: 'row', alignItems: 'center', padding: 16 }}
            activeOpacity={0.7}
            onPress={() => setStatusCollapsed((prev) => !prev)}
          >
            <Text style={{ fontSize: moderateScale(16), flex: 1 }} variant="semiBold">
              Shipment Status
            </Text>
            {statusCollapsed ? (
              <ChevronDown size={20} color="#222" />
            ) : (
              <ChevronUp size={20} color="#222" />
            )}
          </TouchableOpacity>
          {!statusCollapsed &&
            shipmentStatus.map((item) => (
              <View
                key={item.id}
                style={{
                  marginHorizontal: 16,
                  marginBottom: 18,
                  backgroundColor: '#F9FAFB',
                  borderRadius: 12,
                  padding: 14,
                }}
              >
                {/* Top Row: ID & Status */}
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginBottom: 4,
                  }}
                >
                  <Text variant="medium">{item.id}</Text>
                  {/* Status Chip */}
                  <View
                    style={{
                      backgroundColor: item.statusColor + '22',
                      borderRadius: 8,
                      paddingHorizontal: 8,
                      paddingVertical: 2,
                    }}
                  >
                    <Text style={{ color: item.statusColor }} variant="medium">
                      {item.status === 'in transit' ? 'In Transit' : item.status}
                    </Text>
                  </View>
                </View>
                {/* Route */}
                <Text style={{ color: '#64748B', fontSize: 13, marginBottom: 2 }}>
                  {item.route}
                </Text>
                {/* ETA */}
                <Text style={{ color: '#888', fontSize: 13, marginBottom: 8 }}>
                  ETA: {item.eta}
                </Text>
                {/* Progress bar and % */}
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 7 }}>
                  <View style={{ flex: 1 }}>
                    <View
                      style={{
                        height: 6,
                        backgroundColor: '#E0E0E0',
                        borderRadius: 3,
                        overflow: 'hidden',
                      }}
                    >
                      <View
                        style={{
                          width: `${item.percent}%`,
                          height: 6,
                          backgroundColor: item.statusColor,
                          borderRadius: 3,
                        }}
                      />
                    </View>
                  </View>
                  <Text
                    style={{
                      color: item.statusColor,
                      fontWeight: 'bold',
                      fontSize: 13,
                    }}
                  >
                    {item.percent}%
                  </Text>
                </View>
              </View>
            ))}
        </View>

        {/* Recent Activity */}
        <View
          style={{
            backgroundColor: '#fff',
            borderRadius: 16,
            marginHorizontal: 16,
            marginBottom: 16,
            shadowColor: '#000',
            shadowOpacity: 0.06,
            shadowRadius: 8,
            elevation: 2,
          }}
        >
          {/* Collapse Toggle Header */}
          <TouchableOpacity
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              padding: 16,
            }}
            activeOpacity={0.7}
            onPress={() => setRecentCollapsed((prev) => !prev)}
          >
            <View style={{ flex: 1 }}>
              <Text style={{ fontWeight: 'bold', fontSize: 16, color: '#222' }} variant="semiBold">
                Recent Activity
              </Text>
              <Text style={{ color: '#888', fontSize: 13, marginTop: 3 }}>
                Latest updates on your shipments and quotes
              </Text>
            </View>
            {recentCollapsed ? (
              <ChevronDown size={20} color="#222" />
            ) : (
              <ChevronUp size={20} color="#222" />
            )}
          </TouchableOpacity>

          {/* Activity List */}
          {!recentCollapsed &&
            recentActivity.map((item, idx) => {
              const Icon =
                item.icon === 'file-text'
                  ? FileText
                  : item.icon === 'check-circle'
                  ? CheckCircle
                  : item.icon === 'alert-circle'
                  ? AlertCircle
                  : item.icon === 'dollar-sign'
                  ? DollarSign
                  : FileText;

              return (
                <View
                  key={idx}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingHorizontal: 18,
                    paddingVertical: 12,
                    borderTopWidth: idx === 0 ? 1 : 0,
                    borderBottomWidth: idx < recentActivity.length - 1 ? 1 : 0,
                    borderColor: '#F2F3F6',
                  }}
                >
                  <View
                    style={{
                      backgroundColor: item.color + '22',
                      borderRadius: 99,
                      padding: 10,
                      marginRight: 10,
                    }}
                  >
                    <Icon size={17} color={item.color} />
                  </View>
                  <View style={{ flex: 1 }}>
                    <Text style={{ color: '#222', fontSize: 14 }}>{item.text}</Text>
                    <Text style={{ color: '#888', fontSize: 12 }}>{item.time}</Text>
                  </View>
                </View>
              );
            })}
        </View>
      </ScrollView>
    </View>
  );
}

export default CustomerDashboard

const styles = StyleSheet.create({})