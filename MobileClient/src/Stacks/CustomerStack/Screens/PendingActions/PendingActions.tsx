import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import React, { useState, useMemo } from 'react';
import AppHeader from '../../../../Components/AppHeader';
import { FileText, DollarSign, AlertCircle, Package, Calendar, Receipt } from 'lucide-react-native';

const PendingActions = () => {
  const [activeTab, setActiveTab] = useState('All Priority');

  // Sample pending actions data
  const pendingActions = [
    {
      id: 1,
      title: 'Quote Response Required',
      description: 'Electronics Shipment - Shanghai → Los Angeles',
      priority: 'High',
      dueDate: 'Dec 30, 2024',
      amount: '$2,850',
      icon: 'quote',
      hasDate: true,
      hasAmount: true,
    },
    {
      id: 2,
      title: 'Payment Due',
      description: 'Textile Goods Invoice',
      priority: 'High',
      dueDate: 'Dec 30, 2024',
      amount: '$1,950',
      icon: 'payment',
      hasDate: true,
      hasAmount: true,
    },
    {
      id: 3,
      title: 'Customs Clearance Action Required',
      description: 'Additional documentation needed',
      priority: 'High',
      dueDate: null,
      amount: null,
      icon: 'customs',
      hasDate: false,
      hasAmount: false,
    },
    {
      id: 4,
      title: 'POD Submission Required',
      description: 'Consumer Goods - Delivered Dec 22, 2024',
      priority: 'Medium',
      dueDate: null,
      amount: null,
      icon: 'pod',
      hasDate: false,
      hasAmount: false,
    },
    {
      id: 5,
      title: 'Booking Confirmation Pending',
      description: 'Machinery Transport Quote Accepted',
      priority: 'Medium',
      dueDate: null,
      amount: '$4,200',
      icon: 'booking',
      hasDate: false,
      hasAmount: true,
    },
    {
      id: 6,
      title: 'Commercial Invoice Required',
      description: 'Auto Parts Shipment Documentation',
      priority: 'Medium',
      dueDate: 'Dec 28, 2024',
      amount: null,
      icon: 'invoice',
      hasDate: true,
      hasAmount: false,
    },
    {
      id: 7,
      title: 'POD Submission Required',
      description: 'Consumer Goods - Delivered Dec 22, 2024',
      priority: 'Low',
      dueDate: null,
      amount: null,
      icon: 'pod',
      hasDate: false,
      hasAmount: false,
    },
    {
      id: 8,
      title: 'Booking Confirmation Pending',
      description: 'Machinery Transport Quote Accepted',
      priority: 'Low',
      dueDate: null,
      amount: '$4,200',
      icon: 'booking',
      hasDate: false,
      hasAmount: true,
    },
    {
      id: 9,
      title: 'Commercial Invoice Required',
      description: 'Auto Parts Shipment Documentation',
      priority: 'Low',
      dueDate: 'Dec 28, 2024',
      amount: null,
      icon: 'invoice',
      hasDate: true,
      hasAmount: false,
    },
  ];

  const priorityTabs = ['All Priority', 'High', 'Medium', 'Low'];

  // Filter actions based on active tab
  const filteredActions = useMemo(() => {
    if (activeTab === 'All Priority') {
      return pendingActions;
    }
    return pendingActions.filter((action) => action.priority === activeTab);
  }, [activeTab, pendingActions]);

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'High':
        return '#ff4444';
      case 'Medium':
        return '#ff9800';
      case 'Low':
        return '#4caf50';
      default:
        return '#666';
    }
  };

  const getPriorityBgColor = (priority) => {
    switch (priority) {
      case 'High':
        return '#ffebee';
      case 'Medium':
        return '#fff3e0';
      case 'Low':
        return '#e8f5e8';
      default:
        return '#f5f5f5';
    }
  };

  const getIconComponent = (iconType) => {
    switch (iconType) {
      case 'quote':
        return FileText;
      case 'payment':
        return DollarSign;
      case 'customs':
        return AlertCircle;
      case 'pod':
        return Package;
      case 'booking':
        return Calendar;
      case 'invoice':
        return Receipt;
      default:
        return FileText;
    }
  };

  const getIconBgColor = (priority) => {
    switch (priority) {
      case 'High':
        return '#ff4444';
      case 'Medium':
        return '#ff9800';
      case 'Low':
        return '#4caf50';
      default:
        return '#666';
    }
  };

  const handleActionPress = (action) => {
    console.log('Action pressed:', action.title);
    // Add navigation or action logic here
  };

  const TabButton = ({ title, isActive, onPress }) => (
    <TouchableOpacity
      style={[styles.tabButton, isActive && styles.activeTabButton]}
      onPress={onPress}
    >
      <Text style={[styles.tabText, isActive && styles.activeTabText]}>{title}</Text>
    </TouchableOpacity>
  );

  const ActionCard = ({ item }) => {
    const IconComponent = getIconComponent(item.icon);

    return (
      <TouchableWithoutFeedback onPress={() => handleActionPress(item)}>
        <View style={styles.actionCard}>
          {/* Icon and Title Section */}
          <View style={styles.cardHeader}>
            <View
              style={[styles.iconContainer, { backgroundColor: getIconBgColor(item.priority) }]}
            >
              <IconComponent size={20} color="#fff" />
            </View>
            <View style={styles.titleSection}>
              <Text style={styles.actionTitle}>{item.title}</Text>
              <Text style={styles.actionDescription}>{item.description}</Text>
            </View>
          </View>

          {/* Priority, Date, and Amount Section */}
          <View style={styles.cardFooter}>
            <View style={styles.leftSection}>
              <View
                style={[
                  styles.priorityBadge,
                  { backgroundColor: getPriorityBgColor(item.priority) },
                ]}
              >
                <Text style={[styles.priorityText, { color: getPriorityColor(item.priority) }]}>
                  {item.priority}
                </Text>
              </View>
              {item.hasDate && (
                <View style={styles.dateContainer}>
                  <Calendar size={14} color="#666" />
                  <Text style={styles.dateText}>Due: {item.dueDate}</Text>
                </View>
              )}
            </View>
            {item.hasAmount && <Text style={styles.amountText}>{item.amount}</Text>}
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  };

  return (
    <View style={styles.container}>
      <AppHeader title="Pending Actions" showDrawer={true} />

      {/* Priority Filter Tabs */}
      <View style={styles.tabContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.tabScrollContent}
          bounces={false}
        >
          {priorityTabs.map((tab) => (
            <TabButton
              key={tab}
              title={tab}
              isActive={activeTab === tab}
              onPress={() => setActiveTab(tab)}
            />
          ))}
        </ScrollView>
      </View>

      {/* Actions List */}
      <ScrollView
        style={styles.actionsContainer}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        bounces={false}
      >
        {filteredActions.length > 0 ? (
          filteredActions.map((item) => <ActionCard key={item.id} item={item} />)
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              No {activeTab.toLowerCase()} priority actions found
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

export default PendingActions;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  tabContainer: {
    backgroundColor: '#fff',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  tabScrollContent: {
    paddingHorizontal: 16,
    gap: 8,
  },
  tabButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f5f5f5',
    minWidth: 80,
    alignItems: 'center',
  },
  activeTabButton: {
    backgroundColor: '#3377FF',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  activeTabText: {
    color: '#fff',
    fontWeight: '600',
  },
  actionsContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  actionCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: '#f0f0f0',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  titleSection: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#191f2b',
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: 14,
    color: '#666',
    fontWeight: '400',
    lineHeight: 20,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  leftSection: {
    flex: 1,
  },
  priorityBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 6,
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '600',
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  dateText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  amountText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#27c087',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 60,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});
