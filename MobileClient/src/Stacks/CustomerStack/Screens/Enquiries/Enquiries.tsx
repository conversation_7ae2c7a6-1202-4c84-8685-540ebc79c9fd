import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  TouchableWithoutFeedback,
} from 'react-native';
import React, { useState } from 'react';
import AppHeader from '../../../../Components/AppHeader';
import { useTheme, Badge } from 'react-native-paper';
import SearchBar from '../../../../Components/SearchInput';
import { Copy, Funnel, MoreVertical } from 'lucide-react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';

const Enquiries = () => {
  const theme = useTheme();
  const count = 3;
  const [searchValue, setSearchValue] = useState('');

  // Sample enquiries data based on your image
  const enquiries = [
    {
      id: '162/VZKSAE25',
      type: 'Export',
      from: 'AE Dubai, UAE',
      to: 'SA Jeddah, Saudi Arabia',
      transport: 'LCL',
      cargoType: 'Hazardous',
      incoterm: 'FCA',
      readyToLoad: '16-07-2025',
      transit: '10 days',
    },
    {
      id: '161/VZKSAE25',
      type: 'Import',
      from: 'US New York, USA',
      to: 'AE Dubai, UAE',
      transport: 'Bulk',
      cargoType: 'General',
      incoterm: 'FOB',
      readyToLoad: '18-07-2025',
      transit: '12 days',
    },
    {
      id: '160/VZKSAE25',
      type: 'Crosstrade',
      from: 'AE Dubai, UAE',
      to: 'AE Abu Dhabi, UAE',
      transport: 'FCL',
      cargoType: 'Perishable',
      incoterm: 'EXW',
      readyToLoad: '20-07-2025',
      transit: '2 days',
    },
    {
      id: '156/VZKSAE25',
      type: 'Export',
      from: 'US Los Angeles, USA',
      to: 'AE Dubai, UAE',
      transport: 'FCL',
      cargoType: 'Electronics',
      incoterm: 'FOB',
      readyToLoad: '22-07-2025',
      transit: '15 days',
    },
  ];

  const getTypeColor = (type) => {
    switch (type) {
      case 'Export':
        return '#27c087';
      case 'Import':
        return '#47b6e6';
      case 'Crosstrade':
        return '#aeaaff';
      default:
        return '#666';
    }
  };

  const getTypeBgColor = (type) => {
    switch (type) {
      case 'Export':
        return '#e8f5f0';
      case 'Import':
        return '#e3f2fd';
      case 'Crosstrade':
        return '#f3f0ff';
      default:
        return '#f5f5f5';
    }
  };

  const handleEnquiryPress = (enquiry) => {
    console.log('Enquiry pressed:', enquiry.id);
    // Add navigation logic here
    // navigation.navigate('EnquiryDetails', { enquiry });
  };

  const handleFilterPress = () => {
    console.log('Filter pressed');
    // Add filter logic here
  };

  const DetailRow = ({ label, value }) => (
    <View style={styles.detailRow}>
      <Text style={[styles.detailLabel, { color: theme.colors.onSurfaceVariant }]}>{label}</Text>
      <Text style={[styles.detailValue, { color: theme.colors.onSurface }]}>{value}</Text>
    </View>
  );

  const EnquiryCard = ({ item }) => (
    <TouchableWithoutFeedback onPress={() => handleEnquiryPress(item)}>
      <View style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        {/* Card Header */}
        <View style={styles.cardHeader}>
          <View style={styles.headerLeft}>
            <Text style={[styles.enquiryLabel, { color: theme.colors.onSurfaceVariant }]}>
              Enquire ID:
            </Text>
            <Text style={[styles.enquiryId, { color: theme.colors.onSurface }]}>{item.id}</Text>
          </View>
          <View style={[styles.typeBadge, { backgroundColor: getTypeBgColor(item.type) }]}>
            <Text style={[styles.typeBadgeText, { color: getTypeColor(item.type) }]}>
              {item.type}
            </Text>
          </View>
          <TouchableOpacity style={styles.moreverticalIcon}>
            <Copy size={16}  />

          </TouchableOpacity>
        </View>

        {/* Route Information */}
        <View style={styles.routeSection}>
          <DetailRow label="From:" value={item.from} />
          <DetailRow label="To:" value={item.to} />
        </View>

        {/* Transport and Cargo Information */}
        <View style={styles.infoSection}>
          <DetailRow label="Transport:" value={item.transport} />
          <DetailRow label="Cargo Type:" value={item.cargoType} />
        </View>

        {/* Incoterm and Ready to Load */}
        <View style={styles.infoSection}>
          <DetailRow label="Incoterm:" value={item.incoterm} />
          <DetailRow label="Ready to Load:" value={item.readyToLoad} />
        </View>

        {/* Transit Time */}
        <View style={styles.transitSection}>
          <Text style={[styles.detailLabel, { color: theme.colors.onSurfaceVariant }]}>
            Transit:
          </Text>
          <Text style={[styles.detailValue, { color: theme.colors.onSurface }]}>
            {item.transit}
          </Text>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <AppHeader title="My Enquiries" showDrawer={true} />

      {/* Search and Filter Header */}
      <View style={[styles.headerRow, { backgroundColor: theme.colors.background }]}>
        <SearchBar
          placeholder="Search enquiries..."
          onChangeText={setSearchValue}
          value={searchValue}
          style={{ flex: 1 }}
        />
        <TouchableOpacity
          style={[
            styles.filterIconWrap,
            {
              borderColor: count > 0 && count < 3 ? theme.colors.primary : '#E0E0E0',
              backgroundColor: theme.colors.surface,
            },
          ]}
          activeOpacity={0.7}
          onPress={handleFilterPress}
        >
          {count > 0 && count < 3 && (
            <Badge
              size={moderateScale(15)}
              style={{
                position: 'absolute',
                top: -moderateScale(8),
                right: -moderateScale(8),
                backgroundColor: theme.colors.primary,
                zIndex: 2,
              }}
            >
              {count}
            </Badge>
          )}
          <Funnel
            size={moderateScale(20)}
            color={
              count > 0 && count < 3
                ? theme.colors.primary
                : theme.colors.onSurfaceVariant || '#888'
            }
          />
        </TouchableOpacity>
      </View>

      {/* Enquiries List */}
      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        bounces={false}
      >
        {enquiries.map((item) => (
          <EnquiryCard key={item.id} item={item} />
        ))}
      </ScrollView>
    </View>
  );
};

export default Enquiries;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  moreverticalIcon:{
    padding: 8,
   
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    gap: moderateScale(8),
    marginHorizontal: 16,
    marginBottom: verticalScale(5),
  },
  filterIconWrap: {
    height: verticalScale(32),
    width: verticalScale(32),
    borderRadius: 5,
    backgroundColor: '#FFF',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 1,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: '#f0f0f0',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  headerLeft: {
    flex: 1,
  },
  enquiryLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  enquiryId: {
    fontSize: 16,
    fontWeight: '700',
  },
  typeBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  typeBadgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  routeSection: {
    marginBottom: 12,
  },
  infoSection: {
    marginBottom: 12,
  },
  transitSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'right',
    flex: 1,
  },
});
