import { StyleSheet, View, ScrollView, TouchableWithoutFeedback } from 'react-native';
import { Text } from 'react-native-paper';
import React, { useState, useMemo } from 'react';
import AppHeader from '../../../../Components/AppHeader';
import Tabs from '../../../../Components/Tabs';

const QuotesAndBooking = ({route,navigation}) => {
  const [activeTab, setActiveTab] = useState('Active');

  // Sample quotes data
  const quotesData = [
    {
      id: 'QT-2025-001',
      type: 'Import',
      transportMode: 'LCL',
      from: 'Dubai, UAE',
      to: 'Jeddah, KSA',
      cargoType: 'Hazardous',
      readyToLoad: '25-7-2025',
      status: 'Pending',
      isActive: true,
      isBooked: false,
      isExpired: false,
    },
    {
      id: 'QT-2025-002',
      type: 'Export',
      transportMode: 'FCL',
      from: 'New York, USA',
      to: 'Dubai, UAE',
      cargoType: 'General',
      readyToLoad: '28-7-2025',
      status: 'Approved',
      isActive: true,
      isBooked: false,
      isExpired: false,
    },
    {
      id: 'QT-2025-003',
      type: 'Export',
      transportMode: 'LCL',
      from: 'Mumbai, India',
      to: 'Hamburg, Germany',
      cargoType: 'Perishable',
      readyToLoad: '05-8-2025',
      status: 'Sent',
      isActive: true,
      isBooked: false,
      isExpired: false,
    },
  ];

  // Filter quotes based on active tab
  const filteredQuotes = useMemo(() => {
    switch (activeTab) {
      case 'Active':
        return quotesData.filter((quote) => quote.isActive);
      case 'Booked':
        return quotesData.filter((quote) => quote.isBooked);
      case 'Expired':
        return quotesData.filter((quote) => quote.isExpired);
      default:
        return quotesData;
    }
  }, [activeTab, quotesData]);

  // Calculate counts for each tab
  const tabCounts = useMemo(
    () => ({
      Active: quotesData.filter((quote) => quote.isActive).length,
      Booked: quotesData.filter((quote) => quote.isBooked).length,
      Expired: quotesData.filter((quote) => quote.isExpired).length,
    }),
    [quotesData]
  );

  const tabs = [
    { key: 'Active', label: 'Active', count: tabCounts.Active },
    { key: 'Booked', label: 'Booked', count: tabCounts.Booked },
    { key: 'Expired', label: 'Expired', count: tabCounts.Expired },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'Pending':
        return '#ff9800';
      case 'Approved':
        return '#27c087';
      case 'Sent':
        return '#3377FF';
      default:
        return '#666';
    }
  };

  const getStatusBgColor = (status) => {
    switch (status) {
      case 'Pending':
        return '#fff3e0';
      case 'Approved':
        return '#e8f5f0';
      case 'Sent':
        return '#e3f2fd';
      default:
        return '#f5f5f5';
    }
  };

  const getTypeColor = (type) => {
    return type === 'Import' ? '#27c087' : '#3377FF';
  };

  const getTypeBgColor = (type) => {
    return type === 'Import' ? '#e8f5f0' : '#e3f2fd';
  };

  const handleQuotePress = (quote) => {
    console.log('Quote pressed:', quote.id);
    // Navigate to quote details screen
    if (navigation) {
      navigation.navigate('QuoteDetails', { quote });
    }
    // For testing without navigation:
    // You can add modal or other logic here
  };

  const QuoteCard = ({ item }) => (
    <TouchableWithoutFeedback onPress={() => handleQuotePress(item)}>
      <View style={styles.card}>
        {/* Card Header */}
        <View style={styles.cardHeader}>
          <View style={styles.headerLeft}>
            <Text style={styles.quoteId}>{item.id}</Text>
            <View style={styles.badgeContainer}>
              <View style={[styles.typeBadge, { backgroundColor: getTypeBgColor(item.type) }]}>
                <Text style={[styles.typeBadgeText, { color: getTypeColor(item.type) }]}>
                  {item.type}
                </Text>
              </View>
              <View style={[styles.transportBadge, { backgroundColor: getTypeBgColor(item.type) }]}>
                <Text style={[styles.transportBadgeText, { color: getTypeColor(item.type) }]}>
                  {item.transportMode}
                </Text>
              </View>
            </View>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: getStatusBgColor(item.status) }]}>
            <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
              {item.status}
            </Text>
          </View>
        </View>

        {/* Route Information */}
        <View style={styles.routeContainer}>
          <View style={styles.routeItem}>
            <Text style={styles.routeLabel}>From:</Text>
            <Text style={styles.routeValue}>{item.from}</Text>
          </View>
          <View style={styles.routeItem}>
            <Text style={styles.routeLabel}>To:</Text>
            <Text style={styles.routeValue}>{item.to}</Text>
          </View>
        </View>

        {/* Additional Information */}
        <View style={styles.additionalInfo}>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Cargo Type</Text>
            <Text style={styles.infoValue}>{item.cargoType}</Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Ready to Load</Text>
            <Text style={styles.infoValue}>{item.readyToLoad}</Text>
          </View>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );

  return (
    <View style={styles.container}>
      <AppHeader title="My Quotes" showDrawer={true} />

      {/* Tabs */}
      <Tabs tabs={tabs} activeTab={activeTab} onTabPress={setActiveTab} showCounts={true} />

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false} bounces={false}>
        {filteredQuotes.length > 0 ? (
          filteredQuotes.map((item) => <QuoteCard key={item.id} item={item} />)
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No {activeTab.toLowerCase()} quotes found</Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

export default QuotesAndBooking;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 18,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: '#f0f0f0',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  headerLeft: {
    flex: 1,
  },
  quoteId: {
    fontSize: 16,
    fontWeight: '700',
    color: '#191f2b',
    marginBottom: 8,
  },
  badgeContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  typeBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  typeBadgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  transportBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  transportBadgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  routeContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  routeItem: {
    flex: 1,
  },
  routeLabel: {
    fontSize: 12,
    color: '#a0a0ab',
    fontWeight: '500',
    marginBottom: 4,
  },
  routeValue: {
    fontSize: 14,
    color: '#191f2b',
    fontWeight: '600',
  },
  additionalInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  infoItem: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    color: '#a0a0ab',
    fontWeight: '500',
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 14,
    color: '#191f2b',
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 60,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});
