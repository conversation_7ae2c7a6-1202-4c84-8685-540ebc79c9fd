  const renderWeekView = () => {
    // Filter the real calendar data for the selected date's week
    const weekStart = moment(selectedDate).startOf('week');
    const weekEnd = moment(selectedDate).endOf('week');
    
    const weekEvents = calendarData.filter(event => {
      const eventDate = moment(event.start);
      return eventDate.isBetween(weekStart, weekEnd, 'day', '[]');
    });

    return (
      <View style={styles.weekViewContainer}>
        {/* Week header */}
        <View style={styles.weekHeader}>
          {Array.from({ length: 7 }).map((_, index) => {
            const day = moment(weekStart).add(index, 'days');
            const isToday = day.isSame(moment(), 'day');
            const isSelected = day.isSame(selectedDate, 'day');
            
            return (
              <TouchableOpacity
                key={index}
                style={[
                  styles.weekDay,
                  isSelected && styles.selectedWeekDay
                ]}
                onPress={() => setSelectedDate(day.toDate())}
              >
                <Text style={styles.weekDayName}>{day.format('ddd')}</Text>
                <View style={[
                  styles.weekDayNumber,
                  isToday && styles.todayCircle,
                  isSelected && styles.selectedDayCircle
                ]}>
                  <Text style={[
                    styles.weekDayNumberText,
                    isToday && styles.todayText,
                    isSelected && styles.selectedDayText
                  ]}>
                    {day.format('D')}
                  </Text>
                </View>
              </TouchableOpacity>
            );
          })}
        </View>

        {/* Events for the week */}
        <ScrollView style={styles.weekEventsContainer} bounces={false}>
          {weekEvents.length > 0 ? (
            weekEvents.map((event, index) => (
              <TouchableOpacity
                key={index}
                style={styles.eventItem}
                onPress={() => handleEventPress(event)}
              >
                <View style={[styles.eventColorIndicator, { backgroundColor: event.color || '#4285F4' }]} />
                <View style={styles.eventContent}>
                  <Text style={styles.eventTitle}>{event.title}</Text>
                  <Text style={styles.eventTime}>
                    {moment(event.start).format('h:mm A')} - {moment(event.end).format('h:mm A')}
                  </Text>
                  {event.location && (
                    <Text style={styles.eventLocation}>{event.location}</Text>
                  )}
                </View>
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.noEventsContainer}>
              <Text style={styles.noEventsText}>No events for this week</Text>
            </View>
          )}
        </ScrollView>
      </View>
    );
  };