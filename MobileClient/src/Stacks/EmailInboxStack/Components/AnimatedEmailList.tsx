import React, { useMemo } from 'react';
import { 
  FlatList, 
  RefreshControl, 
  StyleSheet, 
  View, 
  Animated,
  Dimensions
} from 'react-native';
import { ActivityIndicator, Text, useTheme } from 'react-native-paper';
import { EmailListProps } from '../Types/EmailTypes';
import EmailItem from './EmailItem';
import { useEmailListAnimation } from '../Hooks/useEmailListAnimation';

const { width } = Dimensions.get('window');

const AnimatedEmailList: React.FC<EmailListProps> = ({
  emails,
  loading,
  onRefresh,
  onEmailPress,
  loadMore
}) => {
  const theme = useTheme();
  
  // Memoize emails to prevent unnecessary re-renders
  const memoizedEmails = useMemo(() => emails, [emails]);
  
  // Get animation styles
  const { getItemAnimationStyle, loadingAnimationStyle } = useEmailListAnimation(
    loading,
    memoizedEmails.length
  );
  
  // Empty state component
  const renderEmptyComponent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <Animated.View style={[styles.loadingIndicator, loadingAnimationStyle]}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
          </Animated.View>
          <Text style={[styles.loadingText, { color: theme.colors.onSurfaceVariant }]}>
            Loading emails...
          </Text>
        </View>
      );
    }
    
    return (
      <View style={styles.emptyContainer}>
        <Text style={[styles.emptyText, { color: theme.colors.onSurface }]}>
          No emails found
        </Text>
        <Text style={[styles.emptySubtext, { color: theme.colors.onSurfaceVariant }]}>
          Your inbox is empty. New emails will appear here.
        </Text>
      </View>
    );
  };
  
  // Loading footer component for pagination
  const renderFooter = () => {
    if (!loading || memoizedEmails.length === 0) return null;
    
    return (
      <View style={styles.footerContainer}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
      </View>
    );
  };
  
  // Render email item with animation
  const renderItem = ({ item, index }: { item: any; index: number }) => {
    return (
      <Animated.View >
        <EmailItem email={item} onPress={onEmailPress} />
      </Animated.View>
    );
  };
  
  return (
    <FlatList
      data={memoizedEmails}
      keyExtractor={(item) => item.email_date_message_id}
      renderItem={renderItem}
      bounces={false}
      contentContainerStyle={[
        styles.listContent,
        memoizedEmails.length === 0 && styles.emptyListContent,
      ]}
      refreshControl={
        <RefreshControl
          refreshing={loading}
          onRefresh={onRefresh}
          colors={[theme.colors.primary]}
          tintColor={theme.colors.primary}
        />
      }
      onEndReached = {() => loadMore && loadMore()}
      onEndReachedThreshold={0.1}
      ListEmptyComponent={renderEmptyComponent}
      ListFooterComponent={renderFooter}
      showsVerticalScrollIndicator={false}
      initialNumToRender={10}
      maxToRenderPerBatch={5}
      windowSize={10}
      removeClippedSubviews
    />
  );
};

const styles = StyleSheet.create({
  listContent: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingBottom : 40
  },
  emptyListContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  footerContainer: {
    paddingVertical: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingIndicator: {
    marginBottom: 16,
  },
  loadingText: {
    fontSize: 16,
    textAlign: 'center',
  },
});

export default AnimatedEmailList;
