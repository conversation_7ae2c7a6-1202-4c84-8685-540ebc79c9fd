import React, { useMemo } from 'react';
import { FlatList, RefreshControl, StyleSheet, View } from 'react-native';
import { ActivityIndicator, Text, useTheme } from 'react-native-paper';
import { EmailListProps } from '../Types/EmailTypes';
import EmailItem from './EmailItem';

const EmailList: React.FC<EmailListProps> = ({
  emails,
  loading,
  onRefresh,
  onEmailPress,
}) => {
  const theme = useTheme();
  
  // Memoize emails to prevent unnecessary re-renders
  const memoizedEmails = useMemo(() => emails, [emails]);
  
  // Empty state component
  const renderEmptyComponent = () => {
    if (loading) return null;
    
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No emails found</Text>
        <Text style={styles.emptySubtext}>
          Your inbox is empty. New emails will appear here.
        </Text>
      </View>
    );
  };
  
  // Loading footer component for pagination
  const renderFooter = () => {
    if (!loading) return null;
    
    return (
      <View style={styles.footerContainer}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
      </View>
    );
  };
  
  return (
    <FlatList
      data={memoizedEmails}
      keyExtractor={(item) => item.email_date_message_id}
      bounces={false}
      renderItem={({ item }) => (
        <EmailItem email={item} onPress={onEmailPress} />
      )}
      contentContainerStyle={[
        styles.listContent,
        memoizedEmails.length === 0 && styles.emptyListContent,
      ]}
      refreshControl={
        <RefreshControl
          refreshing={loading}
          onRefresh={onRefresh}
          colors={[theme.colors.primary]}
          tintColor={theme.colors.primary}
        />
      }
      ListEmptyComponent={renderEmptyComponent}
      ListFooterComponent={renderFooter}
      showsVerticalScrollIndicator={false}
      initialNumToRender={10}
      maxToRenderPerBatch={5}
      windowSize={10}
      removeClippedSubviews
    />
  );
};

const styles = StyleSheet.create({
  listContent: {
    flexGrow: 1,
  },
  emptyListContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#757575',
    textAlign: 'center',
  },
  footerContainer: {
    paddingVertical: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default EmailList;
