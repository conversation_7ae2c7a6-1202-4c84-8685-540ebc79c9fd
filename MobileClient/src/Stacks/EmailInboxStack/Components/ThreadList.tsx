import React, {useState, useCallback, useEffect, useMemo} from 'react';
import {View, StyleSheet, FlatList, TouchableOpacity,ScrollView} from 'react-native';
import {
  Text,
  useTheme,
  ActivityIndicator,
  Surface,
  Divider,
  Avatar,
  IconButton,
  Card,
  Chip,
  Menu,
} from 'react-native-paper';
import {EmailMessage, EmailAttachment} from '../Types/EmailTypes';
import {
  sortThreadByDate,
  formatFullEmailDateTime,
  getAttachmentCount,
  getAttachmentIcon,
} from '../Utils/ThreadUtils';
import {getInitials} from '../Utils/EmailFormatter';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import HtmlMessageContent from './HtmlMessageContent';
import { moderateScale } from 'react-native-size-matters';

interface ThreadListProps {
  messages: EmailMessage[];
  loading: boolean;
  onReply: (message: EmailMessage) => void;
  onReplyAll?: (message: EmailMessage) => void;
  onForward: (message: EmailMessage) => void;
  onAttachmentPress: (attachment: EmailAttachment) => void;
}

const ThreadList: React.FC<ThreadListProps> = ({
  messages,
  loading,
  onReply,
  onReplyAll,
  onForward,
  onAttachmentPress,
}) => {
  const theme = useTheme();
  const styles = React.useMemo(() => createStyles(theme), [theme]);
  const [sortedMessages, setSortedMessages] = useState<EmailMessage[]>([]);
  const [expandedMessageIds, setExpandedMessageIds] = useState<Set<string>>(
    new Set(),
  );
  // console.log("messages in thread", messages)
  // Sort messages by date (newest first)
  useEffect(() => {
    if (messages && messages.length > 0) {
      const sorted = sortThreadByDate(messages);
      setSortedMessages(sorted);
    }
  }, [messages]);

  // Get the subject from the first message
  const emailSubject = useMemo(() => {
    if (sortedMessages.length > 0) {
      return sortedMessages[0].email_subject;
    }
    return '';
  }, [sortedMessages]);

  // Handle toggle expand
  const handleToggleExpand = useCallback((messageId: string) => {
    setExpandedMessageIds(prevIds => {
      const newIds = new Set(prevIds);
      if (newIds.has(messageId)) {
        newIds.delete(messageId);
      } else {
        newIds.add(messageId);
      }
      return newIds;
    });
  }, []);

  // Render empty state
  const renderEmptyState = () => {
    if (loading) {
      return (
        <View style={[styles.emptyContainer, {padding: 16}]}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text
            style={[
              styles.emptyText,
              {
                color: theme.colors.onSurfaceVariant,
                marginTop: 16,
              },
            ]}>
            Loading thread messages...
          </Text>
        </View>
      );
    }

    return (
      <View style={[styles.emptyContainer, {padding: 16}]}>
        <Text
          style={[styles.emptyText, {color: theme.colors.onSurfaceVariant}]}>
          No messages in this thread
        </Text>
      </View>
    );
  };

  // Render email subject header
  const renderSubjectHeader = () => {
    if (!emailSubject) return null;

    return (
      <Surface style={styles.subjectHeader} elevation={0}>
        <View style={styles.subjectContainer}>
          <Text style={styles.subjectText} numberOfLines={5}>
            {emailSubject}
          </Text>
          <IconButton
            icon="star-outline"
            // iconColor={theme.colors.onSurface}
            size={22}
            // onPress={onReply}
            style={styles.actionButton}
          />
        </View>

        <Text style={styles.threadCount}>
          {sortedMessages.length}{' '}
          {sortedMessages.length === 1 ? 'message' : 'messages'}
        </Text>
      </Surface>
    );
  };

  // Render thread item
  const ThreadItem = ({item, index}: {item: EmailMessage; index: number}) => {
    // console.log('setSortedMessages', item?.cc_address?.length);
    const isExpanded = expandedMessageIds.has(item.message_id);
    const attachmentCount = getAttachmentCount(item);
    const [menuVisible, setMenuVisible] = useState(false);

    const openMenu = () => setMenuVisible(true);
    const closeMenu = () => setMenuVisible(false);

    const handleMenuAction = (action: () => void) => {
      closeMenu();
      action();
    };

    return (
      <Card style={[styles.emailCard, { borderColor: theme.colors.outlineVariant }]} elevation={0}>
        {/* Email Header - Always visible */}
        <TouchableOpacity
          onPress={() => handleToggleExpand(item.message_id)}
          style={styles.emailHeader}
        >
          <Avatar.Text
            size={40}
            label={getInitials(item.sender_name)}
            style={[styles.avatar, { backgroundColor: theme.colors.primary }]}
          />
          <View style={styles.headerContent}>
            <View style={styles.headerTopRow}>
              <Text style={styles.senderName}>{item.sender_name}</Text>
              <Text style={styles.dateText}>{formatFullEmailDateTime(item.email_date)}</Text>
            </View>
            <View style={styles.headerBottomRow}>
              <View style={{ flexDirection: 'column', flex: 1 }}>
                <Text style={styles.recipientText} numberOfLines={1}>
                  To: {item.to_address?.map((addr) => addr.name || addr.email).join(', ')}
                </Text>
                {(isExpanded || index === 0) && item?.cc_address?.length > 0 ? (
                  <View style={{ flexDirection: 'column' }}>
                    <Text style={styles.recipientText}>Cc: </Text>
                    {item.cc_address.map((addr, idx) => (
                      <Text key={idx} style={styles.recipientText}>
                        {addr.name || addr.email}
                      </Text>
                    ))}
                  </View>
                ) : null}
              </View>

              {attachmentCount > 0 && (
                <Icon
                  name="attachment"
                  size={16}
                  color={theme.colors.onSurfaceVariant}
                  style={styles.attachmentIcon}
                />
              )}
              <View style={styles.actionsContainer}>
                <Menu
                  visible={menuVisible}
                  onDismiss={closeMenu}
                  anchor={
                    <IconButton
                      icon="dots-vertical"
                      size={22}
                      onPress={openMenu}
                      style={styles.actionButton}
                    />
                  }
                  contentStyle={{ backgroundColor: theme.colors.surface }}
                >
                  <Menu.Item
                    onPress={() => handleMenuAction(() => onReply(item))}
                    title="Reply"
                    leadingIcon="reply"
                  />
                  <Menu.Item
                    onPress={() =>
                      handleMenuAction(() => (onReplyAll ? onReplyAll(item) : onReply(item)))
                    }
                    title="Reply All"
                    leadingIcon="reply-all"
                  />
                  <Menu.Item
                    onPress={() => handleMenuAction(() => onForward(item))}
                    title="Forward"
                    leadingIcon="forward"
                  />
                  <Menu.Item
                    onPress={() => handleMenuAction(() => onReply(item))}
                    title="Print"
                    leadingIcon="printer"
                  />
                </Menu>
              </View>
            </View>
          </View>
        </TouchableOpacity>

        {/* Email Content - Only visible when expanded */}
        {(isExpanded || index === 0) && (
          <View style={styles.emailContent}>
            <Divider />
            {/* Message Body */}
            <HtmlMessageContent html={item.latest_message} baseStyle={styles.messageText} />

            {/* Attachments */}
            {attachmentCount > 0 && (
              <View style={styles.attachmentsContainer}>
                {/* <Divider style={styles.divider} /> */}
                <Text style={styles.attachmentsTitle} variant="semiBold">
                  Attachments ({attachmentCount})
                </Text>
                <View style={styles.attachmentsList}>
                  {item.attachments?.map((attachment, idx) => (
                    // <Chip
                    //   key={`${attachment.attachment_id}-${idx}`}
                    //   icon={getAttachmentIcon(attachment.attachment_file_type)}
                    //   onPress={() => onAttachmentPress(attachment)}
                    //   style={styles.attachmentChip}>
                    //   {attachment.attachment_file_name}
                    // </Chip>
                    <View style={styles.attachmentsList}>
                      {item.attachments?.map((attachment, idx) => (
                        <TouchableOpacity
                          key={`${attachment.attachment_id}-${idx}`}
                          onPress={() => onAttachmentPress(attachment)}
                          style={styles.attachmentChip}
                        >
                          <Icon
                            name={getAttachmentIcon(attachment.attachment_file_type)}
                            size={16}
                            color="#555"
                            style={{ marginRight: 6 }}
                          />
                          <Text style={styles.attachmentText} numberOfLines={1} ellipsizeMode="tail">
                            {attachment.attachment_file_name}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  ))}
                </View>
              </View>
            )}
          </View>
        )}
      </Card>
    );
  };

  return (
    <ScrollView style={styles.container} bounces={false}>
      {renderSubjectHeader()}

      {sortedMessages.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={sortedMessages}
          renderItem={({item, index}) => (
            <ThreadItem item={item} index={index} />
          )}
          scrollEnabled={false}
          keyExtractor={item => item.message_id}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          bounces={false}
        />
      )}
    </ScrollView>
  );
};

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    subjectHeader: {
      paddingHorizontal: 16,
      paddingBottom: 16,
      // borderBottomWidth: 1,
      // borderBottomColor: '#E0E0E0',
      // backgroundColor: '#FFFFFF',
    },
    subjectText: {
      fontSize: 18,
      fontWeight: 'bold',
      width: '85%',
    },
    subjectContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 4,
    },
    threadCount: {
      fontSize: 12,
      // color: '#757575',
    },
    listContent: {
      // padding: 6,
      paddingBottom: 30,
    },
    emailCard: {
      marginBottom: 12,
      overflow: 'hidden',
      // backgroundColor: '#FFFFFF',
      borderRadius: 0,
      borderWidth: 1,
    },
    emailHeader: {
      flexDirection: 'row',
      paddingTop: 12,
      paddingHorizontal: 12,
    },
    avatar: {
      marginRight: 12,
    },
    headerContent: {
      flex: 1,
    },
    headerTopRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 4,
    },
    headerBottomRow: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    senderName: {
      fontWeight: 'bold',
      fontSize: 14,
    },
    dateText: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
    },
    recipientText: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      flex: 1,
    },
    attachmentIcon: {
      marginHorizontal: 4,
    },
    emailContent: {
      // padding: 12,
    },
    messageText: {
      fontSize: 14,
      lineHeight: 20,
      marginVertical: 12,
      color: theme.colors.onSurface,
      fontFamily: theme.fonts.bodyMedium.fontFamily,
    },
    divider: {
      marginVertical: 8,
    },
    attachmentsContainer: {
      marginBottom: 8,
      padding: 10,
    },
    attachmentsTitle: {
      fontSize: 12,

      marginBottom: 8,
    },
    attachmentsList: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    attachmentChip: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 10,
      paddingVertical: 6,
      marginRight: 8,
      marginBottom: 8,
      borderRadius: 20,
      backgroundColor: '#f0f0f0',
    },
    attachmentText: {
      fontSize: moderateScale(12),
      color: '#333',
    },
    actionsContainer: {
      padding: 0,
    },
    actionButton: {
      margin: 0,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    emptyText: {
      textAlign: 'center',
    },
  });

export default ThreadList;
