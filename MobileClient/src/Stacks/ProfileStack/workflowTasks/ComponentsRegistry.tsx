import React from 'react';
import { View, StyleSheet } from "react-native";
import { Text } from "react-native-paper";
import { CostSheetDetailView, CostSheetsByEnquiry } from "./Components";

// Define types for component props
interface ComponentProps {
  data: any[];
  item: {
    componentId?: string;
    accountId?: string;
    entityInstanceId?: string;
    workspaceId?: string;
    controlUnitId?: string;
    [key: string]: any;
  };
}

// Define the registry type
type ComponentRegistryType = {
  [key: string]: React.ComponentType<ComponentProps>;
};

export const ComponentRegistry: ComponentRegistryType = {  // Actual CostSheetDetailView component
  'CostSheetDetailView': ({ data, item }: ComponentProps) => {
    return <CostSheetDetailView data={data} item={item} />;
  },

  'CostSheetsByEnquiry': ({ data, item }: ComponentProps) => {
    return <CostSheetsByEnquiry />;
  },

  // Billing View component
  'BillingView': ({ data, item }: ComponentProps) => (
    <View style={styles.container}>
      <Text variant="headlineSmall" style={styles.title}>
        Billing View
      </Text>
      <View style={styles.infoContainer}>
        <Text style={styles.label}>Component ID:</Text>
        <Text style={styles.value}>{item?.componentId || 'N/A'}</Text>
      </View>
      <View style={styles.infoContainer}>
        <Text style={styles.label}>Account ID:</Text>
        <Text style={styles.value}>{item?.accountId || 'N/A'}</Text>
      </View>
      <View style={styles.infoContainer}>
        <Text style={styles.label}>Entity Instance ID:</Text>
        <Text style={styles.value}>{item?.entityInstanceId || 'N/A'}</Text>
      </View>
      <View style={styles.infoContainer}>
        <Text style={styles.label}>Data Items:</Text>
        <Text style={styles.value}>{data?.length || 0}</Text>
      </View>
    </View>
  ),

  // Add more components as needed
  // 'YourComponentName': ({ data, item }: ComponentProps) => <YourComponent data={data} item={item} />,
};

// Helper function to get component by name
export const getComponent = (componentName: string): React.ComponentType<ComponentProps> | null => {
  return ComponentRegistry[componentName] || null;
};

// Helper function to check if component exists
export const hasComponent = (componentName: string): boolean => {
  return componentName in ComponentRegistry;
};

// Get all available component names
export const getAvailableComponents = (): string[] => {
  return Object.keys(ComponentRegistry);
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    marginBottom: 20,
    fontWeight: 'bold',
  },
  infoContainer: {
    flexDirection: 'row',
    marginBottom: 10,
    paddingVertical: 5,
  },
  label: {
    fontWeight: 'bold',
    width: 140,
    color: '#333',
  },
  value: {
    flex: 1,
    color: '#666',
  },
});