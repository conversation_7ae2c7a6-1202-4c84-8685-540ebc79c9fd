import { StyleSheet, View, ActivityIndicator, TouchableOpacity } from 'react-native'
import React, { useEffect, useState } from 'react'
import { Text } from 'react-native-paper'
import ApiClient from '../../../../Common/API/ApiClient'
import { WORKFLOW_ENDPOINTS } from '../../../../Common/API/ApiEndpoints'
import { Eye } from 'lucide-react-native'

interface CostSheetData {
  profit?: number;
  json_data?: string;
  status_id?: number;
  account_id?: string;
  carrier_id?: string;
  created_at?: string;
  enquiry_id?: string;
  customer_id?: string;
  supplier_id?: string;
  carrier_name?: string;
  customer_name?: string;
  supplier_name?: string;
  price_sheet_id?: string;
  reference_number?: string;
  total_cost_price?: number;
  margin_percentage?: number;
  price_sheet_name?: string;
  [key: string]: any;
}

interface CostSheetDetailViewProps {
  data?: any[];
  item?: {
    componentId?: string;
    accountId?: string;
    entityInstanceId?: string;
    workspaceId?: string;
    controlUnitId?: string;
    [key: string]: any;
  };
}

const CostSheetDetailView: React.FC<CostSheetDetailViewProps> = ({ data, item }) => {
  const [costSheetData, setCostSheetData] = useState<CostSheetData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCostSheetDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      const payload = {
        payloadJson: JSON.stringify({
          contextJson: {
            workspace_id: item?.workspaceId ,
            account_id: item?.accountId ,
            control_unit_id: item?.controlUnitId
          },
          inputParamsJson: {
            instance_id: item?.entityInstanceId 
          }
        }),
        schemaName: "c1s1_billing_crm_DEV_1"
      };

      console.log('🚀 Calling getCostSheetById with payload:', payload);

      const response = await ApiClient.post(WORKFLOW_ENDPOINTS.COSTSHEET_BY_ID, payload);
      console.log('✅ getCostSheetById Response:', response.data);

      if (response.data && response.data.data && response.data.data[0] && response.data.data[0].result && response.data.data[0].result.data && response.data.data[0].result.data[0]) {
        const costSheetInfo = response.data.data[0].result.data[0];
        setCostSheetData(costSheetInfo);
        console.log('📊 Cost Sheet Data:', costSheetInfo);
      } else {
        console.log('⚠️ No cost sheet data found in response');
        setError('No cost sheet data found');
      }
    } catch (error) {
      console.error('❌ Error fetching cost sheet details:', error);
      setError('Failed to load cost sheet details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCostSheetDetails();
  }, []);

  const calculateSellingPrice = () => {
    if (!costSheetData?.total_cost_price || !costSheetData?.margin_percentage) {
      return 0;
    }
    const margin = costSheetData.total_cost_price * (costSheetData.margin_percentage / 100);
    return costSheetData.total_cost_price + margin;
  };

  const handleViewDetails = () => {
    console.log('👁️ View details for cost sheet:', costSheetData);
    // TODO: Navigate to detailed cost sheet view or show modal
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Loading cost sheet details...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchCostSheetDetails} activeOpacity={0.7}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!costSheetData) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.emptyText}>No cost sheet data available</Text>
      </View>
    );
  }

  const sellingPrice = calculateSellingPrice();

  return (
    <View style={styles.container}>
      <View style={styles.card}>
        <View style={styles.cardContent}>
          {/* Header */}
          <Text style={styles.title} variant="semiBold">{costSheetData.price_sheet_name || 'Untitled'}</Text>

          {/* Cost Sheet Details */}
          <View style={styles.detailsContainer}>
            <DetailRow
              label="Reference Number:"
              value={costSheetData.reference_number || 'PS-06-SEP-25-0275'}
            />
            <DetailRow
              label="Enquiry Reference:"
              value={costSheetData.enq_reference_number || 'WZUAEE-06-SEP-25-S-0224'}
            />
            <DetailRow
              label="Customer:"
              value={costSheetData.customer_name || 'SNAMPROGETTI SAUDI ARABIA CO. LTD.'}
            />
            <DetailRow label="Supplier:" value={costSheetData.supplier_name || 'Red Gear'} />
            <DetailRow label="Carrier:" value={costSheetData.carrier_name || 'Antong Holdings'} />
            <DetailRow label="Margin:" value={`${costSheetData.margin_percentage || 7}`} />
            <DetailRow
              label="Cost Price:"
              value={`$${costSheetData.total_cost_price?.toFixed(2) || '1,298.00'}`}
            />
            <DetailRow label="Selling Price:" value={`$${sellingPrice.toFixed(2)}`} />
            <DetailRow
              label="Profit:"
              value={`$${costSheetData.profit?.toFixed(2) || '91.00'}`}
              valueStyle={styles.profitValue}
            />
          </View>

          {/* View Details Button */}
          {/* <TouchableOpacity
            style={styles.viewButton}
            onPress={handleViewDetails}
            activeOpacity={0.7}
          >
           <Eye size={16} color="#fff" strokeWidth={2.5}/><Text style={styles.buttonText} variant='semiBold'> View Details</Text>
          </TouchableOpacity> */}
        </View>
      </View>
    </View>
  );
};

const DetailRow = ({ label, value, valueStyle }: { label: string; value: string; valueStyle?: any }) => (
  <View style={styles.detailRow}>
    <Text style={styles.label} variant='semiBold'>{label}</Text>
    <Text style={[styles.value, valueStyle]} variant="regular">{value}</Text>
  </View>
);

export default CostSheetDetailView

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: '#f5f5f5',
    // padding: 16,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 8,
    
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  cardContent: {
    padding: 20,
  },
  title: {
    // fontSize: 24,
  
    color: '#333',
    marginBottom: 20,
    textAlign: 'left',
  },
  detailsContainer: {
    marginBottom: 20,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 0.5,
    borderBottomColor: '#e0e0e0',
  },
  label: {
    color: '#666',
    // fontWeight: '500',
    flex: 1,
    // fontSize: 16,
  },
  value: {
    color: '#333',
    textAlign: 'right',
    flex: 1,
    // fontWeight: '600',
    // fontSize: 16,
  },
  profitValue: {
    color: '#4CAF50',
    // fontWeight: 'bold',
  },
  viewButton: {
    backgroundColor: '#3377ff',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection:"row"
   
 

  },
  buttonText: {
    color: '#fff',
    // fontWeight: 'bold',
    // fontSize: 16,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    color: '#666',
    fontSize: 16,
  },
  errorText: {
    color: '#F44336',
    textAlign: 'center',
    marginBottom: 16,
    fontSize: 16,
  },
  emptyText: {
    color: '#666',
    fontSize: 16,
  },
  retryButton: {
    marginTop: 16,
    backgroundColor: '#4285F4',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#4285F4',
  },
  retryButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
})