import { StyleSheet, View, FlatList, ActivityIndicator, TouchableOpacity } from 'react-native'
import { Text } from 'react-native-paper'
import React, { useEffect, useState } from 'react'
import ApiClient from '../../../../Common/API/ApiClient'
import { LEAD_MANAGEMENT_ENDPOITNS } from '../../../../Common/API/ApiEndpoints'
import { getSchemaName } from '../../../../Common/Utils/Storage'
import { Eye } from 'lucide-react-native'
interface EnquiryItem {
  enquiry_id?: string;
  enquiry_number?: string;
  organization_name?: string;
  enquiry_type?: string;
  direction_name?: string;
  from_port_name?: string;
  to_port_name?: string;
  transportation_type?: string;
  cargo_type_name?: string;
  incoterm_name?: string;
  ready_to_load?: string;
  status_name?: string;
  from_city_name?: string;
  to_city_name?: string;
  from_country_name?: string;
  to_country_name?: string;
  [key: string]: any;
}

const CostSheetsByEnquiry = ({data, item}: any) => {
  const [enquiries, setEnquiries] = useState<EnquiryItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
    const schemaName = getSchemaName();
  const fetchEnquiries = async () => {
    try {
      setLoading(true);
      setError(null);

      const payload = {
        schemaName: "c1s1_billing_crm_DEV_1",
        contextJson: {
          workspace_id: item?.workspaceId || "c51da6b3-460e-41db-831e-a5b4411b5cbe",
          account_id: item?.accountId || "633f20ca-5986-4f7a-a8bc-3723bc260706",
          control_unit_id: item?.controlUnitId || "814deabb-127b-4662-93d3-74d5ff9588fa"
        },
        payloadJson: {
          enquiry_type: "ALL"
        }
      };

      console.log('🚀 Calling getAllEnquiries with payload:', payload);

      const response = await ApiClient.post(LEAD_MANAGEMENT_ENDPOITNS.ALL_ENQUIRIES, payload);
      console.log('✅ getAllEnquiries Response:', response.data);

      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        // Data structure: response.data.data[0] = total_records, response.data.data[1] = enquiries array
        const totalRecords = response.data.data[0] || [];
        const enquiriesData = response.data.data[1] || [];

        console.log('📊 Total Records:', totalRecords);
        console.log('📋 Enquiries Data:', enquiriesData[0]);

        setEnquiries(enquiriesData);
        console.log(`📊 Loaded ${enquiriesData[0].status_name} enquiries`);
      } else {
        console.log('⚠️ No data found in response');
        setEnquiries([]);
      }
    } catch (error) {
      console.error('❌ Error fetching enquiries:', error);
      setError('Failed to load enquiries. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEnquiries();
  }, []);

  const renderEnquiryItem = ({ item: enquiry }: { item: EnquiryItem }) => (
    <View style={styles.card}>
      <View style={styles.cardContent}>
        {/* Header with Enquiry Number */}
        <Text style={styles.enquiryNumber}>
          {enquiry.reference_number || enquiry.enquiry_id || 'N/A'}
        </Text>

        {/* Company Name */}
        <Text style={styles.companyName}>{enquiry.organization_name || 'N/A'}</Text>

        {/* Enquiry Details */}
        <View style={styles.detailsContainer}>
          <DetailRow label="Enquiry Type:" value={enquiry.enquiry_type || 'AIR'} />
          <DetailRow label="Direction:" value={enquiry.direction_name || 'Export'} />
          <DetailRow label="From Port:" value={enquiry.from_port_name || 'N/A'} />
          <DetailRow label="To Port:" value={enquiry.to_port_name || 'Denis Island'} />
          <DetailRow
            label="Transportation:"
            value={enquiry.transportation_type || 'Standard cargo'}
          />
          <DetailRow label="Cargo Type:" value={enquiry.cargo_type_name || 'general cargo'} />
          <DetailRow label="Incoterm:" value={enquiry.incoterm_name || 'Carriage Paid To'} />
          <DetailRow label="Ready to Load:" value={enquiry.ready_to_load || 'N/A'} />
          <DetailRow
            label="Status:"
            value={enquiry.status_name || 'Quoted'}
            valueStyle={[styles.statusValue, getStatusStyle(enquiry.status_name || 'Quoted')]}
          />
        </View>
        {console.log(enquiry.status_name, 'testtt')}
        {/* View Details Button */}
        {/* <TouchableOpacity
          style={styles.viewButton}
          onPress={() => handleViewDetails(enquiry)}
          activeOpacity={0.7}
        >
          <Eye size={16} color="#fff" strokeWidth={2.5} />
          <Text style={styles.buttonText} variant="semiBold">
            {' '}
            View Details
          </Text>
        </TouchableOpacity> */}
      </View>
    </View>
  );

  const DetailRow = ({ label, value, valueStyle }: { label: string; value: string; valueStyle?: any }) => (
    <View style={styles.detailRow}>
      <Text style={styles.label} variant="semiBold">{label}</Text>
      <Text style={[styles.value, valueStyle]} variant="regular">{value}</Text>
    </View>
  );

  const getStatusStyle = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'quoted':
        return { color: '#4CAF50', fontWeight: 'bold' };
      case 'pending':
        return { color: '#FF9800', fontWeight: 'bold' };
      case 'rejected':
        return { color: '#F44336', fontWeight: 'bold' };
      default:
        return { color: '#666' };
    }
  };

  const handleViewDetails = (enquiry: EnquiryItem) => {
    console.log('👁️ View details for enquiry:', enquiry);
    // TODO: Navigate to enquiry details screen or show modal
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Loading enquiries...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchEnquiries} activeOpacity={0.7}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {enquiries.length === 0 ? (
        <View style={styles.centerContainer}>
          <Text style={styles.emptyText}>No enquiries found</Text>
        </View>
      ) : 
        // <FlatList
        //   data={enquiries}
        //   renderItem={renderEnquiryItem}
        //   keyExtractor={(item, index) => item.enquiry_id || item.enquiry_number || index.toString()}
        //   contentContainerStyle={styles.listContainer}
        //   showsVerticalScrollIndicator={false}
        // />
        renderEnquiryItem({ item: enquiries[0] })
      }
    </View>
  )
}

export default CostSheetsByEnquiry

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: '#f5f5f5',
  },
  title: {
    padding: 20,
    paddingBottom: 10,
    // fontWeight: 'bold',
    color: '#333',
    // //fontSize: 20,
  },
  listContainer: {
    padding: 16,
    paddingTop: 0,
  },
  card: {
    marginBottom: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  cardContent: {
    padding: 16,
  },
  enquiryNumber: {
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    //fontSize: 18,
  },
  companyName: {
    color: '#666',
    marginBottom: 16,
    //fontSize: 14,
  },
  detailsContainer: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 6,
    borderBottomWidth: 0.5,
    borderBottomColor: '#e0e0e0',
  },
  label: {
    color: '#666',
    fontWeight: '500',
    flex: 1,
    //fontSize: 14,
  },
  value: {
    color: '#333',
    textAlign: 'right',
    flex: 1,
    
    //fontSize: 14,
  },
  statusValue: {
   
  },
  viewButton: {
    marginTop: 8,
    backgroundColor: '#4285F4',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection:"row"
  },
  buttonText: {
    color: '#fff',
    
    //fontSize: 14,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    color: '#666',
    //fontSize: 16,
  },
  errorText: {
    color: '#F44336',
    textAlign: 'center',
    marginBottom: 16,
    //fontSize: 16,
  },
  emptyText: {
    color: '#666',
    //fontSize: 16,
  },
  retryButton: {
    marginTop: 16,
    backgroundColor: '#4285F4',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#4285F4',
  },
  retryButtonText: {
    color: '#fff',

    //fontSize: 14,
  },
})