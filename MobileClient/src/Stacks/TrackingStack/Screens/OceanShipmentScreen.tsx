import { View, FlatList, Pressable } from 'react-native';
import React from 'react';
import {
  Text,
  useTheme,
  Appbar,

  ActivityIndicator,
} from 'react-native-paper';
import { useNavigation, NavigationProp, useRoute } from '@react-navigation/native';
import { useOceanShipments } from '../Hooks/OceanShipments';
import { createOceanShipmentStyles } from '../Styles/OceanShipmentScreen.Styles';
import SafeContainerView from '../../../Components/SafeContainer';
import { RootStackParamList } from '../../../Common/Routes/StackTypes';
import moment from 'moment';

import { moderateScale, ms, scale, verticalScale } from 'react-native-size-matters';
import { s } from 'react-native-size-matters';
import TrackStepper from '../Components/OceanEvents/OceanTrackTimeLine';
import EmptyComponent from '../Components/NoData/EmptyComponent';
import { useSelector } from 'react-redux';
import SearchBar from '../../../Components/SearchInput';
import AppHeader from '../../../Components/AppHeader';
import EmptyorErrorComponent from '../../../Components/EmptyOrError/EmptyOrErrorComponent';
import ShipmentCard from '../Components/ShipmentCard';

export const OceanShipmentHeader = () => {
  const theme = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  return (
    <View>
      <Appbar.Header
        mode="small"
        style={{
          // backgroundColor: theme.colors.primary,
          marginBottom: 0,
        }}
      >
        <Appbar.BackAction
          onPress={() => navigation.goBack()}
          // color={theme.colors.onPrimary}
        />
        <Appbar.Content
          title="Ocean Shipments"
          titleStyle={
            {
              // color: theme.colors.onPrimary,
              // fontFamily: fontFamily.semiBold,
            }
          }
        />
      </Appbar.Header>
    </View>
  );
};

const OceanShipmentScreen = () => {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const theme = useTheme();
  const styles = createOceanShipmentStyles(theme);
  const routeData = useRoute();
  // console.log('routeData in shipment screen', routeData.params.clientItem);
  const clientData = routeData.params.clientItem;
  const controlUnitselectedData = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedControlUnit?.data
  );
  // Control units data with error handling
  // console.log('app data in shipmentscreen', controlUnitselectedData);
  const { oceanShipments, error, searchOceanShipments, setSearchOceanShipments, loading } =
    useOceanShipments(controlUnitselectedData?.id || '', clientData.org_id);

  // console.log("oceanShipments data rendered", oceanShipments)
  const getDeliveryDuration = (src: string, destination: string) => {
    const source =moment(src)
    const dest = moment(destination);
    console.log("test",source,dest)
    const result = Math.abs(source.diff(dest, 'days'));
    console.log("result days", result);
    return result;
  };

  return (
    <View style={{ backgroundColor: theme.colors.background, flex: 1 }}>
      <AppHeader title="Ocean Shipments" />
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator animating={true} size={40} />
        </View>
      ) : error ? (
        <EmptyorErrorComponent message="There is an issue" />
      ) : (
        <View style={styles.content}>
          <SearchBar
            placeholder="Seach Shipments"
            onChangeText={(text) => setSearchOceanShipments(text)}
            value={searchOceanShipments}
            // style={{ flex: 1 }}
          />
          {/* <Searchbar
            placeholder="Seach Shipments"
            onChangeText={text => setSearchOceanShipments(text)}
            value={searchOceanShipments}
            style={{
              borderRadius: scale(10),
              elevation: 2,
            }}
            iconColor={theme.colors.primary}
          /> */}
          <FlatList
            data={oceanShipments || []}
            keyExtractor={(item, index) => `${index} ${item?.containerNumber}`}
            bounces={false}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.contentContainerStyle}
            renderItem={({ item }) => (
            
              // <Card style={styles.card}>
              //   <Card.Content>
              //     {/* Header */}
              //     <View style={styles.headerContainer}>
              //       <View style={styles.companyInfoContainer}>
              //         <Avatar.Text
              //           size={scale(40)}
              //           label={item.seaLineName.slice(0, 1)}
              //           style={styles.avatar}
              //         />
              //         <View style={{ flex: 1 }}>
              //           <Text style={styles.companyName}>{item.seaLineName}</Text>
              //           <Text style={styles.idText}>ID: {item.containerNumber}</Text>
              //         </View>
              //       </View>
              //       <Button
              //         mode="contained"
              //         icon="map"
              //         style={styles.mapButton}
              //         onPress={() => {
              //           navigation.navigate('ShipmentTrackingScreen', {
              //             oceanItem: item,
              //           });
              //         }}
              //       >
              //         Map View
              //       </Button>
              //     </View>

              //     <Divider style={styles.divider} />

              //     {/* Timeline */}
              //     <View style={styles.timelineContainer}>
              //       <View style={{ flexDirection: 'row' }}>
              //         <TrackStepper shipmentData={item} />
              //         <View style={{ flex: 1 }}>
              //           <View style={{ marginBottom: verticalScale(130) }}>
              //             <View style={styles.locationRow}>
              //               <Icon source={'anchor'} size={16} color={theme.colors.secondary} />
              //               <Text variant="labelMedium" style={styles.locationText}>
              //                 {`${item?.loadingLocationName}, ${item.loadingLocationCountryCode}`}
              //               </Text>
              //             </View>
              //           </View>
              //           <View>
              //             <View style={styles.locationRow}>
              //               <Icon source={'anchor'} size={16} color={theme.colors.secondary} />
              //               <Text variant="labelMedium" style={styles.locationText}>
              //                 {`${item?.dischargeLocationName}, ${item.dischargeLocationCountryCode}`}
              //               </Text>
              //             </View>
              //           </View>
              //         </View>
              //       </View>
              //     </View>

              //     {/* Footer */}
              //     <View style={styles.footerContainer}>
              //       <Chip icon="package-variant" style={styles.chip}>
              //         FTL
              //       </Chip>
              //       <Chip icon="calendar" style={styles.chip}>
              //         {getDeliveryDuration(item.portOfLoading, item.portOfDeparture)}{' '}
              //         {getDeliveryDuration(item.portOfLoading, item.portOfDeparture) === 1
              //           ? 'day'
              //           : 'days'}
              //       </Chip>
              //       <Chip
              //         style={[
              //           styles.statusChip,
              //           {
              //             backgroundColor:
              //               item.statusOfContainer === 'DELIVERED' ? '#B7E4C7' : '#FF525220',
              //           },
              //         ]}
              //       >
              //         <Text
              //           variant="labelMedium"
              //           style={{
              //             color: item.statusOfContainer === 'DELIVERED' ? 'green' : '#FF5252',
              //           }}
              //         >
              //           {item.statusOfContainer}
              //         </Text>
              //       </Chip>
              //     </View>
              //   </Card.Content>
              // </Card>
              <ShipmentCard
                item={item}
                navigation={navigation}
                getDeliveryDuration={getDeliveryDuration}
              />
              
            )}
            ListEmptyComponent={() => (
              <EmptyorErrorComponent
                message="There is an issue"
                emptyImage={require('../../../../assets/icons/noData.png')}
              />
              // <EmptyComponent
              //   emptyTitle="No Shipments Found"
              //   emptyImage={require('../../../../assets/icons/noData.png')}
              // />
            )}
          />
        </View>
      )}
    </View>
  );
};

export default OceanShipmentScreen;
