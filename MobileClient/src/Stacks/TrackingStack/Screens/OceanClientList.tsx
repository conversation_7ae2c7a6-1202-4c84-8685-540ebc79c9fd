import { FlatList, View ,TouchableOpacity,StyleSheet} from 'react-native';
import { ChevronRight } from 'lucide-react-native';
import React from 'react';
import {
  Text,
  useTheme,
  Appbar,

  ActivityIndicator,
} from 'react-native-paper';
import { useNavigation, NavigationProp } from '@react-navigation/native';
import { RootStackParamList } from '../../../Common/Routes/StackTypes';
import { useOceanClientsList } from '../Hooks/OceanShipments';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import EmptyComponent from '../Components/NoData/EmptyComponent';
import SearchBar from '../../../Components/SearchInput';
export const OceanClientsListHeader = () => {
  const theme = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  return (
    <Appbar.Header
      mode="small"
      style={
        {
          // backgroundColor: theme.colors.primary,
        }
      }
    >
      <Appbar.BackAction
        onPress={() => navigation.goBack()}
        // color={theme.colors.onPrimary}
      />
      <Appbar.Content
        title="Client List"
        titleStyle={{
          // color: theme.colors.onPrimary,
          fontSize: moderateScale(18),
   
        }}
      />
    </Appbar.Header>
  );
};

const OceanClientList = ({ controlUnitId }) => {
  // console.log("controlUnitId in ocean clients", controlUnitId)
  const { oceanClients, setSearchOceanClientList, searchOceanClientList, clientsLoading } =
    useOceanClientsList(controlUnitId || '');
  const theme = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  return (
    <View style={{ flex: 1, backgroundColor: theme.colors.background }}>
      {/* <OceanClientsListHeader /> */}
      {clientsLoading ? (
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <ActivityIndicator size="large" />
        </View>
      ) : (
        <View
          style={{
            // paddingVertical: moderateScale(12),
            flex: 1,
            gap: moderateScale(10),
          }}
        >
          <SearchBar
            placeholder="Search Clients"
            onChangeText={(text) => setSearchOceanClientList(text)}
            value={searchOceanClientList}
            // style={{ flex: 1 }}
          />
          {/* <Searchbar
            placeholder="Search Clients"
            onChangeText={text => setSearchOceanClientList(text)}
            value={searchOceanClientList}
            style={{
              borderRadius: scale(10),
              elevation: 2,
              // backgroundColor: theme.colors.inverseOnSurface,
            }}
            iconColor={theme.colors.primary}
          /> */}
          <FlatList
            data={oceanClients}
            keyExtractor={(item) => item?.org_id}
            bounces={false}
            contentContainerStyle={{
              paddingBottom: moderateScale(10),
              gap: moderateScale(8),
              flex: 1,
            }}
            renderItem={({ item, index }) => (
              <TouchableOpacity
                style={[
                  styles.card,
                  {
                    borderColor: theme.colors.outlineVariant,
                    backgroundColor: theme.colors.surface,
                  },
                ]}
                activeOpacity={0.8}
                onPress={() => {
                  navigation.navigate('OceanShipmentScreen', {
                    clientItem: item,
                  });
                }}
              >
                <View style={styles.cardRow}>
                  {/* Avatar */}
                  <View style={[styles.avatar, { backgroundColor: theme.colors.primary }]}>
                    <Text style={[styles.avatarText, { color: theme.colors.onPrimary }]} variant='bold'>
                      {item.organization_name[0]}
                    </Text>
                  </View>
                  {/* Organization Name */}
                  <Text style={styles.title} variant='semiBold'>{item?.organization_name}</Text>
                  {/* Chevron Icon */}
                  <ChevronRight color={theme.colors.primary} size={24} />
                </View>
                {/* You can add more details here below, similar to your shipment image layout */}
              </TouchableOpacity>
            )}
            ListEmptyComponent={() => {
              return (
                <EmptyComponent
                  emptyTitle={'No Clients Found'}
                  emptyImage={require('../../../../assets/icons/noData.png')}
                />
              );
            }}
          />
        </View>
      )}
    </View>
  );
};

export default OceanClientList;
const styles = StyleSheet.create({
  card: {
    borderRadius: 10,
    borderWidth: 1,
    marginBottom: 14,
    padding: 8,
  },
  cardRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor:"#fff"
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 14,
  },
  avatarText: {
    fontSize: moderateScale(14),
  
  },
  title: {
    flex: 1,
fontSize:moderateScale(13)
  },
});