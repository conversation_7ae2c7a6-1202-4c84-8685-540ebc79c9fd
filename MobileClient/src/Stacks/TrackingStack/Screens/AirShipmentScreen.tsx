import { View, FlatList } from 'react-native';
import React from 'react';
import {
  Text,
  useTheme,
  Appbar,
  Button,
  Searchbar,
  Card,
  Badge,
  Avatar,
  Divider,
  Chip,
  Icon,
  ActivityIndicator,
} from 'react-native-paper';
import { useNavigation, NavigationProp, useRoute } from '@react-navigation/native';
import { moderateScale, verticalScale, scale, mvs } from 'react-native-size-matters';
import { useShipments } from '../Hooks/AirShipments';
import { createAirShipmentStyles } from '../Styles/AirShipmentScreen.Styles';
import SafeContainerView from '../../../Components/SafeContainer';
import { RootStackParamList } from '../../../Common/Routes/StackTypes';
import AirShipmentTimeLine from '../Components/AirShipmentCard/AirShipmentTimeLine';
import EmptyComponent from '../Components/NoData/EmptyComponent';
import { useSelector } from 'react-redux';
import SearchBar from '../../../Components/SearchInput';
import EmptyorErrorComponent from '../../../Components/EmptyOrError/EmptyOrErrorComponent';
import AppHeader from '../../../Components/AppHeader';
import AirShipmentCard from '../Components/AirShipmentTrackingCard';

const AirShipmentScreen = () => {
  const theme = useTheme();
  const styles = createAirShipmentStyles(theme);
  const routeData = useRoute();
  const orgId = routeData.params.clientItem?.org_id;

  const controlUnitselectedData = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedControlUnit?.data
  );

  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { airShipments, error, searchAirShipments, setSearchAirShipments, loading } = useShipments(
    controlUnitselectedData?.id || '',
    orgId
  );

  return (
    <View style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <AppHeader title="Air Shipments" />

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator animating={true} size={40} />
        </View>
      ) : error ? (
        <EmptyorErrorComponent message="There is an issue" />
      ) : (
        <View style={styles.content}>
          <SearchBar
            placeholder="Search Clients"
            onChangeText={(text) => setSearchAirShipments(text)}
            value={searchAirShipments}
            // style={{ flex: 1 }}
          />

          <FlatList
            data={airShipments}
            keyExtractor={(item, index) => `${item.awb_number}-${index}`}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContentContainer}
            bounces={false}
            renderItem={({ item }) => (
              // <Card style={styles.card}>
              //   <Card.Content>
              //     {/* Header */}
              //     <View style={styles.headerContainer}>
              //       <View style={styles.companyInfoContainer}>
              //         <Avatar.Text
              //           size={scale(40)}
              //           label={item.airline_info.name.slice(0, 1)}
              //           style={styles.avatar}
              //         />
              //         <View style={{ flex: 1 }}>
              //           <Text style={styles.companyName}>{item.airline_info.name}</Text>
              //           <Text style={styles.idText}>ID: {item.awb_number}</Text>
              //         </View>
              //       </View>
              //       <Button
              //         mode="contained"
              //         icon="map"
              //         style={styles.mapButton}
              //         onPress={() =>
              //           navigation.navigate('AirShipmentDetail', {
              //             airInfoItem: item,
              //           })
              //         }
              //       >
              //         Detail View
              //       </Button>
              //     </View>

              //     <Divider style={styles.divider} />

              //     <View style={styles.timelineContainer}>
              //       <View style={{ flex: 1,justifyContent:"space-between",flexDirection:"row" }}>
              //         <View>
              //           <View style={styles.locationRow}>
              //             <Icon source={'anchor'} size={16} color={theme.colors.secondary} />
              //             <Text variant="labelMedium" style={styles.locationText}>
              //               Origin: {item.origin}
              //             </Text>
              //           </View>
              //         </View>

              //         <View>
              //           <View style={styles.locationRow}>
              //             <Icon source={'anchor'} size={16} color={theme.colors.secondary} />
              //             <Text variant="labelMedium" style={styles.locationText}>
              //               Destination: {item.destination}
              //             </Text>
              //           </View>
              //         </View>
              //       </View>
              //       <View style={{ flexDirection: 'row' }}>
              //         <AirShipmentTimeLine airClientData={item} />
              //       </View>
              //     </View>

              //     <View style={styles.footerContainer}>
              //       <Chip icon="package-variant" style={styles.chip}>
              //         {`${item.weight} KG`}
              //       </Chip>
              //       <Chip icon="calendar" style={styles.chip}>
              //         {`${item.piece} ${item.piece == 1 ? 'Piece' : 'Pieces'}`}
              //       </Chip>
              //       <Chip
              //         style={[
              //           styles.statusChip,
              //           {
              //             backgroundColor:
              //               item.awb_status === 'Delivered' ? '#B7E4C7' : '#FF525220',
              //           },
              //         ]}
              //       >
              //         <Text
              //           style={{
              //             color: item.awb_status === 'Delivered' ? 'green' : '#FF5252',
              //           }}
              //         >
              //           {item.awb_status}
              //         </Text>
              //       </Chip>
              //     </View>
              //   </Card.Content>
              // </Card>
              <AirShipmentCard item={item} navigation={navigation}/>
            )}
            ListEmptyComponent={() => (
              <EmptyorErrorComponent
                message="No Shipments Found"
                emptyImage={require('../../../../assets/icons/noData.png')}
              />
            )}
          />
        </View>
      )}
    </View>
  );
};

export default AirShipmentScreen;
