import { FlatList, Image, View } from 'react-native';
import React from 'react';
import {
  Text,
  useTheme,
  Appbar,
  Avatar,
  Button,
  Searchbar,
  Card,
  Badge,
  IconButton,
  ActivityIndicator,
} from 'react-native-paper';
import { useNavigation, NavigationProp } from '@react-navigation/native';
import { RootStackParamList } from '../../../Common/Routes/StackTypes';
import { useAirClientsList } from '../Hooks/AirShipments';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import MyHeader from '../../../Components/UI/Appbar';
import EmptyComponent from '../Components/NoData/EmptyComponent';
import SearchBar from '../../../Components/SearchInput';
import ClientCard from '../Components/ClientCard';


const AirClientList = ({ controlUnitId }) => {
  const { airClients, setSearchAirClientList, searchAirClientList, clientsLoading } =
    useAirClientsList(controlUnitId || '');
  const theme = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  return (
    <View style={{ flex: 1, backgroundColor: theme.colors.background }}>
      {/* <MyHeader title={'Client List'} handleBack={() => navigation.goBack()} /> */}
      {clientsLoading ? (
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <ActivityIndicator size="large" />
        </View>
      ) : (
        <View
          style={{
            // paddingVertical: moderateScale(12),
            flex: 1,
            gap: moderateScale(10),
          }}
        >
          <SearchBar
            placeholder="Seach Clients"
            onChangeText={(text) => setSearchAirClientList(text)}
            value={searchAirClientList}
            // style={{ flex: 1 }}
          />
        
          <FlatList
            data={airClients}
            keyExtractor={(item) => item.org_id}
            bounces={false}
            contentContainerStyle={{
              // marginHorizontal : moderateScale(2),
              paddingBottom: moderateScale(10),
              gap: moderateScale(8),
              flex: 1,
            }}
            renderItem={({ item, index }) => {
              // console.log("item data", item, index);
              return (
                // <Card
                //   elevation={1}
                //   mode="outlined"
                //   style={{
                //     borderRadius: scale(10),
                //     marginHorizontal: 1,
                //     borderColor: theme.colors.outlineVariant,
                //     // backgroundColor: theme.colors.primaryContainer ,
                //   }}
                //   contentStyle={{
                //     padding: moderateScale(8),
                //   }}
                //   onPress={() => {
                //     // console.log('Card pressed');
                //     navigation.navigate('AirShipmentScreen', {
                //       clientItem: item,
                //     });
                //   }}
                // >
                //   <Card.Title
                //     title={item.organization_name || 'NA'}
                //     //   subtitle="Card Subtitle"
                //     titleStyle={{
                //       fontSize: moderateScale(16),
                //       fontWeight: '500',
                //     }}
                //     left={(props) => (
                //       <View
                //         style={{
                //           backgroundColor: theme.colors.primary,
                //           width: scale(40),
                //           height: scale(40),
                //           borderRadius: scale(8),
                //           alignItems: 'center',
                //           justifyContent: 'center',
                //         }}
                //       >
                //         <Text
                //           style={{
                //             color: theme.colors.onPrimary,
                //             fontSize: moderateScale(18),
                //             fontWeight: '600',
                //           }}
                //         >
                //           {item.organization_name.slice(0, 1)}
                //         </Text>
                //       </View>
                //     )}
                //     right={(props) => (
                //       <IconButton
                //         icon="chevron-right"
                //         iconColor={theme.colors.primary}
                //         size={moderateScale(24)}
                //       />
                //     )}
                //   />
                // </Card>
                <ClientCard
                  item={item}
                  onPress={() => navigation.navigate('AirShipmentScreen', { clientItem: item })}
                />
              );
            }}
            ListEmptyComponent={() => {
              return (
                <EmptyComponent
                  emptyTitle={'No Clients Found'}
                  emptyImage={require('../../../../assets/icons/noData.png')}
                />
              );
            }}
          />
        </View>
      )}
    </View>
  );
};

export default AirClientList;
