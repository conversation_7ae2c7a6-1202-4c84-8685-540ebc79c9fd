import React from 'react';
import { StyleSheet, View, ScrollView, Text, TouchableOpacity } from 'react-native';
import { scale, verticalScale, moderateScale } from 'react-native-size-matters';
import {
  CheckCircle,
  Package,
  PlaneLanding,
  Plane,
  PlaneTakeoff,
  Weight,
} from 'lucide-react-native';
import { fontFamily } from '../../../../Common/Theme/typography';
import moment from 'moment';
import { useSelector } from 'react-redux';

const StatusIcon = ({ status }) => {
  const getIconAndColor = () => {
    switch (status) {
      case 'DLV':
        return {
          icon: CheckCircle,
          color: '#00C851',
          background: 'rgba(0, 200, 81, 0.15)',
          shadowColor: '#00C851',
        };
      case 'NFD':
        return {
          icon: Package,
          color: '#FF3547',
          background: 'rgba(255, 53, 71, 0.15)',
          shadowColor: '#FF3547',
        };
      case 'RCF':
        return {
          icon: PlaneLanding,
          color: '#007BFF',
          background: 'rgba(0, 123, 255, 0.15)',
          shadowColor: '#007BFF',
        };
      case 'ARR':
        return {
          icon: Plane,
          color: '#6F42C1',
          background: 'rgba(111, 66, 193, 0.15)',
          shadowColor: '#6F42C1',
        };
      case 'DEP':
        return {
          icon: PlaneTakeoff,
          color: '#FD7E14',
          background: 'rgba(253, 126, 20, 0.15)',
          shadowColor: '#FD7E14',
        };
      default:
        return {
          icon: Plane,
          color: '#6C757D',
          background: 'rgba(108, 117, 125, 0.15)',
          shadowColor: '#6C757D',
        };
    }
  };

  const { icon: IconComponent, color, background, shadowColor } = getIconAndColor();

  return (
    <View style={[styles.iconContainer, { backgroundColor: background }]}>
      <View
        style={[
          styles.iconInner,
          {
            backgroundColor: color,
            shadowColor: shadowColor,
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.3,
            shadowRadius: 4,
            elevation: 4,
          },
        ]}
      >
        <IconComponent size={moderateScale(14)} color="#FFFFFF" strokeWidth={2.5} />
      </View>
    </View>
  );
};

const StatusBadge = ({ status }) => {
  const getStatusInfo = () => {
    switch (status) {
      case 'DLV':
        return {
          label: status,
          color: '#00C851',
          background: 'rgba(0, 200, 81, 0.1)',
          borderColor: 'rgba(0, 200, 81, 0.3)',
        };
      case 'NFD':
        return {
          label: status,
          color: '#FF3547',
          background: 'rgba(255, 53, 71, 0.1)',
          borderColor: 'rgba(255, 53, 71, 0.3)',
        };
      case 'RCF':
        return {
          label: status,
          color: '#007BFF',
          background: 'rgba(0, 123, 255, 0.1)',
          borderColor: 'rgba(0, 123, 255, 0.3)',
        };
      case 'ARR':
        return {
          label: status,
          color: '#6F42C1',
          background: 'rgba(111, 66, 193, 0.1)',
          borderColor: 'rgba(111, 66, 193, 0.3)',
        };
      case 'DEP':
        return {
          label: status,
          color: '#FD7E14',
          background: 'rgba(253, 126, 20, 0.1)',
          borderColor: 'rgba(253, 126, 20, 0.3)',
        };
      default:
        return {
          label: status,
          color: '#6C757D',
          background: 'rgba(108, 117, 125, 0.1)',
          borderColor: 'rgba(108, 117, 125, 0.3)',
        };
    }
  };

  const { label, color, background, borderColor } = getStatusInfo();

  return (
    <View
      style={[
        styles.badge,
        {
          backgroundColor: background,
          borderColor: borderColor,
          borderWidth: 1,
        },
      ]}
    >
      <Text
        style={[
          styles.badgeText,
          {
            color: color,
            fontFamily: fontFamily.semiBold,
          },
        ]}
      >
        {label}
      </Text>
    </View>
  );
};

const TrackingTimeline = ({ trackEvents }) => {
  //need to check whether it a timezone based or not
  const timezone = useSelector((state: any) => state.selectedTimezone.timezone);

  const formatTime = (time: string) => {
    return time && timezone && moment(time).isValid()
      ? moment.utc(time).tz(timezone).format('hh:mm A - DD/MM/YYYY')
      : 'N/A';
  };
  return (
    <View style={styles.timelineContainer}>
      {trackEvents.map((item, index) => {
        return (
          <View key={index} style={styles.timelineItem}>
            <View style={styles.timelineLeft}>
              {  <View style={[styles.timelineConnector, {backgroundColor: index === 0 ? '#fffbfff' : '#E2E8F0' }]} />}
              <StatusIcon status={item.status} />
              {<View style={[styles.timelineConnector , {backgroundColor :  index < trackEvents.length - 1 ? '#E2E8F0' : '#fffbfff'}]}  />}
            </View>

            <TouchableOpacity style={styles.timelineContent} activeOpacity={0.95}>
              <View style={styles.timelineHeader}>
                <StatusBadge status={item.status} />
                <Text style={[styles.timelineDate, { fontFamily: fontFamily.medium }]}>
                  {formatTime(item.actual_date)}
                </Text>
              </View>

              <View style={styles.timelineStation}>
                <Text style={[styles.stationText, { fontFamily: fontFamily.bold }]}>
                  {item.station}
                </Text>
              </View>

              <Text style={[styles.eventText, { fontFamily: fontFamily.regular }]}>
                {item.event}
              </Text>

              <View style={styles.timelineDivider} />

              <View style={styles.timelineFooter}>
                <View style={styles.piecesInfo}>
                  <Package size={moderateScale(14)} color="#8E9AAF" strokeWidth={2} />
                  <Text style={[styles.footerText, { fontFamily: fontFamily.medium }]}>
                    {item.piece} Pieces
                  </Text>
                </View>
                <View style={styles.weightInfo}>
                  <Weight size={moderateScale(14)} color="#8E9AAF" strokeWidth={2} />
                  <Text style={[styles.footerText, { fontFamily: fontFamily.medium }]}>
                    {item.weight} kg
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          </View>
        );
      })}
    </View>
  );
};

export const AirTracking = ({ trackingInfo }) => {
  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.scrollContainer}
      showsVerticalScrollIndicator={false}
      bounces={false}
    >
      <View style={styles.card}>
        <View style={styles.cardContent}>
          <TrackingTimeline trackEvents={trackingInfo.track_info} />
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: '#F8FAFC',
  },
  scrollContainer: {
    // paddingVertical: verticalScale(16),
    // paddingHorizontal: scale(16),
    marginHorizontal: 16,
  },
  card: {
    // backgroundColor: '#FFFFFF',
    borderRadius: moderateScale(20),
    // shadowColor: '#000000',
    // shadowOffset: {
    //   width: 0,
    //   height: 2,
    // },
    // shadowOpacity: 0.08,
    // shadowRadius: 4,
    // elevation: 4,
    // overflow: 'hidden',
  },
  cardContent: {
    paddingVertical: moderateScale(11),
    paddingHorizontal: moderateScale(5),
  },
  timelineContainer: {
    // paddingTop: verticalScale(8),
  },
  timelineItem: {
    // flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',

    // marginBottom: verticalScale(10),
  },
  timelineLeft: {
    alignItems: 'center',
    // width: scale(50),
    paddingLeft: 5,
  },
  iconContainer: {
    // width: moderateScale(40),
    // height: moderateScale(40),
    borderRadius: moderateScale(25),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: scale(5),
    zIndex: 2,
  },
  iconInner: {
    // width: moderateScale(26),
    // height: moderateScale(26),
    padding: scale(5),
    borderRadius: moderateScale(18),
    justifyContent: 'center',
    alignItems: 'center',
  },
  timelineConnector: {
    // position: 'absolute',
    flex: 1,
    // height : 70,
    // top: moderateScale(50),
    width: moderateScale(3),
    // bottom: verticalScale(-24),
    // left: moderateScale(23.5),

    backgroundColor: '#E2E8F0',
    borderRadius: moderateScale(1.5),
    // zIndex: 1,
  },
  timelineContent: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
 
    paddingVertical: moderateScale(18),
    paddingHorizontal: moderateScale(10),
    marginLeft: scale(8),

    borderWidth: 1,
    borderColor: '#E5E7EB',
    // shadowColor: '#000000',
    // shadowOffset: {
    //   width: 0,
    //   height: 1,
    // },
    // shadowOpacity: 0.05,
    // shadowRadius: 4,
    // elevation: 2,
    marginBottom: verticalScale(10),
  },
  timelineHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: verticalScale(4),
  },
  badge: {
    paddingHorizontal: moderateScale(8),
    paddingVertical: verticalScale(2),
    borderRadius: moderateScale(20),
  },
  badgeText: {
    fontSize: moderateScale(9),
    letterSpacing: 0.5,
  },
  timelineDate: {
    fontSize: moderateScale(11),
    color: '#64748B',
  },
  timelineStation: {
    // marginBottom: verticalScale(10),
  },
  stationText: {
    fontSize: moderateScale(13),
    color: '#1E293B',
    lineHeight: moderateScale(22),
  },
  eventText: {
    fontSize: moderateScale(11),
    color: '#475569',
    lineHeight: moderateScale(20),
    marginBottom: verticalScale(2),
  },
  timelineDivider: {
    height: 1,
    backgroundColor: '#F1F5F9',
    marginBottom: verticalScale(4),
  },
  timelineFooter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  piecesInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: scale(24),
  },
  weightInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  footerText: {
    marginLeft: scale(6),
    fontSize: moderateScale(11),
    color: '#8E9AAF',
  },
});
