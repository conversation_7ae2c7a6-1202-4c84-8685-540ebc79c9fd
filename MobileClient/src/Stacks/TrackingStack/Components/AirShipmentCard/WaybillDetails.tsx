import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { AlertCircle, Plane, Barcode, Weight, Package } from 'lucide-react-native';
import {Text} from "react-native-paper"
import { moderateScale } from 'react-native-size-matters';
// Helper for avatar initial
const getInitial = (name) => name?.[0]?.toUpperCase() || 'N/A';

const DetailItem = ({ label, value, valueStyle = {}, icon: Icon }) => (
  <View style={styles.detailItemContainer}>
    <View style={styles.labelContainer}>
      {Icon && <Icon size={18} color="#3377ff" style={styles.icon} />}
      <Text style={styles.detailLabel} variant='medium'>{label}</Text>
    </View>
    <Text style={[styles.detailValue, valueStyle]} variant='regular'>{value}</Text>
  </View>
);

const AirWaybillDetails = ({ waybillData }) => {
  const calculateDuration = (departTime, arrivalTime) => {
    try {
      const depart = new Date(departTime);
      const arrival = new Date(arrivalTime);
      const diff = arrival.getTime() - depart.getTime();
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      return `${hours}h ${minutes}m`;
    } catch {
      return 'N/A';
    }
  };

  if (!waybillData) {
    return (
      <View style={styles.screenBackground}>
        <View style={styles.card}>
          <View style={styles.noDataContainer}>
            <AlertCircle size={22} color="#FF5252" style={styles.noDataIcon} />
            <Text style={styles.noDataText} variant='medium'>No waybill data available</Text>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.screenBackground}>
      <View style={styles.card}>
        <ScrollView style={{ paddingBottom: 20 }} bounces={false}>
          {/* Header Section with Status Badge */}
          <View style={styles.headerContainer}>
            <View>
              <Text style={styles.cardTitle} variant='semiBold'>Airline Info</Text>
              <Text style={styles.cardSubtitle}>{waybillData.awb_number}</Text>
            </View>
            <View
              style={[
                styles.statusBadge,
                {
                  backgroundColor: waybillData.awb_status === 'Delivered' ? '#2ECC71' : '#FF5252',
                },
              ]}
            >
              <Text style={styles.statusText} variant='semiBold'>{waybillData.awb_status}</Text>
            </View>
          </View>

          {/* Origin - Plane - Destination route row */}
          <View style={styles.routeRow}>
            <Text style={styles.routeCode} variant='semiBold'>
              {waybillData.origin || waybillData.flight_way_station?.[0] || 'N/A'}
            </Text>
            <View style={styles.routeDotted} />
            <Plane size={18} color="#3377ff" style={{ marginHorizontal: 6 }} />
            <View style={styles.routeDotted} />
            <Text style={styles.routeCode} variant='semiBold'>
              {waybillData.destination ||
                (waybillData.flight_way_station &&
                  waybillData.flight_way_station[waybillData.flight_way_station.length - 1]) ||
                'N/A'}
            </Text>
          </View>

          {/* Shipment Details Section */}
          <View style={styles.detailsSection}>
            <View style={styles.detailsCard}>
              <DetailItem
                label="AWB Number"
                value={waybillData.awb_number}
                valueStyle={{ color: '#3377ff' }}
                icon={Barcode}
              />

              <View style={styles.itemDivider} />

              <DetailItem label="Airline" value={waybillData.airline_info.name} icon={Plane} />

              <View style={styles.itemDivider} />

              <DetailItem label="Weight" value={`${waybillData.weight} Kg`} icon={Weight} />

              <View style={styles.itemDivider} />

              <DetailItem label="Volume" value={waybillData.volume || '0.23 m³'} icon={Package} />
            </View>
          </View>

          {/* Flight Data Section */}
          {!waybillData.flight_info_new || waybillData.flight_info_new.length === 0 ? (
            <View style={styles.noDataContainer}>
              <Plane size={22} color="#95A5A6" style={styles.noDataIcon} />
              <Text style={styles.noDataText} variant='medium'>No Flight Data To Display</Text>
            </View>
          ) : (
            <View>
              <Text style={styles.sectionTitle} variant='semiBold'>Flight Data</Text>
              {waybillData.flight_info_new.map((item, idx) => (
                <View style={styles.flightCard} key={idx}>
                  <View style={styles.flightHeader}>
                    <View style={styles.airlineInfo}>
                      <View style={styles.avatar}>
                        <Text style={styles.avatarText} variant='bold'>
                          {getInitial(waybillData.airline_info.name)}
                        </Text>
                      </View>
                      <View style={styles.airlineDetails}>
                        <Text style={styles.airlineName} variant='medium'>
                          {waybillData.airline_info.name || 'Emirates'}
                        </Text>
                        <Text style={styles.flightNumber} variant='medium'>{item.flight_number || '3425234'}</Text>
                      </View>
                    </View>
                  </View>
                  {/* Flight route */}
                  <View style={styles.flightRoute}>
                    <View style={styles.stationInfo}>
                      <Text style={styles.stationCode} variant='semiBold'>{item.depart_station}</Text>
                      <Text style={styles.timeText}>
                        {new Date(item.depart_time).toLocaleTimeString([], {
                          hour: '2-digit',
                          minute: '2-digit',
                          day: 'numeric',
                          month: 'numeric',
                          year: 'numeric',
                        })}
                      </Text>
                    </View>
                    <View style={styles.flightProgress}>
                      <View style={styles.durationContainer}>
                        <Plane size={28} color="#3377ff" />
                        <Text style={styles.durationText} variant='semiBold'>
                          {calculateDuration(item.depart_time, item.arrival_time)}
                        </Text>
                      </View>
                    </View>
                    <View style={styles.stationInfo}>
                      <Text style={styles.stationCode} variant='semiBold'>{item.arrival_station}</Text>
                      <Text style={styles.timeText} variant='regular'>
                        {new Date(item.arrival_time).toLocaleString([], {
                          hour: '2-digit',
                          minute: '2-digit',
                          hour12: true,
                          day: '2-digit',
                          month: '2-digit',
                          year: 'numeric',
                        })}
                      </Text>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          )}
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  screenBackground: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 13,
    padding: 14,
    margin: 12,
    borderColor: '#E5E7EB',
    borderWidth: 1,
  },
  cardTitle: {

    fontSize: moderateScale(13),
    color: '#3377ff',
  },
  cardSubtitle: {
    fontSize: moderateScale(12),
    color: '#7F8C8D',
    marginTop: 4,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 14,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  statusText: {
    color: '#fff',

    fontSize: moderateScale(12),
  },
  routeRow: {
    marginTop: 13,
    marginBottom: 22,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  routeCode: {
   
    fontSize: moderateScale(13),
    color: '#3377ff',
    minWidth: 60,
    textAlign: 'center',
  },
  routeDotted: {
    flex: 1,
    borderStyle: 'dashed',
    borderColor: '#bbb',
    marginHorizontal: 3,
    borderWidth:1,
  },
  detailsSection: {
    marginBottom: 24,
  },
  detailsCard: {
    borderRadius: 8,
    backgroundColor: '#F8F9FA',
    padding: 10,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  detailItemContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 7,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: 7,
  },
  detailLabel: {
    color: '#222',
    fontSize: moderateScale(12),
    marginLeft: 3,
    
  },
  detailValue: {
    fontSize: moderateScale(12),
    color: '#333',
  
  },
  itemDivider: {
    height: 1,
    backgroundColor: '#eee',
    marginVertical: 2.5,
    borderRadius: 1,
  },
  sectionTitle: {

    fontSize: moderateScale(13),
    color: '#3377ff',
    marginBottom: 6,
    marginTop: 2,
  },
  noDataContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    backgroundColor: '#F3F6F9',
    borderRadius: 8,
    marginTop: 18,
  },
  noDataText: {
    color: '#7F8C8D',
    marginLeft: 7,
    fontSize: moderateScale(12),
  },
  noDataIcon: {
    marginRight: 6,
  },
  flightCard: {
    marginVertical: 8,
    borderRadius: 12,
    padding: 14,
    borderWidth: 1,
    backgroundColor: '#fff',
    borderColor: '#E5E7EB',
  },
  flightHeader: {
    marginBottom: 11,
  },
  airlineInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 38,
    height: 38,
    borderRadius: 7,
    backgroundColor: '#3377ff',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  avatarText: {
    fontSize: 17,
    color: '#fff',
    
  },
  airlineDetails: {
    justifyContent: 'center',
  },
  airlineName: {

    fontSize: moderateScale(13),
    color: '#222',
    marginBottom: 2,
  },
  flightNumber: {
    color: '#3377ff',
    fontSize: moderateScale(12),
  },
  flightRoute: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  stationInfo: {
    flex: 1,
    alignItems: 'center',
  },
  stationCode: {

    fontSize: moderateScale(13),
    color: '#3377ff',
  },
  timeText: {
    color: '#7F8C8D',
    marginTop: 4,
    textAlign: 'center',
    fontSize: moderateScale(11),
  },
  flightProgress: {
    flex: 1.5,
    alignItems: 'center',
  },
  durationContainer: {
    alignItems: 'center',
    marginBottom: 2,
  },
  durationText: {
    color: '#3377ff',
    marginTop: 2,
    
    fontSize: moderateScale(12),
  },
});

export default AirWaybillDetails;
