import {View, ScrollView} from 'react-native';
import React, {useMemo} from 'react';
import {
  Surface,
  Text,
  IconButton,
  useTheme as usePaperTheme,
  SegmentedButtons,
  Card,
  Divider,
  ActivityIndicator,
  Icon,
} from 'react-native-paper';
import {createStyles} from '../../Screens/ShipmentTrackingScreen.styles';
import {formatShipmentEventsDate} from '../../../../Utils';
import { MapPin } from 'lucide-react-native';
export const RouteData: React.FC<{shipmentData: any}> = shipmentData => {
  const styles = createStyles();
  const paperTheme = usePaperTheme();

  const waypoints = useMemo(() => {
    const routeData = Array.isArray(shipmentData?.shipmentData)
      ? shipmentData.shipmentData
      : [];
    return routeData;
  }, [shipmentData]);

  return (
    <View style={{flex: 1}}>
      {waypoints.length > 0 ? (
        <ScrollView
          style={styles.routeContainer}
          contentContainerStyle={{paddingBottom: 50}}
          showsVerticalScrollIndicator={false} bounces={false}>
          {
            <View style={styles.routeCard}>
              {waypoints.map((waypoint, index) => {
                const isCompleted = true;
                const isCurrent = index === 0;

                return (
                  <View key={index} style={styles.routeItemRow}>
                    <View style={styles.routeTimelineContainer}>
                      <MapPin
                        size={24}
                        // color={
                        //   isCurrent
                        //     ? paperTheme.colors.primary
                        //     : paperTheme.colors.onSurfaceVariant
                        // }
                      />
                      {index !== waypoints.length - 1 && (
                        <View
                          style={[
                            styles.timelineConnector,
                            isCompleted && styles.completedTimelineConnector,
                          ]}
                        />
                      )}
                    </View>

                    <View style={styles.routeContentContainer}>
                      <Text style={styles.routeLocationText} variant='semiBold'>
                        {`${waypoint.locationOfContainer}, ${waypoint.countryCode}` ||
                          'No Name Available'}
                      </Text>
                      <Text style={styles.routeEventDate} variant='medium'>
                        {formatShipmentEventsDate(waypoint.date)}
                      </Text>
                      <Text style={styles.routeDescription}>
                        {waypoint.description}
                      </Text>
                      {index !== waypoints.length - 1 && (
                        <Divider style={styles.routeDivider} />
                      )}
                    </View>
                  </View>
                );
              })}
            </View>
          }
        </ScrollView>
      ) : (
        <View style={styles.emptyStateContainer}>
          <Text style={styles.emptyStateText}>No tracking data available</Text>
        </View>
      )}
    </View>
  );
};
