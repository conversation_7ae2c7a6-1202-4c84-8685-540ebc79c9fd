import React, { useEffect, useState } from 'react';
import { View, Image, TouchableOpacity, Alert, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import ApiClient from '../Common/API/ApiClient'; // Import your configured Axios instance
import { WORKFLOW_ENDPOINTS } from '../Common/API/ApiEndpoints';
import { getAccountId, getSelectedWorkspaceId, getUserId } from '../Common/Utils/Storage';
import { ComponentRegistry } from '../Stacks/ProfileStack/workflowTasks/ComponentsRegistry';
import { encryptPayload } from './payloadEncryption';


const UnknownComponent = ({ componentName, data, item }) => (
  <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
    <Text variant="headlineSmall" style={{ marginBottom: 10, color: 'orange' }}>
      Unknown Component
    </Text>
    <Text style={{ textAlign: 'center', marginBottom: 20 }}>
      Component "{componentName}" is not registered in the ComponentRegistry.
    </Text>
    <Text style={{ fontSize: 12, color: 'gray', textAlign: 'center' }}>
      Please add "{componentName}" to the ComponentRegistry in WorkFlowRender.tsx
    </Text>
  </View>
);

// Helper: convert CSS-like styles (e.g. "120px") to numbers for RN
const convertSelectiveStyles = (styles) => {
  if (!styles) return {};
  const allowedKeys = [
    // 'padding',
    // 'paddingTop',
    // 'paddingBottom',
    // 'paddingLeft',
    // 'paddingRight',
    'backgroundColor',
    'flex',
    'flexDirection',
    'justifyContent',
    // 'width',
    'alignItems',
    'flexWrap',
    'gap',
    'flexBasis',
    'flexGrow',
    'flexShrink',
    'justifySelf',
    'alignSelf',
    'alignContent',

    'margin',
    // 'marginTop',
    // 'marginBottom',
    // 'marginLeft',
    // 'marginRight',
    'borderRadius',
    // 'height',

    // 'fontSize',
    'color',
    'fontWeight',
  ];

  const convertedStyles = {};

  for (const key of allowedKeys) {
    if (styles.hasOwnProperty(key)) {
      let val = styles[key];
      // Remove 'px' and convert to number for padding, margin, fontSize
      if (typeof val === 'string' && val.endsWith('px')) {
        val = parseFloat(val);
      }
      if (key === 'fontWeight' && val === 'bold') {
        val = 'bold';
      }

      // For colors, allow string as is
      convertedStyles[key] = val;
    }
  }

  return convertedStyles;
};

// Event handler that triggers API call for button's events
const handleElementEvents = async (events = [], element = null, itemData = null) => {
  if (!events.length) return;
  console.log('Handling element events:', events);
  console.log('Item data received in handleElementEvents:', itemData);
  console.log('Element data received in handleElementEvents:', element);
  const onClickEvent = events.find((e) => e.eventType === 'onclick');
  if (!onClickEvent) return;

  try {
    const { config } = onClickEvent;
    console.log('onClickEvent', onClickEvent);

    // Construct payload using item data from DynamicUI component
    const parameterData = {
      contextJson: {
        account_id:
          itemData?.accountId || (await getAccountId()) || '633f20ca-5986-4f7a-a8bc-3723bc260706',
        workspace_id:
          itemData?.workspaceId ||
          (await getSelectedWorkspaceId()) ||
          '859cd915-99d9-42cb-98c8-1ff8c432b43f',
      },
      inputJson: {
        entityData: { from_date: '2025-09-06', to_date: '2025-09-09', reason: 'going to manali' },
        isWaiting: false,
        workflowTransitionId: element._viewConfig.transitionId,
        entityId: itemData?.entityId,
        entityInstanceId: itemData?.entityInstanceId,
        schema_id: itemData?.schemaId,
        createdBy: await getUserId(),
        schema_name: 'Editor_Schema_dev',
      },
    };

    console.log('parameterData constructed from item:', parameterData);
    console.log('Element data for workflow:', element);

    // Check if this is a workflow transition event
    if (config) {
      await handleWorkflowTransition(parameterData, element);
    } else if (config && config.apiEndpoint) {
      // Handle other API endpoints
      await handleGenericApiCall(config, parameterData);
    } else {
      console.log('⚠️ No valid API endpoint found in onClick event');
      Alert.alert('Warning', 'No action configured for this element.');
    }
  } catch (error) {
    console.error('❌ Error handling event:', error);
    Alert.alert('Error', 'Failed to execute action. Please try again.');
  }
};

// Handle workflow transition specifically
const handleWorkflowTransition = async (parameterData, itemData) => {
  try {
    console.log('🔄 Executing workflow transition with parameterData:', parameterData);
    console.log('🔄 Item data for workflow transition:', itemData);

    // Extract payload from item data if available, otherwise use parameterData
    let payload;

    if (itemData && (itemData.contextJson || itemData.inputJson)) {
      // Use payload directly from item data
      payload = {
        contextJson: itemData.contextJson || {
          account_id: await getAccountId() || "633f20ca-5986-4f7a-a8bc-3723bc260706",
          workspace_id: itemData.workspace_id || "859cd915-99d9-42cb-98c8-1ff8c432b43f"
        },
        inputJson: itemData.inputJson || itemData
      };
    } else {
      // Fallback to parameterData structure
      payload = {
        contextJson: parameterData.contextJson || {
          account_id: await getAccountId() || "633f20ca-5986-4f7a-a8bc-3723bc260706",
          workspace_id: parameterData.workspace_id || "859cd915-99d9-42cb-98c8-1ff8c432b43f"
        },
        inputJson: parameterData.inputJson || parameterData
      };
    }

    console.log('🚀 Calling WORKFLOW_TRANSISITON with payload:', payload);

    const response = await ApiClient.post(WORKFLOW_ENDPOINTS.WORKFLOW_TRANSISITON, payload);
    console.log('✅ Workflow transition response:', response.data);

    if (response.data && response.data.statusCode === 200) {
      Alert.alert('Success', response.data.message || 'Workflow transition executed successfully.');

      // Optionally refresh the current data or navigate
      console.log('🎉 Workflow transition completed successfully');
    } else {
      throw new Error(response.data?.message || 'Workflow transition failed');
    }
  } catch (error) {
    console.error('❌ Error in workflow transition:', error);
    const errorMessage = error.response?.data?.message || error.message || 'Workflow transition failed';
    Alert.alert('Error', errorMessage);
  }
};

// Handle generic API calls
const handleGenericApiCall = async (config, parameterData) => {
  try {
    console.log('🌐 Making generic API call:', config);

    const endpoint = WORKFLOW_ENDPOINTS[config.apiEndpoint] || config.url || config.apiEndpoint;
    const method = config.method || 'POST';

    if (!endpoint) {
      throw new Error('No valid endpoint found');
    }

    console.log(`🚀 Calling ${endpoint} with method ${method}`);

    let response;
    if (method.toUpperCase() === 'GET') {
      response = await ApiClient.get(endpoint, { params: parameterData });
    } else {
      response = await ApiClient.post(endpoint, parameterData);
    }

    console.log('✅ Generic API response:', response.data);

    if (response.data) {
      Alert.alert('Success', 'Action completed successfully.');
    }
  } catch (error) {
    console.error('❌ Error in generic API call:', error);
    const errorMessage = error.response?.data?.message || error.message || 'API call failed';
    Alert.alert('Error', errorMessage);
  }
};

// Main element renderer
const RenderElement = ({ element, children, onCollectParameter, itemContext }) => {
  const style = convertSelectiveStyles(element.styles);
  useEffect(() => {
    if (element.parameterData && onCollectParameter) {
      onCollectParameter(element.parameterData);
    }
  }, [element, onCollectParameter]);
  switch (element.type) {
    case 'page':
    case 'div':
      return (
        <View
          style={[
            style,
            element.parentId === '' && { borderWidth: 1, borderColor: '#ddd', padding: 10 },
          ]}
        >
          {children}
        </View>
      );

    case 'text':
      return (
        <Text
          style={[style, element.fontWeight && defaultStyles.text]}
          variant={element.fontWeight}
        >
          {element.content}
        </Text>
      );

    case 'image':
      return <Image source={{ uri: element.src }} style={[style]} alt={element.alt} />;

    case 'button':
      return (
        <TouchableOpacity
          style={[style, defaultStyles.button]}
          onPress={() => handleElementEvents(element.events, element, itemContext)}
        >
          <Text style={{ color: 'white' }} variant="semiBold">
            {element.content}
          </Text>
        </TouchableOpacity>
      );

    // Add more types here as needed

    default:
      return null;
  }
};

// Recursive renderer
const RecursiveRenderer = ({ elements, parentId, onCollectParameter, itemContext }) => {
  const children = elements.filter((e) => e.parentId === parentId);

  return children.map((child) => (
    <RenderElement key={child.id} element={child} onCollectParameter={onCollectParameter} itemContext={itemContext}>
      <RecursiveRenderer elements={elements} parentId={child.id} onCollectParameter={onCollectParameter} itemContext={itemContext} />
    </RenderElement>
  ));
};

// The dynamic UI component entry
export default function DynamicUI({ data, item, schemaId }) {
  const [schemaName, setSchemaName] = useState('');
  const [parameters, setParameters] = useState([]);
  const [populatedData, setPopulatedData] = useState(data);
  const [componentName, setComponentName] = useState('');
  const [loading, setLoading] = useState(false);
  console.log("data",item)

  // Make item data available to child components
  const itemContext = item;

  // Function to fetch component name for screen type
  const fetchComponentName = async () => {
    try {
      setLoading(true);
      const payload = {
        contextJson: {
          account_id: item?.accountId 
        },
        schemaName: "c1s1_billing",
        inputJson: {
          component_id: item?.screenId 
        }
      };

      console.log('🚀 Calling WORKFLOW_TASKS_BY_ID with payload:', payload);

      const response = await ApiClient.post(WORKFLOW_ENDPOINTS.WORKFLOW_TASKS_BY_ID, encryptPayload(payload));
      console.log('✅ WORKFLOW_TASKS_BY_ID Response:', response.data);

      if (response.data && response.data.data && response.data.data.length > 0) {
        const componentNameFromResponse = response.data.data[0].componentName;
        setComponentName(componentNameFromResponse);
        console.log('🎯 Component name retrieved:', componentNameFromResponse);
      }
    } catch (error) {
      console.error('❌ Error fetching component name:', error);
    } finally {
      setLoading(false);
    }
  };

  // Check if item.screenType is "screen" - if so, fetch and render component
  if (item?.screenType === "screen") {
    // Use useEffect to fetch component name when component mounts
    React.useEffect(() => {
      fetchComponentName();
    }, []);

    if (loading) {
      return (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <Text>Loading component...</Text>
        </View>
      );
    }

    if (componentName) {
      // Get the component from the registry
      const ComponentToRender = ComponentRegistry[componentName];

      if (ComponentToRender) {
        console.log(`🎨 Rendering registered component: ${componentName}`);
        return <ComponentToRender data={data} item={item} />;
      } else {
        console.log(`⚠️ Component "${componentName}" not found in registry`);
        return <UnknownComponent componentName={componentName} data={data} item={item} />;
      }
    }

    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text>No component found</Text>
      </View>
    );
  }

  // Function to extract parameter IDs from data structure
  const extractParameterIdsFromData = (dataArray) => {
    console.log('🔍 Extracting parameter IDs from data...');
    const foundParameters = [];
    const scanElement = (element, path = '') => {
      console.log(`🔍 Scanning element at ${path}:`, element);

      // Method 1: Check direct parameterId property
      if (element.parameterId) {
        const paramId = element.parameterId;
        if (typeof paramId === 'string' && (paramId.includes('-') || paramId.length > 10)) {
          if (!foundParameters.find(p => p.parameterId === paramId)) {
            foundParameters.push({ parameterId: paramId });
            console.log(`✅ Found parameter ID in parameterId: ${paramId}`);
          }
        }
      }

      // Method 2: Check _viewConfig.parameterId (your structure)
      if (element._viewConfig && element._viewConfig.parameterId) {
        const paramId = element._viewConfig.parameterId;
        if (typeof paramId === 'string' && (paramId.includes('-') || paramId.length > 10)) {
          if (!foundParameters.find(p => p.parameterId === paramId)) {
            foundParameters.push({ parameterId: paramId });
            console.log(`✅ Found parameter ID in _viewConfig.parameterId: ${paramId}`);
          }
        }
      }

      // Method 3: Check parameterData.parameterId (your structure)
      if (element.parameterData && element.parameterData.parameterId) {
        const paramId = element.parameterData.parameterId;
        if (typeof paramId === 'string' && (paramId.includes('-') || paramId.length > 10)) {
          if (!foundParameters.find(p => p.parameterId === paramId)) {
            foundParameters.push({ parameterId: paramId });
            console.log(`✅ Found parameter ID in parameterData.parameterId: ${paramId}`);
          }
        }
      }

      // Method 4: Check other nested objects for parameterId
      const nestedObjects = ['viewConfig', 'config', 'data', 'attributes', 'props'];
      nestedObjects.forEach(objKey => {
        if (element[objKey] && element[objKey].parameterId) {
          const paramId = element[objKey].parameterId;
          if (typeof paramId === 'string' && (paramId.includes('-') || paramId.length > 10)) {
            if (!foundParameters.find(p => p.parameterId === paramId)) {
              foundParameters.push({ parameterId: paramId });
              console.log(`✅ Found parameter ID in ${objKey}.parameterId: ${paramId}`);
            }
          }
        }
      });
    };

    // Scan all elements in the data array
    dataArray.forEach((element, index) => {
      scanElement(element, `data[${index}]`);
    });

    console.log('📋 Total parameter IDs found:', foundParameters);
    return foundParameters;
  };

  // Function to populate elements with API response values
  const populateElementsWithResponse = (elements, responseParameters) => {
    console.log('🎨 Populating elements with response values...');
    console.log('📊 Response parameters:', responseParameters);

    // Create a mapping of parameter ID to value
    const parameterValueMap = {};
    responseParameters.forEach(param => {
      parameterValueMap[param.parameterId] = param.value;
      console.log(`📝 Mapped ${param.parameterId} = "${param.value}"`);
    });

    // Update elements with the values
    const updatedElements = elements.map(element => {
      const updatedElement = { ...element };
      let parameterIdToCheck = null;
      let parameterSource = '';

      // Method 1: Check direct parameterId
      if (element.parameterId && parameterValueMap[element.parameterId]) {
        parameterIdToCheck = element.parameterId;
        parameterSource = 'parameterId';
      }
      // Method 2: Check _viewConfig.parameterId (your structure)
      else if (element._viewConfig && element._viewConfig.parameterId && parameterValueMap[element._viewConfig.parameterId]) {
        parameterIdToCheck = element._viewConfig.parameterId;
        parameterSource = '_viewConfig.parameterId';
      }
      // Method 3: Check parameterData.parameterId (your structure)
      else if (element.parameterData && element.parameterData.parameterId && parameterValueMap[element.parameterData.parameterId]) {
        parameterIdToCheck = element.parameterData.parameterId;
        parameterSource = 'parameterData.parameterId';
      }

      // Update the content if we found a matching parameter
      if (parameterIdToCheck) {
        const newValue = parameterValueMap[parameterIdToCheck];
        updatedElement.content = newValue;

        // Also update the label if it exists and matches the old content
        if (updatedElement.label && updatedElement.label === element.content) {
          updatedElement.label = newValue;
        }

        // Update parameterData label if it exists
        if (updatedElement.parameterData && updatedElement.parameterData.label === element.content) {
          updatedElement.parameterData = {
            ...updatedElement.parameterData,
            label: newValue
          };
        }

        console.log(`✅ Updated element ${element.id} (${parameterSource}: ${parameterIdToCheck}) with value: "${newValue}"`);
        console.log(`   Old content: "${element.content}" → New content: "${newValue}"`);
      }

      return updatedElement;
    });

    console.log('🎯 Element population complete');
    return updatedElements;
  };

  const collectParameter = (param) => {
    setParameters((prev) => {
      if (!prev.find((p) => p.parameterId === param.parameterId)) {
        return [...prev, param];
      }
      return prev;
    });
  };

  useEffect(() => {
    console.log('Collected parameters:', parameters);
  }, [parameters]);
  const fetchSchemaName = async (schemaId: string) => {
    try {
      console.log('schemaId----<>', schemaId);
      const response = await ApiClient.post(WORKFLOW_ENDPOINTS.SCHEMA_NAME, { schemaId });

      console.log('response', response.data.data.schema_name);
      return response?.data?.data?.schema_name;
    } catch (error) {
      console.error('Error fetching schema name:', error);
      return null;
    }
  };

  console.log('accountId----------', item);
  
  const getApiResponse = async (schemaName) => {
    const userId = await getUserId()
    try {
      // Extract parameter IDs dynamically from data
      const dynamicParameters = extractParameterIdsFromData(data);
      console.log('dynamicParameters', dynamicParameters);
      // If no parameters found in data, use fallback parameters
      const parametersToUse = dynamicParameters.length > 0 ? dynamicParameters : [];

      console.log('🎯 Using parameters for API call:', parametersToUse);

      const payload = {
        schemaName: schemaName,
        jsonData: JSON.stringify({
          api_method: 'getOne',
          entities: [
            {
              entityId: item?.entityId || 'a321ee96-5bca-11f0-965b-020337e84d33',
              targetId: item?.entityInstanceId || '011fce8b-8952-11f0-91d2-020337e84d33',
              parameters: parametersToUse,
            },
          ],
          contextObject: {
            accountId: item?.accountId || '633f20ca-5986-4f7a-a8bc-3723bc260706',
            workspaceId: null,
            controlUnitId: item?.controlUnitId || '6fa4ee64-4fcc-480f-8bed-f2f6fafdf374',
            userId: userId || '91c39d6a-f061-7072-26e6-e91193dc5f04',
            schemaId: null,
          },
        }),
      };
      console.log('payload', payload);
      const response = await ApiClient.post(WORKFLOW_ENDPOINTS.API_RESPONSE, payload);
      console.log("responsee",response)
      console.log('response12121', response.data.data[0].result_json.entities[0].parameters[0]);

      // Extract the parameter values from the response
      const responseParameters = response.data.data[0].result_json.entities[0].parameters[0];
      console.log('🎯 Extracted response parameters:', responseParameters);

      // Log the structure of the first element for debugging
      if (data.length > 0) {
        console.log('🔍 Sample element structure:', {
          id: data[0].id,
          content: data[0].content,
          parameterId: data[0].parameterId,
          _viewConfig: data[0]._viewConfig,
          parameterData: data[0].parameterData
        });
      }

      // Populate the UI elements with the response values
      const updatedElements = populateElementsWithResponse(data, responseParameters);
      setPopulatedData(updatedElements);
      console.log('🎨 UI elements updated with response values');

      // Log a sample of updated elements
      const updatedCount = updatedElements.filter((el, index) => el.content !== data[index]?.content).length;
      console.log(`📊 Updated ${updatedCount} out of ${data.length} elements`);

      return response?.data?.data;
    } catch (error) {
      console.error('Error fetching api response:', error);
      return null;
    }
  };
  console.log('schemaName', item);
  useEffect(() => {
    const getSchemaName = async () => {
      const name = await fetchSchemaName(schemaId);
      setSchemaName(name);
      await getApiResponse(name);
    };
    getSchemaName();
  }, []);
  console.log('schemaName', schemaName);

  return (
    <View style={{ flex: 1 }}>
      <RecursiveRenderer elements={populatedData} parentId="" onCollectParameter={collectParameter} itemContext={itemContext} />
    </View>
  );
}
const defaultStyles = StyleSheet.create({
  button: {
    // backgroundColor: '#3B82F6',
    padding: 10,
    borderRadius: 5,
    margin: 5,
  },
  text: {
    marginLeft: 10,
  },
});
