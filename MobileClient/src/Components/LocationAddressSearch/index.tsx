import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  View,
  FlatList,
  TouchableOpacity,
  ActivityIndicator as RNActivityIndicator,
  Keyboard,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Text } from 'react-native-paper';
import { MapPin } from 'lucide-react-native';
import Config from 'react-native-config';
import { moderateScale, verticalScale } from 'react-native-size-matters';

interface PlacePrediction {
  place_id: string;
  description: string;
  structured_formatting: {
    main_text: string;
    secondary_text: string;
  };
  types: string[];
}

interface PlaceDetails {
  place_id: string;
  formatted_address: string;
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
  name: string;
  types: string[];
}

interface LocationAddressSearchProps {
  label: string;
  placeholder?: string;
  onPlaceSelect?: (place: PlaceDetails) => void;
  onAddressChange?: (address: string) => void;
  value?: string;
  style?: any;
  disabled?: boolean;
  isMandatory?: boolean;
  testID?: string;
}

const LocationAddressSearch: React.FC<LocationAddressSearchProps> = ({
  label,
  placeholder = "Search for an address",
  onPlaceSelect,
  onAddressChange,
  value = '',
  style,
  disabled = false,
  isMandatory = true,
  testID,
}) => {
  const [searchText, setSearchText] = useState(value);
  const [predictions, setPredictions] = useState<PlacePrediction[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showPredictions, setShowPredictions] = useState(false);
  const searchTimeout = useRef<NodeJS.Timeout | null>(null);
  console.log("i am in location address search",predictions);
  useEffect(() => {
    setSearchText(value);
  }, [value]);

  const searchPlaces = async (query: string) => {
    if (!query || query.length < 3) {
      setPredictions([]);
      setShowPredictions(false);
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodeURIComponent(
          query
        )}&key=${Config.GOOGLE_MAPS_API}&types=address`
      );

      const data = await response.json();

      if (data.status === 'OK') {
        setPredictions(data.predictions || []);
        setShowPredictions(true);
      } else {
        console.warn('Places API error:', data.status);
        setPredictions([]);
        setShowPredictions(false);
      }
    } catch (error) {
      console.error('Error fetching places:', error);
      setPredictions([]);
      setShowPredictions(false);
    } finally {
      setIsLoading(false);
    }
  };

  const getPlaceDetails = async (placeId: string) => {
    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&key=${Config.GOOGLE_MAPS_API}&fields=place_id,formatted_address,geometry,name,types`
      );

      const data = await response.json();

      if (data.status === 'OK' && data.result) {
        return data.result as PlaceDetails;
      } else {
        console.warn('Place details API error:', data.status);
        return null;
      }
    } catch (error) {
      console.error('Error fetching place details:', error);
      return null;
    }
  };

  const handleTextChange = (text: string) => {
    setSearchText(text);
    onAddressChange?.(text);

    // Clear existing timeout
    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current);
    }

    // Set new timeout for search
    searchTimeout.current = setTimeout(() => {
      searchPlaces(text);
    }, 300);
  };

  const handlePlaceSelect = async (prediction: PlacePrediction) => {
    setSearchText(prediction.description);
    setShowPredictions(false);
    Keyboard.dismiss();

    onAddressChange?.(prediction.description);

    // Get detailed place information
    const placeDetails = await getPlaceDetails(prediction.place_id);
    if (placeDetails && onPlaceSelect) {
      onPlaceSelect(placeDetails);
    }
  };

  const renderPredictionItem = ({ item }: { item: PlacePrediction }) => (
    <TouchableOpacity
      style={[styles.predictionItem, { borderBottomColor: '#E0E0E0' }]} // Use static color
      onPress={() => handlePlaceSelect(item)}
    >
      <MapPin size={16} color={'#3377ff'} style={styles.predictionIcon} />
      <View style={styles.predictionTextContainer}>
        <Text variant="medium" style={[styles.mainText, { color: '#111827' }]}> 
          {item.structured_formatting.main_text}
        </Text>
        <Text variant="regular" style={[styles.secondaryText, { color: '#6B7280' }]}> 
          {item.structured_formatting.secondary_text}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <KeyboardAvoidingView
    >
      <View style={[styles.container, style]}>
        <Text variant="semiBold" style={styles.sectionTitle}>
          {label}
          {isMandatory && <Text style={{ color: 'red' }}> *</Text>}
        </Text>
        <View
          style={[
            styles.inputContainer,
            {
              borderColor: '#D1D5DB',
              backgroundColor: '#FFFFFF',
            },
          ]}
        >
          <TextInput
            placeholder={placeholder}
            placeholderTextColor={'#888'}
            value={searchText}
            onChangeText={handleTextChange}
            editable={!disabled}
            multiline
            numberOfLines={3}
            style={[
              styles.textInput,
              {
                color: '#222',
                backgroundColor: 'transparent',
              },
            ]}
            onFocus={() => {
              if (predictions.length > 0) {
                setShowPredictions(true);
              }
            }}
          />
          {isLoading && (
            <RNActivityIndicator size={16} color={'#3377ff'} style={styles.rightIcon} />
          )}
        </View>

        {showPredictions && predictions.length > 0 && (
          <View
            style={[
              styles.predictionsContainer,
              {
                backgroundColor: '#fff',
                borderColor: '#E0E0E0',
                shadowColor: '#000',
              },
            ]}
          >
            <FlatList
              data={predictions}
              renderItem={renderPredictionItem}
              keyExtractor={(item) => item.place_id}
              style={styles.predictionsList}
              keyboardShouldPersistTaps="handled"
              // automaticallyAdjustKeyboardInsets
              showsVerticalScrollIndicator={true}
              nestedScrollEnabled={true}
              // maxToRenderPerBatch={10}
              // removeClippedSubviews
              bounces={false}
              // windowSize={10}
            />
          </View>
        )}
      </View>
    </KeyboardAvoidingView>
  );
};

export default LocationAddressSearch;

const styles = StyleSheet.create({
  container: {
    // position: 'relative',
    // zIndex: 1,
    // marginBottom: verticalScale(5),
  },
  sectionTitle: {
    fontSize: moderateScale(12),
    color: '#111827',
    marginBottom: verticalScale(5),
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 4,
    paddingHorizontal: moderateScale(12),
    // paddingVertical: verticalScale(8),
    backgroundColor: '#FFFFFF',
    // minHeight: verticalScale(48),
  },
  leftIcon: {
    marginRight: moderateScale(8),
  },
  textInput: {
    flex: 1,
    fontSize: moderateScale(12),

    // paddingVertical: verticalScale(4),
    minHeight: 60,
  },
  rightIcon: {
    marginLeft: moderateScale(8),
  },
  predictionsContainer: {
    // position: 'absolute',
    // top: '100%',

    borderRadius: moderateScale(8),
    borderWidth: 1,
    // zIndex: 1000,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginTop: 4,
  },
  predictionsList: {
    // flexGrow: 0,
    // minHeight:verticalScale(80),
    maxHeight: verticalScale(150),
  },
  predictionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: moderateScale(16),
    paddingVertical: verticalScale(6),
    // borderBottomWidth: 0.5,
  },
  predictionIcon: {
    marginRight: moderateScale(12),
  },
  predictionTextContainer: {
    flex: 1,
  },
  mainText: {
    fontWeight: '500',
    marginBottom: verticalScale(2),
  },
  secondaryText: {
    fontSize: moderateScale(11),
  },
});