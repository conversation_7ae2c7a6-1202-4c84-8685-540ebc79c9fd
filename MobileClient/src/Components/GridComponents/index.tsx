import React from 'react';
import { StyleSheet, View, FlatList, Dimensions, TouchableOpacity } from 'react-native';
import Text from "../../Components/UI/Text"
import { Card } from 'react-native-paper';
import Modal from "../../Components/UI/Modal"
import { useTheme } from 'react-native-paper';
import ThemeCustomizer from '../../Common/ThemeCustomizer';
interface GridItem {
  id: string;
  title: string;
  component?: React.ReactNode;
  onPress: () => void;
}
interface GridComponentProps {
  data: GridItem[];
  numColumns?: number;
}
const GridComponent: React.FC<GridComponentProps> = ({ data, numColumns = 2 }) => {
  const screenWidth = Dimensions.get('window').width;
  const [visible,setVisible] = React.useState(false);
  const [component,setComponent] = React.useState(null);
  const theme = useTheme()
  const [item,setItem] = React.useState(null);
  const renderItem = ({ item,index }: { item: GridItem,index:any }) => (
      <Card style={[styles.card,{backgroundColor: theme.colors.background}]}>
        
      <TouchableOpacity 
      onPress={()=>{
        setItem(item)
        setVisible(true)
      }}
    >
      <Text variant="bodyMedium">{item.title}</Text></TouchableOpacity>
      </Card>
  );
  const renderSelectedComponent = () => {
    console.log(item,"itemm")
    return <><ThemeCustomizer />{item?.component}</>
  };
const CustomComponent = ()=>{return(<></>)}
  return (
    <>
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Text variant="displaySmall" style={{alignSelf:"center",padding:20}}>Components</Text>
      <FlatList
        bounces={false}
        data={data}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        numColumns={numColumns}
        contentContainerStyle={styles.gridContainer}
        columnWrapperStyle={styles.row}
      />
      
    </View>
    <Modal visible={visible} setVisible={setVisible} >
          {renderSelectedComponent()}
      </Modal>
      </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  gridContainer: {
    padding: 16,
  },
  row: {
    justifyContent: 'space-evenly',
    marginBottom: 16,
  },
  card: {
    // backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 4,
    // shadowColor: '#000',
    width: '30%',
    maxHeight: 200,

    // shadowOffset: {
    //   width: 0,
    //   height: 2,
    // },
    // shadowOpacity: 0.25,
    // shadowRadius: 3.84,
    // aspectRatio: 1,
  },
  
});

export default GridComponent;