import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { Searchbar } from 'react-native-paper'
import { Search } from 'lucide-react-native'
import { moderateScale,verticalScale,scale } from 'react-native-size-matters'
type SearchBarProps = {
    placeholder:string,
  value: string;
  onChangeText: (text: string) => void;
  style?:any,
  inputStyle?:any,
   helperText?: string 
};

const SearchBar = ({placeholder="Search",value,onChangeText,style={},inputStyle={}, helperText}: SearchBarProps) => {
  return (
    <>
    <Searchbar
      placeholder={placeholder}
      onChangeText={onChangeText}
      value={value}
      style={[
        {
          // flex: 1,
          borderRadius: moderateScale(5),
          height: verticalScale(32),
          borderWidth: 1,
          borderColor: '#DDD',
          backgroundColor: '#FFF',
        },
        style,
      ]}
      inputStyle={{
        fontSize: moderateScale(12),
        marginLeft : -moderateScale(7),
        fontFamily: 'Geist-Medium',
        
        alignSelf: 'center',
       
      }}
      icon={() => (
        <Search size={moderateScale(18)} color={'#888'}  />
      )}
      placeholderTextColor={'#888'}
      // autoFocus
    />
    {helperText ? ( 
        <Text
          style={{
            fontSize: moderateScale(10),
            marginTop: moderateScale(2),
            color: '#888',
          }}
        >
          {helperText}
        </Text>
      ) : null}
    </>
  );
}

export default SearchBar

const styles = StyleSheet.create({})