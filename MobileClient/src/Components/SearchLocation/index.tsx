// SearchLocation.tsx
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Text, useTheme } from 'react-native-paper';
import React, { useState } from 'react';
import { MapPin } from 'lucide-react-native';
import { scale, moderateScale, verticalScale } from 'react-native-size-matters';
import { LocationSeachModal } from '../../Stacks/LeadsAppStack/components/LocationSearch/LocationSearchModal';
import SearchModalChildren from './SearchModalChildren';

const SearchLocation = ({
  label = 'From',
  isMandatory = true,
  value = null,
  onLocationSelect,
  transitType = null,
  modeOfShipment = 'sea',
  placeholder = 'Country, City, Port, Airport',
}) => {
  const theme = useTheme();
  const [openModal, setOpenModal] = useState(false);

  const onClose = () => setOpenModal(false);

  return (
    <View style={[styles.container]}>
      <Text style={styles.searchLabel} variant="semiBold">
        {label}
        {isMandatory && <Text style={{ color: 'red' }}> *</Text>}
      </Text>
      <TouchableOpacity
        style={styles.searchContainer}
        onPress={() => setOpenModal(true)}
      >
        <MapPin size={15} color="#3377FF" />
        <Text style={styles.searchText}>
          {value ? value : <Text style={styles.placeholder}>{placeholder}</Text>}
        </Text>
      </TouchableOpacity>

      <LocationSeachModal onClose={onClose} visible={openModal}>
        <SearchModalChildren
          transitType={transitType}
          modeOfShipment={modeOfShipment}
          onLocationSelect={onLocationSelect}
          onClose={onClose}
        />
      </LocationSeachModal>
    </View>
  );
};

export default SearchLocation;

const styles = StyleSheet.create({
  container: {},
  searchLabel: {
    marginBottom: verticalScale(4),
    fontSize: moderateScale(12),
  },
  searchContainer: {
    borderWidth: 1,
    backgroundColor: '#fff',
    paddingHorizontal: scale(10),
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    borderRadius: 8,
    borderColor: '#E0E0E0',
    height: verticalScale(28),
    marginBottom: verticalScale(8),
  },
  searchText: {
    fontSize: moderateScale(12),
  },
  placeholder: {
    color: '#999',
  },
});
