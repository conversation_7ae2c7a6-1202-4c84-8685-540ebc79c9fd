import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Dimensions,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Text } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { MapPin, Building2, Plane, Anchor, Globe } from 'lucide-react-native';
import ApiClient from '../../Common/API/ApiClient';
import { ENQUIRY_FORM_ENDPOINTS } from '../../Common/API/ApiEndpoints';
import AppHeader from '../AppHeader';
import SearchBar from '../SearchInput';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { encryptPayload } from '../../Utils/payloadEncryption';
import { getSchemaName } from '../../Common/Utils/Storage';
import { showToast } from '../AppToaster/AppToaster';

const LocationSearchScreen = ({ route, navigation }: any) => {
  const { onLocationSelect, modeOfShipment } = route?.params;

  const [searchQuery, setSearchQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState('');
  const [loading, setLoading] = useState(false);
  const [schemaName, setSchemaName] = useState('');

  const searchTimeout = useRef(null);

  const searchLocations = async (query: any) => {
    if (!query || query.length < 3) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }
    setLoading(true);
// const { searchText, country_id, limit } = payloadJson;
    try {
      const payload = {
        schemaName: schemaName,
        payloadJson: {
          searchText: query,
          portType: modeOfShipment,  // no need for portType for get cities
          limit: 200,
        },
      };

      const encryptionPayload = await encryptPayload(payload);
      const response = await ApiClient.post(
        ENQUIRY_FORM_ENDPOINTS.SEARCH_LOCATION,
        encryptionPayload
      );
      const result = response.data;

      const formattedSuggestions = result.data.map((location: any) => ({
        id: location.id,
        name: location.name,
        type: location.type,
        coordinate: {
          latitude: parseFloat(location.latitude),
          longitude: parseFloat(location.longitude),
        },
        latitude: parseFloat(location.latitude),
        longitude: parseFloat(location.longitude),
        flag: location?.flag || null,
      }));

      setSuggestions(formattedSuggestions);
      setShowSuggestions(true);

      // Show the first suggestion on map
    } catch (error) {
      console.log('Error searching locations:', error);
      // showToast.error('Network error. Please check your connection.');
      setSuggestions([]);
      setShowSuggestions(false);
    } finally {
      setLoading(false);
    }
  };

   const searchCities = async (query: any, country_id : string) => {
    console.log("i am in search cities")
    if (!country_id) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }
    setLoading(true);
// const { searchText, country_id, limit } = payloadJson;
    try {
      const payload = {
        schemaName: schemaName,
        payloadJson: {
          searchText: query,
          country_id: country_id,  // no need for portType for get cities
          limit: 200,
        },
      };

      const encryptionPayload = await encryptPayload(payload);
      const response = await ApiClient.post(
        ENQUIRY_FORM_ENDPOINTS.SEARCH_CITIES,
        encryptionPayload
      );
      const result = response.data;
      // console.log("ciitites result data", result)
      const formattedSuggestions = result.data.map((location: any) => ({
        id: location.city_id,
        name: location.city_name,
        type: location.type,
        coordinate: {
          latitude: parseFloat(location.latitude),
          longitude: parseFloat(location.longitude),
        },
        latitude: parseFloat(location.latitude),
        longitude: parseFloat(location.longitude),
        cityCode : location?.city_code,
        flag: location?.flag || null,
      }));

      setSuggestions(formattedSuggestions);
      setShowSuggestions(true);

      // Show the first suggestion on map
    } catch (error) {
      console.log('Error searching locations:', error);
      // showToast.error('Network error. Please check your connection.');
      setSuggestions([]);
      setShowSuggestions(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current);
    }

    if (searchQuery.length > 0) {
      searchTimeout.current = setTimeout(() => {
        if(selectedCountry === '') {
          
          searchLocations(searchQuery);
        }
        else {
          searchCities(searchQuery, selectedCountry?.id);

        }
      }, 100);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }

    return () => {
      if (searchTimeout.current) {
        clearTimeout(searchTimeout.current);
      }
    };
  }, [searchQuery]);

  useEffect(() => {
    const fetchSchemaName = async () => {
      const schemName = await getSchemaName();
      setSchemaName(schemName);
    };
    fetchSchemaName();
  }, []);

  const handleSuggestionPress = (suggestion) => {
    if(suggestion?.type?.toLowerCase() === 'country'){
      setSearchQuery('')
      setSelectedCountry(suggestion);
      searchCities('', suggestion?.id);
    }
    else {
      setSearchQuery(suggestion.name);
      console.log("i am in city console log", suggestion)
      setShowSuggestions(false);
      if (onLocationSelect) {
        onLocationSelect(suggestion);
      }
      navigation.goBack();
    }

  };

  const getLocationColor = (type) => {
    switch (type?.toUpperCase()) {
      case 'SEA PORT':
        return '#3377FF'; // Blue
      case 'AIR PORT':
        return '#FF9800'; // Orange
      case 'CITY':
        return '#4CAF50'; // Green
      case 'COUNTRY':
        return '#9C27B0'; // Purple
      case 'STATE':
        return '#607D8B'; // Blue Grey
      default:
        return '#666';
    }
  };

  const getLocationIcon = (type) => {
    switch (type?.toUpperCase()) {
      case 'SEA PORT':
        return Anchor;
      case 'AIR PORT':
        return Plane;
      case 'CITY':
        return Building2;
      case 'COUNTRY':
        return Globe;
      case 'STATE':
        return MapPin;
      default:
        return MapPin;
    }
  };

  const renderSuggestionItem = ({ item }) => {
   
    const IconComponent = getLocationIcon(item.type);
    return (
      <TouchableOpacity style={styles.suggestionItem} onPress={() => handleSuggestionPress(item)}>
        <View
          style={[styles.iconContainer, { backgroundColor: getLocationColor(item.type) + '20' }]}
        >
          <IconComponent size={20} color={getLocationColor(item.type)} />
        </View>
        <View style={styles.suggestionText}>
          <Text style={styles.suggestionName} variant="semiBold">
            {item.name}
          </Text>
        
        </View>
        <View style={[styles.typeChip, { backgroundColor: getLocationColor(item.type) }]}>
          <Text style={styles.typeChipText} variant="semiBold">
            {item.type}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <AppHeader title="Search Location" />
      <SafeAreaView style={styles.container}>

        <View style={styles.searchContainer}>
          <SearchBar
            placeholder="Search locations..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            autoCorrect={false}
          />
        </View>

        <View style={styles.mainContent}>

          {/* {
            selectedCountry && 
             return <renderSuggestionItem item = {selectedCountry} />
            
          } */}
          {/* Suggestions List */}
          {
             selectedCountry && (
                     <View style={[styles.selectedCountry]}>
                       {renderSuggestionItem({ item: selectedCountry })}
                      </View>
             )
          }
          {showSuggestions && suggestions.length > 0 && (
            <View style={styles.suggestionsContainer}>
              <FlatList
                data={suggestions}
                renderItem={renderSuggestionItem}
                contentContainerStyle={{
                  paddingBottom: moderateScale(20),
                }}
                keyExtractor={(item) => item.id.toString()}
                style={styles.suggestionsList}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled"
                bounces={false}
              />
            </View>
          )}

          {/* No Results Message */}
          {showSuggestions && suggestions.length === 0 && !loading && searchQuery.length > 2 && (
            <View style={styles.noResultsContainer}>
              <Icon name="search-off" size={48} color="#ccc" />
              <Text style={styles.noResultsText} variant="semiBold">
                No locations found for "{searchQuery}"
              </Text>
              <Text style={styles.noResultsSubtext} variant="medium">
                Try a different search term
              </Text>
            </View>
          )}

          {/* Loading indicator */}
          {loading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#2196F3" />
              <Text style={styles.loadingText} variant="medium">
                Searching locations...
              </Text>
            </View>
          )}
        </View>
        {/* </View> */}
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },


  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    paddingVertical: 0,
  },
  loadingIcon: {
    marginLeft: 8,
  },
  mainContent: {
    flex: 1,
  },
  suggestionsContainer: {
    backgroundColor: '#fff',
    borderBottomColor: '#f0f0f0',
  },
  selectedCountry : {
    // backgroundColor: '#fff',
    borderBottomColor: '#f0f0f0',
    marginHorizontal: 16,
    paddingHorizontal : 4,
    backgroundColor : '#9C27B030',
    borderRadius : 10

  },
  suggestionsList: {
    paddingHorizontal: 16,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f5f5f5',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  suggestionText: {
    flex: 1,
  },
  suggestionName: {
    fontSize: moderateScale(12),
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  suggestionDescription: {
    fontSize: moderateScale(11),
    color: '#666',
  },
  typeChip: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  typeChipText: {
    fontSize: moderateScale(10),
    color: '#fff',
  },
  noResultsContainer: {
    padding: 40,
    alignItems: 'center',
  },
  noResultsText: {
    fontSize: moderateScale(12),
    color: '#666',
    textAlign: 'center',
    marginTop: 16,
    fontWeight: '500',
  },
  noResultsSubtext: {
    fontSize: moderateScale(11),
    color: '#999',
    textAlign: 'center',
    marginTop: 4,
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: moderateScale(12),
    color: '#666',
    marginTop: 16,
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  locationInfoOverlay: {
    position: 'absolute',
    top: 16,
    left: 16,
    right: 16,
  },
  locationInfo: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  locationInfoIcon: {
    marginRight: 12,
  },
  locationInfoText: {
    flex: 1,
  },
  locationInfoName: {
    fontSize: moderateScale(12),
    fontWeight: '600',
    color: '#333',
  },
  locationInfoAddress: {
    fontSize: moderateScale(11),
    color: '#666',
    marginTop: 2,
  },
  bottomContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  confirmButton: {
    backgroundColor: '#007AFF',
    paddingVertical: verticalScale(10),
    borderRadius: 12,
    alignItems: 'center',
  },
  confirmButtonDisabled: {
    backgroundColor: '#E5E5E7',
  },
  confirmButtonText: {
    color: '#fff',
    fontSize: moderateScale(12),
  },
  confirmButtonTextDisabled: {
    color: '#8E8E93',
  },
});

export default LocationSearchScreen;
