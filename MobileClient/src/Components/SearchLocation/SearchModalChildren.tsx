// SearchModalChildren.tsx
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator,
  Keyboard,
} from 'react-native';
import { Text } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { MapPin, Building2, Plane, Anchor, Globe } from 'lucide-react-native';
import ApiClient from '../../Common/API/ApiClient';
import { ENQUIRY_FORM_ENDPOINTS } from '../../Common/API/ApiEndpoints';
import AppHeader from '../AppHeader';
import SearchBar from '../SearchInput';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { encryptPayload } from '../../Utils/payloadEncryption';
import { getSchemaName } from '../../Common/Utils/Storage';

const SearchModalChildren = ({ onLocationSelect, modeOfShipment, onClose }: any) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [schemaName, setSchemaName] = useState('');
  const [firstCitySelection, setFirstCitySelection] = useState(true);
  const [selectedCity, setSelectedCity] = useState<any>(null);
  const searchTimeout = useRef<any>(null);

  const fetchSearchLocations = async (query: string) => {
    if (!query || query.length < 3) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }
    setLoading(true);
    try {
      const payload = {
        schemaName,
        payloadJson: {
          searchText: query,
          portType: modeOfShipment,
          limit: 200,
        },
      };

      const encryptionPayload = await encryptPayload(payload);
      const response = await ApiClient.post(
        ENQUIRY_FORM_ENDPOINTS.SEARCH_LOCATION,
        encryptionPayload
      );
      const result = response.data;
      console.log('result location data', JSON.stringify(result, null, 2));
      console.log('payload location data', JSON.stringify(payload, null, 2));
      const formatted = result.data.map((location: any) => ({
        id: location.id,
        name: location.name,
        type: location.type,
        latitude: parseFloat(location.latitude),
        longitude: parseFloat(location.longitude),
        flag: location?.flag || null,
      }));

      setSuggestions(formatted);
      setShowSuggestions(true);
    } catch (error) {
      console.log('Error searching locations:', error);
      setSuggestions([]);
      setShowSuggestions(false);
    } finally {
      setLoading(false);
    }
  };

  const fetchSearchCities = async (query: string, countryId: string) => {
    if (!countryId) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }
    setLoading(true);
    try {
      const payload = {
        schemaName,
        payloadJson: {
          searchText: query,
          country_id: countryId,
          limit: 200,
        },
      };

      const encryptionPayload = await encryptPayload(payload);
      const response = await ApiClient.post(
        ENQUIRY_FORM_ENDPOINTS.SEARCH_CITIES,
        encryptionPayload
      );
      const result = response.data;

      const formatted = result.data.map((location: any) => ({
        id: location.city_id,
        name: location.city_name,
        type: location.type,
        latitude: parseFloat(location.latitude),
        longitude: parseFloat(location.longitude),
        cityCode: location?.city_code,
        flag: location?.flag || null,
      }));

      setSuggestions(formatted);
      setShowSuggestions(true);
    } catch (error) {
      console.log('Error searching cities:', error);
      setSuggestions([]);
      setShowSuggestions(false);
    } finally {
      setLoading(false);
    }
  };

  const fetchSearchPorts = async (query: string, cityId: string, cityData?: any) => {
    if (!cityId) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }
    setLoading(true);
    try {
      const payload = {
        schemaName,
        payloadJson: {
          searchText: query,
          city_id: cityId,
          portType: modeOfShipment,

          limit: 200,
        },
      };

      const encryptionPayload = await encryptPayload(payload);
      const response = await ApiClient.post(ENQUIRY_FORM_ENDPOINTS.SEARCH_PORTS, encryptionPayload);
      const result = response.data;

      let formatted = result.data.map((location: any) => ({
        id: location.port_id,
        name: location.port_name,
        type: location.port_type,
        latitude: parseFloat(location.latitude),
        longitude: parseFloat(location.longitude),
        portCode: location?.port_code,
        flag: location?.flag || null,
      }));

      if (cityData) {
        formatted = [
          {
            ...cityData,
            type: 'CITY',
          },
          ...formatted,
        ];
      }

      setSuggestions(formatted);
      setShowSuggestions(true);
    } catch (error) {
      console.log('Error searching ports:', error);
      setSuggestions([]);
      setShowSuggestions(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (searchTimeout.current) clearTimeout(searchTimeout.current);

    if (searchQuery.length > 0) {
      searchTimeout.current = setTimeout(() => {
        if (!selectedCountry && !selectedCity) {
          fetchSearchLocations(searchQuery);
        } else if (selectedCity) {
          console.log('i am at selected city');
          // setSelectedCity((prev) => null);
          // setSelectedCity(null);
          //  fetchSearchLocations(searchQuery);
          fetchSearchPorts(searchQuery, selectedCity?.id, selectedCity);
        } else {
          fetchSearchCities(searchQuery, selectedCountry?.id);
        }
      }, 200);
    } else {
      if (selectedCity) {
      } else {
        setSuggestions([]);
        setShowSuggestions(false);
      }
      setFirstCitySelection(true);
      // setSelectedCity(null);
      // setSelectedCountry(null);
    }

    return () => {
      if (searchTimeout.current) clearTimeout(searchTimeout.current);
    };
  }, [searchQuery]);

  useEffect(() => {
    const fetchSchemaName = async () => {
      const sName = await getSchemaName();
      if (sName) {
        setSchemaName(sName);
      }
    };
    fetchSchemaName();
  }, []);

  const handleSuggestionPress = (suggestion: any) => {
    if (suggestion?.type?.toLowerCase() === 'country') {
      setSearchQuery('');
      setSelectedCountry(suggestion);
      fetchSearchCities('', suggestion?.id);
    } else if (suggestion?.type?.toLowerCase() === 'city' && firstCitySelection) {
      // 👇 Call fetchSearchLocations only on first city selection
      setSearchQuery('');
      fetchSearchPorts('', suggestion?.id, suggestion);
      setFirstCitySelection(false);
      setSelectedCity(suggestion);
    } else {
      setSearchQuery(suggestion.name);
      setShowSuggestions(false);
      if (onLocationSelect) {
        onLocationSelect(suggestion);
      }
      onClose();
    }
  };

  function handleClearAll() {
    setSearchQuery('');
    setSelectedCity(null);
    setSelectedCountry(null);
    setSuggestions([]);
    setShowSuggestions(false);
    setFirstCitySelection(true);
  }

  const getLocationColor = (type: string) => {
    switch (type?.toUpperCase()) {
      case 'SEA PORT':
        return '#3377FF';
      case 'AIR PORT':
        return '#FF9800';
      case 'CITY':
        return '#4CAF50';
      case 'COUNTRY':
        return '#9C27B0';
      case 'STATE':
        return '#607D8B';
      default:
        return '#666';
    }
  };

  const getLocationIcon = (type: string) => {
    switch (type?.toUpperCase()) {
      case 'SEA PORT':
        return Anchor;
      case 'AIR PORT':
        return Plane;
      case 'CITY':
        return Building2;
      case 'COUNTRY':
        return Globe;
      case 'STATE':
        return MapPin;
      default:
        return MapPin;
    }
  };

  const renderSuggestionItem = ({
    item,
    isDisabled = false,
  }: {
    item: any;
    isDisabled?: boolean;
  }) => {
    const IconComponent = getLocationIcon(item.type);
    return (
      <TouchableOpacity
        style={styles.suggestionItem}
        onPress={() => handleSuggestionPress(item)}
        disabled={isDisabled}
      >
        <View
          style={[styles.iconContainer, { backgroundColor: getLocationColor(item.type) + '20' }]}
        >
          <IconComponent size={20} color={getLocationColor(item.type)} />
        </View>
        <View style={styles.suggestionText}>
          <Text style={styles.suggestionName} variant="semiBold">
            {item.name}
          </Text>
        </View>
        <View style={[styles.typeChip, { backgroundColor: getLocationColor(item.type) }]}>
          <Text style={styles.typeChipText} variant="semiBold">
            {item.type}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {/* <AppHeader title="Search Location" /> */}
      <SafeAreaView style={styles.container}>
        <View style={styles.searchContainer}>
          <Text variant="semiBold" style={styles.label}>
            Search Location
          </Text>
          <SearchBar
            placeholder="Search.."
            value={searchQuery}
            onChangeText={setSearchQuery}
            autoCorrect={false}
            helperText="Start typing (minimum 3 characters)"
          />
        </View>

        <View style={styles.mainContent}>
          {
            (showSuggestions || selectedCountry || selectedCity || searchQuery.length > 0) && (
          <TouchableOpacity onPress={handleClearAll} style={styles.clearAllButton}>
            <Text variant="semiBold" style={styles.clearAllText}>
              Clear All
            </Text>
          </TouchableOpacity>

            )
          }
          {selectedCountry && (
            <View style={styles.selectedCountry}>
              {renderSuggestionItem({ item: selectedCountry, isDisabled: true })}
            </View>
          )}
          {selectedCity && (
            <View style={[styles.selectedCountry, { backgroundColor: '#4CAF5030' }]}>
              {renderSuggestionItem({ item: selectedCity, isDisabled: true })}
            </View>
          )}

          {showSuggestions && suggestions.length > 0 && (
            <FlatList
              bounces={false}
              data={suggestions}
              renderItem={renderSuggestionItem}
              contentContainerStyle={{ paddingBottom: moderateScale(20) }}
              keyExtractor={(item) => item.id.toString()}
              style={styles.suggestionsList}
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="handled"
              // keyboardDismissMode="on-drag"
              // onScrollBeginDrag={() => {
              //   setTimeout(() => {
              //     Keyboard.dismiss();
              //   }, 1000);
              // }}
            />
          )}

          {showSuggestions && suggestions.length === 0 && !loading && searchQuery.length > 2 && (
            <View style={styles.noResultsContainer}>
              <Icon name="search-off" size={48} color="#ccc" />
              <Text style={styles.noResultsText} variant="semiBold">
                No locations found for "{searchQuery}"
              </Text>
              <Text style={styles.noResultsSubtext} variant="medium">
                Try a different search term
              </Text>
            </View>
          )}

          {loading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#2196F3" />
              <Text style={styles.loadingText} variant="medium">
                Searching locations...
              </Text>
            </View>
          )}
        </View>
      </SafeAreaView>
    </View>
  );
};

export default SearchModalChildren;

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#fff' },
  label: {
    fontSize: moderateScale(15),
    marginVertical: verticalScale(5),
    color: '#1A1A1A',
    textAlign: 'center',
    textTransform: 'capitalize',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 2,
  },
  mainContent: { flex: 1 },
  clearAllButton: {
    alignSelf: 'flex-end',
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  clearAllText: {
    color: '#2196F3', 
    fontSize : moderateScale(10),
    textTransform: 'uppercase',
  },
  suggestionsList: { paddingHorizontal: 16 },
  selectedCountry: {
    marginHorizontal: 16,
    paddingHorizontal: 4,
    backgroundColor: '#9C27B030',
    borderRadius: 10,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f5f5f5',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  suggestionText: { flex: 1 },
  suggestionName: {
    fontSize: moderateScale(12),
    fontWeight: '500',
    color: '#333',
  },
  typeChip: { paddingHorizontal: 8, paddingVertical: 4, borderRadius: 12 },
  typeChipText: { fontSize: moderateScale(10), color: '#fff' },
  noResultsContainer: { padding: 40, alignItems: 'center' },
  noResultsText: {
    fontSize: moderateScale(12),
    color: '#666',
    textAlign: 'center',
    marginTop: 16,
    fontWeight: '500',
  },
  noResultsSubtext: {
    fontSize: moderateScale(11),
    color: '#999',
    textAlign: 'center',
    marginTop: 4,
  },
  loadingContainer: { padding: 40, alignItems: 'center' },
  loadingText: {
    fontSize: moderateScale(12),
    color: '#666',
    marginTop: 16,
  },
});
