import { View,TouchableOpacity } from "react-native"
import { Text } from "react-native-paper"
import { Mic,Square,Play,Trash2,PauseIcon } from "lucide-react-native"
import styles from "./styles"
export const RecordVoice = ({startRecording}: {startRecording: () => void})=>{
    return (
         <View
                      style={{
                        borderWidth: 1,
                        width: '100%',
                        paddingVertical: 16,
                        borderColor: '#ccc',
                        borderStyle: 'dashed',
                        borderRadius: 5,
                      }}
                    >
                      <TouchableOpacity style={styles.recordButton} onPress={startRecording}>
                        <Mic size={24} color="white" />
                      </TouchableOpacity>
                      <Text style={{ textAlign: 'center', marginVertical: 16 }} variant="regular">
                        Tap to record voice note
                      </Text>
                    </View>
    )
}
export const OnRecording = ({recordTime, stopRecording}: {recordTime: string, stopRecording: () => void})=>{
           return   <View style={styles.recordingBox}>
                <View style={styles.recordingHeader}>
                  <View style={styles.recordingIndicator} />
                  <Text style={styles.recordingText}>Recording...</Text>
                  <Text style={styles.recordingTime}>{recordTime}</Text>
                </View>
    
                <TouchableOpacity style={styles.stopButton} onPress={stopRecording}>
                  <Square size={18} color="white" />
                  <Text style={styles.stopButtonText}>Stop Recording</Text>
                </TouchableOpacity>
              </View>
}

export const AudioPlayer = ({
  isPlaying,
  pausePlaying,
  deleteRecording,
  playTime,
  duration,
  startPlaying,
  hasDelete = true,
}: {
  isPlaying: boolean;
  pausePlaying: () => void;
  deleteRecording: () => void;
  playTime: string;
  duration: string;
  startPlaying: () => void;
  hasDelete: boolean;
}) => {
  return (
    <View
      style={{
        borderColor: '#bfdcff',
        borderWidth: 1,
        backgroundColor: '#f0f6ff',
        width: '100%',
        borderRadius: 10,
      }}
    >
      <>
        <View
          style={{
            flexDirection: 'row',
            padding: 10,
            alignItems: 'center',
            justifyContent: 'space-between',
            borderRadius: 10,
          }}
        >
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}>
            <TouchableOpacity
              style={{ backgroundColor: '#3377FF', borderRadius: 24, padding: 12 }}
              onPress={isPlaying ? pausePlaying : startPlaying}
            >
              {!isPlaying ? <Play color={'#fff'} /> : <PauseIcon color="#fff" />}
            </TouchableOpacity>
            <View style={{ flexDirection: 'row' }}>
              <View>
                <Text style={{ color: '#3377FF' }} variant="semiBold">
                  Voice Note
                </Text>
                <Text style={{ color: '#3377FF' }} variant="medium">
                  {playTime}/{duration}
                </Text>
              </View>
            </View>
          </View>
          {hasDelete && (
            <TouchableOpacity
              style={{
                paddingHorizontal: 5,
                paddingTop: 5,
                paddingBottom: 2,
                backgroundColor: '#ffe3e3',
                borderRadius: 20,
              }}
              onPress={deleteRecording}
            >
              <Trash2 color="#db2b25" size={20} />
            </TouchableOpacity>
          )}
        </View>
        {/* {renderStaticWaveform()} */}
      </>
    </View>
  );
};
