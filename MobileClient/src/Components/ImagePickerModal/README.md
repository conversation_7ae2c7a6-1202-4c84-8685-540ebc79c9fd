# ImagePickerModal Component

A reusable React Native modal component for image selection that handles camera capture, gallery selection, and image removal functionality internally.

## Features

- **Camera Capture**: Take photos using the device camera
- **Gallery Selection**: Choose images from the device gallery
- **Image Removal**: Remove currently selected images
- **Permission Handling**: Automatically handles camera and gallery permissions
- **Self-contained**: All image picking logic is contained within the modal

## Props

| Prop | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
| `visible` | `boolean` | Yes | - | Controls modal visibility |
| `onClose` | `() => void` | Yes | - | Callback when modal is closed |
| `onChange` | `(uri: string) => void` | Yes | - | Callback when image is selected/removed |
| `label` | `string` | No | `'Image'` | Modal title text |
| `hasImage` | `boolean` | Yes | - | Whether an image is currently selected (enables/disables remove option) |

## Usage

```tsx
import React, { useState } from 'react';
import { View, TouchableOpacity, Text } from 'react-native';
import { ImagePickerModal } from '../Components/ImagePickerModal';

const MyComponent = () => {
  const [imageUri, setImageUri] = useState<string>('');
  const [modalVisible, setModalVisible] = useState(false);

  const handleImageChange = (uri: string) => {
    setImageUri(uri);
    console.log('Selected image URI:', uri);
  };

  return (
    <View>
      <TouchableOpacity onPress={() => setModalVisible(true)}>
        <Text>Select Image</Text>
      </TouchableOpacity>

      <ImagePickerModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onChange={handleImageChange}
        label="Profile Picture"
        hasImage={!!imageUri}
      />
    </View>
  );
};
```

## Dependencies

The component requires the following dependencies to be installed:

- `react-native-image-picker` - For camera and gallery functionality
- `react-native-permissions` - For permission handling
- `react-native-paper` - For UI components
- `lucide-react-native` - For icons
- `react-native-size-matters` - For responsive sizing

## Permissions

The component automatically handles the following permissions:

- **iOS**: Camera and Photo Library access
- **Android**: Camera, Read External Storage (API < 33), Read Media Images/Video (API ≥ 33)

## Notes

- The modal automatically closes after an action is performed
- Camera permission is checked before attempting to use the camera
- Gallery selection is limited to 1 image by default
- The remove option is disabled when no image is selected
- All image processing and selection logic is handled internally
