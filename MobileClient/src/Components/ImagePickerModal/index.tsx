import React from 'react';
import {
  Modal,
  TouchableOpacity,
  TouchableWithoutFeedback,
  SafeAreaView,
  View,
  StyleSheet,
} from 'react-native';
import { Text } from 'react-native-paper';
import { Camera, Image as ImageIcon, Trash2 } from 'lucide-react-native';
import { verticalScale, scale, moderateScale } from 'react-native-size-matters';
import { fontFamily} from '../../Common/Theme/typography';
import { cameraSelect, selectImages } from '../../Stacks/ChatStack/ChatUtils/ImageUtils';
import { requestCameraAndGalleryPermissions } from '../../Common/Permissions/MediaPermissions';

type ImagePickerModalProps = {
  visible: boolean;
  onClose: () => void;
  onChange: (uri: string) => void;
  label?: string;
  hasImage: boolean;
};

export const ImagePickerModal: React.FC<ImagePickerModalProps> = ({
  visible,
  onClose,
  onChange,
  label = 'Image',
  hasImage,
}) => {
  if (!visible) return null;

  const handleCamera = async () => {
    const permissions = await requestCameraAndGalleryPermissions();
    if (!permissions.camera) {
      console.log('Camera permission not granted!');
      return;
    }
    onClose();
    try {
      const assets: any = await cameraSelect('photo');
      if (assets?.length > 0) {
        onChange(assets[0].uri);
      }
    } catch (err) {
      console.log('Camera error:', err);
    }
  };

  const handleGallery = async () => {
    const permissions = await requestCameraAndGalleryPermissions();
    if (!permissions.gallery) {
      console.log('Gallery permission not granted!');
      return;
    }
    onClose();
    const imageData = await selectImages({ selectionLimit: 1 });
    if (imageData?.length > 0) {
      onChange(imageData[0].uri);
    }
  };

  const handleRemove = () => {
    onClose();
    onChange('');
  };

  return (
    <Modal animationType="slide" transparent visible={visible} onRequestClose={onClose}>
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.backdrop}>
          <View style={styles.modalContainer}>
            <SafeAreaView>
              <Text style={styles.modalTitle}>{label}</Text>
              <TouchableOpacity style={styles.optionRow} onPress={handleCamera}>
                <Camera size={moderateScale(18)} color="#2563EB" />
                <Text style={styles.optionText}>Use Camera</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.optionRow} onPress={handleGallery}>
                <ImageIcon size={moderateScale(18)} color="#2563EB" />
                <Text style={styles.optionText}>Choose from Gallery</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.optionRow, !hasImage && { opacity: 0.4 }]}
                disabled={!hasImage}
                onPress={handleRemove}
              >
                <Trash2 size={moderateScale(18)} color="#DC2626" />
                <Text style={[styles.optionText, { color: '#DC2626' }]}>Remove Image</Text>
              </TouchableOpacity>
            </SafeAreaView>
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  modalContainer: {
    backgroundColor: '#fff',
    paddingVertical: verticalScale(10),
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  modalTitle: {
    paddingVertical: verticalScale(10),
    fontSize: moderateScale(14),
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
    marginHorizontal: scale(16),
    fontFamily: fontFamily.semiBold,
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: verticalScale(15),
    paddingHorizontal: scale(16),
  },
  optionText: {
    fontSize: moderateScale(12),
    marginLeft: scale(10),
    color: '#000',
    fontFamily: fontFamily.medium,
  },
});
