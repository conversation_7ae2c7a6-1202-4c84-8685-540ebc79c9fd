import React, { useState } from 'react';
import { View, TouchableOpacity, Text, StyleSheet, Alert } from 'react-native';
import { ImagePickerModal } from './index';

/**
 * Test component for ImagePickerModal with individual permission handling
 */
const ImagePickerModalTest = () => {
  const [imageUri, setImageUri] = useState<string>('');
  const [modalVisible, setModalVisible] = useState(false);

  const handleOpenModal = () => {
    setModalVisible(true);
  };

  const handleCloseModal = () => {
    setModalVisible(false);
  };

  const handleImageChange = (uri: string) => {
    setImageUri(uri);
    
    if (uri) {
      Alert.alert(
        'Image Selected',
        `Image URI: ${uri.substring(0, 50)}...`,
        [{ text: 'OK' }]
      );
    } else {
      Alert.alert('Image Removed', 'Image has been removed', [{ text: 'OK' }]);
    }
  };

  const handleClearImage = () => {
    setImageUri('');
    Alert.alert('Image Cleared', 'Image has been cleared', [{ text: 'OK' }]);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>ImagePickerModal Test</Text>
      <Text style={styles.subtitle}>Individual Camera & Gallery Permissions</Text>
      
      <TouchableOpacity style={styles.primaryButton} onPress={handleOpenModal}>
        <Text style={styles.primaryButtonText}>Open Image Picker</Text>
      </TouchableOpacity>

      {imageUri ? (
        <View style={styles.imageSection}>
          <Text style={styles.imageLabel}>Selected Image:</Text>
          <Text style={styles.imageUri} numberOfLines={3}>
            {imageUri}
          </Text>
          <TouchableOpacity style={styles.clearButton} onPress={handleClearImage}>
            <Text style={styles.clearButtonText}>Clear Image</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.noImageSection}>
          <Text style={styles.noImageText}>No image selected</Text>
        </View>
      )}

      <View style={styles.infoSection}>
        <Text style={styles.infoTitle}>Permission Handling:</Text>
        <Text style={styles.infoText}>• Camera: Individual cameraAccess() permission</Text>
        <Text style={styles.infoText}>• Gallery: Individual galleryAccess() permission</Text>
        <Text style={styles.infoText}>• Platform-specific permission dialogs</Text>
        <Text style={styles.infoText}>• Settings navigation for denied permissions</Text>
      </View>

      <ImagePickerModal
        visible={modalVisible}
        onClose={handleCloseModal}
        onChange={handleImageChange}
        label="Test Image"
        hasImage={!!imageUri}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    color: '#666',
  },
  primaryButton: {
    backgroundColor: '#007AFF',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  primaryButtonText: {
    fontSize: 18,
    color: '#fff',
    fontWeight: '600',
  },
  imageSection: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  imageLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  imageUri: {
    fontSize: 14,
    color: '#666',
    backgroundColor: '#f8f8f8',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
    fontFamily: 'monospace',
  },
  clearButton: {
    backgroundColor: '#FF3B30',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  clearButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  noImageSection: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  noImageText: {
    fontSize: 16,
    color: '#999',
    fontStyle: 'italic',
  },
  infoSection: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
});

export default ImagePickerModalTest;
