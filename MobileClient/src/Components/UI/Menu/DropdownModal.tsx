import React, { useEffect, useMemo, useState } from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Modal,
  FlatList,
  TextInput,
  StatusBar,
} from 'react-native';
import { Text, IconButton, useTheme } from 'react-native-paper';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { fontFamily } from '../../../Common/Theme/typography';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { debounce } from 'lodash';
import SearchBar from '../../SearchInput';

interface ObjectType {
  Text: string;
  Value: string;
}

interface DropdownFieldProps<T> {
  label: string;
  value: string | null | ObjectType;
   data:T[];
  keyField: keyof T;
  valueField: keyof T;
  labelField:keyof T;
  onSelect: (item: T) => void;
  error?: boolean;
  placeholder?: string;
  icon?: string;
  disable?: boolean;
  searchable?: boolean;
  paginated?: boolean;
  onEndReached?: () => void;
  onSearchTextChange?: (text: string) => void;
  isLoading?: boolean;
  hasMoreData?: boolean;
  showAsterisk?: boolean;
  errorText?: string;
  toPascal:boolean;
}
interface DropdownModalProps<T> {
  visible: boolean;
  data: T[];
  onClose: () => void;
  onSelect: (item: T) => void;
  keyField: keyof T;
  valueField: keyof T;
  title: string;
  value: string | null | ObjectType;
  searchable?: boolean;
  paginated?: boolean;
  onEndReached?: () => void;
  onSearchTextChange?: (text: string) => void;
  isLoading?: boolean;
  hasMoreData?: boolean;
  labelField?:keyof T | null;
  toPascal:boolean;
}
export const toPascalCase = (text: string): string => {
  const str = text?.trim();
  if (!str) return str;
  return str
    .split(' ')
    .map(word => word?.charAt(0).toUpperCase() + word?.slice(1))
    .join(' ');
};

// Radio Button Component matching the image
const RadioButton = ({ selected }: { selected: boolean }) => (
  <View
    style={[
      styles.radioButton,
      {
        borderColor: selected ? '#3377FF' : '#E0E0E0',
        backgroundColor:"#fff",
      },
    ]}
  >
    {selected && <View style={styles.radioButtonInner} />}
  </View>
);

// Inline Options Component matching SI/US units design
function InlineOptions<T>({
  data,
  keyField,
  valueField,
  labelField,
  value,
  onSelect,
  radioOptionContainerStyle,
}: {
   data:T[];
  keyField: keyof T;
  valueField: keyof T;
  value: string | null | ObjectType;
  onSelect: (item: T) => void;
  radioOptionContainerStyle?: boolean;
}) {
  return (
    <View style={styles.inlineOptionsContainer}>
      {data.map((item, index) => {
        const isSelected =
          typeof value === 'object' && value !== null && !Array.isArray(value)
            ? item[valueField] === (value as T)[valueField]
            : item[valueField] === value;

        return (
          <TouchableOpacity
            key={String(item?.[keyField] ?? index)}
            style={[
              styles.optionCard,
              {
                borderColor: isSelected ? '#3377FF' : '#E0E0E0',
                backgroundColor: !isSelected ? '#FFFFFF' : '#F7FAFF',
                // marginBottom: verticalScale(5),
                borderWidth: isSelected ? 2 : 1,
              },
              radioOptionContainerStyle ? styles.radioContainer : {},
            ]}
            onPress={() => onSelect(item)}
            activeOpacity={0.7}
          >
            <View style={styles.optionCardContent}>
              <RadioButton selected={isSelected} />
              <Text style={[styles.optionCardLabel,{color:isSelected?"#3377FF":""}]} variant={isSelected?"medium":"regular"} numberOfLines={1}>
                {labelField ?toPascalCase(String(item[labelField])):toPascalCase(String(item[valueField]))}
              </Text>
            </View>
          </TouchableOpacity>
        );
      })}
    </View>
  );
}

// No Data Component
const NoDataComponent = ({ searchInput }: { searchInput: string }) => (
  <View style={styles.noDataContainer}>
    <Text style={styles.noDataText}>
      {searchInput ? `No results found for "${searchInput}"` : 'No data available'}
    </Text>
  </View>
);

// Original Modal component for larger datasets
function DropdownModal<T>({
  visible,
  data,
  onClose,
  onSelect,
  keyField,
  valueField,
  labelField=null,
  title,
  value,
  searchable = true,
  paginated = false,
  onSearch,
  onEndReached,
  isLoading = false,
  hasMoreData = true,
  searchPlaceholderText,
  toPascal=true,
}: DropdownModalProps<T> & {
  searchable?: boolean;
  paginated?: boolean;
  onSearch?: (text: string) => void;
  onEndReached?: () => void;
  isLoading?: boolean;
  searchPlaceholderText?: string;
}) {
  const [searchInput, setSearchInput] = useState('');
  const { top } = useSafeAreaInsets();
  const theme = useTheme();
  
  const debouncedSearch = useMemo(() => {
  return debounce((text: string) => {
    onSearch?.(text);
  }, 300);
}, [onSearch]);

useEffect(() => {
  return () => {
    debouncedSearch.cancel(); // clean up on unmount
  };
}, []);
  const filteredData = !searchable || onSearch
  ? data
  : data.filter((item) =>
      String(item[valueField]).toLowerCase().includes(searchInput.toLowerCase())
    );

const formatLabel = (
  item: Record<string, any>, 
  labelField?: string, 
  valueField?: string, 
  toPascal?: boolean
) => {
  let rawValue = "";

  if (labelField && item[labelField] != null && item[labelField] !== "") {
    rawValue = String(item[labelField]);
  } else if (valueField && item[valueField] != null && item[valueField] !== "") {
    rawValue = String(item[valueField]);
  } else {
    return "--";
  }

  return toPascal ? toPascalCase(rawValue) : rawValue;
};

  return (
    <Modal
      visible={visible}
      animationType="slide"
      onRequestClose={onClose}
      transparent={true}
    >
      <TouchableOpacity
        style={{ height: '100%' }}
        onPress={onClose}
        activeOpacity={1}
      >
        <View style={{ height: verticalScale(200), backgroundColor: 'rgba(0,0,0,0.3)' }} />
        <View style={{
          flex: 1,
          backgroundColor: 'white',
          borderTopLeftRadius: 8,
          borderTopRightRadius: 8,
          paddingBottom: 20,
        }}>
          <Text style={{
            textAlign: 'center',
            fontFamily: fontFamily.semiBold,
            fontSize: moderateScale(14),
            marginTop: verticalScale(10),
            marginBottom: verticalScale(6.5),
          }}>
            {title}
          </Text>

          {(searchable || (searchInput !== '' || data.length > 7)) && (
            <View 
            style={{ paddingHorizontal: 12, paddingBottom: 8 }}
            >
              <SearchBar
                placeholder={searchPlaceholderText || 'Search...'}
                value={searchInput}
                onChangeText={(text) => {
                  setSearchInput(text);
                  debouncedSearch(text);
                }}
              />
            </View>
          )}

          <FlatList
            data={filteredData}
            keyExtractor={(item, index) => String(item?.[keyField] ?? index)}
            contentContainerStyle={{ marginBottom: 80 }}
            bounces={false}
            renderItem={({ item }) => {
              const isSelected =
                typeof value === 'object' && value !== null && !Array.isArray(value)
                  ? item[valueField] === (value as T)[valueField]
                  : item[valueField] === value;
              return (
                <TouchableOpacity
                  style={{
                    padding: 20,
                    borderBottomWidth: 1,
                    borderColor: '#ccc',
                  }}
                  onPress={() => {
                    onSelect(item);
                    onClose();
                  }}
                >
                  <Text style={{
                    fontWeight: isSelected ? 'bold' : 'normal',
                    color: isSelected ? '#2563EB' : '#000',
                  }}>
                    
                {formatLabel(item, labelField, valueField, toPascal)}
                  </Text>
                </TouchableOpacity>
              );
            }}
            ListEmptyComponent={() => <NoDataComponent searchInput={searchInput} />}
            onEndReachedThreshold={0.5}
            onEndReached={() => {
              if (paginated && onEndReached && !isLoading) {
                onEndReached();
              }
            }}
            ListFooterComponent={
              isLoading ? (
                <View style={{ paddingVertical: 12, alignItems: 'center' }}>
                  <Text style={{ color: '#666' }}>Loading more...</Text>
                </View>
              ) : !hasMoreData ? (
                <View style={{ paddingVertical: 12, alignItems: 'center' }}>
                  <Text style={{ color: '#999' }}>No more data</Text>
                </View>
              ) : null
            }
          />
        </View>
      </TouchableOpacity>
    </Modal>
  );
}

export function DropdownField<T>({
  label,
  value,
  data,
  keyField,
  valueField,
  labelField,
  onSelect,
  error,
  icon,
  disable,
  placeholder = 'Select',
  // enableSearch,
  enablePagination,
  onSearch,
  onEndReached,
  isLoading,
  showAsterisk = true,
  type = 'dropdown', // NEW prop with default value
  searchPlaceholderText,
  radioOptionContainerStyle,
  errorText,
  dropDownButtonStyles,
  toPascal=true,
}: DropdownFieldProps<T> & {
  // enableSearch?: boolean;
  enablePagination?: boolean;
  onSearch?: (text: string) => void;
  onEndReached?: () => void;
  isLoading?: boolean;
  type?: 'radio' | 'dropdown'; // NEW PROP
  searchPlaceholderText?: string;
  radioOptionContainerStyle?: boolean;
}) {
  const [visible, setVisible] = useState(false);
  const theme = useTheme();

  const isRadio = type === 'radio';

  return (
    <View style={{
      flex:1
    }} >
      {/* Label */}
      {label && (
        <Text variant="semiBold" style={styles.label}>
          {label}
          {showAsterisk && <Text style={{ color: 'red', fontSize: moderateScale(12) }}> *</Text>}
        </Text>
      )}

      {/* Conditionally render inline or dropdown based on `type` */}
      {isRadio ? (
        <InlineOptions
          data={data}
          keyField={keyField}
          valueField={valueField}
          labelField={labelField}
          value={value}
          onSelect={onSelect}
          radioOptionContainerStyle={radioOptionContainerStyle}
        />
      ) : (
        <>
          <TouchableOpacity
            disabled={disable}
            onPress={() => setVisible(true)}
            style={[
              styles.dropdownButton,
              dropDownButtonStyles,
              error ? { borderColor: theme.colors.error } : null,
              disable && { opacity: 0.5 },
              
            ]}
          >
            <Text
              style={{
                // flex: 1,
                color: value ? '#000' : '#888',
                fontSize: moderateScale(12),

              }}
              numberOfLines={1}
            >
              {typeof value === 'object' && value !== null
                ? toPascal ? toPascalCase(String(value[labelField || valueField as keyof ObjectType])) : (String(value[labelField || valueField as keyof ObjectType])) ?? placeholder
                : typeof value === 'string' && value
                ? toPascal ? toPascalCase(value) : value
                : placeholder}
            </Text>
            <IconButton
              icon={icon || 'chevron-down'}
              size={moderateScale(18)}
              iconColor="#888"
              style={{ margin: 0 }}
            />
          </TouchableOpacity>
           {errorText ? <Text style={styles.errorText}>{errorText}</Text> : null}
      <DropdownModal
        visible={visible}
        data={data}
        keyField={keyField}
        valueField={valueField}
        onClose={() => setVisible(false)}
        onSelect={(item) => {
          onSelect(item);
          setVisible(false);
        }}
        title={label}
        value={value}
        searchable={true}
        paginated={enablePagination}
        onSearch={onSearch}
        onEndReached={onEndReached}
        isLoading={isLoading}
        searchPlaceholderText={searchPlaceholderText}
        toPascal={toPascal}
        labelField={labelField}
      />
    </>)}
    </View>
  );
}


const styles = StyleSheet.create({
  label: {
    fontSize: moderateScale(12),
    marginVertical: verticalScale(5),
    color: '#1A1A1A',
    // marginBottom: verticalScale(12),
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent : 'space-between',
    paddingHorizontal: scale(12),
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#ccc',
    backgroundColor: 'white',
    marginBottom: verticalScale(5),
    height: verticalScale(28),
  },
  // Inline Options Styles matching the image
  inlineOptionsContainer: {
    // No dark container - options directly displayed
    flexDirection: 'row',
    // justifyContent:"space-between",
    gap: moderateScale(8),
    // width:"100%",
    flexWrap: 'wrap',
    marginBottom: verticalScale(5),
    // flex:1
  },
  optionCard: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: scale(8),
    paddingVertical: verticalScale(10),
    minWidth: '30%',
    // flex:1,
    // maxWidth:"80%",
    // flexWrap:"wrap",
  },
  optionCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(5),
    // justifyContent: 'space-between',
    // backgroundColor:"#c4c"
    // flex:1,
    flexGrow: 1,
  },
  optionCardLabel: {
    fontSize: moderateScale(12),
    // fontWeight: '400',
    // width:"70%",
    color: '#1A1A1A',
    // flex: 1,
  },
  radioButton: {
    width: moderateScale(16),
    height: moderateScale(16),
    borderRadius: 30,
    borderWidth:moderateScale(2),
    alignItems: 'center',
    justifyContent: 'center',
    // marginRight: scale(6),
    // backgroundColor:"#fff"
  },
  radioButtonInner: {
    width: moderateScale(8),
    height: moderateScale(8),
    borderRadius: 30,
    backgroundColor: '#3377ff',
  },
  radioContainer: {
    borderWidth: 0,
    backgroundColor: '',
    paddingHorizontal: scale(2.5),
    marginHorizontal: 0,
    marginBottom: 0,
    paddingVertical: 0,
    paddingTop: verticalScale(5),
    // backgroundColor: '#fff',
  },
  // No Data Styles
  noDataContainer: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingVertical: verticalScale(40),
    paddingHorizontal: scale(20),
  },
  noDataText: {
    fontSize: moderateScale(14),
    color: '#888',
    textAlign: 'center',
    fontFamily: fontFamily.regular,
  },
  errorText: {
    color: '#DC2626',
    fontSize: moderateScale(11),
    marginTop: verticalScale(0),
    marginBottom: verticalScale(5),
    fontFamily: fontFamily.medium,
  },
});
