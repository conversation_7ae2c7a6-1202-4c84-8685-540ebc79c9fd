import React, { useState } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import FastImage from 'react-native-fast-image';
import { Building2, Pencil } from 'lucide-react-native';
import { verticalScale, scale, moderateScale } from 'react-native-size-matters';
import ImagePreview from '../../../features/chat/components/common/ImagePreview';
import { ImagePickerModal } from '../../ImagePickerModal';

type EditableImagePickerProps = {
  label?: string;
  imageUri?: string;
  onChange: (uri: string) => void;
  placeholderIcon?: React.ReactNode;
};

const EditableImagePicker: React.FC<EditableImagePickerProps> = ({
  label = 'Image',
  imageUri,
  onChange,
  placeholderIcon,
}) => {
  const [isShowImageOptions, setIsShowImageOptions] = useState(false);
  const [isPreviewVisible, setIsPreviewVisible] = useState(false);

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={() => imageUri && setIsPreviewVisible(true)} activeOpacity={0.9}>
        {imageUri ? (
          <FastImage source={{ uri: imageUri }} style={styles.image} />
        ) : (
          <View style={styles.placeholder}>
            {placeholderIcon || <Building2 size={moderateScale(50)} color={'#bbb'} />}
          </View>
        )}
        <TouchableOpacity style={styles.editButton} onPress={() => setIsShowImageOptions(true)}>
          <Pencil size={16} color="#fff" />
        </TouchableOpacity>
      </TouchableOpacity>

      {/* Use the new modal */}
      <ImagePickerModal
        visible={isShowImageOptions}
        onClose={() => setIsShowImageOptions(false)}
        onChange={onChange}
        label={label}
        hasImage={!!imageUri}
      />

      {/* Preview */}
      <ImagePreview
        visible={isPreviewVisible}
        onClose={() => setIsPreviewVisible(false)}
        images={imageUri ? [{ uri: imageUri }] : []}
        senderDetails={{ senderName: label, createdTime: '' }}
      />
    </View>
  );
};

export default EditableImagePicker;

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: verticalScale(10),
  },
  image: {
    height: moderateScale(100),
    width: moderateScale(100),
    borderRadius: 60,
    backgroundColor: '#e5e5e5',
  },
  placeholder: {
    height: moderateScale(100),
    width: moderateScale(100),
    borderRadius: 60,
    backgroundColor: '#e5e5e5',
    alignItems: 'center',
    justifyContent: 'center',
  },
  editButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#2563EB',
    borderRadius: 999,
    padding: scale(6),
  },
});
