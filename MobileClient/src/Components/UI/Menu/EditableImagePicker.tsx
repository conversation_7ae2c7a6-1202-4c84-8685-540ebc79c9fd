import React, { useState } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import FastImage from 'react-native-fast-image';
import { Building2, Pencil } from 'lucide-react-native';
import { verticalScale, scale, moderateScale } from 'react-native-size-matters';
import { usePermissions } from '../../../Common/Permissions/ChatAttachPermissions';
import { cameraSelect, selectImages } from '../../../Stacks/ChatStack/ChatUtils/ImageUtils';
import ImagePreview from '../../../features/chat/components/common/ImagePreview';
import { ImagePickerModal } from '../../ImagePickerModal'; 
import { fontFamily } from '../../../Common/Theme/typography';

type EditableImagePickerProps = {
  label?: string;
  imageUri?: string;
  onChange: (uri: string) => void;
  placeholderIcon?: React.ReactNode;
};

const EditableImagePicker: React.FC<EditableImagePickerProps> = ({
  label = 'Image',
  imageUri,
  onChange,
  placeholderIcon,
}) => {
  const [isShowImageOptions, setIsShowImageOptions] = useState(false);
  const [isPreviewVisible, setIsPreviewVisible] = useState(false);
  const { cameraPermission, checkAndRequestPermissions } = usePermissions();

  const handleCamera = async () => {
    const granted = await checkAndRequestPermissions();
    if (!cameraPermission) {
      console.log('Camera permission not granted!');
      return;
    }
    setIsShowImageOptions(false);
    try {
      const assets: any = await cameraSelect('photo');
      if (assets?.length > 0) {
        onChange(assets[0].uri);
      }
    } catch (err) {
      console.log('Camera error:', err);
    }
  };

  const handleGallery = async () => {
    setIsShowImageOptions(false);
    const imageData = await selectImages({ selectionLimit: 1 });
    if (imageData?.length > 0) {
      onChange(imageData[0].uri);
    }
  };

  const handleRemove = () => {
    setIsShowImageOptions(false);
    onChange('');
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={() => imageUri && setIsPreviewVisible(true)} activeOpacity={0.9}>
        {imageUri ? (
          <FastImage source={{ uri: imageUri }} style={styles.image} />
        ) : (
          <View style={styles.placeholder}>
            {placeholderIcon || <Building2 size={moderateScale(50)} color={'#bbb'} />}
          </View>
        )}
        <TouchableOpacity style={styles.editButton} onPress={() => setIsShowImageOptions(true)}>
          <Pencil size={16} color="#fff" />
        </TouchableOpacity>
      </TouchableOpacity>

      {/* Use the new modal */}
      <ImagePickerModal
        visible={isShowImageOptions}
        onClose={() => setIsShowImageOptions(false)}
        onCameraPress={handleCamera}
        onGalleryPress={handleGallery}
        onRemovePress={handleRemove}
        label={label}
        hasImage={!!imageUri}
      />

      {/* Preview */}
      <ImagePreview
        visible={isPreviewVisible}
        onClose={() => setIsPreviewVisible(false)}
        images={imageUri ? [{ uri: imageUri }] : []}
        senderDetails={{ senderName: label }}
      />
    </View>
  );
};

export default EditableImagePicker;

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: verticalScale(10),
  },
  image: {
    height: moderateScale(100),
    width: moderateScale(100),
    borderRadius: 60,
    backgroundColor: '#e5e5e5',
  },
  placeholder: {
    height: moderateScale(100),
    width: moderateScale(100),
    borderRadius: 60,
    backgroundColor: '#e5e5e5',
    alignItems: 'center',
    justifyContent: 'center',
  },
  editButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#2563EB',
    borderRadius: 999,
    padding: scale(6),
  },
  backdrop: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  modalContainer: {
    backgroundColor: '#fff',
    paddingVertical: verticalScale(10),
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  modalTitle: {
    paddingVertical: verticalScale(10),
    fontSize: moderateScale(14),
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
    marginHorizontal: scale(16),
    fontFamily: fontFamily.semiBold,
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: verticalScale(15),
    paddingHorizontal: scale(16),
  },
  optionText: {
    fontSize: moderateScale(12),
    marginLeft: scale(10),
    color: '#000',
    fontFamily: fontFamily.medium,
  },
});
