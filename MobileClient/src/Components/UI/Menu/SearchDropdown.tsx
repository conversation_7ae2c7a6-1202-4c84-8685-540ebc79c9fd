// import React, { useState } from 'react';
// import {
//   View,
//   TouchableOpacity,
//   StyleSheet,
//   FlatList,
//   TextInput,
//   Modal,
//   KeyboardAvoidingView,
//   TouchableWithoutFeedback,
//   Keyboard,
//   Platform,
// } from 'react-native';
// import { Text, IconButton, useTheme } from 'react-native-paper';
// import { moderateScale, verticalScale } from 'react-native-size-matters';
// import { fontFamily } from '../../../Common/Theme/typography';

// interface ObjectType {
//   Text: string;
//   Value: string;
// }

// interface DropdownFieldProps<T> {
//   label: string;
//   value: string | null | ObjectType;
//   data: T[];
//   keyField: keyof T;
//   valueField: keyof T;
//   onSelect: (item: T) => void;
//   error?: boolean;
//   placeholder?: string;
//   icon?: string;
//   disable?: boolean;
//   searchable?: boolean;
//   paginated?: boolean;
//   onEndReached?: () => void;
//   onSearchTextChange?: (text: string) => void;
//   isLoading?: boolean;
//   hasMoreData?: boolean;
// }

// interface DropdownModalProps<T> {
//   visible: boolean;
//   data: T[];
//   onClose: () => void;
//   onSelect: (item: T) => void;
//   keyField: keyof T;
//   valueField: keyof T;
//   title: string;
//   value: string | null | ObjectType;
//   searchable?: boolean;
//   paginated?: boolean;
//   onSearch?: (text: string) => void;
//   onEndReached?: () => void;
//   isLoading?: boolean;
//   hasMoreData?: boolean;
// }

// function DropdownModal<T>({
//   visible,
//   data,
//   onClose,
//   onSelect,
//   keyField,
//   valueField,
//   title,
//   value,
//   searchable = false,
//   paginated = false,
//   onSearch,
//   onEndReached,
//   isLoading = false,
//   hasMoreData = true,
// }: DropdownModalProps<T>) {
//   const [searchInput, setSearchInput] = useState('');

//   const handleSearchChange = (text: string) => {
//     setSearchInput(text);
//     onSearch?.(text);
//   };

//   // ✅ Safe close function with keyboard dismissal
//   const handleClose = () => {
//     Keyboard.dismiss();
//     setTimeout(onClose, 100); // wait for keyboard animation to finish
//   };

//   return (
//     <Modal
//       visible={visible}
//       statusBarTranslucent={true}
//       transparent
//       animationType="none" // ← can switch to "slide" if you prefer
//       onRequestClose={handleClose}
//     >
//       <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
//         <View style={styles.modalBackground}>
//           <KeyboardAvoidingView
//             behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
//             style={styles.modalContainer}
//           >
//             <View style={styles.modalContent}>
//               <Text style={styles.modalTitle}>{title}</Text>

//               {searchable && (
//                 <View style={styles.searchWrapper}>
//                   <TextInput
//                     value={searchInput}
//                     onChangeText={handleSearchChange}
//                     placeholder="Search..."
//                     style={styles.searchInput}
//                   />
//                 </View>
//               )}

//               <FlatList
//                 data={data}
//                 keyExtractor={(item) => String(item[keyField])}
//                 renderItem={({ item }) => {
//                   const isSelected =
//                     typeof value === 'object' && value !== null && !Array.isArray(value)
//                       ? item[valueField] === (value as T)[valueField]
//                       : item[valueField] === value;

//                   return (
//                     <TouchableOpacity
//                       style={styles.itemWrapper}
//                       onPress={() => {
//                         onSelect(item);
//                         handleClose();
//                       }}
//                     >
//                       <Text
//                         style={[
//                           styles.itemText,
//                           isSelected && { fontWeight: 'bold', color: '#2563EB' },
//                         ]}
//                       >
//                         {String(item[valueField])}
//                       </Text>
//                     </TouchableOpacity>
//                   );
//                 }}
//                 onEndReachedThreshold={0.5}
//                 onEndReached={() => {
//                   if (paginated && onEndReached && !isLoading) {
//                     onEndReached();
//                   }
//                 }}
//                 ListFooterComponent={
//                   isLoading ? (
//                     <View style={styles.footer}>
//                       <Text style={styles.loadingText}>Loading more...</Text>
//                     </View>
//                   ) : !hasMoreData ? (
//                     <View style={styles.footer}>
//                       <Text style={styles.footerText}>No more data</Text>
//                     </View>
//                   ) : null
//                 }
//               />
//             </View>
//           </KeyboardAvoidingView>
//         </View>
//       </TouchableWithoutFeedback>
//     </Modal>
//   );
// }

// export function SearchDropdown<T>({
//   label,
//   value,
//   data,
//   keyField,
//   valueField,
//   onSelect,
//   error,
//   icon,
//   disable,
//   placeholder = 'Select',
//   enableSearch,
//   enablePagination,
//   onSearch,
//   onEndReached,
//   isLoading,
// }: DropdownFieldProps<T> & {
//   enableSearch?: boolean;
//   enablePagination?: boolean;
//   onSearch?: (text: string) => void;
//   onEndReached?: () => void;
//   isLoading?: boolean;
// }) {
//   const [visible, setVisible] = useState(false);
//   const theme = useTheme();

//   return (
//     <View style={{ flex: 1 }}>
//       <Text style={styles.label}>
//         {label}
//         <Text style={{ color: 'red', fontSize: moderateScale(14) }}> *</Text>
//       </Text>
//       <TouchableOpacity
//         disabled={disable}
//         onPress={() => setVisible(true)}
//         style={[
//           styles.dropdownButton,
//           error ? { borderColor: theme.colors.error } : null,
//           disable && { opacity: 0.5 },
//         ]}
//       >
//         <Text style={{ flex: 1, color: value ? '#000' : '#888' }}>
//           {typeof value === 'object' && value !== null
//             ? value[valueField as keyof ObjectType] ?? placeholder
//             : typeof value === 'string' && value
//             ? value
//             : placeholder}
//         </Text>
//         <IconButton
//           icon={icon || 'chevron-down'}
//           size={20}
//           iconColor="#888"
//           style={{ margin: 0 }}
//         />
//       </TouchableOpacity>

//       <DropdownModal
//         visible={visible}
//         data={data}
//         keyField={keyField}
//         valueField={valueField}
//         onClose={() => setVisible(false)}
//         onSelect={(item) => {
//           onSelect(item);
//           setVisible(false);
//         }}
//         title={label}
//         value={value}
//         searchable={enableSearch}
//         paginated={enablePagination}
//         onSearch={onSearch}
//         onEndReached={onEndReached}
//         isLoading={isLoading}
//       />
//     </View>
//   );
// }

// const styles = StyleSheet.create({
//   label: {
//     fontFamily: fontFamily.semiBold,
//     fontSize: moderateScale(14),
//     marginTop: verticalScale(10),
//     marginBottom: verticalScale(6.5),
//   },
//   dropdownButton: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     paddingHorizontal: 12,
//     height: 48,
//     borderRadius: 4,
//     borderWidth: 1,
//     borderColor: '#ccc',
//     backgroundColor: 'white',
//     marginBottom: 12,
//   },
//   modalBackground: {
//     flex: 1,
//     backgroundColor: 'rgba(0,0,0,0.5)',
//     justifyContent: 'flex-end',
//   },
//   modalContainer: {
//     width: '100%',
//   },
//   modalContent: {
//     backgroundColor: '#fff',
//     borderTopLeftRadius: 12,
//     borderTopRightRadius: 12,
//     maxHeight: verticalScale(460),
//     paddingBottom: 20,
//     paddingHorizontal: 12,
//   },
//   modalTitle: {
//     textAlign: 'center',
//     fontFamily: fontFamily.semiBold,
//     fontSize: moderateScale(14),
//     marginTop: verticalScale(10),
//     marginBottom: verticalScale(6.5),
//   },
//   searchWrapper: {
//     marginBottom: 8,
//   },
//   searchInput: {
//     height: 40,
//     borderColor: '#ccc',
//     borderWidth: 1,
//     borderRadius: 6,
//     paddingHorizontal: 10,
//   },
//   itemWrapper: {
//     padding: 20,
//     borderBottomWidth: 1,
//     borderColor: '#ccc',
//   },
//   itemText: {
//     color: '#000',
//   },
//   footer: {
//     paddingVertical: 12,
//     alignItems: 'center',
//   },
//   loadingText: {
//     color: '#666',
//   },
//   footerText: {
//     color: '#999',
//   },
// });

import React, { useEffect, useMemo, useState } from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Modal,
  FlatList,
  TextInput,
} from 'react-native';
import { Text, IconButton, useTheme } from 'react-native-paper';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { fontFamily } from '../../../Common/Theme/typography';
import { debounce } from 'lodash';
import SearchBar from '../../SearchInput';

function StringListModal({
  visible,
  data,
  onClose,
  onAdd,
  title = 'Add Item',
  onSelectClose,
}) {
  const [input, setInput] = useState('');
  const theme = useTheme();

  const debouncedSearch = useMemo(() => {
    return debounce((text: string) => {
      // debounced external search, if needed
    }, 300);
  }, []);

  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, []);

  const handleAdd = () => {
    if (input.trim()) {
      onAdd(input.trim());
      setInput('');
      onSelectClose?.();
    }
  };
    const filteredData = input.trim().length === 0
  ? data
  : data.filter((item:string) =>
      item.toLowerCase().includes(input.trim().toLowerCase())
    );

  const showCreateOption =
    input.trim().length > 0 && !data.includes(input.trim());

  return (
    <Modal visible={visible} animationType="slide" onRequestClose={onClose} transparent={true}>
      <TouchableOpacity style={{ height: '100%' }} onPress={onClose} activeOpacity={1}>
        <View
          style={{
            height: verticalScale(200),
            backgroundColor: 'rgba(0,0,0,0.5)',
          }}
        />
        <View
          style={{
            flex: 1,
            backgroundColor: 'white',
            borderTopLeftRadius: 8,
            borderTopRightRadius: 8,
            paddingBottom: 20,
          }}
        >
          <Text
            style={{
              textAlign: 'center',
              fontFamily: fontFamily.semiBold,
              fontSize: moderateScale(14),
              marginTop: verticalScale(10),
              marginBottom: verticalScale(6.5),
            }}
          >
            {title}
          </Text>
          <View style={{ paddingHorizontal: 12, paddingBottom: 8 }}>
            {/* <TextInput
              value={input}
              onChangeText={(text) => {
                setInput(text);
                debouncedSearch(text);
              }}
              placeholder={'Search or create tag'}
              style={{
                height: 40,
                borderColor: '#ccc',
                borderWidth: 1,
                borderRadius: 6,
                paddingHorizontal: 10,
              }}
              cursorColor={theme.colors.primary}
            /> */}
            <SearchBar
             value={input}
              onChangeText={(text) => {
                setInput(text);
                debouncedSearch(text);
              }}
              placeholder={'Search or create tag'}
            />
          </View>

          {showCreateOption && (
            <TouchableOpacity
              onPress={handleAdd}
              style={{
                paddingVertical: 10,
                paddingHorizontal: 20,
                borderBottomWidth: 1,
                borderColor: '#ccc',
              }}
            >
              <Text style={{ color: '#3377FF' }}>{`+ create "${input.trim()}"`}</Text>
            </TouchableOpacity>
          )}

          <FlatList
            data={filteredData}
            keyExtractor={(item, idx) => `${item}-${idx}`}
            contentContainerStyle={{ paddingBottom: verticalScale(50) }}
            bounces={false}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={{
                  padding: 20,
                  borderBottomWidth: 1,
                  borderColor: '#ccc',
                }}
                onPress={() => {
                  onAdd(item);
                  onSelectClose?.();
                }}
              >
                <Text style={styles.listItemText}>{item}</Text>
              </TouchableOpacity>
            )}
          />
        </View>
      </TouchableOpacity>
    </Modal>
  );
}
export function CustomStringDropdown({
  label,
  value,
  onSelect,
  data,
  placeholder = 'Select tags',
  showAsterisk = false,
}) {
  const [modalVisible, setModalVisible] = useState(false);

  const handleSelect = (item: string) => {
    onSelect(item);
    setModalVisible(false);
  };

  return (
    <View>
      {label && (
        <Text variant="semiBold" style={styles.label}>
          {label}
          {showAsterisk && (
            <Text style={{ color: 'red', fontSize: moderateScale(12) }}> *</Text>
          )}
        </Text>
      )}
      <TouchableOpacity
        onPress={() => setModalVisible(true)}
        style={styles.dropdownButton}
      >
        <Text
          style={{
            flex: 1,
            color: '#888',
            fontSize: moderateScale(12),
          }}
        >
          {placeholder}
        </Text>
        <IconButton
          icon={'plus'}
          size={20}
          iconColor="#888"
          style={{ margin: 0 }}
        />
      </TouchableOpacity>

      <StringListModal
        visible={modalVisible}
        data={data}
        onClose={() => setModalVisible(false)}
        onAdd={handleSelect}
        onSelectClose={() => setModalVisible(false)}
        title={label}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  label: {
    fontSize: moderateScale(12),
    marginVertical: verticalScale(5),
    color: '#1A1A1A',
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(12),
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#ccc',
    backgroundColor: 'white',
    marginBottom: verticalScale(5),
    height: verticalScale(28),
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalBody: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    padding: 16,
    maxHeight: '70%',
  },
  modalTitle: {
    fontSize: moderateScale(14),
    fontFamily: fontFamily.semiBold,
    textAlign: 'center',
    marginBottom: 12,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  inputField: {
    flex: 1,
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 10,
  },
  addButton: {
    marginLeft: 8,
    backgroundColor: '#3377FF',
    borderRadius: 6,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addButtonText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  listItem: {
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderColor: '#eee',
  },
  listItemText: {
    fontSize: 14,
    color: '#1A1A1A',
  },
});