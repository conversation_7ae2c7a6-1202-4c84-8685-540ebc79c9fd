import React, {useState, useEffect} from 'react';
import {
  View,

  TouchableOpacity,
  StyleSheet,
  Modal,
  FlatList,
  Platform,
} from 'react-native';
import {Text,Button, Checkbox, IconButton, useTheme} from 'react-native-paper';
import {moderateScale, scale, verticalScale} from 'react-native-size-matters';
import {fontFamily} from '../../../Common/Theme/typography';
import { CustomerType } from '../../../Stacks/SalesManagementStack/screens/CreateActivity/CreateActivityScreen';

interface DropdownFieldProps<T> {
  label: string;
  value: string | { first_name: string; account_user_id: string; }[] | CustomerType | any;
  data: T[];
  keyField: keyof T;
  valueField: keyof T;
  onSelect: (item: T | T[]) => void | any;
  error?: boolean;
  placeholder?: string;
  icon?: string;
  disable?: boolean;
  multiSelect?: boolean;
  initialSelected?: T[];
  isMandatory:boolean
}

interface DropdownModalProps<T> {
  visible: boolean;
  data: T[];
  onClose: () => void;
  onSelect: (item: T | T[]) => void  | any;
  keyField: keyof T;
  valueField: keyof T;
  title: string;
  multiSelect?: boolean;
  initialSelected?: T[];
  value:any
}

function DropdownModal<T>({
  visible,
  data,
  onClose,
  onSelect,
  keyField,
  valueField,
  title,
  multiSelect = false,
  initialSelected = [],
  value
}: DropdownModalProps<T>) {
  const [selectedItems, setSelectedItems] = useState<T[]>([]);

  useEffect(() => {
    setSelectedItems(value);
  }, [visible]);

  const toggleItem = (item: T) => {
    if (!multiSelect) {
      onSelect(item);
      onClose();
      return;
    }

    const exists = selectedItems.some(i => i[keyField] === item[keyField]);
    if (exists) {
      setSelectedItems(prev =>
        prev.filter(i => i[keyField] !== item[keyField]),
      );
    } else {
      setSelectedItems(prev => [...prev, item]);
    }
  };

  const isSelected = (item: T) =>
    selectedItems.some(i => i[keyField] === item[keyField]);

  return (
    <Modal visible={visible} animationType="slide" transparent>
      <TouchableOpacity
        style={styles.overlay}
        onPress={onClose}
        activeOpacity={1}>
        <View style={styles.modalContainer}>
          <Text style={styles.title}>{title}</Text>

          <FlatList
            data={data}
            bounces={false}
            keyExtractor={item => String(item[keyField])}
            renderItem={({item}) => (
              <TouchableOpacity
                style={styles.itemRow}
                onPress={() => toggleItem(item)}>
                {multiSelect && (
                  <Checkbox
                    status={isSelected(item) ? 'checked' : 'unchecked'}
                    onPress={() => toggleItem(item)}
                  />
                )}
                <Text>{String(item[valueField])}</Text>
              </TouchableOpacity>
            )}
          />

          {multiSelect && (
            <View style={styles.actionRow}>
              <Button
                mode="contained"
                style={{
                  flex: 1,
                  borderRadius: 4,
                  backgroundColor: 'rgba(239, 241, 246, 1)',
                }}
                textColor="rgba(0, 0, 0, 1)"
                onPress={onClose}>
                Cancel
              </Button>
              <Button
                mode="contained"
                style={{
                  flex: 1,
                  borderRadius: 4,
                  backgroundColor: 'rgba(51, 119, 255, 1)',
                }}
                textColor="rgba(255, 255, 255, 1)"
                onPress={() => {
                  onSelect(selectedItems);
                  onClose();
                }}>
                Done
              </Button>
            </View>
          )}
        </View>
      </TouchableOpacity>
    </Modal>
  );
}

export function DropdownMulti<T>({
  label,
  value,
  data,
  keyField,
  valueField,
  onSelect,
  error,
  icon,
  disable,
  placeholder = 'Select',
  multiSelect = false,
  initialSelected = [],
  isMandatory
}: DropdownFieldProps<T>) {
  const [visible, setVisible] = useState(false);
  const theme = useTheme();
  return (
    <View>
      <Text variant="semiBold" style={styles.label}>
        {label}
        {isMandatory && (
          <Text variant="semiBold" style={{ color: 'red', fontSize: moderateScale(14) }}>
            {' '}
            *
          </Text>
        )}
      </Text>

      {multiSelect && data.length !== 0 ? (
        <TouchableOpacity
          disabled={disable}
          onPress={() => setVisible(true)}
          style={[
            styles.multiSelectContainer,
            error ? { borderColor: theme.colors.error } : null,
            disable && { opacity: 0.5 },
            value.length !== 0 && {
              paddingHorizontal: verticalScale(5),
              paddingTop: verticalScale(5),
            },
          ]}
        >
          <View style={styles.selectedTags}>
            {Array.isArray(value) &&
              (value.length === 0 ? (
                <Text style={{ flex: 1, color: '#888', fontSize: moderateScale(12), }}>{placeholder}</Text>
              ) : (
                value.map((item) => {
                  const itemTyped = item as T;
                  return (
                    <View key={String(item[keyField])} style={styles.tag}>
                      <Text style={styles.tagText}variant="labelMedium">{String(item[valueField])}</Text>
                      <TouchableOpacity
                        onPress={() => {
                          const newValue = (value as T[]).filter(
                            (i) => i[keyField] !== itemTyped[keyField]
                          );
                          onSelect(newValue);
                        }}
                      >
                        <Text style={styles.tagRemove}variant="labelMedium">×</Text>
                      </TouchableOpacity>
                    </View>
                  );
                })
              ))}
          </View>
          <View style={styles.dropdownIcon}>
            <IconButton
              icon={icon || 'chevron-down'}
              size={20}
              iconColor="#888"
              style={{ margin: 0 }}
            />
          </View>
        </TouchableOpacity>
      ) : (
        <TouchableOpacity
          disabled={disable}
          onPress={() => setVisible(true)}
          style={[
            styles.dropdownButton,
            error ? { borderColor: theme.colors.error } : null,
            disable && { opacity: 0.5 },
          ]}
        >
          <Text
            style={{
              flex: 1,
              color:'#888',
              fontSize: moderateScale(12),
            }}
          >
            { placeholder}
          </Text>
          <IconButton
            icon={icon || 'chevron-down'}
            size={20}
            iconColor="#888"
            style={{ margin: 0 }}
          />
        </TouchableOpacity>
      )}

      <DropdownModal
        visible={visible}
        data={data}
        value={value}
        keyField={keyField}
        valueField={valueField}
        title={label}
        onClose={() => setVisible(false)}
        onSelect={(item) => {
          onSelect(item);
          setVisible(false);
        }}
        multiSelect={multiSelect}
        initialSelected={initialSelected}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  label: {
     fontSize: moderateScale(12),
    marginVertical: verticalScale(5),
    color: '#1A1A1A',
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(12),
    height: verticalScale(28),
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#ccc',
    backgroundColor: 'white',
    marginBottom: verticalScale(5),
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    maxHeight: '75%',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 24,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 12,
  },
  itemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#ddd',
    paddingTop: 12,
  },
  footerButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
  },
  footerText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#007AFF',
  },

  multiSelectContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    borderWidth: 1,
    borderRadius: 4,
    borderColor: '#ccc',
    backgroundColor: 'white',
    // paddingHorizontal: verticalScale(5),
    // paddingTop: verticalScale(5),
    paddingHorizontal:scale(12),
    marginBottom: verticalScale(5),
  },

  selectedTags: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },

  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EFF1F6',
    borderRadius: 15,
    paddingVertical: verticalScale(3),
    paddingHorizontal:scale(8),
    marginRight: scale(5),
    marginBottom: scale(5),
  },

  tagText: {
    marginHorizontal: scale(5),
    color: '#000',
    fontSize: moderateScale(11),
  },

  tagRemove: {
    color: '#999',
    fontWeight: 'bold',
    fontSize: moderateScale(13),
  },

  dropdownIcon: {
    justifyContent: 'center',
  },
  actionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    padding: 16,
    gap: scale(10),
    marginBottom: Platform.OS === 'ios' ? verticalScale(10) : 0,
  },
});
