import React, { useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { TextInput, Text } from 'react-native-paper';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { fontFamily } from '../../../Common/Theme/typography';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

const CustomInput = ({
  value,
  onChangeText,
  label,
  placeholder = '',
  leftIcon = false,
  rightIcon = false,
  required = false,
  editable = true,
  style = {},
  onRightIconPress = () => {},
  rightIconColor = '#2196F3',
  onFocus = () => {},
  onBlur = () => {},
  isMandatory = false,
  keyboardType = 'default',
  secureTextEntry = false,
  multiline = false,
  numberOfLines = 1,
  mode = 'outlined',
  error = false,
  errorText = '',
  disabled = false,
  maxLength = 70,
  autoCapitalize = 'sentences',
  autoCorrect = true,
  activeColor = '#3377FF',
  inactiveColor = '#CCC',
  units = '',
  ...otherProps
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = (e) => {
    setIsFocused(true);
    onFocus && onFocus(e);
  };

  const handleBlur = (e) => {
    setIsFocused(false);
    onBlur && onBlur(e);
  };

  const getOutlineStyle = () => {
    if (error) {
      return [styles.outline, styles.errorOutline];
    }
    if (isFocused) {
      return [styles.outline, { borderColor: activeColor, borderWidth: 2 }];
    }
    return [styles.outline, { borderColor: inactiveColor }];
  };

  const getInputStyle = () => {
    return [
      styles.input,
      isFocused && styles.focusedInput,
      style,
      disabled && { backgroundColor: '#e2e8f0' },
    ];
  };

  return (
    <View style={styles.container}>
      {label && (
        <Text style={styles.label} variant="semiBold">
          {label}
          {isMandatory && (
            <Text style={{ color: 'red' }} variant="semiBold">
              {' '}
              *
            </Text>
          )}
          {units && (
            <Text
              style={{
                fontSize: moderateScale(12),
                color: '#6B7280',
                marginLeft: 8,
                marginTop: 15,
              }}
              variant="semiBold"
            >
              {`   (${units})`}
            </Text>
          )}
        </Text>
      )}
      <TextInput
        // label={displayLabel}
        value={value}
        onChangeText={(text) => onChangeText(text)}
        placeholder={placeholder}
        placeholderTextColor="#9CA3AF"
        mode={'outlined'}
        editable={editable && !disabled}
        onFocus={handleFocus}
        onBlur={handleBlur}
        keyboardType={keyboardType}
        secureTextEntry={secureTextEntry}
        multiline={multiline}
        numberOfLines={numberOfLines}
        error={error}
        maxLength={maxLength}
        autoCapitalize={autoCapitalize}
        underlineColor="transparent"
        autoCorrect={autoCorrect}
        left={leftIcon ? <TextInput.Icon icon={leftIcon} color="#2196F3" size={20} /> : null}
        right={rightIcon ? <TextInput.Icon icon={rightIcon} color="#2196F3" /> : null}
        style={getInputStyle()}
        outlineStyle={getOutlineStyle()}
        contentStyle={styles.inputContent}
        theme={{
          colors: {
            primary: activeColor,
            outline: isFocused ? activeColor : inactiveColor,
            placeholder: '#9CA3AF',
          },
        }}
        {...otherProps}
      />
      {errorText ? <Text style={styles.errorText}>{errorText}</Text> : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // flex: 1,
  },
  input: {
    backgroundColor: '#fff',
    // marginVertical: 4,
    // borderRadius: moderateScale(5),
    height: verticalScale(28),
    

    // backgroundColor: '#FFF',
    marginBottom: verticalScale(5),
    width: '100%',
  },
  focusedInput: {
    backgroundColor: '#fff',
  },
  outline: {
    // borderColor:"#ccc"
    borderWidth: 1,
    borderColor: 'red',
    borderRadius: 4,
  },
  errorOutline: {
    borderColor: '#ff5722',
    borderWidth: 1,
  },
  inputContent: {
    // paddingLeft: 8,
    fontSize: moderateScale(12),
    color: '#000',
  },
  label: {
    fontSize: moderateScale(12),
    // fontWeight: '600',
    marginVertical: verticalScale(5),
    color: '#111827',
    // marginBottom: verticalScale(5),
  },
  errorText: {
    color: '#DC2626',
    fontSize: moderateScale(11),
    marginTop: verticalScale(0),
    marginBottom: verticalScale(5),
    fontFamily: fontFamily.medium,
  },
});

export default CustomInput;

// Usage Examples:

// 1. Location Input (like your original design)
/*
<CustomInput
  value={location}
  onChangeText={setLocation}
  label="From"
  placeholder="Country, City, Port, Airport"
  leftIcon="map-marker"
  required={true}
/>
*/

// 2. Email Input
/*
<CustomInput
  value={email}
  onChangeText={setEmail}
  label="Email"
  placeholder="Enter your email"
  leftIcon="email"
  keyboardType="email-address"
  autoCapitalize="none"
  required={true}
/>
*/

// 3. Password Input
/*
<CustomInput
  value={password}
  onChangeText={setPassword}
  label="Password"
  placeholder="Enter password"
  leftIcon="lock"
  rightIcon="eye"
  secureTextEntry={true}
  required={true}
/>
*/

// 4. Phone Input
/*
<CustomInput
  value={phone}
  onChangeText={setPhone}
  label="Phone Number"
  placeholder="Enter phone number"
  leftIcon="phone"
  keyboardType="phone-pad"
  required={true}
/>
*/

// 5. Search Input
/*
<CustomInput
  value={searchTerm}
  onChangeText={setSearchTerm}
  label="Search"
  placeholder="Search for products..."
  leftIcon="magnify"
  rightIcon="close"
/>
*/

// 6. Simple Text Input
/*
<CustomInput
  value={name}
  onChangeText={setName}
  label="Full Name"
  placeholder="Enter your name"
  required={true}
/>
*/
