import React, { useCallback, useEffect, useState } from 'react';
import { View, StatusBar, ActivityIndicator, Text, TouchableOpacity } from 'react-native';
import * as Animatable from 'react-native-animatable';
import { useSplashScreen } from './Splash.hooks';
import { SplashScreenProps } from './Splash.types';
import { createStyles } from './Splash.styles';
import { useTheme } from '../../../../Common/Theme/hooks/useTheme';
import { useFocusEffect } from '@react-navigation/native';
import UpgradeModal from '../../../../Stacks/TestStack/Modals/UpgradeModal';

const SplashScreen: React.FC<SplashScreenProps> = () => {
  const { initializeApp, loadingState, loadingMessage } = useSplashScreen();
  const { colors, isDark } = useTheme();
  const styles = createStyles(colors);

    useFocusEffect(
    useCallback(() => {
      initializeApp();
    }, [initializeApp])
  );

    const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  const handleUpgrade = () => {
    // Handle upgrade logic here
    console.log('User wants to upgrade!');
    // You might navigate to payment screen, call API, etc.
    setShowUpgradeModal(false);
  };

  const renderContent = () => {
    switch (loadingState) {
      case 'initializing':
        return (
          <Animatable.View
            animation="fadeInUp"
            duration={800}
            style={styles.logoContainer}
          >
            <Animatable.Image
              source={require('../../../../../assets/icons/wz-logo.png')}
              style={styles.logo}
              accessibilityLabel="App Logo"
              animation="pulse"
              iterationCount={2}
              duration={1000}
              direction="alternate"
            />
            <Animatable.Text
              animation="fadeIn"
              delay={500}
              duration={600}
              style={styles.versionText}
            >
              Version 1.0.1
            </Animatable.Text>
          </Animatable.View>
        );

      case 'loading':
        return (
          <Animatable.View
            animation="fadeIn"
            duration={500}
            style={styles.loadingContainer}
          >
            <Animatable.Image
              source={require('../../../../../assets/icons/wz-logo.png')}
              style={[styles.logo, styles.logoSmall]}
              accessibilityLabel="App Logo"
            />
            <View style={styles.loadingContent}>
              <ActivityIndicator 
                size="large" 
                color={colors.primary} 
                style={styles.loader}
              />
              {/* <Animatable.Text
                animation="pulse"
                iterationCount="infinite"
                duration={1500}
                style={styles.loadingText}
              >
                {loadingMessage}
              </Animatable.Text> */}
            </View>
          </Animatable.View>
        );

      case 'error':
        return (
          <Animatable.View
            animation="shake"
            duration={500}
            style={styles.errorContainer}
          >
            <Animatable.Image
              source={require('../../../../../assets/icons/wz-logo.png')}
              style={[styles.logo, styles.logoSmall]}
              accessibilityLabel="App Logo"
            />
            <View style={styles.errorContent}>
             
              <Text style={styles.errorText}>{loadingMessage}</Text>
            </View>
          </Animatable.View>
        );

      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor="transparent"
        translucent
      />
      {renderContent()}
    {/* <TouchableOpacity
        style={styles.button}
        onPress={() => setShowUpgradeModal(true)}
      >
        <Text style={styles.buttonText}>Show Upgrade Modal</Text>
      </TouchableOpacity> */}

      {/* Upgrade Modal */}
      <UpgradeModal
        visible={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        onUpgrade={handleUpgrade}
        title="Upgrade Required"
        description="Please update our app for an improved experience!. This version is no longer supported."
        buttonText="Upgrade Now"
      />

    </View>
  );
};

export default SplashScreen;