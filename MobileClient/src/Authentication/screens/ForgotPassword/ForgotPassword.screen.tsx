import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { ForgotPasswordProps, ForgotPasswordFormValues } from './ForgotPassword.types';
import { createStyles } from './ForgotPassword.styles';
import { forgotPasswordSchema } from './ForgotPassword.validation';
import { useForgotPassword } from './ForgotPassword.hooks';
import { useTheme } from '../../../Common/Theme/hooks/useTheme';

import Layout from '../../../components/Layout';
import FormContainer from '../../../components/FormContainer';
import FormInput from '../../../components/FormInput';
import Button from '../../../components/Button';

const ForgotPassword: React.FC<ForgotPasswordProps> = ({ navigation }) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const { 
    isLoading, 
    error, 
    isSuccess, 
    email, 
    handleForgotPassword, 
    navigateToChangePassword, 
    navigateToSignIn,
    setError 
  } = useForgotPassword(navigation);

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      setError(null);
    };
  }, []);

  return (
    <Layout>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <ScrollView 
          style={styles.container}
          contentContainerStyle={styles.contentContainer}
          keyboardShouldPersistTaps="handled"
          bounces={false}             
          
        >
          <View style={styles.headerContainer}>
            <TouchableOpacity 
              style={styles.backButton}
              onPress={navigateToSignIn}
              disabled={isLoading}
            >
              <Icon name="arrow-back" size={24} color={colors.text} />
            </TouchableOpacity>
            
            <Text style={styles.title}>Forgot Password</Text>
            <Text style={styles.subtitle}>
              Enter your email address and we'll send you a link to reset your password
            </Text>
          </View>
          
          <FormContainer<ForgotPasswordFormValues>
            defaultValues={{ email: '' }}
            onSubmit={handleForgotPassword}
            validationSchema={forgotPasswordSchema}
            style={styles.formContainer}
          >
            {({ control, handleSubmit, formState: { isValid } }) => (
              <>
                <FormInput
                  label="Email"
                  name="email"
                  control={control}
                  placeholder="Enter your email"
                  keyboardType="email-address"
                  isRequired
                  leftIcon={<Icon name="email" size={20} color={colors.icon} />}
                  disabled={isSuccess}
                />
                
                {error && <Text style={styles.errorText}>{error}</Text>}
                
                {isSuccess && (
                  <View style={styles.successContainer}>
                    <Text style={styles.successText}>
                      We have sent a password reset link to {email}. Please check your email and follow the instructions to reset your password.
                    </Text>
                  </View>
                )}
                
                <Button
                  title={isLoading ? "Sending..." : isSuccess ? "Resend Link" : "Send Reset Link"}
                  onPress={handleSubmit(handleForgotPassword)}
                  isLoading={isLoading}
                  disabled={isLoading || (!isSuccess && !isValid)}
                  buttonStyle={styles.resetButton}
                />
                
                {isSuccess && (
                  <Button
                    title="I Have a Code"
                    onPress={navigateToChangePassword}
                    variant="outline"
                    buttonStyle={{ marginTop: 12 }}
                  />
                )}
              </>
            )}
          </FormContainer>
          
          <TouchableOpacity 
            style={styles.backToLoginContainer}
            onPress={navigateToSignIn}
            disabled={isLoading}
          >
            <Text style={styles.backToLoginText}>Back to Login</Text>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </Layout>
  );
};

export default ForgotPassword;