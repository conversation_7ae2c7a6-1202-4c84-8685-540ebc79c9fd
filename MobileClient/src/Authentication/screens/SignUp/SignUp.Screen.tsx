// import React, {useState} from 'react';
// import {ScrollView, TouchableOpacity} from 'react-native';
// import {
//   TextInput,
//   Button,
//   Text,
//   Checkbox,
//   ProgressBar,
//   Surface,
// } from 'react-native-paper';
// import SafeContainerView from '../../../Components/SafeContainer';
// import { useTheme } from 'react-native-paper';
// const SignUpScreen: React.FC = () => {
//   const [secureTextEntry, setSecureTextEntry] = useState(true);
//   const [confirmSecureTextEntry, setConfirmSecureTextEntry] = useState(true);
//   const [agreeToTerms, setAgreeToTerms] = useState(false);
//   const theme = useTheme();

//   return (
//     <SafeContainerView backgroundColor={theme.colors.background}>
//       <Surface style={{flex: 1,  backgroundColor: theme.colors.background }} elevation={0}>
//         <ScrollView
//           contentContainerStyle={{
//             flexGrow: 1,
//             paddingHorizontal: 24,
//             paddingVertical: 32,
//           }}>
//           <Surface
//             style={{marginBottom: 32, elevation: 0, padding: 16}}
//             elevation={0}>
//             <Text variant="headlineLarge" style={{marginBottom: 8}}>
//               Create Account
//             </Text>
//             <Text variant="bodyMedium">Sign up to get started</Text>
//           </Surface>

//           <Surface style={{width: '100%'}} elevation={0}>
//             <Surface style={{marginBottom: 20}} elevation={0}>
//               <TextInput
//                 label="Full Name"
//                 mode="outlined"
//                 left={<TextInput.Icon icon="account" />}
//               />
//             </Surface>

//             <Surface style={{marginBottom: 20}} elevation={0}>
//               <TextInput
//                 label="Email Address"
//                 mode="outlined"
//                 keyboardType="email-address"
//                 autoCapitalize="none"
//                 left={<TextInput.Icon icon="email" />
               
//                 }
//               />
//             </Surface>

//             <Surface style={{marginBottom: 20}} elevation={0}>
//               <TextInput
//                 label="Password"
//                 mode="outlined"
//                 secureTextEntry={secureTextEntry}
//                 left={<TextInput.Icon icon="lock" />}
//                 right={
//                   <TextInput.Icon
//                     icon={secureTextEntry ? 'eye' : 'eye-off'}
//                     onPress={() => setSecureTextEntry(!secureTextEntry)}
//                   />
//                 }
//               />
//             </Surface>

//             <Surface style={{marginBottom: 20}} elevation={0}>
//               <TextInput
//                 label="Confirm Password"
//                 mode="outlined"
//                 secureTextEntry={confirmSecureTextEntry}
//                 left={<TextInput.Icon icon="lock-check" />}
//                 right={
//                   <TextInput.Icon
//                     icon={confirmSecureTextEntry ? 'eye' : 'eye-off'}
//                     onPress={() =>
//                       setConfirmSecureTextEntry(!confirmSecureTextEntry)
//                     }
//                   />
//                 }
//               />
//             </Surface>

//             <Surface
//               style={{
//                 flexDirection: 'row',
//                 alignItems: 'center',
//                 marginBottom: 24,
//               }}
//               elevation={0}>
//               <Checkbox
//                 status={agreeToTerms ? 'checked' : 'unchecked'}
//                 onPress={() => setAgreeToTerms(!agreeToTerms)}
//               />
//               <Text style={{marginLeft: 8}}>
//                 I agree to the{' '}
//                 <Text style={{fontWeight: 'bold'}}>Terms & Conditions</Text> and{' '}
//                 <Text style={{fontWeight: 'bold'}}>Privacy Policy</Text>
//               </Text>
//             </Surface>

//             <Button
//               mode="contained"
//               onPress={() => {}}
//               style={{borderRadius: 8, marginBottom: 24}}
//               contentStyle={{height: 50}}
//               labelStyle={{fontSize: 16, fontFamily: 'Montserrat-Regular'}}>
//               Create Account
//             </Button>

//             <Surface
//               style={{
//                 flexDirection: 'row',
//                 justifyContent: 'center',
//                 alignItems: 'center',
//               }}
//               elevation={0}>
//               <Text>Already have an account?</Text>
//               <TouchableOpacity>
//                 <Text style={{marginLeft: 4, fontWeight: 'bold'}}>Sign In</Text>
//               </TouchableOpacity>
//             </Surface>
//           </Surface>
//         </ScrollView>
//       </Surface>
//     </SafeContainerView>
//   );
// };

// export default SignUpScreen;


import React, {useState} from 'react';
import {ScrollView, TouchableOpacity, Platform} from 'react-native';
import {
  TextInput,
  Button,
  Text,
  Checkbox,
  Surface,
  
} from 'react-native-paper';
import SafeContainerView from '../../../Components/SafeContainer';
import {scale, verticalScale, moderateScale} from '../../../Utils/responsiveUtils';
import {useTheme} from '../../../Common/Theme/hooks/useTheme';
const SignUpScreen: React.FC = () => {
  const [secureTextEntry, setSecureTextEntry] = useState(true);
  const [confirmSecureTextEntry, setConfirmSecureTextEntry] = useState(true);
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const theme = useTheme();
  const {spacing, fontFamily, fontSize, shadow, borderRadius} = theme;

  return (
    <SafeContainerView backgroundColor={theme.colors.background}>
      <Surface style={{flex: 1, backgroundColor: theme.colors.background}} elevation={0}>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            paddingHorizontal: spacing.l,
            paddingVertical: spacing.xl,
            
          }}
          bounces={false}>
          <Surface
            style={{marginBottom: spacing.xl, elevation: 0, padding: spacing.m}}
            elevation={0}>
            <Text 
              variant="headlineLarge" 
              // style={{
              //   marginBottom: spacing.s,
              //   fontFamily: fontFamily.bold,
              //   fontSize: fontSize.title,
              // }}
              >
              Create Account
            </Text>
            <Text 
              // variant="bodyMedium"
              style={{
                fontFamily: fontFamily.regular,
                fontSize: fontSize.m,
              }}>
              Sign up to get started
            </Text>
          </Surface>

          <Surface style={{width: '100%'}} elevation={0}>
            <Surface style={{marginBottom: spacing.m}} elevation={0}>
              <TextInput
                label="Full Name"
                mode="outlined"
                style={{
                  fontSize: fontSize.m,
                  fontFamily: fontFamily.regular,
             
                 
                      height: verticalScale(48),
                }}              
                left={<TextInput.Icon icon="account" />}
              />
            </Surface>

            <Surface style={{marginBottom: spacing.m}} elevation={0}>
              <TextInput
                label="Email Address"
                mode="outlined"
                style={{
                  fontSize: fontSize.m,
                  fontFamily: fontFamily.regular,
                  height: verticalScale(48),
                }}
                keyboardType="email-address"
                autoCapitalize="none"
                left={<TextInput.Icon icon="email" />}
              />
            </Surface>

            <Surface style={{marginBottom: spacing.m}} elevation={0}>
              <TextInput
                label="Password"
                mode="outlined"
                style={{
                  fontSize: fontSize.m,
                  fontFamily: fontFamily.regular,
                  height: verticalScale(48),
                }}
                secureTextEntry={secureTextEntry}
                left={<TextInput.Icon icon="lock" />}
                right={
                  <TextInput.Icon
                    icon={secureTextEntry ? 'eye' : 'eye-off'}
                    onPress={() => setSecureTextEntry(!secureTextEntry)}
                  />
                }
              />
            </Surface>

            <Surface style={{marginBottom: spacing.m}} elevation={0}>
              <TextInput
                label="Confirm Password"
                mode="outlined"
                style={{
                  fontSize: fontSize.m,
                  fontFamily: fontFamily.regular,
                  height: verticalScale(48),
                }}
         
                secureTextEntry={confirmSecureTextEntry}
                left={<TextInput.Icon icon="lock-check" />}
                right={
                  <TextInput.Icon
                    icon={confirmSecureTextEntry ? 'eye' : 'eye-off'}
                    onPress={() =>
                      setConfirmSecureTextEntry(!confirmSecureTextEntry)
                    }
                  />
                }
              />
            </Surface>

            <Surface
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginBottom: spacing.l,
              }}
              elevation={0}>
              <Checkbox
                status={agreeToTerms ? 'checked' : 'unchecked'}
                onPress={() => setAgreeToTerms(!agreeToTerms)}
              />
              <Text style={{
                marginLeft: spacing.s,
                fontFamily: fontFamily.regular,
                fontSize: fontSize.m,
                
              }}>
                I agree to the{' '}
                <Text style={{fontFamily: fontFamily.bold}}>Terms & Conditions</Text> and{' '}
                <Text style={{fontFamily: fontFamily.bold}}>Privacy Policy</Text>
              </Text>
            </Surface>

            <Button
              mode="contained"
              onPress={() => {}}
              style={{
                borderRadius: borderRadius.m, 
                marginBottom: spacing.l,
                ...shadow,
                // paddingVertical: scale(4)
              }}
              // contentStyle={{ paddingVertical: scale(4)}}
              // labelStyle={{
              //   fontSize: fontSize.l, 
              //   fontFamily: fontFamily.regular,
               
              // }}
              // allowFontScaling = {true}
              >
              Create Account
            </Button>

            <Surface
              style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
              }}
              elevation={0}>
              <Text style={{
                fontFamily: fontFamily.regular,
                fontSize: fontSize.m,
              }}>
                Already have an account?
              </Text>
              <TouchableOpacity>
                <Text style={{
                  marginLeft: spacing.xs, 
                  fontFamily: fontFamily.bold,
                  fontSize: fontSize.m,
                }}>
                  Sign In
                </Text>
              </TouchableOpacity>
            </Surface>
          </Surface>
        </ScrollView>
      </Surface>
    </SafeContainerView>
  );
};

export default SignUpScreen;