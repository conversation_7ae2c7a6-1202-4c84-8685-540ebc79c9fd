import React, { useEffect } from 'react';
import {
  View,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TouchableOpacity,
  Pressable,
  StatusBar,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { SignInProps, SignInFormValues } from './SignIn.types';
import { createStyles } from './SignIn.styles';
import { signInSchema } from './SignIn.validation';
import { useSignIn } from './SignIn.hooks';
import { extractSubdomain } from '../../services/authApi';
import SafeContainerView from '../../../Components/SafeContainer/index';
import FormContainer from '../../../Components/FormContainer';
import { Controller } from 'react-hook-form';

// React Native Paper components
import { Text, TextInput, Button, TouchableRipple, Surface, useTheme } from 'react-native-paper';

// Import SSO icons
import {
  GoogleIcon,
  FacebookIcon,
  AmazonCognitoIcon,
  SSOIcon,
} from '../../components/SocialLoginButtons/LoginIcons';

const SignIn: React.FC<SignInProps> = ({ navigation, route }) => {
  const theme = useTheme();
  const styles = createStyles(theme.colors);
  // const authSchemeFromRoute = route.params?.authScheme;

  const {
    isLoading,
    error,
    authScheme,
    serverUrl,
    handleSignIn,
    handleSSOLogin,
    navigateToForgotPassword,
    navigateToSignUp,
    navigateToServerUrlScreen,
    setError,
  } = useSignIn(navigation);
  // console.log("authScheme", JSON.stringify(authScheme, null, 2))
  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      setError(null);
    };
  }, []);

  // Get SSO options from auth scheme
  const ssoOptions = authScheme?.data?.domainEntrypointInfo?.signinScheme?.integrations || [];
  const showEmailPasswordForm =
    !ssoOptions.length ||
    ssoOptions.some((option: any) => option.integrationName === 'AWS COGNITO');

  const subdomain = serverUrl ? extractSubdomain(serverUrl) : '';

  // Render the SSO button based on provider
  const renderSSOButton = (integration: any) => {
    // console.log("integration",integration)
    const { identityProvider, integrationName } = integration;

    // Determine which icon and styles to use
    let icon;
    let buttonStyle = {};
    let textStyle = {};

    if (
      identityProvider === 'microsoft-voltuswave' ||
      identityProvider.toLowerCase().includes('microsoft')
    ) {
      icon = <SSOIcon size={24} />;
      buttonStyle = styles.microsoftButton;
      textStyle = styles.microsoftButtonText;
    } else if (identityProvider === 'Google' || identityProvider.toLowerCase().includes('google')) {
      icon = <GoogleIcon size={24} />;
      buttonStyle = styles.googleButton;
      textStyle = styles.googleButtonText;
    } else if (
      identityProvider === 'Facebook' ||
      identityProvider.toLowerCase().includes('facebook')
    ) {
      icon = <FacebookIcon size={24} />;
      buttonStyle = styles.facebookButton;
      textStyle = styles.facebookButtonText;
    } else {
      // Default for AWS Cognito or unknown providers
      icon = <AmazonCognitoIcon size={24} color={theme.colors.primary} />;
      buttonStyle = styles.cognitoButton;
      textStyle = styles.cognitoButtonText;
    }

    return (
      <Button
        key={integrationName}
        mode="outlined"
        style={[styles.ssoButton]}
        contentStyle={{ justifyContent: 'flex-start', paddingVertical: 5 }}
        icon={() => <View style={styles.ssoButtonIcon}>{icon}</View>}
        onPress={() => handleSSOLogin(integration)}
        disabled={isLoading}
        labelStyle={[styles.ssoButtonText]}
      >
        {integrationName === 'SSO'
          ? 'Sign in with SSO'
          : integrationName === 'AWS COGNITO'
          ? 'Sign in with Email'
          : `Sign in with ${integrationName}`}
      </Button>
    );
  };
  // console.log('loading in sign in ', isLoading);
  // Main sign-in screen
  return (
    <SafeContainerView>
      <View style={styles.container}>
        <StatusBar translucent={false} />
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.container}
        >
          <ScrollView
            style={styles.container}
            contentContainerStyle={styles.contentContainer}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            bounces={false}
          >
            <View style={styles.logoContainer}>
              {/* <Image
                source={require('../../../../assets/icons/worldzone_icon.png')}
                style={styles.logo}
                resizeMode="contain"
              /> */}
            </View>

            {/* Email/Password Form */}

            {/* {authScheme ?
             ( */}
            <>
              <Text
                variant="headlineMedium"
                style={[styles.subtitle, { color: theme.colors.onBackground }]}
              >
                Sign In
              </Text>
              {showEmailPasswordForm && (
                <FormContainer<SignInFormValues>
                  defaultValues={{ email: '', password: '' }}
                  onSubmit={handleSignIn}
                  validationSchema={signInSchema}
                  mode="onSubmit"
                  style={styles.formContainer}
                >
                  {({ control, handleSubmit, formState: { isValid, errors } }) => (
                    <>
                      <Controller
                        control={control}
                        name="email"
                        render={({ field: { onChange, onBlur, value } }) => (
                          <TextInput
                            label="Email"
                            value={value}
                            onChangeText={onChange}
                            onBlur={onBlur}
                            mode="outlined"
                            placeholder="Enter your email"
                            left={<TextInput.Icon icon="email" />}
                            error={!!errors.email}
                            style={styles.inputSpacing}
                            outlineStyle={{ borderRadius: 10 }}
                            contentStyle={{ paddingHorizontal: 16 }}
                            autoCapitalize="none"
                            // keyboardType="email-address"
                          />
                        )}
                      />
                      {errors.email && <Text style={styles.errorText}>{errors.email.message}</Text>}

                      <Controller
                        control={control}
                        name="password"
                        render={({ field: { onChange, onBlur, value } }) => {
                          const [passwordVisible, setPasswordVisible] = React.useState(false);
                          return (
                            <TextInput
                              label="Password"
                              value={value}
                              onChangeText={onChange}
                              onBlur={onBlur}
                              mode="outlined"
                              placeholder="Enter your password"
                              secureTextEntry={!passwordVisible}
                              left={<TextInput.Icon icon="lock" />}
                              autoCapitalize="none"
                              right={
                                <TextInput.Icon
                                  icon={passwordVisible ? 'eye-off' : 'eye'}
                                  onPress={() => setPasswordVisible(!passwordVisible)}
                                />
                              }
                              error={!!errors.password}
                              style={{ marginBottom: 8 }}
                              outlineStyle={{ borderRadius: 10 }}
                              contentStyle={{ paddingHorizontal: 16 }}
                            />
                          );
                        }}
                      />
                      {errors.password && (
                        <Text style={styles.errorText}>{errors.password.message}</Text>
                      )}

                      {/* <TouchableRipple
                          style={styles.forgotPasswordContainer}
                          onPress={navigateToForgotPassword}
                          disabled={isLoading}>
                          <Text
                            style={[
                              styles.forgotPasswordText,
                              {color: theme.colors.primary},
                            ]}>
                            Forgot Password?
                          </Text>
                        </TouchableRipple> */}
              

                      <Button
                        mode="contained"
                        onPress={handleSubmit(handleSignIn)}
                        loading={isLoading}
                        disabled={isLoading || !isValid}
                        style={[styles.signInButton, { borderRadius: 10, marginTop: 20 }]}
                        contentStyle={{ paddingVertical: 3 }}
                        labelStyle={{ fontSize: 16, fontWeight: '600' }}
                      >
              
                        {isLoading ? 'Signing In...' : 'Sign In'}
                      </Button>

                      {/* {error && (
                          <Text
                            style={[
                              styles.errorText,
                              {color: theme.colors.error},
                            ]}>
                            {error}
                          </Text>
                        )} */}
                    </>
                  )}
                </FormContainer>
              )}

              {/* Divider if we have both SSO and email form */}
              {/* {showEmailPasswordForm &&
                  ssoOptions.length > 0 &&
                  ssoOptions.some(
                    (option: any) => option.integrationName !== 'AWS COGNITO',
                  ) && (
                    <View style={styles.dividerContainer}>
                      <View style={styles.divider} />
                      <Text variant="bodyMedium" style={styles.dividerText}>
                        or
                      </Text>
                      <View style={styles.divider} />
                    </View>
                  )} */}

              {/* SSO Options */}
              {/* {ssoOptions.length > 0 && (
                  <View style={styles.ssoButtonsContainer}>
                    {ssoOptions
                      .filter(
                        (option: any) =>
                          option.integrationName !== 'AWS COGNITO',
                      )
                      .map(renderSSOButton)}
                  </View>
                )} */}
              {/* Footer */}
              {/* <View style={[styles.footerContainer, {marginTop: 20}]}>
                  <Text
                    variant="bodyMedium"
                    style={[
                      styles.footerText,
                      {color: theme.colors.onBackground},
                    ]}>
                    Don't have an account?
                  </Text>
                  <TouchableRipple
                    onPress={navigateToSignUp}
                    disabled={isLoading}>
                    <Text
                      style={[
                        styles.signUpText,
                        {
                          color: theme.colors.primary,
                          fontWeight: '600',
                          paddingHorizontal: 8,
                        },
                      ]}>
                      Sign Up
                    </Text>
                  </TouchableRipple>
                </View> */}
            </>
            {/* ) 
            : 
            (
              <>
                <Text
                  variant="headlineLarge"
                  style={[styles.title, {color: theme.colors.onBackground}]}>
                  Server Connection Required
                </Text>
                <Text
                  variant="titleMedium"
                  style={[
                    styles.subtitle,
                    {color: theme.colors.onBackground, opacity: 0.8},
                  ]}>
                  Please configure your server connection to continue
                </Text>
                <Button
                  mode="contained"
                  onPress={navigateToServerUrlScreen}
                  style={{marginTop: 40, borderRadius: 25, paddingVertical: 5}}
                  contentStyle={{paddingVertical: 5}}
                  labelStyle={{fontSize: 16, fontWeight: '600'}}>
                  Configure Server
                </Button>
              </>
            ) */}
          </ScrollView>
        </KeyboardAvoidingView>

        {/* Server URL Display */}
        {serverUrl && (
          <Surface
            style={[
              styles.serverUrlContainer,
              { backgroundColor: theme.colors.surfaceVariant, borderRadius: 20 },
            ]}
            elevation={1}
          >
            <Text
              variant="bodySmall"
              style={[styles.serverUrlText, { color: theme.colors.onSurfaceVariant }]}
            >
              Connected to: {subdomain}
            </Text>
            <TouchableRipple
              hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
              style={styles.serverUrlButton}
              onPress={navigateToServerUrlScreen}
            >
              <Icon name="edit" size={16} color={theme.colors.primary} />
            </TouchableRipple>
          </Surface>
        )}
      </View>
    </SafeContainerView>
  );
};

export default SignIn;
