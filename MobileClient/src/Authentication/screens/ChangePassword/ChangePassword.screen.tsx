import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { ChangePasswordProps, ChangePasswordFormValues } from './ChangePassword.types';
import { createStyles } from './ChangePassword.styles';
import { changePasswordSchema } from './ChangePassword.validation';
import { useChangePassword } from './ChangePassword.hooks';
import { useTheme } from '../../../Common/Theme/hooks/useTheme';

import Layout from '../../../components/Layout';
import FormContainer from '../../../components/FormContainer';
import FormInput from '../../../components/FormInput';
import Button from '../../../components/Button';

const ChangePassword: React.FC<ChangePasswordProps> = ({ navigation, route }) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const initialEmail = route.params?.email || '';
  const [password, setPassword] = useState('');
  
  const { 
    isLoading, 
    error, 
    email,
    handleChangePassword, 
    navigateToSignIn,
    navigateToForgotPassword,
    setError 
  } = useChangePassword(navigation, initialEmail);

  // Password validation states
  const [passwordValidation, setPasswordValidation] = useState({
    length: false,
    uppercase: false,
    lowercase: false,
    number: false,
    special: false,
  });

  // Update password validation on change
  const updatePasswordValidation = (value: string) => {
    setPassword(value);
    setPasswordValidation({
      length: value.length >= 8,
      uppercase: /[A-Z]/.test(value),
      lowercase: /[a-z]/.test(value),
      number: /[0-9]/.test(value),
      special: /[@$!%*?&]/.test(value),
    });
  };

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      setError(null);
    };
  }, []);

  return (
    <Layout>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <ScrollView 
          style={styles.container}
          contentContainerStyle={styles.contentContainer}
          keyboardShouldPersistTaps="handled"
          bounces={false}
        >
          <View style={styles.headerContainer}>
            <TouchableOpacity 
              style={styles.backButton}
              onPress={() => navigation.goBack()}
              disabled={isLoading}
            >
              <Icon name="arrow-back" size={24} color={colors.text} />
            </TouchableOpacity>
            
            <Text style={styles.title}>Reset Password</Text>
            <Text style={styles.subtitle}>
              Enter the verification code sent to your email and create a new password
            </Text>
          </View>
          
          <FormContainer<ChangePasswordFormValues>
            defaultValues={{ 
              email: email,
              verificationCode: '',
              newPassword: '',
              confirmPassword: ''
            }}
            onSubmit={handleChangePassword}
            validationSchema={changePasswordSchema}
            style={styles.formContainer}
          >
            {({ control, handleSubmit, formState: { isValid }, watch, setValue }) => {
              // Watch password field changes
              const watchPassword = watch('newPassword');
              if (watchPassword !== password) {
                updatePasswordValidation(watchPassword);
              }
              
              return (
                <>
                  <FormInput
                    label="Email"
                    name="email"
                    control={control}
                    placeholder="Enter your email"
                    keyboardType="email-address"
                    isRequired
                    leftIcon={<Icon name="email" size={20} color={colors.icon} />}
                    containerStyle={styles.inputSpacing}
                    defaultValue={email}
                  />
                  
                  <FormInput
                    label="Verification Code"
                    name="verificationCode"
                    control={control}
                    placeholder="Enter verification code"
                    keyboardType="number-pad"
                    isRequired
                    leftIcon={<Icon name="vpn-key" size={20} color={colors.icon} />}
                    containerStyle={styles.inputSpacing}
                  />
                  
                  <FormInput
                    label="New Password"
                    name="newPassword"
                    control={control}
                    placeholder="Enter new password"
                    secureTextEntry
                    isRequired
                    leftIcon={<Icon name="lock" size={20} color={colors.icon} />}
                    containerStyle={styles.inputSpacing}
                  />
                  
                  <View style={styles.passwordRulesContainer}>
                    <Text style={styles.passwordRulesTitle}>Password must contain:</Text>
                    <View style={styles.passwordRule}>
                      <Icon 
                        name={passwordValidation.length ? "check-circle" : "cancel"} 
                        size={16} 
                        color={passwordValidation.length ? colors.success : colors.error} 
                      />
                      <Text style={styles.passwordRuleText}>At least 8 characters</Text>
                    </View>
                    <View style={styles.passwordRule}>
                      <Icon 
                        name={passwordValidation.uppercase ? "check-circle" : "cancel"} 
                        size={16} 
                        color={passwordValidation.uppercase ? colors.success : colors.error} 
                      />
                      <Text style={styles.passwordRuleText}>At least one uppercase letter</Text>
                    </View>
                    <View style={styles.passwordRule}>
                      <Icon 
                        name={passwordValidation.lowercase ? "check-circle" : "cancel"} 
                        size={16} 
                        color={passwordValidation.lowercase ? colors.success : colors.error} 
                      />
                      <Text style={styles.passwordRuleText}>At least one lowercase letter</Text>
                    </View>
                    <View style={styles.passwordRule}>
                      <Icon 
                        name={passwordValidation.number ? "check-circle" : "cancel"} 
                        size={16} 
                        color={passwordValidation.number ? colors.success : colors.error} 
                      />
                      <Text style={styles.passwordRuleText}>At least one number</Text>
                    </View>
                    <View style={styles.passwordRule}>
                      <Icon 
                        name={passwordValidation.special ? "check-circle" : "cancel"} 
                        size={16} 
                        color={passwordValidation.special ? colors.success : colors.error} 
                      />
                      <Text style={styles.passwordRuleText}>At least one special character (@$!%*?&)</Text>
                    </View>
                  </View>
                  
                  <FormInput
                    label="Confirm Password"
                    name="confirmPassword"
                    control={control}
                    placeholder="Confirm new password"
                    secureTextEntry
                    isRequired
                    leftIcon={<Icon name="lock" size={20} color={colors.icon} />}
                    containerStyle={styles.inputSpacing}
                  />
                  
                  {error && <Text style={styles.errorText}>{error}</Text>}
                  
                  <Button
                    title={isLoading ? "Resetting..." : "Reset Password"}
                    onPress={handleSubmit(handleChangePassword)}
                    isLoading={isLoading}
                    disabled={isLoading || !isValid}
                    buttonStyle={styles.resetButton}
                  />
                  
                  <TouchableOpacity 
                    style={styles.backToLoginContainer}
                    onPress={navigateToSignIn}
                    disabled={isLoading}
                  >
                    <Text style={styles.backToLoginText}>Back to Login</Text>
                  </TouchableOpacity>
                </>
              );
            }}
          </FormContainer>
        </ScrollView>
      </KeyboardAvoidingView>
    </Layout>
  );
};

export default ChangePassword;