import React, { useEffect, useState } from 'react';
import {
  View,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Pressable,
  SafeAreaView,
} from 'react-native';
import {DomainEntryProps, DomainEntryFormValues} from './DomainEntry.types';
import {createStyles} from './DomainEntry.styles';
import {domainEntrySchema} from './DomainEntry.validation';
import {useDomainEntry} from './DomainEntry.hooks';
import SafeContainerView from '../../../Components/SafeContainer/index';
import FormContainer from '../../../Components/FormContainer';
import {Controller} from 'react-hook-form';
import {verticalScale} from '../../../Utils/responsiveUtils';
import AsyncStorage from '@react-native-async-storage/async-storage';

import {
  Text,
  Button,
  TextInput,
  Menu,
  Surface,
  TouchableRipple,
  useTheme,
  IconButton,
} from 'react-native-paper';


const DomainEntry: React.FC<DomainEntryProps> = ({navigation}) => {
  const theme = useTheme();
  const styles = createStyles(theme.colors);
  const {isLoading, error, handleServerUrlSubmit, handleCancel, setError} =
    useDomainEntry(navigation);

  const domainOptions = [
    {label: 'voltuswave.com', value: 'voltuswave.com'},
    {label: 'voltusfreight.net', value: 'voltusfreight.net'},
    {label: 'shipwave.net', value: 'shipwave.net'},
    {label: 'voltusfreight.com', value: 'voltusfreight.com'},
  ];

  useEffect(() => {
    return () => {
      setError(null);
    };
  }, []);

  interface DropdownOption {
    label: string;
    value: string;
  }

  interface CustomDropdownProps {
    value: string;
    onValueChange: (value: string) => void;
    options: DropdownOption[];
    label: string;
  }

  const CustomDropdown = ({
    value,
    onValueChange,
    options,
    label,
  }: CustomDropdownProps) => {
    const [menuVisible, setMenuVisible] = useState(false);

    return (
      <View style={styles.dropdownContainer}>
        <Text
          variant="labelLarge"
          style={[styles.urlLabelText, {color: theme.colors.onBackground}]}>
          {label}
        </Text>
        <Menu
          visible={menuVisible}
          onDismiss={() => setMenuVisible(false)}
          anchor={
            <Pressable
              style={[styles.dropdownButton, {borderRadius: 8}]}
              // onPress={() => setMenuVisible(true)}
              >
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: 5,
                }}>
                <Text
                  style={[
                    styles.dropdownButtonText,
                    {color: theme.colors.onBackground},
                  ]}>
                  {value}
                </Text>
                {/* <IconButton
                  icon="menu-down"
                  size={20}
                  iconColor={theme.colors.onBackground}
                /> */}
              </View>
            </Pressable>
          }>
          <View style={{borderRadius: 8}}>
            {options.map((option: DropdownOption) => (
              <Menu.Item
                key={option.value}
                title={option.label}
                titleStyle={[
                  styles.optionText,
                  value === option.value && {
                    color: theme.colors.primary,
                    fontWeight: '600',
                  },
                ]}
                onPress={() => {
                  onValueChange(option.value);
                  setMenuVisible(false);
                }}
                leadingIcon={value === option.value ? 'check' : undefined}
              />
            ))}
          </View>
        </Menu>
      </View>
    );
  };

  return (
    <SafeAreaView style={{flex:1,  backgroundColor: theme.colors.background,}}>
    <SafeContainerView>
      <View style={styles.container}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.container}>
          <ScrollView
            style={styles.container}
            contentContainerStyle={styles.contentContainer}
            keyboardShouldPersistTaps="handled"
            bounces={false}
            showsVerticalScrollIndicator={false}>
            <View
              style={{alignItems: 'center', marginBottom: verticalScale(40)}}
             >
              {/* <Image
                source={require('../../../../assets/icons/worldzone_icon.png')}
                style={styles.logo}
                resizeMode="contain"
              /> */}
            </View>

            <Text
              variant="headlineMedium"
              style={[styles.title, {color: theme.colors.onBackground}]}>
              Connect to Your Server
            </Text>
            <Text
              variant="bodyLarge"
              style={[
                styles.description,
                {color: theme.colors.onBackground, opacity: 0.8},
              ]}>
              To log in, please provide your account Code.
              This is required to securely connect to your account.
            </Text>

            <FormContainer<DomainEntryFormValues>
              defaultValues={{
                accountId: '',
                domain: 'voltuswave.com',
                // domain: 'shipwave.net',  //pre-prod/
                // domain: 'voltusfreight.net',  //qa
                // domain:'voltusfreight.com', //prod
                serverUrl: '',
              }}
              onSubmit={values => {
                // Construct the complete URL from the accountId and domain
                const serverUrl = `https://${values.accountId}.${values.domain}`;
                handleServerUrlSubmit({serverUrl});
              }}
              validationSchema={domainEntrySchema}
              mode="onChange"
              style={styles.formContainer}>
              {({
                control,
                handleSubmit,
                formState: {isValid},
                setValue,
                watch,
              }) => {
                const selectedDomain = watch('domain') || 'voltuswave.com';   // need to change here for qa & prod

                return (
                  <>
                    <Controller
                      control={control}
                      name="accountId"
                      render={({
                        field: {onChange, onBlur, value},
                        fieldState: {error},
                      }) => (
                        <>
                          <TextInput
                            label="ACCOUNT CODE"
                            value={value}
                            onChangeText={onChange}
                            onBlur={onBlur}
                            mode="outlined"
                            placeholder="Enter your account Code"
                            error={!!error}
                            style={{
                              marginBottom: error ? 0 : verticalScale(16),
                              height: verticalScale(42),
                            }}
                            outlineStyle={{borderRadius: 10}}
                            contentStyle={{paddingHorizontal: 16}}
                            left={<TextInput.Icon icon="account" />}
                            autoCapitalize="none"
                          />
                          {error && (
                            <Text
                              style={[
                                styles.errorText,
                                {color: theme.colors.error},
                              ]}>
                              {error.message}
                            </Text>
                          )}
                        </>
                      )}
                    />

                    <CustomDropdown
                      label="DOMAIN"
                      value={selectedDomain}
                      onValueChange={(value: string) =>
                        setValue('domain', value, {shouldValidate: true})
                      }
                      options={domainOptions}
                    />

                    {error && (
                      <Text
                        style={[styles.errorText, {color: theme.colors.error}]}>
                        {error}
                      </Text>
                    )}

                    <View style={styles.buttonContainer}>
                      {/* <Button
                        mode="outlined"
                        onPress={handleCancel}
                        style={[styles.buttonStyle, {borderRadius: 25}]}
                        contentStyle={{paddingVertical: 8}}
                        labelStyle={{fontSize: 16, fontWeight: '600'}}
                        disabled={isLoading}>
                        Cancel
                      </Button> */}
                      <Button
                        mode="contained"
                        onPress={handleSubmit(values => {
                          // Construct the complete URL from the accountId and domain
                          AsyncStorage.setItem(
                            'domain',
                            values.domain || 'voltuswave.com',
                          );
                          const serverUrl = `https://${values.accountId}.${values.domain}`;
                          handleServerUrlSubmit({serverUrl});
                        })}
                        loading={isLoading}
                        disabled={isLoading || !isValid}
                        style={[
                          styles.buttonStyle,
                          {borderRadius: 10},
                        ]}
                        contentStyle={{paddingVertical: 4}}
                        labelStyle={{fontSize: 16, fontWeight: '600'}}>
                        {isLoading ? 'Connecting...' : 'Apply'}
                      </Button>
                    </View>
                  </>
                );
              }}
            </FormContainer>
          </ScrollView>
        </KeyboardAvoidingView>
      </View>
    </SafeContainerView>
    </SafeAreaView>
  );
};

export default DomainEntry;