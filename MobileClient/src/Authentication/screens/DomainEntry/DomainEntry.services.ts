import AsyncStorage from '@react-native-async-storage/async-storage';
import ApiClient from '../../../Common/API/ApiClient';
import { ENDPOINTS } from '../../../Common/API/ApiEndpoints';
import { DomainEntryFormValues, AuthSchemeResponse } from './DomainEntry.types';
import { extractSubdomain, formatServerUrl } from '../../services/authApi';

export const validateServerUrl = async (data: DomainEntryFormValues) => {
  const formattedUrl = formatServerUrl(data.serverUrl);
  const subDomain = extractSubdomain(formattedUrl);

  const accountPayload = {
    accountCode: subDomain?.trim(),
    schemaName: 'c1s1_billing',
  };

  const accountResponse = await ApiClient.post(ENDPOINTS.AUTH.ACCOUNT, accountPayload);
  // console.log("account response",accountResponse)
  const accountInfo = accountResponse.data?.data?.accountInfo;
  if (!accountInfo) {
    throw new Error('Account information not found.');
  }

  const {
    accountCode,
    accountId,
    entrypoint: entryPointId,
    domain: { domainName: domain } = {},
  } = accountInfo;
  console.log("account info",accountInfo)
  await storeAccountData({ formattedUrl, accountInfo });

  return {
    formattedUrl,
    accountInfo,
    authSchemePayload: {
      accountCode,
      domain,
      entryPointId,
      schemaName: 'c1s1_billing',
    },
  };
};

export const fetchAuthScheme = async (
  authSchemePayload: any
): Promise<AuthSchemeResponse | null> => {
  try {
    const response = await ApiClient.post(ENDPOINTS.AUTH.AUTH_SCHEME, authSchemePayload);
    return response.data as AuthSchemeResponse;
    // return Promise.reject();
  } catch (err) {
    
    console.log('AuthScheme fetch failed, continuing without it:', err);
    return null;
  }
};

export const storeAccountData = async ({
  formattedUrl,
  accountInfo,

}: {
  formattedUrl: string;
  accountInfo: any;
 
}) => {
  const { accountCode, accountId, domain: { domainName: domain } = {} } = accountInfo;

  await AsyncStorage.multiSet([
    ['accountInfo', JSON.stringify(accountInfo)],
    ['accountCode', accountCode],
    ['accountId', accountId],
    // ['domain', domain],
    ['formattedUrl', formattedUrl],
    
  ]);
};
