import { createSlice, PayloadAction } from "@reduxjs/toolkit";

const initialState = {
  unitSystemRedux: 'SI',
};

const unitSystem = createSlice({
  name: 'unitSystem',
  initialState,
  reducers: {
    setUnitSystem: (state, action: PayloadAction<string>) => {
      state.unitSystemRedux = action.payload;
    },
    clearUnitSystem: () => initialState,
  },
});

export const { setUnitSystem } = unitSystem.actions;
export default unitSystem.reducer;