import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Dimensions,
  StyleSheet,
  Modal,
  SafeAreaView,
  TouchableOpacity,
  Text,
  StatusBar,
  Platform,
  BackHandler,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
  runOnJS,
  useDerivedValue,
} from 'react-native-reanimated';
import { GestureDetector, Gesture, GestureHandlerRootView } from 'react-native-gesture-handler';
import { useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { moderateScale } from 'react-native-size-matters';
import dayjs from 'dayjs';
import ImageViewing from 'react-native-image-viewing';

const { width, height } = Dimensions.get('window');
const clamp = (value: number, min: number, max: number): number => {
  'worklet';
  return Math.min(Math.max(value, min), max);
};
interface ImagePreviewProps {
  visible: boolean;
  onClose: () => void;
  images: string[] | { uri: string }[];
  initialIndex?: number;
  senderDetails: {
    senderName: string;
    createdTime: string;
  };
}

const ImagePreview: React.FC<ImagePreviewProps> = ({
  visible,
  onClose,
  images,
  initialIndex = 0,
  senderDetails,
}) => {
  const baseScale = useSharedValue(1);
  const pinchScale = useSharedValue(1);
  const scale = useDerivedValue(() => baseScale.value * pinchScale.value);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const savedTranslateX = useSharedValue(0);
  const savedTranslateY = useSharedValue(0);
  const theme = useTheme();
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [headerVisible, setHeaderVisible] = useState(true);

  const normalizedImages = images
    .filter((img) => img !== undefined && img !== null)
    .map((img) => (typeof img === 'string' ? img : img && img.uri ? img.uri : ''));

  const animatedImageStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { scale: scale.value },
    ],
  }));

  const pinchGesture = Gesture.Pinch()
    .onUpdate((e) => {
      const newScale = clamp(baseScale.value * e.scale, 1, 3);
      pinchScale.value = newScale / baseScale.value;

      const originX = e.focalX - width / 2;
      const originY = e.focalY - height / 2;

      translateX.value = clamp(
        savedTranslateX.value + originX * (pinchScale.value - 1),
        -((width * newScale - width) / 2),
        (width * newScale - width) / 2
      );

      translateY.value = clamp(
        savedTranslateY.value + originY * (pinchScale.value - 1),
        -((height * newScale - height) / 2),
        (height * newScale - height) / 2
      );
    })
    .onEnd(() => {
      baseScale.value = clamp(baseScale.value * pinchScale.value, 1, 3);
      pinchScale.value = 1;

      const scaledWidth = width * baseScale.value;
      const scaledHeight = height * baseScale.value;

      const maxX = (scaledWidth - width) / 2;
      const maxY = (scaledHeight - height) / 2;

      translateX.value = withSpring(clamp(translateX.value, -maxX, maxX));
      translateY.value = withSpring(clamp(translateY.value, -maxY, maxY));

      savedTranslateX.value = translateX.value;
      savedTranslateY.value = translateY.value;
    });

  const panGesture = Gesture.Pan()
    .onUpdate((e) => {
      if (scale.value > 1) {
        const scaledWidth = width * scale.value;
        const scaledHeight = height * scale.value;

        const maxX = (scaledWidth - width) / 2;
        const maxY = (scaledHeight - height) / 2;

        translateX.value = clamp(savedTranslateX.value + e.translationX, -maxX, maxX);
        translateY.value = clamp(savedTranslateY.value + e.translationY, -maxY, maxY);
      } else {
        translateY.value = e.translationY;
      }
    })
    .onEnd((e) => {
      if (scale.value > 1) {
        const scaledWidth = width * scale.value;
        const scaledHeight = height * scale.value;

        const maxX = (scaledWidth - width) / 2;
        const maxY = (scaledHeight - height) / 2;

        translateX.value = withSpring(clamp(translateX.value, -maxX, maxX));
        translateY.value = withSpring(clamp(translateY.value, -maxY, maxY));

        savedTranslateX.value = translateX.value;
        savedTranslateY.value = translateY.value;
      } else {
        if (e.translationY > 100) {
          runOnJS(onClose)();
        } else {
          translateY.value = withSpring(0);
        }

        // if (Math.abs(e.translationX) > 50 && Math.abs(e.velocityX) > 200) {
        //   if (e.translationX < 0 && currentIndex < normalizedImages.length - 1) {
        //     // swipe left → next image
        //     runOnJS(setCurrentIndex)(currentIndex + 1);
        //   } else if (e.translationX > 0 && currentIndex > 0) {
        //     // swipe right → prev image
        //     runOnJS(setCurrentIndex)(currentIndex - 1);
        //   }
        // } else if (e.translationY > 100) {
        //   // swipe down to close
        //   runOnJS(onClose)();
        // } else {
        //   // reset vertical drag
        //   translateY.value = withSpring(0);
        // }
      }
    });

  const doubleTapGesture = Gesture.Tap()
    .numberOfTaps(2)
    .onEnd(() => {
      if (scale.value > 1) {
        baseScale.value = withTiming(1);
        pinchScale.value = 1;
        translateX.value = withTiming(0);
        translateY.value = withTiming(0);
        savedTranslateX.value = 0;
        savedTranslateY.value = 0;
      } else {
        baseScale.value = withTiming(2);
        pinchScale.value = 1;

        translateX.value = withTiming(0);
        translateY.value = withTiming(0);

        savedTranslateX.value = 0;
        savedTranslateY.value = 0;
      }
    });

  const handlePrevImage = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      setHeaderVisible(true)
    }
  };
  const handleNextImage = () => {
    if (currentIndex < normalizedImages.length - 1) {
      setCurrentIndex(currentIndex + 1);
      setHeaderVisible(true)
    }
  };
  const toggleHeader = () => {
    setHeaderVisible((prev) => !prev);
  };

  useEffect(() => {
    if (visible) {
      setCurrentIndex(initialIndex);
      translateX.value = 0;
      translateY.value = 0;
    }
  }, [visible, initialIndex]);
  useEffect(() => {
    const onBackPress = () => {
      if (visible) {
        onClose();
        return true;
      }
      return false;
    };

    const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);
    return () => subscription.remove();
  }, [visible, onClose]);

  const singleTapGesture = Gesture.Tap()
    .numberOfTaps(1)
    .onEnd(() => {
      runOnJS(toggleHeader)();
    });
  const tapGesture = Gesture.Exclusive(doubleTapGesture, singleTapGesture);
    const composedGesture = Gesture.Simultaneous(
    Gesture.Simultaneous(panGesture, pinchGesture),
    tapGesture
  );
  const formattedTime = dayjs(senderDetails?.createdTime).format('MMM, DD hh:mm A');
  const imageSources = images
    .filter(Boolean)
    .map((img) =>
      typeof img === 'string'
        ? { uri: img }
        : img && typeof img === 'object' && img.uri
        ? { uri: img.uri }
        : null
    )
    .filter(Boolean) as { uri: string }[];
  const handleImageTap = useCallback(() => {
    setHeaderVisible((prev) => !prev);
  }, []);

  return (
    <Modal
      visible={visible}
      onRequestClose={onClose}
      hardwareAccelerated={true}
      transparent
      animationType="fade"
      presentationStyle="overFullScreen"
    >
      <StatusBar backgroundColor="black" barStyle="light-content" />
      <GestureHandlerRootView style={styles.overlay}>
          <SafeAreaView
            style={[
              styles.safeArea,
              {
                backgroundColor: '#000',
              },
            ]}
          >
        <GestureDetector gesture={composedGesture}>
            <Animated.View style={[styles.imageContainer, animatedImageStyle]}>
              <FastImage
                source={{ uri: normalizedImages[currentIndex] }}
                style={styles.image}
                resizeMode={FastImage.resizeMode.contain}
              />
            </Animated.View>
        </GestureDetector>

            {headerVisible && (
              <View style={styles.header} pointerEvents="box-none">
                <TouchableOpacity onPress={onClose} style={styles.headerBack}>
                  <Icon name="arrow-left" size={24} color="white" />
                </TouchableOpacity>
                <View style={styles.headerInfo}>
                  <Text style={styles.senderName} numberOfLines={1}>{senderDetails?.senderName || ''}</Text>
                  <Text style={styles.time}>{formattedTime || ''}</Text>
                </View>
              </View>
            )}

            {headerVisible && normalizedImages.length > 1 && (
              <View style={styles.navigationContainer} pointerEvents="box-none">
                {currentIndex > 0 && (
                  <TouchableOpacity
                    style={[styles.navButton, styles.prevButton]}
                    onPress={handlePrevImage}
                  >
                    <Icon
                      name="chevron-left"
                      size={moderateScale(36)}
                      color={theme.dark ? '#fff' : '#000'}
                    />
                  </TouchableOpacity>
                ) }

                {currentIndex < normalizedImages.length - 1 && (
                  <TouchableOpacity
                    style={[styles.navButton, styles.nextButton]}
                    onPress={handleNextImage}
                  >
                    <Icon
                      name="chevron-right"
                      size={moderateScale(36)}
                      color={theme.dark ? '#fff' : '#000'}
                    />
                  </TouchableOpacity>
                )}
              </View>
            )}
          </SafeAreaView>
      </GestureHandlerRootView>

      {/* <ImageViewing
        images={imageSources}
        imageIndex={initialIndex}
        visible={visible}
        onRequestClose={onClose}
        HeaderComponent={() =>
          headerVisible ? (
            <View style={styles.header}>
              <TouchableOpacity onPress={onClose} style={styles.headerBack}>
                <Icon name="arrow-left" size={24} color="white" />
              </TouchableOpacity>
              <View style={styles.headerInfo}>
                <Text style={styles.senderName}>{senderDetails?.senderName}</Text>
                <Text style={styles.time}>{formattedTime}</Text>
              </View>

            </View>
          ) : null
        }
        FooterComponent={() => (
          <View style={styles.arrowsContainer}>
            {currentIndex > 0 && (
              <TouchableOpacity
                style={styles.leftArrow}
                onPress={() => setCurrentIndex((prev) => prev - 1)}
              >
                <Icon name="chevron-left" size={40} color="white" />
              </TouchableOpacity>
            )}
            {currentIndex < imageSources.length - 1 && (
              <TouchableOpacity
                style={styles.rightArrow}
                onPress={() => setCurrentIndex((prev) => prev + 1)}
              >
                <Icon name="chevron-right" size={40} color="white" />
              </TouchableOpacity>
            )}
          </View>
        )}
        onImageIndexChange={setCurrentIndex}
        presentationStyle="overFullScreen"
        backgroundColor="black"
        swipeToCloseEnabled={true}
        doubleTapToZoomEnabled={true}
        animationType="fade"
      /> */}
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'black',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  safeArea: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    // paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  imageContainer: {
    width: width,
    height: height,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
    maxWidth: width,
    maxHeight: height,
  },
  controlsContainer: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : StatusBar.currentHeight || 0 + 10,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: moderateScale(16),
  },
  closeButton: {
    padding: moderateScale(10),
    borderRadius: moderateScale(20),
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  indexContainer: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: moderateScale(15),
    paddingHorizontal: moderateScale(10),
    paddingVertical: moderateScale(5),
  },
  indexText: {
    color: 'white',
    fontSize: moderateScale(14),
    fontFamily: 'Montserrat-Medium',
  },

  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: moderateScale(10),
    paddingTop:
      Platform.OS === 'ios' ? moderateScale(50) : moderateScale(0),
    paddingBottom: moderateScale(10),
    zIndex: 10,
  },
  headerBack: {
    paddingRight: moderateScale(10),
  },
  headerInfo: {
    flexDirection: 'column',
  },
  senderName: {
    color: 'white',
    fontSize: moderateScale(16),
    fontWeight: '600',
  },
  time: {
    color: 'lightgray',
    fontSize: moderateScale(12),
  },
  arrowsContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    pointerEvents: 'box-none',
  },
  leftArrow: {
    position: 'absolute',
    left: 20,
    top: '50%',
    marginTop: -20,
    zIndex: 10,
  },
  rightArrow: {
    position: 'absolute',
    right: 20,
    top: '50%',
    marginTop: -20,
    zIndex: 10,
  },
  navigationContainer: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  navButton: {
    position: 'absolute',
    top: '50%',
    marginTop: -moderateScale(20),
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: moderateScale(25),
    padding: moderateScale(10),
  },
  prevButton: {
    left: moderateScale(10),
  },
  nextButton: {
    right: moderateScale(10),
  },
});

export default ImagePreview;
