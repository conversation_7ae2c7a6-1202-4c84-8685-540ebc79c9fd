
import {View, Image, Animated, TouchableOpacity} from 'react-native';
import { moderateScale } from 'react-native-size-matters';
import React, {
  memo,
  forwardRef,
  useImperativeHandle,
  useCallback,
  useState,
} from 'react';
import {Text, useTheme} from 'react-native-paper';
import CopyFeedback from './CopyFeedback';
// Import themed styles utility
import {createThemedStyles} from '../../../utils/themedStyles';
import moment from 'moment';
// import {DoubleTicks} from '../../../assets/icons/ChatRoomSvg';
import AnimatedMessageStatus from '../AnimatedMessageStatus';
import {useSelector} from 'react-redux';
import Clipboard from '@react-native-clipboard/clipboard';
import {Menu} from 'react-native-paper';
import {showToast} from '../../../../../Components/AppToaster/AppToaster';
import * as Images from '../../../assets';
import EventTrackingMessage from './AdaptiveCard/EventTrackingMessage';
import FileMessage from './FileMessage';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import Swipeable from 'react-native-gesture-handler/Swipeable';
// We don't need reanimated imports for this component
// We don't need the Attachment type import here
import ReplyMessage from './ReplyMessage';
import FastImage from 'react-native-fast-image';
interface MessageProps {
  key: string;
  item: any;
  messages: any;
  setReplyOnSwipeOpen: any;
  routeData?: any;
}
const arePropsEqual = (prevProps: MessageProps, nextProps: MessageProps) => {
  return (
    prevProps.item.payload === nextProps.item.payload &&
    prevProps.item.created_date === nextProps.item.created_date &&
    prevProps.item.message_type === nextProps.item.message_type
  );
};

const Message = memo(
  forwardRef<any, MessageProps>(
    ({item, setReplyOnSwipeOpen, routeData}, ref) => {
      // Get Paper theme
      const theme = useTheme();
      // Create styles with theme
      const styles = createThemedStyles(theme);
      const userId = useSelector((state: any) => state.userId);
      const isSender = item.sender_id === userId;
      const isGroup = routeData?.room_details?.type === 'group' ? true : false;
      // Determine if message is fully read by checking if read_by_user_ids exists and contains entries
      // Also ensure current_users_count is valid and greater than 0
      const isFullyRead =
        item?.read_by_user_ids &&
        Array.isArray(item.read_by_user_ids) &&
        item.read_by_user_ids.length > 0 &&
        item?.current_users_count &&
        item.current_users_count > 0 &&
        item.read_by_user_ids.length >= item.current_users_count;
      // console.log("item in message",item)
      // Reference to the swipeable component
      const swipeRef = React.useRef<any>(null);

      // State for context menu and copy feedback
      const [menuVisible, setMenuVisible] = useState<boolean>(false);
      const [menuPosition, setMenuPosition] = useState({x: 0, y: 0});
      const [showCopyFeedback, setShowCopyFeedback] = useState<boolean>(false);

      // Swipe handlers

      useImperativeHandle(ref, () => ({
        close: () => swipeRef.current?.close(),
      }));

      // React.useEffect(() => {
      //   console.log("swipeRef.current",swipeRef.current)
      //   if (swipeRef.current) {

      //     updateRowRef(swipeRef.current, item);
      //   }
      // }, [swipeRef.current]);

      const onSwipeableOpen = () => {
        setTimeout(() => {
          setReplyOnSwipeOpen(item);
          swipeRef.current?.close();
        }, 0);

        console.log('Swipe Open Detected for:', item);
      };

      // Function to copy message text to clipboard with visual feedback
      const copyMessageToClipboard = () => {
        if (item.payload) {
          let textToCopy = item.payload;

          // If there are attachments, add information about them
          if (
            Array.isArray(item?.attachments) &&
            item?.attachments.length > 0
          ) {
            try {
              const attachmentInfo = item.attachments
                .map((attachment: string) => {
                  const parsedAttachment = JSON.parse(attachment);
                  return `\n[Attachment: ${parsedAttachment.name || 'File'}]`;
                })
                .join('');

              textToCopy += attachmentInfo;
            } catch (error) {
              console.log('Error parsing attachments:', error);
            }
          }

          // Copy to clipboard
          Clipboard.setString(textToCopy);

          // Show custom copy toast
          // showToast.copy('Message copied to clipboard');

          // Show copy feedback animation
          setMenuVisible(false);
          setShowCopyFeedback(true);
        }
      };

      // Handle long press on message
      const handleLongPress = (event: any) => {
        // Get the position of the touch event
        const {nativeEvent} = event;
        if (nativeEvent && nativeEvent.pageX && nativeEvent.pageY) {
          setMenuPosition({
            x: nativeEvent.pageX,
            y: nativeEvent.pageY,
          });
        }
        setMenuVisible(true);
      };

      const [isExpanded, setIsExpanded] = useState(false);

      const renderMessageContent = () => {
        if (item.message_type === 'adaptive-card') {
          return (
            <EventTrackingMessage
              payloadData={item.payload}
              // payloadData={''}
            />
          );
        }

        if (Array.isArray(item?.attachments) && item?.attachments.length > 0) {
          const senderName = item?.sender_details?.user_name;
          const createdTime = item.created_date;
          return (
            <FileMessage attachments={item.attachments} senderDetails={{senderName,createdTime}} >
              {renderTextWithReadMore(item.payload)}
            </FileMessage>
          );
        }

        return renderTextWithReadMore(item.payload);
      };

      const renderTextWithReadMore = (text: string) => {
        if (!text || text.length === 0) return null;

        // Check if text is likely to be more than 4 lines (rough estimate)
        const shouldTruncate = text.length > 150 && !isExpanded;

        return (
          <View style= {{paddingVertical : 5}}>
            <Text
              variant="regular"
              numberOfLines={shouldTruncate ? 4 : undefined}
              style={[
                isSender
                  ? {color: theme.colors.onPrimary}
                  : {color: theme.colors.onSurfaceVariant},
              ]}>
              {text}
            </Text>
            {shouldTruncate && (
              <TouchableOpacity onPress={() => setIsExpanded(true)}>
                <Text
                  variant="semiBold"
                  style={{
                    color: isSender
                      ? theme.colors.onPrimary
                      : theme.colors.primary,
                    marginTop: 4,
                    opacity: 0.8,
                    fontSize:moderateScale(11)
                    // fontWeight: 'bold',
                  }}>
                  Read more
                </Text>
              </TouchableOpacity>
            )}
          </View>
        );
      };

      const renderLeftActions = useCallback(
        (progress: Animated.AnimatedInterpolation<number>) => {
          const size = progress.interpolate({
            inputRange: [0, 0.5, 1],
            outputRange: [0.5, 0.8, 1],
            extrapolate: 'clamp',
          });

          const translateX = progress.interpolate({
            inputRange: [0, 0.5, 1],
            outputRange: [0, -10, -20],
            extrapolate: 'clamp',
          });

          const rotate = progress.interpolate({
            inputRange: [0, 0.5, 1],
            outputRange: ['0deg', '45deg', '0deg'],
            extrapolate: 'clamp',
          });

          const opacity = progress.interpolate({
            inputRange: [0, 0.5, 1],
            outputRange: [0, 0.7, 1],
            extrapolate: 'clamp',
          });

          return (
            <Animated.View
              style={{
                width: 80,
                transform: [{scale: size}, {translateX}, {rotate}],
                opacity,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <View
                style={{
                  width: 40,
                  height: 40,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: '#3B6FF6',
                  borderRadius: 20,
                  shadowColor: '#000',
                  shadowOffset: {width: 0, height: 3},
                  shadowOpacity: 0.2,
                  shadowRadius: 3,
                  // elevation: 5,
                }}>
                <FastImage
                  source={{
                    uri: 'https://cdn-icons-png.flaticon.com/128/6996/6996004.png',
                  }}
                  style={{width: 22, height: 22, tintColor: 'white'}}
                  resizeMode="contain"
                />
              </View>
            </Animated.View>
          );
        },
        [],
      );

      return (
        <GestureHandlerRootView>
          <Swipeable
            ref={swipeRef}
            friction={2}
            leftThreshold={30}
            renderLeftActions={renderLeftActions}
            onSwipeableOpen={onSwipeableOpen}
            // overshootFriction={5}
            overshootLeft={false}
          >
            <View
              style={[
                { flexDirection: 'row', transform: [{ translateX: 0 }] },
                isSender ? { alignSelf: 'flex-end' } : { alignSelf: 'flex-start' },
              ]}
            >
              {!isSender && <FastImage source={Images.User1} style={[styles.ProfileIconStyle]} />}

              <TouchableOpacity
                onLongPress={(event) => handleLongPress(event)}
                delayLongPress={500}
                activeOpacity={0.9}
                style={[
                  styles.messageContainer,
                  item.message_type === 'adaptive-card' ? { width: '85%' } : { maxWidth: '80%' },
                  isSender ? styles.sender : styles.receiver,
                  { padding: 8, position: 'relative' },
                ]}
              >
                <View style={{ overflow: 'hidden', borderRadius: moderateScale(8) }}>
                  {isGroup && (
                    <Text
                      variant="regular"
                      style={[
                        isSender
                          ? { color: theme.colors.onPrimary }
                          : { color: theme.colors.onSurfaceVariant },
                      ]}
                    >
                      {item?.sender_details?.user_name}
                    </Text>
                  )}

                  {item?.is_reply && (
                    <View style={{ marginBottom: 3 }}>
                      <ReplyMessage item={item} />
                    </View>
                  )}
                  {renderMessageContent()}
                  <View style={{ flexDirection: 'row', alignItems: 'center' ,alignSelf:'flex-end'}}>
                    <Text
                      variant="semiBold"
                      style={[
                        styles.timestamp,
                        isSender
                          ? { color: theme.colors.onPrimary, opacity: 0.8 }
                          : { color: theme.colors.onSurfaceVariant, opacity: 0.8 },
                      ]}
                    >
                      {moment(new Date(item.created_date)).format('HH:mm')}
                    </Text>
                    <Text>{isSender && <AnimatedMessageStatus isRead={isFullyRead} />}</Text>
                  </View>

                  {/* Copy feedback animation */}
                  <CopyFeedback
                    visible={showCopyFeedback}
                    onAnimationComplete={() => setShowCopyFeedback(false)}
                  />
                </View>
              </TouchableOpacity>

              {/* Enhanced Context Menu */}
              <Menu
                visible={menuVisible}
                onDismiss={() => setMenuVisible(false)}
                anchor={menuPosition}
                contentStyle={{
                  backgroundColor: theme.colors.surface,
                  borderRadius: 12,
                  elevation: 4,
                  shadowColor: theme.colors.shadow,
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.2,
                  shadowRadius: 8,
                  padding: 4,
                }}
              >
                <Menu.Item
                  onPress={copyMessageToClipboard}
                  title="Copy"
                  titleStyle={{ ...theme.fonts.labelMedium }}
                  leadingIcon="content-copy"
                  style={{ borderRadius: 8 }}
                  dense
                />
                <Menu.Item
                  onPress={() => {
                    setReplyOnSwipeOpen(item);
                    setMenuVisible(false);
                  }}
                  title="Reply"
                  titleStyle={{ ...theme.fonts.labelMedium }}
                  leadingIcon="reply"
                  style={{ borderRadius: 8 }}
                  dense
                />
              </Menu>

              {isSender && (
                <FastImage
                  source={Images.User2}
                  style={[
                    styles.ProfileIconStyle,
                    { marginRight: 0, marginLeft: moderateScale(3) },
                  ]}
                />
              )}
            </View>
          </Swipeable>
        </GestureHandlerRootView>
      );
    },
  ),
  arePropsEqual,
);

export default Message;
