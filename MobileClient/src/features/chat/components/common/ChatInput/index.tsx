import React, {useEffect, useState, useRef} from 'react';
import {
  View,
  TouchableOpacity,
  Alert,
  Image,
  Keyboard,
  ScrollView,
  SafeAreaView,
  TextInput,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import { Text,  useTheme, IconButton } from 'react-native-paper';
import {Categories} from 'react-native-emoji-selector';
import CustomEmojiSelector from '../EmojiSelector';
// Import themed styles utility
import { createThemedStyles } from '../../../utils/themedStyles';
import {AttachPinIcon} from '../../../assets/icons/ChatRoomSvg';
import { moderateScale} from 'react-native-size-matters';
import ImagePreviewModal from '../Attachment/ImagePreviewModal';
import ImagePreview from '../ImagePreview/ImagePreview';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  handleSelectImage,
  handleSelectCamera,
  handleSelectDoc,
  handleVideoCam,
} from '../../../hooks/useAttachments';
import AttachmentModal from '../../../components/attachment/AttachmentModal';
import {FileItem} from '../../../types/message.types';
import {AttachmentOption} from '../../../types/chatRoom.types';
import {ChatInputProps} from '../../../types/chatRoom.types';
import {fileOptions} from '../../../constants/attachmentOptions';
import {getFileIcon, formatFileSize} from '../../../utils/messageFormatters';
// Navigation imports removed as they're not used
import InputReplyMessage from '../ReplyMessage/InputReplyMessage';
import {usePermissions} from '../../../../../../src/Common/Permissions/ChatAttachPermissions';
import { Paperclip, Send, SendHorizonal } from 'lucide-react-native';

const ChatInput: React.FC<ChatInputProps> = ({
  message,
  onChangeText,
  onSendMessage,
  // onFocus and onBlur are not used but kept for API compatibility
  onFocus: _onFocus,
  onBlur: _onBlur,
  setImages,
  images,
  setFilesSelected,
  filesSelected,
  replyMessage,
  setReplyMessage,
  // onScroll is not used but kept for API compatibility
  onScroll: _onScroll,
  buttonDisable
}) => {
  const [selectedImages, setSelectedImages] = useState<{uri: string}[]>([]);
  const [imagePreviewModal, setImagePreviewModal] = useState(false);
  const [fileModalVisible, setFileModalVisible] = useState<boolean>(false);
  // Input container Y position is no longer needed
  const [showPreview, setShowPreview] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState<boolean>(false);
  const [onFocussed, setOnFocussed] = useState<boolean>(false);
  // Get Paper theme
  const theme = useTheme();
  // Create styles with theme
  const styles = createThemedStyles(theme);

  // Track keyboard visibility state
  const [isKeyboardVisible, setIsKeyboardVisible] = useState<boolean>(false);
  const inputRef = useRef<any>(null);
  // Styles are now created with the theme above
  const {cameraPermission, checkAndRequestPermissions} = usePermissions();

  function onPressScroll() {
    // if(onScroll){
    //   console.log("i am in press scroll")
    //   // onScroll()
    // }
  }

  useEffect(() => {
    checkAndRequestPermissions(); // Run on mount

    // Add keyboard listeners
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setIsKeyboardVisible(true);
        setShowEmojiPicker(false);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setIsKeyboardVisible(false);
      },
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  // This effect ensures that when emoji picker is shown, keyboard is dismissed
  useEffect(() => {
    if (showEmojiPicker) {
      Keyboard.dismiss();
    }
  }, [showEmojiPicker]);

  const handleFileSelect = async (option: AttachmentOption) => {
    console.log('Selected option:', option.name);

    if (option.name === 'Camera' && !cameraPermission) {
      Alert.alert(
        'Permission Required',
        'Please grant camera access to take photos.',
      );
      return;
    }

    if (option.name === 'Video' && !cameraPermission) {
      Alert.alert(
        'Permission Required',
        'Please grant camera and microphone access to record videos.',
      );
      return;
    }

    switch (option.name) {
      case 'Emoji':
        setFileModalVisible(false);
        setShowEmojiPicker(true);
        break;
      case 'Gallery':
        const imageResponse = await handleSelectImage();
        // Append new images to existing images instead of replacing them
        if (imageResponse && imageResponse.length > 0) {
          setImages([...images, ...imageResponse]);
          setSelectedImages([
            ...selectedImages,
            ...imageResponse.map(img => ({uri: img.uri})),
          ]);
        }
        break;
      case 'Camera':
        const cameraPhotoResponse = await handleSelectCamera();
        console.log('camera data', cameraPhotoResponse);
        // Append new camera photos to existing images instead of replacing them
        if (cameraPhotoResponse && Array.isArray(cameraPhotoResponse)) {
          setImages([...images, ...(cameraPhotoResponse as any[])]);
        }
        break;
      case 'Document':
        const docResponse = await handleSelectDoc();
        if (docResponse && Array.isArray(docResponse)) {
          const formattedResponse: FileItem[] = docResponse.map(
            (file: any) => ({
              name: file.name || '',
              size: file.size || 0,
              type: file.type || '',
              uri: file.uri,
              fileCopyUri: file.fileCopyUri || '',
            }),
          );
          // Append new files to existing files instead of replacing them
          setFilesSelected([...filesSelected, ...formattedResponse]);
        }
        break;
      case 'Video':
        const cameraVideoResponse = await handleVideoCam();
        console.log('cameraVideoResponse', cameraVideoResponse);
        setFileModalVisible(false);
        // Append new videos to existing images instead of replacing them
        if (cameraVideoResponse && Array.isArray(cameraVideoResponse)) {
          setImages([...images, ...(cameraVideoResponse as any[])]);
        }
        break;
      default:
        console.log('option selected', option.name);
    }
    setFileModalVisible(false);
  };

  const handleClosePreview = () => {
    setShowPreview(false);
  };

  // Function to remove image at specific index
  const removeImage = (indexToRemove: number) => {
    const updatedImages = images.filter((_, index) => index !== indexToRemove);
    setImages(updatedImages);
    
    const updatedSelectedImages = selectedImages.filter((_, index) => index !== indexToRemove);
    setSelectedImages(updatedSelectedImages);
  };

  // Function to remove file at specific index
  const removeFile = (indexToRemove: number) => {
    const updatedFiles = filesSelected.filter((_, index) => index !== indexToRemove);
    setFilesSelected(updatedFiles);
  };

  const renderFile = (file: FileItem, index: number) => {
    if(!file) return null
    const {icon, color} = getFileIcon(file?.type);
    const size = formatFileSize(file?.size);
    return (
      <View key={index} style={styles.fileWrapper}>
        <TouchableOpacity
          style={styles.fileDeleteButton}
          onPress={() => removeFile(index)}
          hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
          <Icon name="close" size={16} color={theme.colors.error} />
        </TouchableOpacity>
        <Icon name={icon} size={24} color={color} />
        <View style={styles.fileInfo}>
          <Text numberOfLines={1} style={styles.fileName}>
            {file.name}
          </Text>
          <Text style={styles.fileSize}>{size}</Text>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={{backgroundColor: theme.colors.surface,}} >
      {(Array.isArray(images) && images.length > 0) ||
      filesSelected.length > 0 ? (
        <View style={styles.chatcontainer}>
          {Array.isArray(images) && images.length > 0 && (
            <ScrollView 
              horizontal
              showsHorizontalScrollIndicator={false}
              bounces={false}
              style={styles.imageScrollContainer}
              contentContainerStyle={styles.imageGridContainer}>
              {images.map((image: {uri: string}, index: number) => (
                <View key={index} style={styles.imagePreviewWrapper}>
                  <TouchableOpacity
                    style={styles.imageDeleteButton}
                    onPress={() => removeImage(index)}
                    hitSlop={{top: 8, bottom: 8, left: 8, right: 8}}>
                    <Icon name="close-circle" size={20} color={theme.colors.error} />
                  </TouchableOpacity>
                  <FastImage source={{uri: image.uri}} style={styles.image} />
                  {/* {index === 3 && images.length > 4 && (
                    <View style={styles.remainingCountOverlay}>
                      <Text style={styles.remainingCountText}>
                        +{images.length - 4}
                      </Text>
                    </View>
                  )} */}
                </View>
              ))}
            </ScrollView>
          )}

          {filesSelected.length > 0 && (
            <ScrollView 
              horizontal
              showsHorizontalScrollIndicator={false}
              bounces={false}
              style={styles.fileScrollContainer}
              contentContainerStyle={styles.fileContainer}>
              {filesSelected.map((file, index) => renderFile(file, index))}
            </ScrollView>
          )}
        </View>
      ) : null}

      {replyMessage && (
        <InputReplyMessage
          message={replyMessage}
          clearReply={setReplyMessage}
        />
      )}

      <View style={styles.mainInputWrapper}>
        <View style={styles.inputContainer}>
          <View style={[styles.inputContainerMessage,{
            borderColor : onFocussed ? theme.colors.primary : theme.colors.surfaceVariant
          }]}>
            <TextInput
            
              autoFocus={false}
              style={styles.input}
              placeholder="Type a message"
              placeholderTextColor={theme.colors.onSurfaceVariant}
              value={message}
              onChangeText={onChangeText}
              onFocus={() => {
                onPressScroll();
                setShowEmojiPicker(false);
                setIsKeyboardVisible(true);
                setOnFocussed(true);
              }}
              onBlur={() => {
                onPressScroll()
                setOnFocussed(false);
              }}
              // placeholderTextColor={theme.colors.primary}
              multiline={true}
              numberOfLines={3}
              ref={inputRef}
              
             
            />
            <TouchableOpacity
              style={styles.attachmentButton}
              onPress={() => setFileModalVisible(true)}
              hitSlop={{
                top: moderateScale(15),
                bottom: moderateScale(15),
                left: moderateScale(15),
                right: moderateScale(15),
              }}>
              <Paperclip
              size={moderateScale(16)}
                color={theme.colors.primary}
              />
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={[
              styles.sendButton,
              buttonDisable
                ? styles.sendButtonInactive
                : message.trim() || filesSelected?.length > 0 || images?.length > 0
                ? styles.sendButtonActive
                : styles.sendButtonInactive,
            ]}
            onPress={() => {
              if (
                message.trim().length > 0 ||
                filesSelected?.length > 0 ||
                images?.length > 0
              ) {
                onSendMessage();
              }
            }}
            disabled={
              buttonDisable ||
              (message.trim().length === 0 && filesSelected?.length === 0 && images?.length === 0)
            }>
              {message.trim() || filesSelected?.length > 0 || images?.length > 0 ? (
                <SendHorizonal
                  size={moderateScale(18)}
                  color={theme.colors.onPrimary}
                />
              ) : (
                <Send
                  size={moderateScale(18)}
                  color={theme.colors.onPrimary}
                  // style={{ transform: [{ rotate: '-30deg' }] }}
                />
              )}
            {/* <Send
              size={moderateScale(16)}
              color={theme.colors.onPrimary}

            /> */}
          </TouchableOpacity>

          {selectedImages.length > 0 && (
            <ImagePreviewModal
              visible={imagePreviewModal}
              images={selectedImages}
              onClose={() => setImagePreviewModal(false)}
              message={message}
              onChangeText={onChangeText}
              onSendMessage={onSendMessage}
            />
          )}
          <ImagePreview
            visible={showPreview}
            onClose={handleClosePreview}
            images={images.map((img: {uri: string}) => img.uri)}
          />
        </View>
      </View>

      {/* Emoji picker rendered as a keyboard */}
      {showEmojiPicker && (
        <View style={styles.keyboardEmojiContainer}>
          {/* Removed X icon header */}
          <CustomEmojiSelector
            category={Categories.emotion}
            showSearchBar={false}
            onEmojiSelected={(emoji: string) => {
              // Insert emoji at cursor position or append to end
              const newText = message + emoji;
              onChangeText(newText);

              // Keep emoji picker open after selection
              // Don't focus input to avoid keyboard showing up
            }}
            showTabs={true}
            showHistory={true}
            columns={10}
            showSectionTitles={false}
          />
        </View>
      )}

      {/* Attachment Modal */}
      <AttachmentModal
        visible={fileModalVisible}
        onClose={() => setFileModalVisible(false)}
        onSelectOption={handleFileSelect}
        options={fileOptions}
      />
    </SafeAreaView>
  );
};

export default ChatInput;