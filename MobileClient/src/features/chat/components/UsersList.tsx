import React, { useState, useEffect } from 'react';
import { FlatList, StyleSheet, View, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Searchbar, Text, useTheme } from 'react-native-paper';
import { useQuery } from '@apollo/client';
import { GET_ACCOUNT_USERS } from '../api/queries';
import { moderateScale } from 'react-native-size-matters';
import UserListItem from './UserListItem';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useSelector } from 'react-redux';
import SearchBar from '../../../Components/SearchInput';

interface UsersListProps {
  selectedUsers: string[];
  setSelectedUsers: (users: string[]) => void;
}

const UsersList: React.FC<UsersListProps> = ({
  selectedUsers,
  setSelectedUsers,
}) => {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredUsers, setFilteredUsers] = useState<any[]>([]);
  const [accountId, setAccountId] = useState<string | null>('');

  const userId = useSelector((state: any) => state.userId);
  // Get account ID
  // console.log("user id in list", userId)
  useEffect(() => {
    const getAccountId = async () => {
      try {
        const accountIdResponse = await AsyncStorage.getItem('accountId');
        setAccountId(accountIdResponse);
      } catch (error) {
        console.log('Failed to fetch account ID:', error);
      }
    };

    getAccountId();
  }, []);

  // Fetch users from account
  const {data, loading, refetch} = useQuery(GET_ACCOUNT_USERS, {
    variables: {account_id: accountId},
    skip: !accountId,
  });
  // console.log("data of users", data)
  // Refetch when account ID changes
  useEffect(() => {
    if (accountId) {
      refetch();
    }
  }, [accountId, refetch]);

  // Filter users based on search query
  useEffect(() => {
    if (data?.GetAccountUsers) {
      // Keep the original structure but filter based on search query
      const filtered = data.GetAccountUsers.filter((user: any) => {
        // console.log("user data", userId !== user?.user_id)
        if(userId !== user?.user_id ){
          const userName = user.user_details?.user_name || '';

          return userName.toLowerCase().includes(searchQuery.toLowerCase());
        }
      
      });
      setFilteredUsers(filtered);
    }
  }, [data, searchQuery, userId]);

  const renderItem = ({item}: {item: any}) => {
    // Handle both direct user objects and objects with user_details
    const userId = item.user_details ? item.user_details.user_id : item.user_id;
    const userName = item.user_details
      ? item.user_details.user_name
      : item.user_name;

    const user = {
      user_id: userId,
      user_name: userName,
    };

    return (
      <TouchableOpacity style={styles.itemContainer}>
        <UserListItem
          user={user}
          isSelected={selectedUsers.includes(userId)}
          onPress={() => {
            if (selectedUsers.includes(userId)) {
              setSelectedUsers(selectedUsers.filter((id) => id !== userId));
            } else {
              setSelectedUsers([...selectedUsers, userId]);
            }
          }}
        />
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <SearchBar
        placeholder="Search users"
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchBar}
        inputStyle={styles.searchInput}
        // iconColor={theme.colors.primary}
      />

      {/* <Text variant="titleMedium" style={styles.sectionTitle}>
        Users in the group
      </Text> */}

      {loading ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
        </View>
      ) : (
        <FlatList
          data={filteredUsers}
          renderItem={renderItem}
          keyExtractor={item => item.user_id}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps = "always"
          bounces={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No users found</Text>
            </View>
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: moderateScale(16),
  },
  searchBar: {
    // marginBottom: moderateScale(16),
    borderRadius: moderateScale(8),
    elevation: 2,
  },
  searchInput: {
    // fontFamily: 'Montserrat-Regular',
    fontSize: moderateScale(14),
  },
  sectionTitle: {
    // fontFamily: 'Montserrat-SemiBold',
    marginBottom: moderateScale(8),
    marginLeft: moderateScale(4),
  },
  listContent: {
    paddingTop : moderateScale(7),
    paddingBottom: moderateScale(80),
    // Space for FAB
  },
  itemContainer: {
    marginVertical: moderateScale(2),
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: moderateScale(50),
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: moderateScale(50),
  },
  emptyText: {
    // fontFamily: 'Montserrat-Regular',
    fontSize: moderateScale(16),
    color: '#666',
  },
});

export default UsersList;
