import { View,  Modal, TouchableWithoutFeedback, FlatList, TouchableOpacity } from 'react-native'
import React from 'react'
import {styles} from './styles'
import { Text } from 'react-native-paper';
type ListModalProps = {
  visible: boolean;
  onClose: () => void;
  options: string[];
  onSelect: (option: string) => void;
};

const ListModal: React.FC<ListModalProps> = ({ visible, onClose, options, onSelect }) => {
  return (
    <Modal
      transparent={true}
      visible={visible}
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalBackground}>
          <View style={styles.menuContainer}>
            <FlatList
              data={options}
              keyExtractor={(item, index) => index.toString()}
              bounces={false}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.menuItem}
                  onPress={() => {
                    onSelect(item);
                    onClose();
                  }}
                >
                  <Text style={styles.menuItemText}>{item}</Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default ListModal;
                                                                                                                                       