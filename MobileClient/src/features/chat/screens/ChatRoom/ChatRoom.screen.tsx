import React,{useState} from 'react';
import {
  View,
  Text,
  FlatList,
  ImageBackground,
  KeyboardAvoidingView,
  Platform,
  RefreshControl,
} from 'react-native';
import {useTheme as usePaperTheme, ActivityIndicator} from 'react-native-paper';
import {useNavigation, NavigationProp} from '@react-navigation/native';

// Components
import ChatRoomHeader from '../../components/common/ChatHeader/index.paper';
import ChatInput from '../../components/common/ChatInput';
import Message from '../../components/common/MessageItem/ChatMessageItem';
import UploadLoader from '../../../../Components/Loader/ChatLoader/ChatInputLoader';
import SafeContainerView from '../../../../Components/SafeContainer/index';
import DateSeparator from './components/DateSeparator';
import ScrollToBottomButton from './components/ScrollToBottomButton';

// Hooks and Types
import {useChatRoom} from './hooks/useChatRoom';
import {ChatRoomProps, MessageType} from './ChatRoom.types';
import {RootStackParamList} from '../../../../Common/Routes/StackTypes';

// Styles
import {createStyles} from './ChatRoom.styles';

const ChatRoom: React.FC<ChatRoomProps> = ({route}) => {
  const {data: routeData} = route.params;
  // console.log('routeData', routeData);
  const styles = createStyles();
  const paperTheme = usePaperTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const {
    messageRef,
    messages,
    queryLoading,
    error,
    refreshing,
    uploading,
    progress,
    showScrollButton,
    newMessageReceived,
    messageInput,
    images,
    filesSelected,
    replyMessage,
    typingMessage,
    setMessageInput,
    setImages,
    setFilesSelected,
    setReplyMessage,
    createMessageHandler,
    handleScroll,
    scrollToBottom,
    handleListEndReached,
    handleRefresh,
    startTyping,
    stopTyping,
    renderFooter,
    buttonDisable,
  } = useChatRoom(routeData);
  const [inputContainerHeight, setInputContainerHeight] = useState(60);

  const renderMessage = ({item, index}: {item: MessageType; index: number}) => {
    const nextMessage = messages[index + 1];
    const currentDate = new Date(item.created_date).toDateString();
    const nextDate = nextMessage
      ? new Date(nextMessage.created_date).toDateString()
      : null;

    return (
      <>
        <Message
          key={item.message_id}
          item={item}
          messages={messages}
          setReplyOnSwipeOpen={setReplyMessage}
          routeData = {routeData}
        />
        {currentDate !== nextDate && <DateSeparator date={item.created_date} />}
      </>
    );
  };

  // if (error) {
  //   return (
  //     <Text style={{color: paperTheme.colors.error}}>
  //       Error: {error.message}
  //     </Text>
  //   );
  // }
  return (
    <SafeContainerView backgroundColor={paperTheme.colors.background} style={{ flex: 1 }}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
      >
        <ChatRoomHeader
          roomName={routeData?.room_details?.name || routeData?.user?.user_name}
          typingMessage={typingMessage}
          onBackPress={() => {
            // navigation.navigate('BottomTabStack', {
            //   screen: 'Chat',
            //   params: {
            //     screen: 'ChatRoomsListScreen',
            //   },
            // });
            navigation.goBack();
          }}
        />
        <ImageBackground
          // source={require('../../assets/chat-bg.png')}
          style={styles.backgroundImage}
        >
          <View
            style={{
              flex: 1,
              flexDirection: 'column',
              justifyContent: 'space-between',
            }}
          >
            {queryLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size={40} color={paperTheme.colors.primary} />
              </View>
            ) : error ? (
              <View style={styles.loadingContainer}>
                <Text style={{ color: paperTheme.colors.error }}>
                  Failed to fetch the messages.
                </Text>
              </View>
            ) : (
              <View style={{ flex: 1 }}>
                <FlatList
                  ref={messageRef}
                  data={messages}
                  keyExtractor={(item) => item.message_id}
                  renderItem={renderMessage}
                  contentContainerStyle={[
                    styles.messagesList,
                    messages.length === 0 && { flexGrow: 1 },
                  ]}
                  showsVerticalScrollIndicator={false}
                  inverted={true}
                  maxToRenderPerBatch={10}
                  windowSize={10}
                  removeClippedSubviews
                  initialNumToRender={10}
                  onEndReached={handleListEndReached}
                  onEndReachedThreshold={0.1}
                  ListFooterComponent={renderFooter}
                  // updateCellsBatchingPeriod={50}
                  // maintainVisibleContentPosition={{minIndexForVisible: 0}}
                  onScroll={handleScroll}
                  onScrollEndDrag={handleScroll}
                  onMomentumScrollEnd={handleScroll}
                  bounces={false}
                  refreshControl={
                    <RefreshControl
                      refreshing={refreshing}
                      onRefresh={handleRefresh}
                      colors={[paperTheme.colors.primary]}
                      tintColor={paperTheme.colors.primary}
                      progressViewOffset={-20}
                    />
                  }
                />
              </View>
            )}
            {showScrollButton && (
              <ScrollToBottomButton
                onPress={scrollToBottom}
                newMessageReceived={newMessageReceived}
                iconColor={paperTheme.colors.onPrimaryContainer}
                inputContainerHeight={inputContainerHeight}
              />
            )}
            <View
              style={{ width: '100%' }}
              onLayout={(event) => {
                const { height } = event.nativeEvent.layout;
                setInputContainerHeight(height);
              }}
            >
              <ChatInput
                message={messageInput}
                onChangeText={setMessageInput}
                onSendMessage={createMessageHandler}
                onFocus={startTyping}
                onBlur={stopTyping}
                setImages={setImages}
                images={images}
                filesSelected={filesSelected}
                setFilesSelected={setFilesSelected}
                setReplyMessage={setReplyMessage}
                replyMessage={replyMessage}
                buttonDisable={buttonDisable}
              />
            </View>
          </View>
        </ImageBackground>

        <UploadLoader visible={uploading} progress={progress} />
      </KeyboardAvoidingView>
    </SafeContainerView>
  );
};

export default ChatRoom;
