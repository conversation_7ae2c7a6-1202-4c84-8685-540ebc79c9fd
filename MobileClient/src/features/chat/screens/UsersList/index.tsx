import React, {useState, useEffect, useMemo, useCallback} from 'react';
import {View, FlatList, StyleSheet} from 'react-native';
import {useQuery, useMutation} from '@apollo/client';
import {useSelector} from 'react-redux';
import {useNavigation, useRoute} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  Appbar,
  Text,
  ActivityIndicator,
  useTheme,
  Searchbar,
  FAB,
  Switch,
} from 'react-native-paper';
import {moderateScale} from '../../../../../src/Utils/responsiveUtils';
import {GET_ACCOUNT_USERS} from '../../api/queries';
import {ADD_USERS_TO_GROUP} from '../../api/mutations';
import {showToast} from '../../../../../src/Components/AppToaster/AppToaster';
import SafeContainerView from '../../../../../src/Components/SafeContainer/index';
import UserListItem from '../../components/UserListItem';
import AppHeader from '../../components/AppHeader';
import SearchBar from '../../../../Components/SearchInput';

const UsersListScreen = () => {
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredUsers, setFilteredUsers] = useState<any[]>([]);
  const [accountId, setAccountId] = useState<string | null>('');
  const [isChatHistory, setIsChatHistory] = useState(false);
  const theme = useTheme();
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const currentUserId = useSelector((state: any) => state.userId);
  // Get roomId from Redux store - this is the correct way to access it
  const roomId = useSelector((state: any) => state.roomId);

  console.log('Current user ID:', currentUserId);
  console.log('Room ID from Redux:', roomId);

  // Get current users from route params
  const currentUsers = useMemo(() => {
    const users = route.params?.currentUsers || [];
    // console.log('Current users from params:', users);
    return users;
  }, [route.params?.currentUsers]);

  const existingUserIds = useMemo(() => {
    const ids = currentUsers.map((user: any) => user?.user_details?.user_id);
    console.log('Existing user IDs:', ids);
    return ids;
  }, [currentUsers]);

  // Get account ID
  useEffect(() => {
    const getAccountId = async () => {
      try {
        const accountIdResponse = await AsyncStorage.getItem('accountId');
        setAccountId(accountIdResponse);
      } catch (error) {
        console.log('Failed to fetch account ID:', error);
      }
    };

    getAccountId();
  }, []);

  // Fetch users from account
  const {data, loading, refetch} = useQuery(GET_ACCOUNT_USERS, {
    variables: {account_id: accountId},
    skip: !accountId,
  });

  // Add users to group mutation
  const [addUsersToGroup, {loading: addingUsers}] =
    useMutation(ADD_USERS_TO_GROUP);

  // Refetch when account ID changes
  useEffect(() => {
    if (accountId) {
      refetch();
    }
  }, [accountId, refetch]);

  // Filter users based on search query
  const filterUsers = useCallback(() => {
    if (data?.GetAccountUsers) {
      const filtered = data.GetAccountUsers.filter((user: any) => {
        const userName = user.user_details?.user_name || '';
        const userId = user.user_details?.user_id || '';

        // Filter out existing users and current user
        if (existingUserIds.includes(userId) || userId === currentUserId) {
          return false;
        }

        return userName.toLowerCase().includes(searchQuery.toLowerCase());
      });
      setFilteredUsers(filtered);
    }
  }, [data, searchQuery, existingUserIds, currentUserId]);

  // Apply filter when dependencies change
  useEffect(() => {
    filterUsers();
  }, [filterUsers]);

  const handleAddMembers = async () => {
    console.log('handleAddMembers called');
    console.log('Selected users:', selectedUsers);

    if (selectedUsers.length === 0) {
      console.log('No users selected');
      return;
    }

    const addUser = {
      added_by_user_id: currentUserId,
      room_id: roomId,
      user_ids: selectedUsers,
      chat_include_permission: isChatHistory,
    };

    console.log('Add user payload:', addUser);

    try {
      const response = await addUsersToGroup({variables: {input: addUser}});
      console.log('Add users response:', response);
      showToast.success('Users added successfully');
      navigation.goBack();
    } catch (error) {
      console.log('Error adding users to group:', error);
      showToast.error('Failed to add users');
    }
  };

  const renderItem = ({item}: {item: any}) => {
    // Handle both direct user objects and objects with user_details
    const userId = item.user_details ? item.user_details.user_id : item.user_id;
    const userName = item.user_details
      ? item.user_details.user_name
      : item.user_name;

    console.log('Rendering user item:', {userId, userName});

    // Skip rendering if this user is in the existing users list or is the current user
    if (existingUserIds.includes(userId) || userId === currentUserId) {
      console.log('Skipping user (existing or current):', userId);
      return null;
    }

    const user = {
      user_id: userId,
      user_name: userName,
    };

    const isSelected = selectedUsers.includes(userId);

    const toggleSelection = () => {
      console.log('Toggling selection for user:', userId);
      if (isSelected) {
        setSelectedUsers(selectedUsers.filter(id => id !== userId));
      } else {
        setSelectedUsers([...selectedUsers, userId]);
      }
    };

    return (
      <UserListItem
        user={user}
        isSelected={isSelected}
        onPress={toggleSelection}
      />
    );
  };

  return (
    <SafeContainerView backgroundColor={theme.colors.background}>
      <AppHeader title="Add Members" onBack={() => navigation.goBack()} />

      {filteredUsers?.length <= 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No users found</Text>
        </View>
      ) : (
        <View style={styles.container}>
          <SearchBar
            placeholder="Search users"
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
            inputStyle={styles.searchInput}
            // iconColor={theme.colors.primary}
          />

          {loading ? (
            <View style={styles.loaderContainer}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
            </View>
          ) : (
            <View style={{flex: 1}}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom:10,
                }}>
                <Text variant="semiBold">Include chat history?</Text>
                <Switch
                style={{transform: [{scaleX: 0.8}, {scaleY: 0.8}]}}
                  value={isChatHistory}
                  onValueChange={() => setIsChatHistory(!isChatHistory)}
                />
              </View>

              <FlatList
                data={filteredUsers.map(user => user.user_details).flat()}
                renderItem={renderItem}
                keyExtractor={item => item.user_id}
                contentContainerStyle={styles.listContent}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="always"
                bounces={false}
                ListEmptyComponent={
                  <View style={styles.emptyContainer}>
                    <Text style={styles.emptyText}>No users found</Text>
                  </View>
                }
              />
            </View>
          )}

        {selectedUsers.length > 0 && (
          <FAB
            style={[styles.fab, {backgroundColor: theme.colors.primary}]}
            icon="check"
            color='#fff'
            onPress={handleAddMembers}
            loading={addingUsers}
            disabled={addingUsers}
          />
        )}
      </View>
      )}
    </SafeContainerView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: moderateScale(16),
    paddingTop: moderateScale(8),
  },
  searchBar: {
    marginBottom: moderateScale(16),
    borderRadius: moderateScale(8),
    elevation: 2,
  },
  searchInput: {
    fontFamily: 'Montserrat-Regular',
    fontSize: moderateScale(14),
  },
  listContent: {
    paddingBottom: moderateScale(80),
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    // marginTop: moderateScale(50),
  },
  emptyText: {
    fontFamily: 'Montserrat-Regular',
    fontSize: moderateScale(16),
    color: '#666',
  },
  fab: {
    position: 'absolute',
    margin: moderateScale(16),
    right: moderateScale(16),
    bottom: moderateScale(16),
  },
});

export default UsersListScreen;
