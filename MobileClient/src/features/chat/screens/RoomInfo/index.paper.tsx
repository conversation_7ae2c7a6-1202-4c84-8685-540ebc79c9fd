import React from 'react';
import {
  View,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  RefreshControl,
} from 'react-native';
import {
  Avatar,
  Text,
  Chip,
  List,
  Divider,
  ActivityIndicator,
  useTheme,
  TouchableRipple,
} from 'react-native-paper';
import {moderateScale} from 'react-native-size-matters';
import {letterColors, textColors} from '../../constants/colors';
import ListModal from '../../components/modals/ListModal/index';
import {useRoomInfo} from '../../hooks/useRoomInfo';
import AppHeader from '../../components/AppHeader';

import SafeContainerView from '../../../../Components/SafeContainer/index';
import {styles} from './styles.paper';
import { UserPlus, Users2 } from 'lucide-react-native';

type UserDetails = {
  user_id: string;
  user_name: string;
};

type RoomUser = {
  user_details: UserDetails;
  room_role: 'Admin' | 'Member';
};

const RoomInfo: React.FC = () => {
  const theme = useTheme();
  const {
    data,
    error,
    loading,
    loginUserRole,
    showModal,
    setShowModal,
    modalOptions,
    selectTheUser,
    handleOptionSelect,
    userId,
    navigation,
    handleRefresh,
    refreshing,
  } = useRoomInfo();

  const renderCard = ({item}: {item: RoomUser}) => {
    // console.log('item data', item);
    const firstLetter = item?.user_details?.user_name
      ?.substring(0, 1)
      .toUpperCase();
    const secondLetter =
      item?.user_details?.user_name?.substring(1, 2).toUpperCase() || '';
    const initials = firstLetter + (secondLetter ? secondLetter : '');

    const backgroundColor = letterColors[firstLetter] || '#ccc';
    const textColor = textColors[firstLetter] || '#000';
    const canModifyUser =
      item?.user_details?.user_id && item?.user_details?.user_id !== userId;

    return (
      // <List.Item
      //   title={
      //     item?.user_details?.user_id === userId
      //       ? 'You'
      //       : item?.user_details?.user_name
      //   }
      //   titleStyle={styles.userName}
      //   onPress={() =>
      //     canModifyUser && loginUserRole === 'Admin' && selectTheUser(item)
      //   }
      //   disabled={!canModifyUser || loginUserRole !== 'Admin'}
      //   left={() => (
      //     <Avatar.Text
      //       size={moderateScale(35)}
      //       label={initials}
      //       style={[styles.avatar, {backgroundColor}]}
      //       labelStyle={[styles.avatarText, {color: textColor}]}
      //     />
      //   )}
      //   right={() =>
      //     item?.room_role === 'Admin' ? (
      //       <Chip
      //         mode="outlined"
      //         // style={styles.adminChip}
      //         // textStyle={styles.adminChipText}
      //       >
      //         Admin
      //       </Chip>
      //     ) : null
      //   }
      // />
      <TouchableOpacity
        style={styles.userItem}
        onPress={() => canModifyUser && loginUserRole === 'Admin' && selectTheUser(item)}
        disabled={!canModifyUser || loginUserRole !== 'Admin'}
        activeOpacity={canModifyUser && loginUserRole === 'Admin' ? 0.7 : 1}
      >
        <View style={styles.userRow}>
          {/* Avatar */}
          <View style={[styles.avatar, { backgroundColor }]}>
            <Text style={[styles.avatarText, { color: textColor }]} variant='semiBold'>{initials}</Text>
          </View>

          {/* User Info */}
          <View style={styles.userInfo}>
            <Text style={styles.userName} variant='medium'>
              {item?.user_details?.user_id === userId ? 'You' : item?.user_details?.user_name}
            </Text>
          </View>

          {/* Admin Badge */}
          {item?.room_role === 'Admin' && (
            <View style={styles.adminBadge}>
              <Text style={styles.adminBadgeText}>Admin</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeContainerView backgroundColor={theme.colors.background}>
      {/* <StatusBar
        backgroundColor={theme.colors.surface}
        barStyle={theme.dark ? 'light-content' : 'dark-content'}
      /> */}

      <AppHeader title="Group Info" onBack={() => navigation.goBack()} />

      <View style={styles.container}>
        {loading && <ActivityIndicator animating={true} style={styles.loader} />}
        {error && <Text style={styles.errorText}>Error fetching users: {error?.message}</Text>}

        {/* Use FlatList as the main scrollable container */}
        <FlatList
          data={data?.getRoomDetails?.associatedUsers || []}
          renderItem={renderCard}
          keyExtractor={(item) => item?.user_details?.user_id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContent}
          bounces={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
              // progressViewOffset={-20}
            />
          }
          ListHeaderComponent={() => (
            <>
              {/* Room Details */}
              <View style={styles.roomDetails}>
                {/* <Avatar.Icon
                  size={moderateScale(60)}
                  icon="account-group"
                  style={styles.roomAvatar}
                  color={theme.colors.onPrimary}
                /> */}
                <View style={styles.roomAvatar}>
                  <Users2 color={theme.colors.onPrimary} />
                </View>
                <Text variant="semiBold" style={styles.roomName}>
                  {data?.getRoomDetails?.name || 'Room Name'}
                </Text>
                <Text variant="regular" style={styles.roomMembersCount}>
                  {data?.getRoomDetails?.associatedUsers?.length || 0} members
                </Text>
              </View>

              {/* Add Members */}
              {loginUserRole === 'Admin' && (
                <TouchableRipple
                  onPress={() => {
                    navigation.navigate('UsersListScreen', {
                      currentUsers: data?.getRoomDetails?.associatedUsers,
                    });
                  }}
                  style={styles.addMemberContainer}
                >
                  <View style={styles.addMemberRow}>
                    {/* <Avatar.Icon
                      size={moderateScale(40)}
                      icon="account-plus"
                      style={styles.addMemberIcon}
                      color={theme.colors.onPrimary}
                    /> */}
                    <View style={styles.addMemberIcon}>
                      <UserPlus
                        
                        size={moderateScale(20)}
                        // style={styles.addMemberIcon}
                        color={theme.colors.onPrimary}
                      />
                    </View>
                    <Text style={styles.addMemberText} variant='semiBold'>Add members</Text>
                  </View>
                </TouchableRipple>
              )}

              {/* <Divider style={styles.divider} /> */}
              <Text variant="medium" style={styles.membersTitle}>
                Users in the group
              </Text>
            </>
          )}
        />

        <ListModal
          visible={showModal}
          onClose={() => setShowModal(false)}
          options={modalOptions}
          onSelect={handleOptionSelect}
        />
      </View>
    </SafeContainerView>
  );
};

export default RoomInfo;
