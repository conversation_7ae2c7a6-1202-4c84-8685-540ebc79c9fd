import React from 'react';
import { View, StyleSheet, FlatList } from 'react-native';
import { ActivityIndicator, Searchbar, useTheme, Text } from 'react-native-paper';
import { useNavigation, NavigationProp } from '@react-navigation/native';
import { useDispatch } from 'react-redux';

import AppHeader from '../../components/AppHeader';
import UserItem from './components/UserItem';
import { moderateScale } from 'react-native-size-matters';
import { RootStackParamList } from '../../../../Common/Routes/StackTypes';
import { useAccountUsers } from './hooks/useAccountUsers';
import { setRoomId } from '../../../../State/Slices/ChatSlices/RoomId';
import SearchBar from '../../../../Components/SearchInput';

const ContactListScreen: React.FC = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const {
    accountUsers,
    filteredContacts,
    accountUsersLoading,
    refetchAccountUsers,
    errorAccountUsers,
    searchContacts,
    setSearchContacts,
    getSearchData,
  } = useAccountUsers();

  const handleCreateGroup = (user: { user_id: string; user_name: string }) => {
    console.log('user', user);
    dispatch(setRoomId(''));
    navigation.replace('ChatStack', {
      screen: 'ChatRoom',
      params: {
        data: {
          checkGroupExist: true,
          user: user,
        },
      },
    });
  };

  const renderItem = ({ item }: { item: any }) => {
    const userId = item.user_details ? item.user_details.user_id : item.user_id;
    const userName = item.user_details ? item.user_details.user_name : item.user_name;

    const user = {
      user_id: userId,
      user_name: userName,
      email_id: item?.user_details?.email_id,
    };

    return (
      <UserItem
        user={user}
        onPress={() => handleCreateGroup(user)}
      />
    );
  };

  const renderSearchBar = () => (
    <View style={styles.searchContainer}>
      <SearchBar
        placeholder="Search contacts..."
        onChangeText={(value) => {
          getSearchData(value)
        }}
        value={searchContacts}
        // onSubmitEditing={getSearchData}
        style={[styles.searchBar]}
        inputStyle={styles.searchInput}
        // iconColor={theme.colors.primary}
        // placeholderTextColor={theme.colors.onSurfaceVariant}
        // elevation={1}
      />
    </View>
  );

  const renderLoadingState = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator 
        size="large" 
        color={theme.colors.primary}
        style={styles.loadingIndicator}
      />
      <Text 
        variant="bodyMedium" 
        style={[styles.loadingText, { color: theme.colors.onSurfaceVariant }]}
      >
        Loading contacts...
      </Text>
    </View>
  );

  const renderErrorState = () => (
    <View style={styles.errorContainer}>
      <Text 
        variant="headlineSmall" 
        style={[styles.errorTitle, { color: theme.colors.error }]}
      >
        Oops! Something went wrong
      </Text>
      <Text 
        variant="bodyMedium" 
        style={[styles.errorMessage, { color: theme.colors.onSurfaceVariant }]}
      >
        Unable to load contacts. Please try again.
      </Text>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text 
        variant="headlineSmall" 
        style={[styles.emptyTitle, { color: theme.colors.onSurfaceVariant }]}
      >
        No contacts found
      </Text>
    </View>
  );

  const renderContent = () => {
    if (accountUsersLoading) {
      return renderLoadingState();
    }

    if (errorAccountUsers) {
      return renderErrorState();
    }

    return (
      <View style={styles.contentContainer}>
        {renderSearchBar()}
        <FlatList
          data={filteredContacts}
          renderItem={renderItem}
          keyExtractor={(item) => item.user_id}
          contentContainerStyle={styles.flatListContent}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={renderEmptyState()}
          ItemSeparatorComponent={() => <View style={styles.separator} />}
          style={styles.flatList}
          bounces={false}
        />
      </View>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <AppHeader 
        title="Contacts" 
        onBack={() => navigation.goBack()} 
      />
      {renderContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateScale(12),
    paddingBottom: moderateScale(8),
  },
  searchBar: {
    borderRadius: moderateScale(12),
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 1,
    // },
    // shadowOpacity: 0.1,
    // shadowRadius: 1,
  },
  searchInput: {
    fontSize: moderateScale(16),
  },
  flatList: {
    flex: 1,
  },
  flatListContent: {
    paddingHorizontal: moderateScale(16),
    paddingBottom: moderateScale(20),
  },
  separator: {
    // height: moderateScale(8),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: moderateScale(32),
  },
  loadingIndicator: {
    marginBottom: moderateScale(16),
  },
  loadingText: {
    textAlign: 'center',
    fontSize: moderateScale(16),
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: moderateScale(32),
  },
  errorTitle: {
    textAlign: 'center',
    marginBottom: moderateScale(12),
    fontSize: moderateScale(20),
    fontWeight: '600',
  },
  errorMessage: {
    textAlign: 'center',
    fontSize: moderateScale(16),
    lineHeight: moderateScale(24),
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: moderateScale(32),
    paddingTop: moderateScale(60),
  },
  emptyTitle: {
    textAlign: 'center',
    marginBottom: moderateScale(12),
    fontSize: moderateScale(18),
    fontWeight: '500',
  },
  emptyMessage: {
    textAlign: 'center',
    fontSize: moderateScale(14),
    lineHeight: moderateScale(20),
  },
});

export default ContactListScreen;