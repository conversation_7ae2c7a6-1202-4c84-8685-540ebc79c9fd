import React, { useEffect, useState } from "react";
import { View, StyleSheet, ScrollView, TouchableOpacity } from "react-native";
import { ActivityIndicator, Button, Text } from "react-native-paper";
import { useRoute } from "@react-navigation/native";
import CustomInput from "../../../Components/UI/TextInput";
import { DropdownField } from "../../../Components/UI/Menu/DropdownModal";
import AppHeader from "../../../Components/AppHeader";
import { moderateScale, moderateVerticalScale, scale, verticalScale } from "react-native-size-matters";
import ApiClient from "../../../Common/API/ApiClient";
import { getSchemaName } from "../../../Common/Utils/Storage";
import { LEADS_ENDPOINTS } from "../../../Common/API/ApiEndpoints";
import { useUserProfile } from "./useUserDetail";
import { showToast } from "../../../Components/AppToaster/AppToaster";

type UserData = {
  firstName?: string;
  lastName?: string;
  additionalInfo?: string;
  headline?: string;
  currentPosition?: string;
  industry?: string;
  country?: string;
  city?: string;
  user_name?: string
};
const industries = [
  { label: "Software Development", text: "Software Development", value: "1" },
  { label: "Finance", text: "Finance", value: "2" },
  { label: "Pharmaceuticals", text: "Pharmaceuticals", value: "3" },
  { label: "Information Technology (IT)", text: "Information Technology (IT)", value: "4" },
  { label: "Electronics & Hardware", text: "Electronics & Hardware", value: "5" },
  { label: "Aerospace & Defence", text: "Aerospace & Defence", value: "6" },
  { label: "Textiles & Apparel", text: "Textiles & Apparel", value: "7" },
  { label: "Food Processing", text: "Food Processing", value: "8" },
  { label: "Plastics & Polymers", text: "Plastics & Polymers", value: "9" },
  { label: "Education & Training", text: "Education & Training", value: "10" },
  { label: "Healthcare & Biotechnology", text: "Healthcare & Biotechnology", value: "11" },
];

const EditProfile = () => {
  const route = useRoute();
  const { data } = route.params
  useEffect(() => {
    GetCountries()
  }, [])
  const [form, setForm] = useState<UserData>({
    firstName: (data?.user_name || "gfh").split(" ")[0] || "",
    lastName: (data?.user_name || "fdhg").split(" ").slice(1).join(" ") || "",
    additionalInfo: data?.profileDetails[0]?.additionalInfo || "",
    headline: data?.profileDetails[0]?.headLine || "",
    currentPosition: data?.profileDetails[0]?.currentPosition || "",
    industry: data?.profileDetails[0]?.industry || "",
    country: data?.profileDetails[0]?.countryRegion || "",
    city: data?.profileDetails[0]?.city || "",
  });

  const [countries, setCountries] = useState<any[]>([])
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const handleChange = (field: keyof UserData, value: string) => {
    setForm({ ...form, [field]: value });
  };


  const handleReset = () => {
    setForm({
      firstName: "",
      lastName: "",
      additionalInfo: "",
      headline: "",
      currentPosition: "",
      industry: "",
      country: "",
      city: "",
    });
  };
  const validate = () => {
    const newErrors: { [key: string]: string } = {};
    if (!form.firstName) newErrors.firstName = "First name is required";
    if (!form.lastName) newErrors.lastName = "Last name is required";
    if (!form.headline) newErrors.headline = "Headline is required";
    if (!form.industry) newErrors.industry = "Industry is required";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const { updateUserProfileDetails, loading } = useUserProfile();
  const handleSave = async () => {
    if (!validate()) return;

    const payload = {
      user_name: `${form.firstName} ${form.lastName}`,
      user_id: data?.user_id,
      profile_details: JSON.stringify([
        {
          M: {
            headLine: { S: form.headline },
            description: { S: "" },
            industry: { S: form.industry },
            designation: { S: "" },
            currentPosition: { S: form.currentPosition || "" },
            city: { S: form.city || "" },
            countryRegion: { S: form.country || "" },
            additionalInfo: { S: form.additionalInfo || "" },
          },
        },
      ]),
    };
    const success = await updateUserProfileDetails(payload);

    if (success) {
      showToast.success("Profile updated successfully");
    } else {
      showToast.error("Failed to update profile");
    }
  };

  const GetCountries = async () => {
    try {

      const schemaName = await getSchemaName()
      const payload = { schemaName };
      const response = await ApiClient.post(LEADS_ENDPOINTS.GET_COUNTRIES,
        payload
      );

      console.log('countries', response.data.data)
      setCountries(response?.data?.data || [])

    } catch (error) {
      console.log('Error fetching countries:', error);

    }
  }


  return (
    <View style={{ flex: 1, backgroundColor: '#fff', }}>
      <AppHeader title="Edit Intro" />
      <ScrollView contentContainerStyle={styles.container} bounces={false}>

        <CustomInput
          label="First Name"
          value={form.firstName}
          onChangeText={(text) => handleChange("firstName", text)}
          errorText={errors.firstName}
          isMandatory={true}
          placeholder="Enter first name"
        />

        <CustomInput
          label="Last Name"
          value={form.lastName}
          onChangeText={(text) => handleChange("lastName", text)}
          errorText={errors.lastName}
          isMandatory={true}
          placeholder="Enter last name"
        />

        <CustomInput
          label="Additional Info"
          value={form.additionalInfo}
          onChangeText={(text) => handleChange("additionalInfo", text)}
          placeholder="Enter additional info"
        />

        <CustomInput
          label="Headline"
          value={form.headline}
          onChangeText={(text) => handleChange("headline", text)}
          errorText={errors.headline}
          isMandatory={true}
          placeholder="Enter headline"
        />

        <CustomInput
          label="Current Position"
          value={form.currentPosition}
          onChangeText={(text) => handleChange("currentPosition", text)}
          placeholder="Enter current position"
        />

        <DropdownField
          data={industries}
          valueField="Text"
          labelField="label"
          label="Industry"
          placeholder="Select Industry"
          value={form.industry}
          isMandatory={true}
          onSelect={(selected) =>
            handleChange("industry", selected?.Text || "")
          }
        />
        {errors.industry ? (
          <View style={styles.errorText}>
            <Text style={{ color: 'red' }}>{errors.industry}</Text>
          </View>
        ) : null}

        <DropdownField
          data={countries}
          valueField="name"
          labelField="name"
          label="Country"
          placeholder="Select Country"
          value={form.country}
          onSelect={(selected) =>
            handleChange("country", selected?.name || "")
          }
        />

        <CustomInput
          label="City"
          value={form.city}
          onChangeText={(text) => handleChange("city", text)}
          placeholder="Enter city"
        />

      </ScrollView>
      <View style={styles.actionRow}>
        <Button
          mode="contained"
          style={styles.cancelButton}
          textColor="#000"
          onPress={() => handleReset()}
        >
          Reset
        </Button>
        <Button
          mode="contained"
          style={styles.saveButton}
          textColor="#fff"
          onPress={() => handleSave()}
        >
          {loading && <ActivityIndicator color="#f7f1f1ff" size="medium" style={{ marginRight: moderateScale(8), }} />}
          Save
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // position:'relative',
    // flex:1,

    padding: 16,
  },
  errorText: {
    marginBottom: 8,
  },
  actionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    gap: scale(10),
    paddingVertical: verticalScale(10),
  },
  cancelButton: {
    flex: 1,
    borderRadius: 4,
    backgroundColor: '#E5E7EB',
  },
  saveButton: {
    flex: 1,
    borderRadius: 4,
    backgroundColor: '#2563EB',
  },
});

export default EditProfile;
