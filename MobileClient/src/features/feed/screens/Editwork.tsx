import React, { useEffect, useState } from "react";
import { View, ScrollView, StyleSheet, TouchableOpacity } from "react-native";
import { Button, Checkbox, Text } from "react-native-paper";
import { useRoute, RouteProp, useNavigation } from "@react-navigation/native";
import CustomInput from "../../../Components/UI/TextInput";
import { DropdownField } from "../../../Components/UI/Menu/DropdownModal";
import AppHeader from "../../../Components/AppHeader";
import { moderateScale, moderateVerticalScale, scale, verticalScale } from "react-native-size-matters";
import { AddressInput } from "../../../Stacks/LeadsAppStack/components/Shared/AddressInput";
import ImagePreview from "../../chat/components/common/ImagePreview";
import { useUserProfile } from "./useUserDetail";
import { generateUniqueId } from "../../../Utils/util";
import DatePickerField from "./DatePickerFields";
import { showToast } from "../../../Components/AppToaster/AppToaster";

type RootStackParamList = {
  EditWorkInfo: {
    WorkItem?: {
      id?: string;
      role?: string;
      company?: string;
      companyLogo?: string;
      isCurrent?: boolean;
      startDate?: string;
      endDate?: string;
      location?: string;
      locationType?: string;
      description?: string;
      employmentType?: string;
    };
    isEditing?: boolean;
    data?: { workExperience: any[] };
  };
};

const employmentTypes = [
  { label: "Full-time", Text: "Full-time", value: "Full-time" },
  { label: "Part-time", Text: "Part-time", value: "Part-time" },
  { label: "Internship", Text: "Internship", value: "Internship" },
  { label: "Contract", Text: "Contract", value: "Contract" },
  { label: "Freelance", Text: "Freelance", value: "Freelance" },
  { label: "Temporary", Text: "Temporary", value: "Temporary" },
  { label: "Apprenticeship", Text: "Apprenticeship", value: "Apprenticeship" },
  { label: "Volunteer", Text: "Volunteer", value: "Volunteer" },
  { label: "Remote / Work from Home", Text: "Remote / Work from Home", value: "Remote" },
];

const locationTypes = [
  { label: "On-site", Text: "On-site", Value: "On-site" },
  { label: "Remote", Text: "Remote", Value: "Remote" },
  { label: "Hybrid", Text: "Hybrid", Value: "Hybrid" },
];

const EditWorkInfoScreen: React.FC = () => {
  const route = useRoute<RouteProp<RootStackParamList, "EditWorkInfo">>();
  const { WorkItem, isEditing, data } = route.params || {};
  console.log("WorkItem data:", WorkItem?.id, data?.workExperience, isEditing);

  const navigation = useNavigation();
  const { updateUserWorkExperience, updating } = useUserProfile();
  const validateDateFormat = (date: string) =>
    date && typeof date === "string" && /^\d{4}-\d{2}$/.test(date) ? date : "";


  const [form, setForm] = useState({
    title: WorkItem?.role || "",
    employmentType: WorkItem?.employmentType || "",
    organization: WorkItem?.company || "",
    companyLogo: WorkItem?.companyLogo || "",
    isCurrent: WorkItem?.isCurrent || false,
    startDate: validateDateFormat(WorkItem?.startDate || ""),
    endDate: validateDateFormat(WorkItem?.endDate || ""),
    location: WorkItem?.location || "",
    locationType: WorkItem?.locationType || "",
    description: WorkItem?.description || "",
  });
  console.log('asdfasd', form)

  const [formErrors, setFormErrors] = useState<any>({});
  const [isPreviewVisible, setIsPreviewVisible] = useState(false);
  const userId = "b1f3edba-d0a1-70ee-6565-fb4eeff19322";

  // const handleReset = () => {
  //   setForm({
  //     title: "",
  //     employmentType: "",
  //     organization: "",
  //     companyLogo: "",
  //     isCurrent: false,
  //     startDate: "",
  //     endDate: "",
  //     location: "",
  //     locationType: "",
  //     description: "",
  //   });
  //   setFormErrors({});
  // };




  const handleChange = (field: string, value: string | boolean) => {
    setForm((prev) => ({ ...prev, [field]: value }));
    setFormErrors((prev: any) => ({ ...prev, [field]: undefined }));
  };

  const validateForm = () => {
    let errors: any = {};
    if (!form.title.trim()) errors.title = "Title is required";
    if (!form.organization.trim()) errors.organization = "Organization is required";
    if (!form.startDate.trim()) errors.startDate = "Start date is required";
    if (!form.isCurrent && !form.endDate.trim()) errors.endDate = "End date is required";
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleDelete = async () => {
  const existingWorkExperience = data?.workExperience || [];
  try {
    const success = await updateUserWorkExperience(userId, null, WorkItem?.id, existingWorkExperience, true);
    if (success) {
      showToast.success("Work experience deleted!");
        navigation.goBack()
      
    } else {
      showToast.error("Error Failed to delete.");
    }
  } catch (error) {
    console.error(error);
  }
};

  const handleSave = async () => {
    if (!validateForm()) return;
    const workExperienceData = {
      role: form.title,
      company: form.organization,
      companyLogo: form.companyLogo,
      employmentType: form.employmentType,
      startDate: form.startDate,
      endDate: form.isCurrent ? "" : form.endDate,
      location: form.location,
      locationType: form.locationType,
      description: form.description,
      id: isEditing ? WorkItem?.id : generateUniqueId(), // Use existing ID for edit, new ID for create
    };

    // Get existing work experience from route params
    const existingWorkExperience = data?.workExperience || [];

    // Prepare updated work experience array
    let updatedWorkExperience;
    if (isEditing) {
      // Update existing item
      updatedWorkExperience = existingWorkExperience.map((item) =>
        item.id === WorkItem?.id ? workExperienceData : item
      );
    } else {
      // Add new item
      updatedWorkExperience = [...existingWorkExperience, workExperienceData];
    }

    try {
      const success = await updateUserWorkExperience(
        userId,
        workExperienceData,
        isEditing ? WorkItem?.id : undefined,
        updatedWorkExperience
      );

      if (success) {
      showToast.success(`Work experience ${isEditing ? "updated" : "added"} successfully!`);
      } else {
        console.log("Error", "Failed to save work experience. Please try again.");
      }
    } catch (error) {
      console.error("Error saving work experience:", error);
    }
  };

  const handlePreviewImage = () => {
    if (form.companyLogo) {
      setIsPreviewVisible(true);
    }
  };
  console.log("EditWorkInfoScreen - form.startDate:", form.startDate);
  useEffect(() => {
    console.log("form.startDate updated:", form.startDate);
  }, [form.startDate]);

  return (
    <View style={{ flex: 1, backgroundColor: "#fff" }}>
      <AppHeader title={isEditing ? "Edit Work Experience" : "Add Work Experience"} />
      <ScrollView contentContainerStyle={styles.container} bounces={false}>
        <CustomInput
          label="Title"
          value={form.title}
          onChangeText={(text) => handleChange("title", text)}
          errorText={formErrors.title}
          isMandatory
          placeholder="Ex: Software Developer"
        />

        <DropdownField
          data={employmentTypes}
          valueField="value"
          labelField="label"
          label="Employment type"
          placeholder="Please select"
          value={form.employmentType}
          isMandatory={false}
          onSelect={(selected) => handleChange("employmentType", selected?.value || "")}
        />

        <CustomInput
          label="Company or organization"
          value={form.organization}
          onChangeText={(text) => handleChange("organization", text)}
          errorText={formErrors.organization}
          isMandatory
          placeholder="Ex: Voltuswave"
        />

        <CustomInput
          label="Company logo"
          value={form.companyLogo}
          onChangeText={(text) => handleChange("companyLogo", text)}
          placeholder="Logo URL"
        />

        {form.companyLogo ? (
          <TouchableOpacity onPress={handlePreviewImage}>
            <Text style={styles.previewText}>Preview Logo</Text>
          </TouchableOpacity>
        ) : null}

        <ImagePreview
          visible={isPreviewVisible}
          onClose={() => setIsPreviewVisible(false)}
          images={form.companyLogo ? [form.companyLogo] : []}
          initialIndex={0}
        // senderDetails={{ senderName: form.organization || "Company Logo" }}
        />

        <View style={styles.checkboxRow}>
          <Checkbox
            status={form.isCurrent ? "checked" : "unchecked"}
            onPress={() => handleChange("isCurrent", !form.isCurrent)}
          />
          <Text>I am currently working in this role</Text>
        </View>

        <DatePickerField
          label="Start date"
          value={form.startDate}
          onChange={(text) => handleChange("startDate", text)}
        />

        {!form.isCurrent && (
          <DatePickerField
            label="End date"
            value={form.endDate}
            onChange={(text) => handleChange("endDate", text)}
          />
        )}

        <CustomInput
          label="Location"
          value={form.location}
          onChangeText={(text) => handleChange("location", text)}
          placeholder="Ex: London, United Kingdom"
        />

        <DropdownField
          data={locationTypes}
          valueField="Value"
          labelField="label"
          label="Location type"
          placeholder="Select location type"
          value={form.locationType}
          isMandatory={false}
          onSelect={(selected) => handleChange("locationType", selected?.Value || "")}
        />

        <AddressInput
          label="Description"
          value={form.description}
          onChangeText={(text) => handleChange("description", text)}
          placeholder="Add role description"
        />
      </ScrollView>

      <View style={styles.actionRow}>
        <Button
          mode="contained"
          style={styles.cancelButton}
          textColor="#000"
          onPress={()=>handleDelete()}
          disabled={updating}
        >
          DELETE
        </Button>
        <Button
          mode="contained"
          style={styles.saveButton}
          textColor="#fff"
          onPress={handleSave}
          loading={updating}
          disabled={updating}
        >
          {isEditing ? "Update" : "Save"} Changes
        </Button>
      </View>
    </View>
  );
};

export default EditWorkInfoScreen;

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: moderateScale(12),
    paddingVertical: moderateVerticalScale(8),
  },
  checkboxRow: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 8,
  },
  actionRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: scale(16),
    gap: scale(10),
    paddingVertical: verticalScale(10),
  },
  cancelButton: {
    flex: 1,
    borderRadius: 4,
    backgroundColor: "#ebe6e5ff",
  },
  saveButton: {
    flex: 1,
    borderRadius: 4,
    backgroundColor: "#2563EB",
  },
  previewText: {
    color: "#2563EB",
    fontSize: moderateScale(14),
    marginTop: moderateScale(8),
    textDecorationLine: "underline",
  },
});