import React from "react";
import { View, TouchableOpacity, Image, StyleSheet } from "react-native";
import { moderateScale } from "react-native-size-matters";
import { Pencil } from "lucide-react-native";
import { Text } from "react-native-paper";
import { useNavigation, useRoute } from "@react-navigation/native";

const BarComponent = ({ text, icon: Icon, data, onEdit }) => {

  const navigation = useNavigation()
  const route = useRoute()
  return (
    <View
      style={{
        backgroundColor: "#fff",
        marginTop: moderateScale(8),
        paddingVertical: moderateScale(12),
        paddingHorizontal: moderateScale(16),
        borderRadius: moderateScale(4),
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        elevation: 4,
      }}
    >
      <View style={styles.reusableBars}>
        <Text variant="semiBold" style={styles.sectionTitle}>{text}</Text>
        <TouchableOpacity style={styles.actionButton} onPress={() => {
          console.log("Navigating to EditWorkInfoScreen...");
          navigation.navigate("EditWorkInfoScreen", {
            WorkItem: null,
            isEditing: false,
            data: { workExperience: data },
          });
        }}
        >
          {Icon && <Icon size={16} color="#fff" />}
        </TouchableOpacity>
      </View>

      {!data || data.length === 0 ? (
        <View style={styles.placeholderContent}>
          <Text style={styles.emptyText}>No {text.toLowerCase()} available</Text>
        </View>
      ) : (
        data.map((item, index) => (
          <View key={index} style={styles.itemContainer}>
            {item.companyLogo ? (
              <Image
                source={{ uri: item.companyLogo }}
                style={styles.companyLogo}
              />
            ) : null}

            <View style={{ flex: 1 }}>
              <Text style={styles.company}>{item.company}</Text>
              <Text style={styles.role}>{item.role}</Text>
              {item.description ? (
                <Text style={styles.description}>{item.description}</Text>
              ) : null}
              <Text style={styles.duration}>
                {calculateDuration(item.startDate, item.endDate)}
              </Text>
            </View>

            <TouchableOpacity
              style={styles.editButton}
              onPress={() => {
                console.log("Navigating to EditWorkInfoScreen for editing...");
                onEdit({
                  WorkItem: item,
                  isEditing: true,
                  data: { workExperience: data }, 
                });
              }}
            >
              <Pencil size={16} color="#fff" />
            </TouchableOpacity>
          </View>
        ))
      )}
    </View>
  );
};

const calculateDuration = (start, end) => {
  if (!start || !end) return "";
  try {
    const [startYear, startMonth] = start.split("-").map(Number);
    const [endYear, endMonth] = end.split("-").map(Number);

    let months = (endYear - startYear) * 12 + (endMonth - startMonth);
    const years = Math.floor(months / 12);
    months = months % 12;

    return `${years} years ${months} months`;
  } catch (e) {
    return "";
  }
};

const styles = StyleSheet.create({
  reusableBars: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: moderateScale(8),
  },
  sectionTitle: {
    fontSize: moderateScale(13),
    color: "#000",
  },
  actionButton: {
    backgroundColor: "#3b82f6",
    padding: moderateScale(6),
    borderRadius: moderateScale(20),
  },
  placeholderContent: {
    marginTop: moderateScale(8),
  },
  emptyText: {
    color: "#777",
    fontSize: moderateScale(12),
  },
  itemContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: moderateScale(8),
    paddingBottom: moderateScale(8)
  },
  companyLogo: {
    width: moderateScale(40),
    height: moderateScale(40),
    borderRadius: moderateScale(40),
    marginRight: moderateScale(10),
    borderColor: '#363535ff',
    borderWidth: 0.3,
    alignSelf: 'flex-start'
  },
  company: {
    fontWeight: "600",
    fontSize: moderateScale(13),
    color: "#111",
  },
  role: {
    fontSize: moderateScale(12),
    color: "#333",
  },
  description: {
    fontSize: moderateScale(11),
    color: "#666",
    marginTop: moderateScale(2),
  },
  duration: {
    fontSize: moderateScale(11),
    color: "#999",
    marginTop: moderateScale(2),
  },
  editButton: {
    backgroundColor: "#3b82f6",
    padding: moderateScale(6),
    borderRadius: moderateScale(20),
    marginLeft: moderateScale(8),
  },
});

export default BarComponent;
