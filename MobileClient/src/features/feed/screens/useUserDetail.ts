import { useState } from "react";
import axios from "axios";
import { useNavigation } from "@react-navigation/native";
import { generateUniqueId } from "../../../Utils/util";

const USER_PROFILE_ENDPOINT =
  "https://fo6vj4g255fdplqab3nauqgonu.appsync-api.ap-south-1.amazonaws.com/graphql";
const USER_PROFILE_APIKEY = "da2-72umdjat6rdihd2hpfwqxmp6bi";

const parseDynamoDBAttribute = (attr: any): any => {
  if (!attr || typeof attr !== "object") return attr;
  if (attr.S !== undefined) return attr.S;
  if (attr.N !== undefined) return Number(attr.N);
  if (attr.BOOL !== undefined) return attr.BOOL;
  if (attr.NULL !== undefined) return null;
  if (attr.L !== undefined) return attr.L.map(parseDynamoDBAttribute);
  if (attr.M !== undefined) {
    const result: any = {};
    Object.keys(attr.M).forEach((key) => {
      result[key] = parseDynamoDBAttribute(attr.M[key]);
    });
    return result;
  }
  return attr;
};

const parseJsonField = (field: any): any => {
  if (!field) return [];

  const tryParse = (value: any): any => {
    if (typeof value === "string") {
      try {
        const parsed = JSON.parse(value);
        return tryParse(parsed);
      } catch {
        return value;
      }
    }
    if (Array.isArray(value)) return value.map(tryParse);
    if (typeof value === "object") return parseDynamoDBAttribute(value);
    return value;
  };

  return tryParse(field);
};

const toDynamoDBFormat = (obj: any): any => {
  if (obj === null || obj === undefined) return { NULL: true };
  if (typeof obj === "string") return { S: obj };
  if (typeof obj === "number") return { N: obj.toString() };
  if (typeof obj === "boolean") return { BOOL: obj };
  if (Array.isArray(obj)) return { L: obj.map(toDynamoDBFormat) };
  if (typeof obj === "object") {
    const result: any = { M: {} };
    Object.keys(obj).forEach((key) => {
      result.M[key] = toDynamoDBFormat(obj[key]);
    });
    return result;
  }
  return obj;
};

export const useUserProfile = () => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState(false);
  const navigation=useNavigation()

  const fetchUserProfileData = async (user_id: string) => {
    console.log("+++called fecthedusers")
    setLoading(true);
    setError(null);
    try {
      const response = await axios.post(
        USER_PROFILE_ENDPOINT,
        {
          query: `
            query GetUserProfileData($user_id: ID!) {
              getUserProfileData(user_id: $user_id) {
                skills
                user_id
                user_name
                profile_image
                background_image_url
                contact_details
                interests
                profile_details
                work_experience
                email_id
              }
            }
          `,
          variables: { user_id },
        },
        {
          headers: {
            "Content-Type": "application/json",
            "x-api-key": USER_PROFILE_APIKEY,
          },
        }
      );

      if (response.data.errors) {
        setError("GraphQL error");
        return null;
      }

      const rawProfile = response.data.data?.getUserProfileData ?? null;
      const parsedProfile = {
        user_id: rawProfile.user_id,
        user_name: rawProfile.user_name,
        email_id: rawProfile.email_id,
        profile_image: rawProfile.profile_image,
        background_image_url: rawProfile.background_image_url,
        skills: parseJsonField(rawProfile.skills),
        interests: parseJsonField(rawProfile.interests),
        contactDetails: parseJsonField(rawProfile.contact_details),
        profileDetails: parseJsonField(rawProfile.profile_details),
        workExperience: parseJsonField(rawProfile.work_experience),
      };

      setData(parsedProfile);
      return parsedProfile;
    } catch (err) {
      console.error("Error fetching user profile data:", err);
      setError("Network error");
      return null;
    } finally {
      setLoading(false);
    }
  };

  const updateUserSkills = async (user_id: string, skills: any[]) => {
    setUpdating(true);
    try {
      await axios.post(
        USER_PROFILE_ENDPOINT,
        {
          query: `
            mutation UpdateUserSkills($user_id: ID!, $skills: AWSJSON) {
              updateUserSkills(user_id: $user_id, skills: $skills)
            }
          `,
          variables: { user_id, skills: JSON.stringify(skills) },
        },
        {
          headers: {
            "Content-Type": "application/json",
            "x-api-key": USER_PROFILE_APIKEY,
          },
        }
      );
      return true;
    } catch (error) {
      console.error("Error updating skills:", error);
      return false;
    } finally {
      setUpdating(false);
    }
  };

  const updateUserInterests = async (user_id: string, interests: any[]) => {
    setUpdating(true);
    try {
      await axios.post(
        USER_PROFILE_ENDPOINT,
        {
          query: `
            mutation UpdateUserInterests($user_id: ID!, $interests: AWSJSON) {
              updateInterests(user_id: $user_id, interests: $interests)
            }
          `,
          variables: { user_id, interests: JSON.stringify(interests) },
        },
        {
          headers: {
            "Content-Type": "application/json",
            "x-api-key": USER_PROFILE_APIKEY,
          },
        }
      );

      await fetchUserProfileData(user_id);
      return true;
    } catch (error) {
      console.error("Error updating interests:", error);
      return false;
    } finally {
      setUpdating(false);
    }
  };

  const updateUserEmail = async (user_id: string, email_id: string) => {
    setUpdating(true);
    try {
      const response = await axios.post(
        USER_PROFILE_ENDPOINT,
        {
          query: `
            mutation updateEmail($user_id: ID!, $email_id: String!) {
              updateEmail(user_id: $user_id, email_id: $email_id)
            }
          `,
          variables: { user_id, email_id },
        },
        {
          headers: {
            "Content-Type": "application/json",
            "x-api-key": USER_PROFILE_APIKEY,
          },
        }
      );

      if (response.data.errors) {
        console.error("GraphQL errors:", response.data.errors);
        return false;
      }

      setData((prev: any) => prev ? { ...prev, email_id } : prev);
      
      return response.data.data?.updateEmail || false;
    } catch (error) {
      console.error("Error updating email:", error);
      return false;
    } finally {
      setUpdating(false);
    }
  };

  const updateUserContactDetails = async (user_id: string, contactDetails: any) => {
    setUpdating(true);
    try {
      const dynamoContactDetails = [toDynamoDBFormat(contactDetails)];
      
      const response = await axios.post(
        USER_PROFILE_ENDPOINT,
        {
          query: `
            mutation UpdateUserContactDetails($user_id: ID!, $contact_details: AWSJSON) {
              updateUserContactDetails(user_id: $user_id, contact_details: $contact_details) {
                success
                user_contact_details
                __typename
              }
            }
          `,
          variables: {
            user_id,
            contact_details: JSON.stringify(dynamoContactDetails),
          },
        },
        {
          headers: {
            "Content-Type": "application/json",
            "x-api-key": USER_PROFILE_APIKEY,
          },
        }
      );

      if (response.data.errors) {
        console.error("GraphQL errors:", response.data.errors);
        return false;
      }

      const result = response.data.data?.updateUserContactDetails;
      
      if (result?.success) {
        const updatedContactDetails = parseJsonField(result.user_contact_details);
        setData((prev: any) => prev ? { 
          ...prev, 
          contactDetails: Array.isArray(updatedContactDetails) 
            ? updatedContactDetails 
            : [updatedContactDetails]
        } : prev);
        return true;
      }

      return false;
    } catch (error) {
      console.error("Error updating contact details:", error);
      return false;
    } finally {
      setUpdating(false);
      navigation.goBack()

    }
  };
const updateUserProfileDetails = async (input: any) => {
  setUpdating(true);
  try {
    const response = await axios.post(
      USER_PROFILE_ENDPOINT,
      {
        query: `
          mutation UpdateUserProfileDetails($input: updateUserProfileInput) {
            updateUserProfileDetails(input: $input) {
              success
              user_profile_details
              __typename
            }
          }
        `,
        variables: { input },
      },
      {
        headers: {
          "Content-Type": "application/json",
          "x-api-key": USER_PROFILE_APIKEY,
        },
      }
    );

    if (response.data.errors) {
      console.error("GraphQL errors:", response.data.errors);
      return false;
    }

    const result = response.data.data?.updateUserProfileDetails;

    if (result?.success) {
      const parsed = parseJsonField(result.user_profile_details);
      setData((prev: any) => ({
        ...prev,
        profileDetails: Array.isArray(parsed) ? parsed : [parsed],
      }));
      return true;
    }

    return false;
  } catch (error) {
    console.error("Error updating profile details:", error);
    return false;
  } finally {
    setUpdating(false);
    navigation.goBack(); 
  }
};

const updateUserWorkExperience = async (
  user_id: string,
  workExperienceData: any,
  existingWorkExperienceId?: string,
  allWorkExperience?: any[],
  deleteMode?: boolean
) => {
  setUpdating(true);
  try {
    const existingWorkExperience = allWorkExperience || data?.workExperience || [];
    let updatedWorkExperience;

    if (deleteMode && existingWorkExperienceId) {
      updatedWorkExperience = existingWorkExperience.filter((item: any) => item.id !== existingWorkExperienceId);
    } else if (existingWorkExperienceId) {
      console.log("===>>> entered update mode");
      updatedWorkExperience = existingWorkExperience.map((item: any) =>
        item.id === existingWorkExperienceId ? { ...workExperienceData, id: existingWorkExperienceId } : item
      );
    } else {
      console.log("===>>> entered create mode");
      const newWorkExperience = {
        ...workExperienceData,
        id: workExperienceData.id || generateUniqueId(),
      };
      updatedWorkExperience = [...existingWorkExperience, newWorkExperience];
    }

    const dynamoWorkExperience = updatedWorkExperience.map((item: any) => toDynamoDBFormat(item));

    const response = await axios.post(
      USER_PROFILE_ENDPOINT,
      {
        query: `
          mutation UpdateUserWorkExperienceDetails($user_id: ID!, $work_experience: AWSJSON) {
            updateUserWorkExperienceDetails(user_id: $user_id, work_experience: $work_experience) {
              success
              work_experience
              __typename
            }
          }
        `,
        variables: {
          user_id,
          work_experience: JSON.stringify(dynamoWorkExperience),
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
          "x-api-key": USER_PROFILE_APIKEY,
        },
      }
    );

    if (response.data.errors) {
      console.error("GraphQL errors:", response.data.errors);
      return false;
    }

    const result = response.data.data?.updateUserWorkExperienceDetails;

    if (result?.success) {
      const updatedWorkExp = parseJsonField(result.work_experience);
      setData((prev: any) => ({
        ...prev,
        workExperience: Array.isArray(updatedWorkExp) ? updatedWorkExp : [updatedWorkExp],
      }));
      return true;
    }

    return false;
  } catch (error) {
    console.error("Error updating work experience:", error);
    return false;
  } finally {
    setUpdating(false);
  }
};


  const updateProfileImage = async (user_id: string, profile_image: string) => {
    setUpdating(true);
    try {
      const response = await axios.post(
        USER_PROFILE_ENDPOINT,
        {
          query: `
            mutation UpdateProfileImage($user_id: ID!, $profile_image: String!) {
              updateProfileImage(user_id: $user_id, profile_image: $profile_image)
            }
          `,
          variables: { user_id, profile_image },
        },
        {
          headers: {
            "Content-Type": "application/json",
            "x-api-key": USER_PROFILE_APIKEY,
          },
        }
      );

      if (response.data.errors) {
        console.error("GraphQL errors:", response.data.errors);
        return false;
      }

      // Update local state
      setData((prev: any) => prev ? { ...prev, profile_image } : prev);
      return true;
    } catch (error) {
      console.error("Error updating profile image:", error);
      return false;
    } finally {
      setUpdating(false);
    }
  };

  const updateBackgroundImage = async (user_id: string, background_image_url: string) => {
    setUpdating(true);
    try {
      const response = await axios.post(
        USER_PROFILE_ENDPOINT,
        {
          query: `
            mutation UpdateBackgroundImage($user_id: ID!, $background_image_url: String!) {
              updateBackgroundImage(user_id: $user_id, background_image_url: $background_image_url)
            }
          `,
          variables: { user_id, background_image_url },
        },
        {
          headers: {
            "Content-Type": "application/json",
            "x-api-key": USER_PROFILE_APIKEY,
          },
        }
      );

      if (response.data.errors) {
        console.error("GraphQL errors:", response.data.errors);
        return false;
      }

      // Update local state
      setData((prev: any) => prev ? { ...prev, background_image_url } : prev);
      return true;
    } catch (error) {
      console.error("Error updating background image:", error);
      return false;
    } finally {
      setUpdating(false);
    }
  };

  return {
    data,
    loading,
    error,
    updating,
    fetchUserProfileData,
    updateUserSkills,
    updateUserInterests,
    updateUserEmail,
    updateUserContactDetails,
    updateUserProfileDetails,
    updateUserWorkExperience,
    updateProfileImage,
    updateBackgroundImage
  };
};