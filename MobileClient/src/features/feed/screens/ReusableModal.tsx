import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, TextInput, Alert } from 'react-native';
import { Modal, Portal, Text, ActivityIndicator } from 'react-native-paper';
import { X } from 'lucide-react-native';
import { moderateScale } from 'react-native-size-matters';
import { generateUniqueId } from '../../../Utils/util';
import { ScrollView } from 'react-native-gesture-handler';
import { showToast } from '../../../Components/AppToaster/AppToaster';

const SkillsInterestsModal = ({ 
  visible, 
  onDismiss, 
  type, 
  initialData = [], 
  onSave,
  loading = false 
}) => {
  const [inputValue, setInputValue] = useState('');
  const [items, setItems] = useState([]);
  useEffect(() => {
    if (visible) {
      setItems([...initialData]);
      setInputValue('');
    }
  }, [visible]);

  const handleAdd = () => {
    if (!inputValue.trim()) return;
    
    const newItem = {
      id: generateUniqueId(),
      [type === 'skills' ? 'skill' : 'interest']: inputValue.trim()
    };
    
    const isDuplicate = items.some(item => 
      (item.skill?.toLowerCase() === inputValue.toLowerCase()) ||
      (item.interest?.toLowerCase() === inputValue.toLowerCase())
    );
    
    if (isDuplicate) {
      showToast.warning(`Duplicate Entry This ${type.slice(0, -1)} already exists.`);
      return;
    }
    
    setItems([newItem,...items]);
    setInputValue('');
  };

  const handleRemove = (id) => {
    setItems(items.filter(item => item.id !== id));
  };

  const handleSave = async () => {
    try {
      await onSave(items);
      onDismiss();
    } catch (error) {
      Alert.alert('Error', `Failed to update ${type}. Please try again.`);
    }
  };


  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={styles.modalContainer}
      >
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.title}>
              {type}
            </Text>
            <TouchableOpacity onPress={onDismiss} style={styles.closeButton}>
              <X size={20} color="#666" />
            </TouchableOpacity>
          </View>
          <View style={styles.inputSection}>
            <Text style={styles.inputLabel}>
              {type}*
            </Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.textInput}
                value={inputValue}
                onChangeText={setInputValue}
                placeholder={`Ex: React${type === 'interests' ? 'ing' : 'js'}`}
                editable={!loading}
              />
              <TouchableOpacity 
                style={[styles.addButton, loading && styles.disabledButton]} 
                onPress={handleAdd}
                disabled={loading || !inputValue.trim()}
              >
                <Text style={styles.addButtonText}>add</Text>
              </TouchableOpacity>
            </View>
          </View>
          <View style={{ maxHeight: moderateScale(160) }}>
          <ScrollView contentContainerStyle={styles.itemsContainer} bounces={false}>
            {items.map((item) => (
              <View key={item.id} style={styles.itemChip}>
                <Text style={styles.itemText}>
                  {item.skill || item.interest}
                </Text>
                {!loading && (
                  <TouchableOpacity 
                    onPress={() => handleRemove(item.id)}
                    style={styles.removeButton}
                  >
                    <X size={14} color="#666" />
                  </TouchableOpacity>
                )}
              </View>
            ))}
          </ScrollView>
          </View>
          <View style={styles.footer}>
            <TouchableOpacity 
              style={[styles.saveButton, loading && styles.disabledButton]} 
              onPress={handleSave}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.saveButtonText}>Save</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    margin: moderateScale(20),
    backgroundColor: 'white',
    borderRadius: moderateScale(8),
   
  },
  modalContent: {
    padding: moderateScale(20),
    marginVertical:moderateScale(12)
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: moderateScale(20),
  },
  title: {
    fontSize: moderateScale(15),
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: moderateScale(4),
  },
  inputSection: {
    marginBottom: moderateScale(20),
  },
  inputLabel: {
    fontSize: moderateScale(12),
    color: '#333',
    marginBottom: moderateScale(8),
    fontWeight: '500',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderTopLeftRadius: moderateScale(4),
    borderBottomLeftRadius:moderateScale(4),
    paddingHorizontal: moderateScale(12),
    paddingVertical: moderateScale(10),
    fontSize: moderateScale(13),
    backgroundColor: '#fff',
  },
  addButton: {
    backgroundColor: '#6c757d',
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateScale(12),
    borderTopRightRadius:moderateScale(4),
    borderBottomRightRadius:moderateScale(4)
  },
  addButtonText: {
    color: '#fff',
    fontSize: moderateScale(13),
    fontWeight: '500',
  },
  itemsContainer: {

    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: moderateScale(8),
    marginBottom: moderateScale(44),
    minHeight: moderateScale(50),

  },
  itemChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e5e7eb',
    paddingHorizontal: moderateScale(12),
    paddingVertical: moderateScale(8),
    borderRadius: moderateScale(16),
    gap: moderateScale(6),
  },
  itemText: {
    fontSize: moderateScale(11),
    color: '#333',
  },
  removeButton: {
    padding: moderateScale(2),
  },
  footer: {
    marginTop:moderateScale(12),
    alignItems: 'flex-end',
  },
  saveButton: {
    backgroundColor: '#4285F4',
    paddingHorizontal: moderateScale(24),
    paddingVertical: moderateScale(10),
    borderRadius: moderateScale(4),
    minWidth: moderateScale(80),
    alignItems: 'center',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: moderateScale(12),
    fontWeight: '500',
  },
  disabledButton: {
    opacity: 0.6,
  },
});

export default SkillsInterestsModal;
