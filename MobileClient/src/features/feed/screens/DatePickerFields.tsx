import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { DropdownField } from "../../../Components/UI/Menu/DropdownModal";
import { moderateScale } from "react-native-size-matters";

const years = Array.from({ length: 100 }, (_, i) => ({
  label: `${new Date().getFullYear() - i}`,
  value: `${new Date().getFullYear() - i}`,
}));

const months = [
  { label: "January", value: "January" },
  { label: "February", value: "February" },
  { label: "March", value: "March" },
  { label: "April", value: "April" },
  { label: "May", value: "May" },
  { label: "June", value: "June" },
  { label: "July", value: "July" },
  { label: "August", value: "August" },
  { label: "September", value: "September" },
  { label: "October", value: "October" },
  { label: "November", value: "November" },
  { label: "December", value: "December" },
];

// Map month names to numerical values for output
const monthToNumber: { [key: string]: string } = {
  January: "01",
  February: "02",
  March: "03",
  April: "04",
  May: "05",
  June: "06",
  July: "07",
  August: "08",
  September: "09",
  October: "10",
  November: "11",
  December: "12",
};

// Map numerical values to month names for input
const numberToMonth: { [key: string]: string } = {
  "01": "January",
  "02": "February",
  "03": "March",
  "04": "April",
  "05": "May",
  "06": "June",
  "07": "July",
  "08": "August",
  "09": "September",
  "10": "October",
  "11": "November",
  "12": "December",
};

const DatePickerField = React.memo(({ label, value, onChange }) => {
  console.log("DatePickerField - value:", value);

  const parseDate = (dateString: string) => {
    console.log("parseDate - input:", dateString);
    if (!dateString || typeof dateString !== "string") {
      console.log("parseDate - invalid, returning:", { year: "", month: "" });
      return { year: "", month: "" };
    }
    const [year, month] = dateString.split("-");
    const result = {
      year: year && /^\d{4}$/.test(year) ? year : "",
      month: month && /^\d{2}$/.test(month) && numberToMonth[month] ? numberToMonth[month] : "",
    };
    console.log("parseDate - output:", result);
    return result;
  };

  const dateValue = parseDate(value);

  const handleDateChange = (newDate: { year: string; month: string }) => {
    console.log("handleDateChange - input:", newDate);
    const numericalMonth = newDate.month ? monthToNumber[newDate.month] || "" : "";
    const formattedDate = newDate.year || numericalMonth ? `${newDate.year || ""}-${numericalMonth}` : "";
    console.log("handleDateChange - output:", formattedDate);
    onChange(formattedDate);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.row}>
        <DropdownField
          style={styles.dropdown}
          data={months}
          labelField="label"
          valueField="value"
          placeholder="Month"
          value={dateValue.month}
          onSelect={(item) => {
            console.log("Month dropdown - selected:", item);
            handleDateChange({ ...dateValue, month: item?.value || "" });
          }}
        />
        <DropdownField
          style={styles.dropdown}
          data={years}
          labelField="label"
          valueField="value"
          placeholder="Year"
          value={dateValue.year}
          onSelect={(item) => {
            console.log("Year dropdown - selected:", item);
            handleDateChange({ ...dateValue, year: item?.value || "" });
          }}
        />
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: { marginBottom: 16 },
  label: { fontSize: 14, fontWeight: "500", marginBottom: 6, color: "#333" },
  row: { flexDirection: "row", justifyContent: "space-between", gap: moderateScale(8) },
  dropdown: {
    flex: 1,
    marginRight: 8,
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 6,
    padding: 8,
    backgroundColor: "#fff",
  },
});

export default DatePickerField;