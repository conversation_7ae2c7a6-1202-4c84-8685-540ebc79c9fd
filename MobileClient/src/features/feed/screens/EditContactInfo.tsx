import React, { useState, useEffect } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  Text,
  TouchableOpacity,
  TextInput,
  Platform,
} from "react-native";
import CustomInput from "../../../Components/UI/TextInput";
import { DropdownField } from "../../../Components/UI/Menu/DropdownModal";
import AppHeader from "../../../Components/AppHeader";
import { useRoute } from "@react-navigation/native";
import { CountryPicker } from "react-native-country-codes-picker";
import { useUserProfile } from "./useUserDetail";
import { AddressInput } from "../../../Stacks/LeadsAppStack/components/Shared/AddressInput";
import { showToast } from "../../../Components/AppToaster/AppToaster";
import { Button } from "react-native-paper";
import { scale, verticalScale, moderateScale } from "react-native-size-matters";
import moment from "moment";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import { Calendar1 } from "lucide-react-native";
import LocationAddressSearch from "../../../Components/LocationAddressSearch";

const EditContactInfo = () => {
  const route = useRoute();
  const { data } = route.params;

  const { updateUserContactDetails, updateUserEmail } = useUserProfile();
  const [showPicker, setShowPicker] = useState(false);
  const [calendarShowPicker, setCalendarShowPicker] = useState(false);

  const initialBirthday = data?.contactDetails[0]?.dateOfBirth
    ? new Date(data.contactDetails[0].dateOfBirth)
    : new Date();
  const [startDate, setStartDate] = useState<Date>(
    isNaN(initialBirthday.getTime()) ? new Date() : initialBirthday
  );

  const [form, setForm] = useState(() => {
    let phoneRaw = data?.contactDetails[0]?.phone || "";
    let countryCode = "+91";
    let phoneNumber = "";

    if (phoneRaw.includes(" ")) {
      const [cc, num] = phoneRaw.split(" ");
      countryCode = cc.startsWith("+") ? cc : `+${cc}`;
      phoneNumber = num;
    }

    const dob = data?.contactDetails[0]?.dateOfBirth || "";
    let birthday = { year: "", month: "", day: "" };
    if (dob && /^\d{4}-\d{2}-\d{2}$/.test(dob)) {
      const [year, month, day] = dob.split("-");
      birthday = { year, month, day };
    }

    return {
      phone: phoneNumber,
      countryCode: data?.contactDetails[0]?.countryCode || countryCode,
      countryFlag: data?.contactDetails[0]?.countryFlag || "🇮🇳",
      phoneType: data?.contactDetails[0]?.phoneType || "",
      email: data?.email_id || "",
      address: data?.contactDetails[0]?.address || "",
      linkedin: data?.contactDetails[0]?.linkedIn || "",
      github: data?.contactDetails[0]?.github || "",
      birthday,
    };
  });

  // Sync startDate with form.birthday changes
  useEffect(() => {
    if (
      form.birthday.year &&
      form.birthday.month &&
      form.birthday.day &&
      /^\d{4}$/.test(form.birthday.year) &&
      /^\d{2}$/.test(form.birthday.month) &&
      /^\d{2}$/.test(form.birthday.day)
    ) {
      const date = new Date(
        `${form.birthday.year}-${form.birthday.month}-${form.birthday.day}`
      );
      if (!isNaN(date.getTime())) {
        setStartDate(date);
      }
    }
  }, [form.birthday]);

  const handleChange = (field: string, value: any) => {
    setForm((prev) => ({ ...prev, [field]: value }));
  };

  const formatted = (date: Date | number) => {
    return String(moment(date).format("MMM DD, YYYY").toUpperCase());
  };

  const handleReset = () => {
    setForm({
      phone: "",
      countryCode: "+91",
      countryFlag: "🇮🇳",
      phoneType: "",
      email: "",
      address: "",
      linkedin: "",
      github: "",
      birthday: { year: "", month: "", day: "" },
    });
    setStartDate(new Date());
  };

  const onDateSelect = (selectedDate: Date) => {
    setCalendarShowPicker(false);
    if (selectedDate) {
      const year = selectedDate.getFullYear().toString();
      const month = (selectedDate.getMonth() + 1).toString().padStart(2, "0");
      const day = selectedDate.getDate().toString().padStart(2, "0");
      setForm((prev) => ({
        ...prev,
        birthday: { year, month, day },
      }));
      setStartDate(selectedDate);
    }
  };

  const handleSave = async () => {
    try {
      const user_id = data?.user_id;
      if (!user_id) {
        showToast.warning("Error User ID missing!");
        return;
      }
      const fullPhone = `${form.countryCode} ${form.phone}`;
      const contactPayload = {
        phoneType: form.phoneType,
        phone: fullPhone,
        address: form.address,
        dateOfBirth: form.birthday.year
          ? `${form.birthday.year}-${form.birthday.month.padStart(2, "0")}-${form.birthday.day.padStart(2, "0")}`
          : "",
        linkedIn: form.linkedin || "",
        github: form.github || "",
        countryFlag: form.countryFlag,
      };

      const results = await Promise.allSettled([
        updateUserContactDetails(user_id, contactPayload),
        updateUserEmail(user_id, form.email),
      ]);
      const [contactRes, emailRes] = results;

      if (
        contactRes.status === "fulfilled" &&
        emailRes.status === "fulfilled"
      ) {
        showToast.success("Successfully Contact Info updated successfully!");
      } else {
        showToast.error("Error Update failed, please try again.");
        if (contactRes.status === "rejected")
          console.error("Contact update failed:", contactRes.reason);
        if (emailRes.status === "rejected")
          console.error("Email update failed:", emailRes.reason);
      }
    } catch (error) {
      console.error("Save error:", error);
      showToast.error("Something went wrong. Please try again.");
    }
  };

  // Define date constraints
  const fiveYearsAgo = new Date();
  fiveYearsAgo.setFullYear(fiveYearsAgo.getFullYear() - 100); // Allow birthdays up to 100 years ago
  const today = new Date();

  return (
    <View style={{ flex: 1, backgroundColor: "#F9FAFB" }}>
      <AppHeader title="Edit Contact Info" />
      <ScrollView contentContainerStyle={styles.container} bounces={false}>
        <View style={styles.section}>
          <Text style={styles.label}>Phone Number</Text>
          <View style={styles.phoneRow}>
            <TouchableOpacity
              onPress={() => setShowPicker(true)}
              style={styles.countryBox}
            >
              <Text style={styles.flag}>{form.countryFlag}</Text>
              <Text style={styles.countryCode}>{form.countryCode}</Text>
            </TouchableOpacity>
            <TextInput
              style={styles.phoneInput}
              keyboardType="phone-pad"
              value={form.phone}
              onChangeText={(text) => handleChange("phone", text)}
              placeholder="Enter phone number"
            />
          </View>
        </View>
        <DropdownField
          label="Phone Type"
          data={[
            { label: "Home", Text: "Home" },
            { label: "Work", Text: "Work" },
            { label: "Mobile", Text: "Mobile" },
          ]}
          valueField="Text"
          labelField="label"
          placeholder="Select Phone Type"
          value={form.phoneType}
          onSelect={(selected) =>
            handleChange("phoneType", selected?.Text || "")
          }
        />
        <CustomInput
          label="Email"
          value={form.email}
          onChangeText={(text) => handleChange("email", text)}
          placeholder="Enter Email"
          keyboardType="email-address"
        />
        {/* <AddressInput
          label="From Address"
          value={form.address || ""}
          onChangeText={(text) => handleChange("address", text)}
          placeholder="Enter Address..."
        /> */}
        <LocationAddressSearch 
          label="From Address"
          value={form.address || ""}
          onAddressChange={(text) => handleChange("address", text)}
          placeholder="Enter Address..."
        />
        <View style={styles.section}>
          <Text style={styles.label}>Birthday</Text>
          <TouchableOpacity
            onPress={() => {
              setCalendarShowPicker(true);
            }}
            style={{
              flexDirection: "row",
              alignItems: "center",
              paddingHorizontal: scale(12),
              borderRadius: 4,
              borderWidth: 1,
              borderColor: "#ccc",
              backgroundColor: "white",
              marginBottom: verticalScale(5),
              height: verticalScale(28),
              gap: scale(2),
            }}
          >
            <Calendar1 size={16}/>
            <Text
              style={{
                fontSize: moderateScale(12),
                color: form.birthday.year ? "#000" : "#666",
              }}
            >
              {form.birthday.year
                ? formatted(startDate)
                : "Select Birthday"}
            </Text>
          </TouchableOpacity>
        </View>
        <DateTimePickerModal
          isVisible={calendarShowPicker}
          mode="date"
          onConfirm={onDateSelect}
          onCancel={() => setCalendarShowPicker(false)}
          date={startDate}
          minimumDate={fiveYearsAgo}
          maximumDate={today}
          accentColor="#3377ff"
          isDarkModeEnabled={false}
          locale="en-IN"
        />
        <CountryPicker
          show={showPicker}
          style={{
            modal: {
              flex: 1,
              marginTop: "50%",
            },
          }}
          pickerButtonOnPress={(item) => {
            setShowPicker(false);
            handleChange("countryCode", item?.dial_code || "+91");
            handleChange("countryFlag", item?.flag || "🇮🇳");
          }}
          onBackdropPress={() => setShowPicker(false)}
          lang="en"
          popularCountries={["IN", "US", "GB", "AE", "QA", "OM", "KW", "SA", "BH"]}
        />
      </ScrollView>
      <View style={styles.actionRow}>
        <Button
          mode="contained"
          style={styles.cancelButton}
          textColor="#000"
          onPress={() => handleReset()}
        >
          Reset
        </Button>
        <Button
          mode="contained"
          style={styles.saveButton}
          textColor="#fff"
          onPress={() => handleSave()}
        >
          Save Changes
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: "#F9FAFB",
  },
  section: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 8,
    color: "#374151",
  },
  phoneRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  countryBox: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: "#D1D5DB",
    borderRadius: 8,
    marginRight: 12,
    backgroundColor: "#FFFFFF",
  },
  actionRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: scale(16),
    gap: scale(10),
    paddingVertical: verticalScale(10),
  },
  cancelButton: {
    flex: 1,
    borderRadius: 4,
    backgroundColor: "#E5E7EB",
  },
  saveButton: {
    flex: 1,
    borderRadius: 4,
    backgroundColor: "#2563EB",
  },
  flag: {
    fontSize: 18,
  },
  countryCode: {
    marginLeft: 8,
    fontSize: 16,
    color: "#374151",
  },
  phoneInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: "#D1D5DB",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: "#FFFFFF",
  },
  saveButtonContainer: {
    width: "30%",
    alignSelf: "flex-end",
    marginTop: 24,
    marginBottom: 20,
  },
});

export default EditContactInfo;