import { Image, StyleSheet, TouchableOpacity, View, Platform } from "react-native"
import { Text } from "react-native-paper"
import AppHeader from "../../../Components/AppHeader"
import { useFocusEffect, useNavigation } from "@react-navigation/native"
import { ScrollView } from "react-native-gesture-handler"
import { moderateScale, } from "react-native-size-matters"
import { MapPin, Mail, Plus, Pencil, PencilIcon, Building2, Camera } from "lucide-react-native"
import { useCallback, useState } from "react"
import SkillsInterestsModal from "./ReusableModal"
import { useUserProfile } from "./useUserDetail"
import { SvgUri } from "react-native-svg"
import BarComponent from "./BarComponent"
import EditableImagePicker from "../../../Components/UI/Menu/EditableImagePicker"
import { getUserId } from "../../../Common/Utils/Storage"

const FollowersDetailsScreen = () => {
  const navigation = useNavigation()
  const { data, loading, error, fetchUserProfileData, updateUserSkills, updateUserInterests, updating } = useUserProfile();
  const [previewImage, setPreviewImage] = useState(false)
  const [modalState, setModalState] = useState({
    isOpen: false,
    type: null
  });


  useFocusEffect(
    useCallback(() => {
      const fetchUserDetails = async () => {
        try {
          const userId = await getUserId();
          console.log("user id in followers details", userId)
          if (userId) {
            fetchUserProfileData(userId);
          }
        } catch (error) {
          console.log("Error fetching user ID:", error);
        }
      };
      fetchUserDetails();
      // fetchUserProfileData(USER_ID);
    }, [])
  );
  const closeModal = () => setModalState({ isOpen: false, type: null });
  const openModal = (type) => setModalState({ isOpen: true, type });

  const handleSaveSkillsOrInterests = async (items) => {
    const { type } = modalState;
    let success = false;

    if (type === 'skills') {
      success = await updateUserSkills(USER_ID, items);
    } else if (type === 'interests') {
      success = await updateUserInterests(USER_ID, items);
    }

    if (!success) {
      throw new Error(`Failed to update ${type}`);
    }
  };

  const SkillsInterestsSection = ({ title, items = [], type, onEditPress }) => {
    return (
      <View style={styles.skillAndInterestComp}>
        <View style={{
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
          paddingLeft: moderateScale(12)
        }}>
          <Text variant='semiBold' style={styles.text}>{title}</Text>
          <TouchableOpacity style={styles.actionButton} onPress={onEditPress}>
            <Pencil size={14} color="#fff" />
          </TouchableOpacity>
        </View>
        <View style={styles.itemsDisplayContainer}>
          {items.length > 0 ? (
            items.map((item, index) => (
              <View key={item.id || index} style={styles.itemChip}>
                <Text variant="semiBold" style={styles.itemChipText}>
                  {item.skill || item.interest}
                </Text>
              </View>
            ))
          ) : (
            <Text style={styles.emptyText}>No {type} added yet</Text>
          )}
        </View>
      </View>
    );
  };
  const onProfilePreview = () => {
    setPreviewImage(true)
    console.log("preview image")

  }
  const uploadProfilePicture = () => {
    console.log("upload profile picture")
  }
  if (loading) return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Text>Loading...</Text>
    </View>
  );

  if (error) return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Text>Error loading profile data</Text>
    </View>
  );

  return (
    <>
      <AppHeader title="User Details" />
      <View style={styles.container}>
        <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
          <SkillsInterestsModal
            visible={modalState.isOpen}
            onDismiss={closeModal}
            type={modalState.type}
            initialData={modalState.type === 'skills' ? data?.skills || [] : data?.interests || []}
            onSave={handleSaveSkillsOrInterests}
            loading={updating}
          />

          <View style={styles.contentWrapper}>
            <View style={styles.roundedContent}>
              <View style={styles.headerBackground}>
                <Image
                  source={data?.background_image_url
                    ? { uri: data.background_image_url }
                    : require("../../../../assets/ChatImages/User_detail_page_camera_icon.png")}
                  resizeMode="cover"
                  style={styles.headerImage}
                />
                <TouchableOpacity
                  style={{
                    position: 'absolute',
                    right: 10,
                    top: 8,
                    backgroundColor: '#fff',
                    padding: moderateScale(4),
                    borderRadius: moderateScale(40)
                  }}
                  onPress={() => { }}
                >
                  <SvgUri
                    uri='https://res.cloudinary.com/dtfefwwnv/image/upload/v1740391578/camera-minimalistic-svgrepo-com_uplvos.svg'
                    width={16}
                    height={16}
                  />
                </TouchableOpacity>
              </View>
              {/* <EditableImagePicker
                // label="Company Logo"
                imageUri={data?.background_image_url}
                onChange={()=>{}}
                // onChange={(uri) => {
                //   setLogo(uri);
                //   handleChange('org_logo_url', uri);
                // }}
                placeholderIcon={null}
              /> */}

              <View style={styles.profileImageWrapper}>
                <View style={styles.profileImageContainer}>
                  <TouchableOpacity
                    onPress={() => {
                      onProfilePreview;
                    }}
                    activeOpacity={0.7}
                  >
                    <Image
                      resizeMode="cover"
                      style={styles.profileImage}
                      source={
                        data?.profile_image
                          ? { uri: data.profile_image }
                          : require('../../../../assets/ChatImages/User2.png')
                      }
                    />
                  </TouchableOpacity>
                </View>
                {/* <TouchableOpacity
                  style={{
                    position: 'absolute',
                    right: -2,
                    bottom: 6,
                    backgroundColor: '#fff',
                    padding: moderateScale(3),
                    borderRadius: moderateScale(40),
                    opacity: 0.9,
                  }}
                  onPress={() => uploadProfilePicture()}
                >
                  <SvgUri
                    uri="https://res.cloudinary.com/dtfefwwnv/image/upload/v1740391578/camera-minimalistic-svgrepo-com_uplvos.svg"
                    width={16}
                    height={16}
                  />
                </TouchableOpacity> */}
                <EditableImagePicker
                  imageUri={data?.profile_image}
                  onChange={() => { }}
                  // onChange={(uri) => {
                  //   setLogo(uri);
                  //   handleChange('org_logo_url', uri);
                  // }}
                />
              </View>

              <View style={styles.userInfoCard}>
                <TouchableOpacity
                  style={{
                    position: 'absolute',
                    right: 10,
                    top: 8,
                    backgroundColor: '#3377ff',
                    paddingVertical: 8,
                    paddingHorizontal: 8,
                    borderRadius: moderateScale(40),
                  }}
                  onPress={() => navigation.navigate('EditProfile', { data })}
                >
                  <PencilIcon size={16} color="#FFF" />
                </TouchableOpacity>

                <Text variant="semiBold" style={styles.userName}>
                  {data?.user_name || 'UNKNOWN'}
                </Text>
                <View style={{ gap: moderateScale(6) }}>
                  <Text style={styles.contactText}>
                    {data?.profileDetails?.[0]?.currentPosition || '--'}
                  </Text>
                  <Text style={styles.contactText}>
                    {data?.profileDetails?.[0]?.headLine || '--'}
                  </Text>
                  <View
                    style={{
                      flexDirection: 'row',
                      paddingHorizontal: moderateScale(2),
                      gap: moderateScale(2),
                    }}
                  >
                    <MapPin size={14} />
                    <Text style={styles.contactText}>
                      {data?.profileDetails?.[0]?.city || '--'},{' '}
                      {data?.profileDetails[0]?.countryRegion || '--'}
                    </Text>
                    <TouchableOpacity
                      onPress={() => navigation.navigate('EditContactInfo', { data })}
                    >
                      <Text style={{ fontSize: moderateScale(12), color: '#3377ff' }}>
                        -Contact info
                      </Text>
                    </TouchableOpacity>
                  </View>
                  <View style={styles.infoRow}>
                    <Text style={styles.contactText}>
                      Contact: {data?.contactDetails?.[0]?.phone || 'N/A'}
                    </Text>
                  </View>

                  <View style={styles.infoRow}>
                    <Mail size={16} color="#666" />
                    <Text style={styles.emailText}>{data?.email_id || ''}</Text>
                  </View>
                </View>
              </View>
            </View>

            <BarComponent
              text="Work Experience"
              icon={Plus}
              onEdit={(params) => {
                console.log('Navigating to EditWorkInfoScreen...');
                navigation.navigate('EditWorkInfoScreen', params);
              }}
              data={data?.workExperience}
            />

            <SkillsInterestsSection
              title="Skills"
              items={data?.skills || []}
              type="skills"
              onEditPress={() => openModal('skills')}
            />

            <SkillsInterestsSection
              title="Interests"
              items={data?.interests || []}
              type="interests"
              onEditPress={() => openModal('interests')}
            />

            <View style={styles.activityBar}>
              <Text>Activity</Text>
            </View>
            <View style={styles.activityContent}>
              <Text style={styles.emptyText}>No posts available</Text>
            </View>
          </View>
          <View style={{ flex: 1 }}>
            {previewImage && (
              <EditableImagePicker
                visible={previewImage}
                imageUrl={data?.profile_image}
                onClose={() => setPrevieImage(false)}
              />
            )}
          </View>
        </ScrollView>
      </View>
    </>
  );
};

export default FollowersDetailsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingVertical: moderateScale(4),
    paddingHorizontal: moderateScale(6),
  },
  contentWrapper: {
    marginHorizontal: moderateScale(6),
  },
  skillAndInterestComp: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    elevation: 4, backgroundColor: "#fff",
    marginTop: moderateScale(8),
    paddingVertical: moderateScale(12),
    paddingHorizontal: moderateScale(12),
    borderRadius: moderateScale(4),
  },
  text: {
    fontSize: moderateScale(14)
  },
  actionButton: {
    borderRadius: moderateScale(40),
    backgroundColor: "#3377ff",
    paddingVertical: 8,
    paddingHorizontal: 8
  },
  roundedContent: {
    borderRadius: moderateScale(8),
    overflow: 'hidden',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.3,
    elevation: 4
  },
  reusableBars: {
    width: "100%",

    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingLeft: moderateScale(12),

  },
  itemsDisplayContainer: {
    backgroundColor: "#fff",
    paddingVertical: moderateScale(12),
    paddingHorizontal: moderateScale(6),
    flexDirection: "row",
    flexWrap: "wrap",
    gap: moderateScale(8),
    minHeight: moderateScale(60),
  },
  itemChip: {
    backgroundColor: '#f1f3f4',
    paddingHorizontal: moderateScale(12),
    paddingVertical: moderateScale(8),
    borderRadius: moderateScale(16),
  },
  itemChipText: {
    fontSize: moderateScale(12),
  },
  placeholderContent: {
    backgroundColor: "#fff",
    paddingVertical: moderateScale(16),
    paddingHorizontal: moderateScale(12),
    minHeight: moderateScale(60),
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: moderateScale(12),
    color: '#999',
    fontStyle: 'italic',
  },
  contactText: {
    fontSize: moderateScale(11),
  },
  emailText: {
    fontSize: moderateScale(11),
    marginLeft: moderateScale(8),
  },
  activityBar: {
    width: '100%',
    backgroundColor: '#fff',
    marginTop: moderateScale(8),
    paddingVertical: moderateScale(16),
    paddingHorizontal: moderateScale(16),
    borderRadius: moderateScale(2),
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    elevation: 4
  },
  activityContent: {
    marginTop: moderateScale(12),
    paddingVertical: moderateScale(20),
    alignItems: 'center',
  },
  headerBackground: {
    marginTop: moderateScale(2),
    height: moderateScale(160),
    backgroundColor: '#e0e6ed',
    position: 'relative',
    overflow: 'hidden',
  },
  headerImage: {
    width: '100%',
    height: '100%',
  },
  profileImageWrapper: {
    position: 'absolute',
    top: moderateScale(110),
    left: moderateScale(24),
    zIndex: 2,
  },
  profileImageContainer: {
    position: 'relative',
    width: moderateScale(80),
    height: moderateScale(80),
    borderRadius: moderateScale(40),
    backgroundColor: '#fff',
    padding: moderateScale(3),
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  profileImage: {
    width: '100%',
    height: '100%',
    borderRadius: moderateScale(37),
  },
  userInfoCard: {
    position: 'relative',
    backgroundColor: '#fff',
    padding: moderateScale(16),
    paddingTop: moderateScale(30),
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  userName: {
    fontSize: moderateScale(14),
    color: "#333",
    marginVertical: moderateScale(8),
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',

  },
})
