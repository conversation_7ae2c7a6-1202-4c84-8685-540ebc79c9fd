import React, { useMemo, useCallback } from 'react';
import { View, FlatList, StyleSheet, RefreshControl } from 'react-native';
import { useTheme } from 'react-native-paper';
import Animated from 'react-native-reanimated';
import FeedItem from './FeedItem';
import { FeedListProps } from '../../types';

const FeedList: React.FC<FeedListProps> = ({
  posts,
  onCommentPress,
  onScroll,
  refreshing = false,
  onRefresh
}) => {
  const theme = useTheme();
  const memoizedPosts = useMemo(() => posts, [posts]);

  const renderItem = useCallback(
    ({ item }: { item: any }) => {
      return (
        <FeedItem
          item={item}
          onCommentPress={() => {
            // Call the global comment handler from FeedScreen
            if (onCommentPress) {
              onCommentPress(item?.post_id);
            }
          }}
        />
      );
    },
    [onCommentPress]
  );

  const AnimatedFlatList = Animated.createAnimatedComponent(FlatList);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <AnimatedFlatList
        data={memoizedPosts}
        keyExtractor={(item) => item.post_id}
        renderItem={renderItem}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
        initialNumToRender={10}
        maxToRenderPerBatch={5}
        windowSize={10}
        removeClippedSubviews
        keyboardShouldPersistTaps="always"
        onScroll={onScroll}
        bounces={false}
        refreshControl={
          onRefresh ? (
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          ) : undefined
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContent: {
    paddingTop: 8,
    // paddingBottom: 100,
  },
});

export default React.memo(FeedList);
