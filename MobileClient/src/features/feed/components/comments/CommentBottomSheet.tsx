import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  ActivityIndicator,
  FlatList,
  Keyboard,
  Platform,
  KeyboardAvoidingView,
} from 'react-native';
import {
  Text,
  Avatar,
  Divider,
  TextInput,
  IconButton,
  useTheme,
  Button,
} from 'react-native-paper';
import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetTextInput,
} from '@gorhom/bottom-sheet';
import {moderateScale} from 'react-native-size-matters';
import {getShortRelativeTime} from '../../utils/dataFormatter';
import {useSelector} from 'react-redux';

interface CommentBottomSheetProps {
  visible: boolean;
  onClose: () => void;
  comments: any[];
  onSubmitComment: (comment: any) => void;
  loading: boolean;
  postId: string;
}

const CommentItem = ({comment}: {comment: any}) => {
  const theme = useTheme();

  // Check if comment is valid
  if (!comment || !comment.comment_by) {
    return null;
  }

  // Get safe values with fallbacks
  const userName = comment.comment_by.user_name || 'Unknown User';
  const userRole = comment.comment_by.role || 'User';
  const createdAt = comment.created_at || new Date().toISOString();
  const content = comment.content || '';
  const profilePic =
    comment.comment_by.profile_pic || 'https://via.placeholder.com/36';

  return (
    <View style={styles.commentItem}>
      <Avatar.Image
        size={moderateScale(36)}
        source={{uri: profilePic}}
        style={styles.avatar}
      />
      <View style={styles.commentContent}>
        <View style={styles.commentHeader}>
          <Text
            variant="titleSmall"
            style={[styles.userName, {color: theme.colors.onSurface}]}>
            {userName}
          </Text>
          <Text
            variant="labelSmall"
            style={[
              styles.commentMeta,
              {color: theme.colors.onSurfaceVariant},
            ]}>
            {userRole} • {getShortRelativeTime(createdAt)}
          </Text>
        </View>
        <Text
          variant="bodyMedium"
          style={[styles.commentText, {color: theme.colors.onSurface}]}>
          {content}
        </Text>
        <View style={styles.commentActions}>
          <Button
            mode="text"
            compact
            style={styles.actionButton}
            labelStyle={{color: theme.colors.primary}}>
            Like
          </Button>
          <Button
            mode="text"
            compact
            style={styles.actionButton}
            labelStyle={{color: theme.colors.primary}}>
            Reply
          </Button>
        </View>
      </View>
    </View>
  );
};

const CommentBottomSheet: React.FC<CommentBottomSheetProps> = ({
  visible,
  onClose,
  comments,
  onSubmitComment,
  loading,
  postId,
}) => {
  const theme = useTheme();
  const bottomSheetRef = useRef<BottomSheet>(null);
  const [comment, setComment] = useState('');
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const userName = useSelector((state: any) => state.userName) || 'You';

  // Snap points for the bottom sheet
  const snapPoints = useMemo(() => ['60%', '90%'], []);

  // Handle keyboard visibility
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
        bottomSheetRef.current?.snapToIndex(1);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      },
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  // Open/close the bottom sheet based on the visible prop
  useEffect(() => {
    if (visible) {
      bottomSheetRef.current?.expand();
    } else {
      bottomSheetRef.current?.close();
    }
  }, [visible]);

  // Handle comment submission
  const handleSubmitComment = useCallback(() => {
    if (comment.trim()) {
      onSubmitComment({
        comment: comment,
        parentCommentId: 'root',
        rootParentId: 'root',
      });
      setComment('');
      Keyboard.dismiss();
    }
  }, [comment, onSubmitComment]);

  // Render backdrop component
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    [],
  );

  // Handle sheet changes
  const handleSheetChanges = useCallback(
    (index: number) => {
      if (index === -1) {
        onClose();
      }
    },
    [onClose],
  );

  // Render empty state
  const renderEmptyComponent = useCallback(() => {
    if (loading) {
      return (
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <Text
          variant="bodyLarge"
          style={{color: theme.colors.onSurfaceVariant}}>
          No comments yet. Be the first to comment!
        </Text>
      </View>
    );
  }, [loading, theme]);

  // Render list header
  const renderHeader = useCallback(
    () => (
      <View style={styles.header}>
        <Text variant="titleLarge" style={{color: theme.colors.onSurface}}>
          Comments
        </Text>
        <IconButton
          icon="close"
          size={moderateScale(24)}
          onPress={onClose}
          iconColor={theme.colors.onSurfaceVariant}
        />
      </View>
    ),
    [onClose, theme],
  );

  // Filter out invalid comments
  const validComments = useMemo(() => {
    return comments?.filter(comment => comment && comment.id) || [];
  }, [comments]);

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={visible ? 0 : -1}
      snapPoints={snapPoints}
      onChange={handleSheetChanges}
      backdropComponent={renderBackdrop}
      enablePanDownToClose
      keyboardBehavior="extend"
      keyboardBlurBehavior="restore"
      android_keyboardInputMode="adjustResize"
      style={styles.bottomSheet}
      handleIndicatorStyle={{backgroundColor: theme.colors.onSurfaceVariant}}
      backgroundStyle={{backgroundColor: theme.colors.background}}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'android' ? 'padding' : undefined}
        style={{flex: 1}}
        keyboardVerticalOffset={Platform.OS === 'android' ? 64 : 0}>
        <View style={styles.contentContainer}>
          {renderHeader()}
          <Divider style={styles.divider} />

          <FlatList
            data={validComments}
            renderItem={({item}) => <CommentItem comment={item} />}
            keyExtractor={item => item.id || Math.random().toString()}
            contentContainerStyle={styles.commentsList}
            ListEmptyComponent={renderEmptyComponent}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
            bounces={false}
          />

          <Divider style={styles.divider} />

          <View style={styles.inputContainer}>
            <Avatar.Icon
              size={moderateScale(36)}
              icon="account"
              style={{backgroundColor: theme.colors.primary}}
            />
            {Platform.OS === 'ios' ? (
              <BottomSheetTextInput
                placeholder="Add a comment..."
                value={comment}
                onChangeText={setComment}
                style={[
                  styles.input,
                  {
                    backgroundColor: theme.colors.surfaceVariant,
                    color: theme.colors.onSurface,
                  },
                ]}
                placeholderTextColor={theme.colors.onSurfaceVariant}
                multiline
              />
            ) : (
              <TextInput
                mode="outlined"
                placeholder="Add a comment..."
                value={comment}
                onChangeText={setComment}
                style={styles.input}
                multiline
                outlineStyle={{borderRadius: moderateScale(20)}}
                dense
              />
            )}
            <IconButton
              icon="send"
              size={moderateScale(24)}
              onPress={handleSubmitComment}
              disabled={!comment.trim()}
              iconColor={
                comment.trim()
                  ? theme.colors.primary
                  : theme.colors.onSurfaceDisabled
              }
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </BottomSheet>
  );
};

const styles = StyleSheet.create({
  bottomSheet: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  contentContainer: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateScale(8),
  },
  divider: {
    marginVertical: moderateScale(8),
  },
  commentsList: {
    paddingHorizontal: moderateScale(16),
    flexGrow: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: moderateScale(20),
    minHeight: moderateScale(200),
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateScale(8),
    paddingBottom: Platform.OS === 'ios' ? moderateScale(24) : moderateScale(8),
  },
  input: {
    flex: 1,
    marginHorizontal: moderateScale(8),
    maxHeight: moderateScale(100),
    borderRadius: moderateScale(20),
    paddingHorizontal: moderateScale(12),
    paddingVertical: moderateScale(8),
    //fontFamily: 'Montserrat-Regular',
  },
  commentItem: {
    flexDirection: 'row',
    marginBottom: moderateScale(16),
  },
  avatar: {
    marginRight: moderateScale(12),
  },
  commentContent: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  commentHeader: {
    marginBottom: moderateScale(4),
  },
  userName: {
    //fontFamily: 'Montserrat-Medium',
  },
  commentMeta: {
    //fontFamily: 'Montserrat-Regular',
    marginTop: moderateScale(2),
  },
  commentText: {
    //fontFamily: 'Montserrat-Regular',
  },
  commentActions: {
    flexDirection: 'row',
    marginTop: moderateScale(4),
  },
  actionButton: {
    marginRight: moderateScale(8),
    paddingHorizontal: 0,
    minWidth: moderateScale(50),
  },
});

export default CommentBottomSheet;
