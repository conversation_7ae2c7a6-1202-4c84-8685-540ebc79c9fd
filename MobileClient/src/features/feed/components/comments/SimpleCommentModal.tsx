import React from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {
  Modal,
  Portal,
  Text,
  Button,
  ActivityIndicator,
  useTheme,
  TextInput,
} from 'react-native-paper';
import {moderateScale} from 'react-native-size-matters';

interface SimpleCommentModalProps {
  visible: boolean;
  onClose: () => void;
  comments: any[];
  onSubmitComment: (comment: any) => void;
  loading: boolean;
  postId: string;
}

const SimpleCommentModal: React.FC<SimpleCommentModalProps> = ({
  visible,
  onClose,
  comments,
  onSubmitComment,
  loading,
  postId,
}) => {
  const theme = useTheme();
  const [commentText, setCommentText] = React.useState('');

  const handleSubmit = () => {
    if (commentText.trim()) {
      onSubmitComment({
        comment: commentText,
        parentCommentId: 'root',
        rootParentId: 'root',
      });
      setCommentText('');
    }
  };

  const renderComments = () => {
    // Log for debugging
    console.log('Rendering comments in SimpleCommentModal:', {
      commentsCount: comments?.length || 0,
      loading,
      postId,
    });

    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
        </View>
      );
    }

    if (!comments || comments.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text variant="bodyLarge">No comments yet</Text>
        </View>
      );
    }

    // Filter out invalid comments
    const validComments = comments.filter(comment => comment && comment.id);

    return validComments.map((comment, index) => (
      <View key={comment.id || `comment-${index}`} style={styles.commentItem}>
        <Text variant="titleSmall" style={styles.userName}>
          {comment.comment_by?.user_name || 'Unknown User'}
        </Text>
        <Text variant="bodyMedium">{comment.content || ''}</Text>
      </View>
    ));
  };

  // Log for debugging
  React.useEffect(() => {
    console.log('SimpleCommentModal visibility changed:', visible);
  }, [visible]);

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onClose}
        contentContainerStyle={[
          styles.modalContainer,
          {backgroundColor: theme.colors.background},
        ]}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={{flex: 1}}>
          <View style={styles.header}>
            <Text variant="titleLarge">Comments</Text>
            <Button onPress={onClose}>Close</Button>
          </View>

          <ScrollView style={styles.commentsContainer} bounces={false}>
            {renderComments()}
          </ScrollView>

          <View style={styles.inputContainer}>
            <TextInput
              mode="outlined"
              label="Add a comment"
              value={commentText}
              onChangeText={setCommentText}
              style={styles.textInput}
              multiline
            />
            <Button
              mode="contained"
              onPress={handleSubmit}
              disabled={!commentText.trim()}
              style={styles.submitButton}>
              Post
            </Button>
          </View>
        </KeyboardAvoidingView>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    margin: moderateScale(20),
    padding: moderateScale(20),
    borderRadius: moderateScale(10),
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: moderateScale(16),
  },
  commentsContainer: {
    flex: 1,
    marginBottom: moderateScale(16),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: moderateScale(20),
  },
  emptyContainer: {
    padding: moderateScale(20),
    alignItems: 'center',
  },
  commentItem: {
    padding: moderateScale(10),
    marginBottom: moderateScale(10),
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  userName: {
    fontWeight: 'bold',
    marginBottom: moderateScale(4),
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: moderateScale(8),
  },
  textInput: {
    flex: 1,
    marginRight: moderateScale(8),
  },
  submitButton: {
    marginLeft: moderateScale(8),
  },
});

export default SimpleCommentModal;
