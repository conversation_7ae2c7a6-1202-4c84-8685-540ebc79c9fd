import React, { useMemo, useCallback } from 'react';
import { View, FlatList } from 'react-native';
import { useTheme } from 'react-native-paper';
import FeedItem from './FeedItem';
import { FeedListProps } from '../types';
import { moderateScale } from '../../../Utils/responsiveUtils';

const FeedList: React.FC<FeedListProps> = ({ posts, onCommentPress }) => {
  const theme = useTheme();
  const memoizedPosts = useMemo(() => posts, [posts]);
  
  const renderItem = useCallback(
    ({ item }) => <FeedItem item={item} onCommentPress={onCommentPress} />,
    [onCommentPress]
  );

  return (
    <View style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <FlatList
        data={memoizedPosts}
        keyExtractor={(item) => item.post_id}
        renderItem={renderItem}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ 
          paddingHorizontal: moderateScale(16),
          paddingBottom: moderateScale(100),
          paddingTop: moderateScale(8),
        }}
        initialNumToRender={10}
        maxToRenderPerBatch={5}
        windowSize={10}
        removeClippedSubviews
        keyboardShouldPersistTaps="always"
        bounces={false}
      />
    </View>
  );
};

export default React.memo(FeedList);
