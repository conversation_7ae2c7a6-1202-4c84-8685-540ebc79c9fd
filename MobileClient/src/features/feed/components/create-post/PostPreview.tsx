import React from 'react';
import {View, StyleSheet, ScrollView} from 'react-native';
import {Card, Avatar, Text, useTheme} from 'react-native-paper';
import {moderateScale} from 'react-native-size-matters';
import ImageGallery from '../feed-item/ImageGallery';
import {getShortRelativeTime} from '../../utils/dataFormatter';

interface PostPreviewProps {
  content: string;
  images: string[];
  profile: any;
}

export const PostPreview: React.FC<PostPreviewProps> = ({
  content,
  images,
  profile,
}) => {
  const theme = useTheme();
  const now = new Date().toISOString();

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      showsVerticalScrollIndicator={false}
     bounces={false}>
      <View style={styles.card} mode="elevated">
        <Card.Content style={styles.cardContent}>
          <View style={styles.header}>
            <Avatar.Image
              size={moderateScale(42)}
              source={{ uri: profile?.profile_pic }}
              style={styles.avatar}
            />

            <View style={styles.headerText}>
              <Text variant="semiBold" style={[styles.userName, { color: theme.colors.onSurface }]}>
                {profile?.user_name}
              </Text>
              <Text
                variant="regular"
                style={[styles.timeText, { color: theme.colors.onSurfaceVariant }]}
              >
                Just now
              </Text>
            </View>
          </View>

          {content && (
            <Text style={[styles.content, { color: theme.colors.onSurface }]}>{content}</Text>
          )}

          {images && images.length > 0 && <ImageGallery images={images} />}
        </Card.Content>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // padding: moderateScale(16),
  },
  card: {
    marginVertical: moderateScale(20),
    // marginHorizontal: moderateScale(16),
    // borderRadius: moderateScale(12),
    borderWidth: 1,
    borderColor: '#E5E7EB', // Remove border
    backgroundColor: '#FFF',
  },
  cardContent: {
    paddingHorizontal: moderateScale(16),
    paddingTop: moderateScale(6),
    paddingBottom: moderateScale(4),
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: moderateScale(8),
  },
  avatar: {
    marginRight: moderateScale(12),
  },
  headerText: {
    flex: 1,
  },
  userName: {
    fontSize: moderateScale(12),
    letterSpacing: 0.1,
  },
  timeText: {
    marginTop: moderateScale(2),
    // fontFamily: 'Montserrat-Regular',
    fontSize: moderateScale(11),
    opacity: 0.8,
  },
  content: {
    fontSize: moderateScale(12),
    // lineHeight: moderateScale(24),
    // marginBottom: moderateScale(4),
    // fontFamily: 'Geist-Regular',
  },
});

export default PostPreview;
