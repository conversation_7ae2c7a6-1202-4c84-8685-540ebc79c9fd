import React, {useState} from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  TextInput,
  SafeAreaView
} from 'react-native';
import {
 
  Avatar,
  Text,
  IconButton,
  useTheme,
  Surface,
} from 'react-native-paper';
import {moderateScale} from 'react-native-size-matters';
import FastImage from 'react-native-fast-image';
import {handleSelectImage} from '../../../../Stacks/ChatStack/ChatRoom/Room/ChatHandlers';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ImageIcon } from 'lucide-react-native';

interface ImageResponse {
  base64: string;
  fileName: string;
  fileSize: number;
  type: string;
  uri: string;
}

interface CreatePostFormProps {
  content: string;
  setContent: (text: string) => void;
  rawImages: ImageResponse[];
  setRawImages: React.Dispatch<React.SetStateAction<ImageResponse[]>>;
  profile: any;
  onImagePreview?: (index: number) => void;
}

export const CreatePostForm: React.FC<CreatePostFormProps> = ({
  content,
  setContent,
  rawImages,
  setRawImages,
  profile,
  onImagePreview,
}) => {
  const theme = useTheme();
const insets = useSafeAreaInsets()
  const imageHandler = async () => {
    try {
      const imageResponse = await handleSelectImage();
      if (Array.isArray(imageResponse) && imageResponse.length > 0) {
        setRawImages(imageResponse);
      }
    } catch (error) {
      console.log('Error selecting images:', error);
    }
  };

  const handleImagePress = (index: number) => {
    if (onImagePreview) {
      onImagePreview(index);
    }
  };
  const {bottom} = useSafeAreaInsets();
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      style={styles.container}
      keyboardVerticalOffset={Platform.OS === 'ios' ? bottom + 100 : 30}
    >
      <View style={styles.header}>
        <Avatar.Image
          size={moderateScale(40)}
          source={{ uri: profile?.profile_pic }}
          style={styles.avatar}
        />
        <View style={styles.headerText}>
          <Text variant="semiBold" style={[styles.userName, { color: theme.colors.onSurface }]}>
            {profile?.user_name}
          </Text>
        </View>
      </View>
      <ScrollView
        style={styles.scrollView}
        bounces={false}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        <View style={{ flex: 1, marginTop: moderateScale(12) }}>
          <TextInput
            autoFocus
            multiline
            placeholder="What's on your mind?"
            value={content}
            onChangeText={setContent}
            style={[styles.input, { backgroundColor: theme.colors.background }]}
            placeholderTextColor={theme.colors.onSurfaceVariant}
            // underlineColor="transparent"
            // activeUnderlineColor="transparent"
            textAlignVertical="top"
          />

          {Array.isArray(rawImages) && rawImages.length > 0 && (
            <View style={styles.imageContainer} testID="image-container">
              {rawImages.map((image, index) => (
                <Surface
                  key={index}
                  style={[styles.imageSurface, { borderColor: theme.colors.surfaceVariant }]}
                  elevation={1}
                >
                  <FastImage source={{ uri: image.uri }} style={styles.image} resizeMode="cover" />

                  <View style={styles.imageActions}>
                    {/* <IconButton
                      icon="pencil-outline"
                      size={moderateScale(16)}
                      onPress={() => handleImagePress(index)}
                      style={[
                        styles.imageButton,
                        {backgroundColor: theme.colors.surfaceVariant},
                      ]}
                      iconColor={theme.colors.onSurfaceVariant}
                    /> */}

                    <IconButton
                      icon="close"
                      size={moderateScale(16)}
                      onPress={() => {
                        const filterdRawImages = rawImages?.filter((item: ImageResponse) => {
                          return item.uri !== image.uri;
                        });
                        setRawImages(filterdRawImages);
                      }}
                      style={[styles.imageButton, { backgroundColor: theme.colors.surfaceVariant }]}
                      iconColor={theme.colors.onSurfaceVariant}
                    />
                  </View>
                </Surface>
              ))}
            </View>
          )}
        </View>
      </ScrollView>
      <SafeAreaView style={styles.toolbar}>
        {/* <IconButton
          icon="image-outline"
          size={moderateScale(24)}
          onPress={() => {
            Keyboard.dismiss();
            imageHandler();
          }}
          style={[styles.toolbarButton, { backgroundColor: theme.colors.primary }]}
          iconColor={theme.colors.onPrimary}
        /> */}
        <TouchableOpacity onPress={()=>{Keyboard.dismiss();
        imageHandler();}}><ImageIcon /></TouchableOpacity>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: moderateScale(16),
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    // marginBottom: moderateScale(10),
  },
  avatar: {
    marginRight: moderateScale(12),
  },
  headerText: {
    // flex: 1,
   
  },
  userName: {
    //fontFamily: 'Geist-Medium',
    fontSize:moderateScale(16),
   
    // textTransform:'capitalize'
  },
  input: {
    // minHeight: moderateScale(150),
    fontSize: moderateScale(16),
    // lineHeight: moderateScale(24),
    //fontFamily: 'Geist-Regular',
    padding: 0,
    marginBottom: moderateScale(16),
  },
  imageContainer: {
    marginBottom: moderateScale(16),
  },
  imageSurface: {
    borderRadius: moderateScale(12),
    overflow: 'hidden',
    borderWidth: 1,
  },
  image: {
    width: '100%',
    height: moderateScale(200),
    borderRadius: moderateScale(12),
  },
  imageActions: {
    position: 'absolute',
    bottom: moderateScale(8),
    right: moderateScale(8),
    flexDirection: 'row',
  },
  imageButton: {
    margin: moderateScale(4),
  },
  toolbar: {
    // position : 'absolute',
    // bottom : 30,
    // right : 20,
// backgroundColor:"white",
    // flexDirection: 'row',
    padding: moderateScale(10),
    
    // paddingBottom: moderateScale(120),
    // // borderTopWidth: 1,
    // borderTopColor: 'rgba(0,0,0,0.1)',
    alignItems: 'flex-start',
  },
  toolbarButton: {
    margin: moderateScale(4),
  },
});

export default CreatePostForm;
