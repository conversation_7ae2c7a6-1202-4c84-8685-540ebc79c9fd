import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { RootStackParamList } from '../Routes/StackTypes';

// Screens
// import LogOut from '../../Stacks/ProfileStack/LogOut';
import ProfileScreen from '../../Stacks/FeedStack/Screens/Profile/ProfileScreen';
import WorkFlowTasks from '../../Stacks/ProfileStack/workflowTasks/WorkFlowTasks';
import FollowersDetailsScreen from '../../features/feed/screens/FollowersDetailsScreen';
import EditProfile from '../../features/feed/screens/EditProfile';
import EditWorkInfoScreen from '../../features/feed/screens/Editwork';
import EditContactInfo from '../../features/feed/screens/EditContactInfo';

const ProfileStack = createNativeStackNavigator<RootStackParamList>();

/**
 * Profile stack navigator for the bottom tab
 */
export const ProfileStackNavigator: React.FC = () => {
  return (
    <ProfileStack.Navigator screenOptions={{ headerShown: false }}>
      <ProfileStack.Screen name="UserProfile" component={ProfileScreen} />
      <ProfileStack.Screen name="WorkFlowTasks" component={WorkFlowTasks} />
      <ProfileStack.Screen name='FollowersDetailsScreen' component={FollowersDetailsScreen}/>
      <ProfileStack.Screen name='EditProfile' component={EditProfile}/>
      <ProfileStack.Screen name='EditWorkInfoScreen' component={EditWorkInfoScreen}/>
      <ProfileStack.Screen name='EditContactInfo' component={EditContactInfo}/>
      
      
    </ProfileStack.Navigator>
  );
};