import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { RootStackParamList } from '../Routes/StackTypes';

// Apollo Provider
import FeedApolloProvider from '../../features/feed/api/ApolloWrapper';

// Screens
import FeedScreen from '../../features/feed/screens/FeedScreen';
import CreatePostScreen from '../../features/feed/screens/CreatePostScreen';
import AddConnectionScreen from '../../features/feed/screens/AddConnectionsScreen';

const FeedStack = createNativeStackNavigator<RootStackParamList>();

/**
 * Feed stack navigator wrapped with Apollo provider
 */
export const FeedStackNavigator: React.FC = () => {
  return (
    <FeedApolloProvider>
      <FeedStack.Navigator screenOptions={{ headerShown: false }}>
      <FeedStack.Screen name="FeedScreen" component={FeedScreen} />
        <FeedStack.Screen  name="CreatePostScreen" component={CreatePostScreen} />
        <FeedStack.Screen name="AddConnectionScreen" component={AddConnectionScreen}/>
      </FeedStack.Navigator>
    </FeedApolloProvider>
  );
};