import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {RootStackParamList} from '../Routes/StackTypes';
import {useTheme as usePaperTheme} from 'react-native-paper';
import LeadsScreenMain from '../../Stacks/LeadsAppStack/Screens/LeadsScreenMain/LeadsScreenMain';
import CreateActivityScreen from '../../Stacks/SalesManagementStack/screens/CreateActivity/CreateActivityScreen';
import ProspectDetails from '../../Stacks/SalesManagementStack/screens/ProspectDetails/ProspectDetails';
import ViewEnquiryForm from '../../Stacks/SalesManagementStack/screens/ProspectDetails/Enquiry/ViewEnquiryForm';
import ViewJobDetails from '../../Stacks/SalesManagementStack/screens/ProspectDetails/Enquiry/ViewJobDetails';
import ActivityDetailsModal from '../../Stacks/SalesManagementStack/screens/ProspectDetails/ActivityDetailsModal';
import QuoteDetailScreen from '../../Stacks/SalesManagementStack/screens/ProspectDetails/Quotes/Components/QuotesDetailCard';
import CompetitiveIntelligenceDetail from '../../Stacks/LeadsAppStack/Screens/CompetitiveIntelligence/CompetitiveIntelligenceDetail';
// import ActivityDetails from '../../Stacks/SalesManagementStack/screens/ProspectDetails/ActivityDetails';

export const LeadsStackNavigation = ({route } : any) => {
  const paperTheme = usePaperTheme();
  const LeadsStack = createNativeStackNavigator<RootStackParamList>();
  const appData = route?.params?.appData;
  return (
    <LeadsStack.Navigator
      screenOptions={{
        headerShown: false,
        contentStyle: {backgroundColor: paperTheme.colors.background},
      }}>
         <LeadsStack.Screen name="LeadsScreenMain" component={LeadsScreenMain} initialParams={{appData}}/>
         {/* <LeadsStack.Screen name="CreateActivityScreen" component={CreateActivityScreen} /> */}
         <LeadsStack.Screen name="ProspectDetails" component={ProspectDetails} />
         <LeadsStack.Screen name="ViewEnquiryForm" component={ViewEnquiryForm} />
         <LeadsStack.Screen name="QuoteDetailScreen" component={QuoteDetailScreen} />
         <LeadsStack.Screen name="ViewJobDetails" component={ViewJobDetails} />
         <LeadsStack.Screen name="CompetitiveIntelligenceDetail" component={CompetitiveIntelligenceDetail} />
        <LeadsStack.Screen
  name="ActivityDetailsModal"
  component={ActivityDetailsModal}
/>

    </LeadsStack.Navigator>
  );
};
