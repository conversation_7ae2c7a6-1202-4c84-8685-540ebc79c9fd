import React from 'react';
import {Dimensions} from 'react-native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {createDrawerNavigator} from '@react-navigation/drawer';
import {RootStackParamList} from './StackTypes';
// Navigation Stacks
import BottomTabStack from './BottomTabStack';
import {ChatStackNavigator} from '../StackRoutes/ChatStackNavigator';
import {FeedStackNavigator} from '../StackRoutes/FeedStackNavigator';
import { BottomSheetProvider } from '../Context/BottomSheetContext';
import {TrackingStackNavigator} from '../StackRoutes/TrackAndTraceStackNavigator';
import DrawerStack from './DrawerStack';
// Sales Management Stack
import SalesManagementStack from '../../Stacks/SalesManagementStack/SalesManagementStack';
// Authentication Screens
import SplashScreen from '../../Authentication/screens/OnBoarding/SplashScreen/Splash.screen';
import WelcomeScreen from '../../Authentication/screens/OnBoarding/WelcomeScreen/Welcome.screen';
import SignIn from '../../Authentication/screens/SignIn/SignIn.screen';
import SignUp from '../../Authentication/screens/SignUp/SignUp.Screen';
import DomainEntry from '../../Authentication/screens/DomainEntry/DomainEntry.screen';
// Other Screens
import AdaptiveCardExample from '../../Stacks/TestStack/AdaptiveCards/AdaptiveScreen';

// import Attendace
//ShipmentTrackingScreen
import ShipmentTrackingScreen from '../../Stacks/TrackingStack/Screens/ShipmentTrackingScreen';
import ProspectDetails from '../../Stacks/SalesManagementStack/screens/ProspectDetails/ProspectDetails';
import EnquiryForm from '../../Stacks/LeadsAppStack/Screens/EnquiryScreens/EnquiryForm';
import CreateActivityScreen from '../../Stacks/SalesManagementStack/screens/CreateActivity/CreateActivityScreen';
import SearchScreen from '../../Components/SearchLocation/SearchScreen';

import LeadSuspectForm from '../../Stacks/LeadsAppStack/Screens/LeadSuspectForm/LeadSuspectForm';
import LeadProspectForm from '../../Stacks/LeadsAppStack/Screens/LeadProspectForm/LeadProspectForm';
import LeadForm from '../../Stacks/LeadsAppStack/Screens/LeadFormScreen/LeadForm';
import EditServicesForm from '../../Stacks/SalesManagementStack/screens/ProspectDetails/Services/EditServicesForm';
import LeadContactForm from '../../Stacks/LeadsAppStack/Screens/LeadContactForm/LeadContactForm';
import CompanyDetailsForm from '../../Stacks/LeadsAppStack/Screens/CompanyDetailsForm/CompanyDetailsForm';
import { AccountDetailsForm } from '../../Stacks/LeadsAppStack/Screens/AccountDetails/AccountDetailsForm';
import { AttendanceStackNavigator } from '../StackRoutes/HomeStackNavigator';
import { Bot } from 'lucide-react-native';
import DrawerStackNavigator from '../../Stacks/DrawerNavigatorStack';
import { CompetitiveIntelligenceForm } from '../../Stacks/LeadsAppStack/Screens/CompetitiveIntelligence/CompetitiveIntelligenceForm';
const Stack = createNativeStackNavigator<RootStackParamList>();


const MainStack: React.FC = () => {
  return (
 
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        <Stack.Group>
          <Stack.Screen name="SplashScreen" component={SplashScreen} />
          <Stack.Screen name="Initial" component={WelcomeScreen} />
          <Stack.Screen name="DomainEntry" component={DomainEntry} />

          <Stack.Screen name="SignIn" component={SignIn} />
          <Stack.Screen name="SignUp" component={SignUp} />
          {/* <Stack.Screen name="BottomTabStack" component={BottomTabStack} /> */}
          <Stack.Screen name="LeadSuspectForm" component={LeadSuspectForm} />
          <Stack.Screen name="LeadForm" component={LeadForm} />
          <Stack.Screen name="LeadProspectForm" component={LeadProspectForm} />
          <Stack.Screen name="EditServicesForm" component={EditServicesForm} />
          <Stack.Screen name="LeadContactForm" component={LeadContactForm} />
          <Stack.Screen name="CompanyDetailsForm" component={CompanyDetailsForm} />
          <Stack.Screen name="AccountDetailsForm" component={AccountDetailsForm} />
          <Stack.Screen name="CompetitiveIntelligenceForm" component={CompetitiveIntelligenceForm} />
        </Stack.Group>

        <Stack.Group>
          <Stack.Screen name="AdaptiveCardExample" component={AdaptiveCardExample} />
          <Stack.Screen name="ShipmentTrackingScreen" component={ShipmentTrackingScreen} />
          <Stack.Screen name="Attendance" component={AttendanceStackNavigator} />
        </Stack.Group>
        <Stack.Group>
          <Stack.Screen name="ChatStack" component={ChatStackNavigator} />
          <Stack.Screen name="FeedStack" component={FeedStackNavigator} />
          <Stack.Screen name="ProspectDetails" component={ProspectDetails} />
          <Stack.Screen name="CreateActivityScreen" component={CreateActivityScreen} />
          {/* <Stack.Screen name="TrackAndTrace" component={TrackingStackNavigator} /> */}
        </Stack.Group>
        <Stack.Screen name="Drawer" component={DrawerStackNavigator} />
        <Stack.Screen name="EnquiryForm" component={EnquiryForm} />
        <Stack.Screen name="searchLocation" component={SearchScreen} />
      </Stack.Navigator>
  
  );
};

export default MainStack;
