import React, { useState } from 'react';
import { View, TouchableOpacity, Text, Alert, StyleSheet } from 'react-native';
import { ImagePickerModal } from '../../../Components/ImagePickerModal';
import { requestCameraAndGalleryPermissions } from '../MediaPermissions';
import galleryAccess, { checkGalleryPermission } from './galleryPermissions';

/**
 * Example component demonstrating gallery permissions usage
 */
const GalleryPermissionsExample = () => {
  const [imageUri, setImageUri] = useState<string>('');
  const [modalVisible, setModalVisible] = useState(false);

  // Example 1: Basic gallery permission check
  const handleCheckGalleryPermission = async () => {
    const hasPermission = await checkGalleryPermission();
    Alert.alert(
      'Gallery Permission Status',
      hasPermission ? 'Gallery permission is granted' : 'Gallery permission is not granted',
      [{ text: 'OK' }]
    );
  };

  // Example 2: Request gallery permission
  const handleRequestGalleryPermission = async () => {
    const granted = await galleryAccess();
    Alert.alert(
      'Gallery Permission Request',
      granted ? 'Gallery permission granted!' : 'Gallery permission denied',
      [{ text: 'OK' }]
    );
  };

  // Example 3: Request both camera and gallery permissions
  const handleRequestBothPermissions = async () => {
    const permissions = await requestCameraAndGalleryPermissions();
    
    let message = '';
    if (permissions.camera && permissions.gallery) {
      message = 'Both camera and gallery permissions granted!';
    } else if (permissions.camera) {
      message = 'Camera permission granted, but gallery permission denied';
    } else if (permissions.gallery) {
      message = 'Gallery permission granted, but camera permission denied';
    } else {
      message = 'Both permissions denied';
    }

    Alert.alert('Permissions Result', message, [{ text: 'OK' }]);
  };

  // Example 4: Open image picker with permission check
  const handleOpenImagePicker = async () => {
    const permissions = await requestCameraAndGalleryPermissions();
    
    if (!permissions.camera || !permissions.gallery) {
      Alert.alert(
        'Permissions Required',
        'Camera and gallery permissions are required to select images.',
        [{ text: 'OK' }]
      );
      return;
    }

    setModalVisible(true);
  };

  const handleImageChange = (uri: string) => {
    setImageUri(uri);
    console.log('Selected image:', uri);
    
    if (uri) {
      Alert.alert('Image Selected', `Image URI: ${uri}`, [{ text: 'OK' }]);
    } else {
      Alert.alert('Image Removed', 'Image has been removed', [{ text: 'OK' }]);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Gallery Permissions Example</Text>
      
      <TouchableOpacity style={styles.button} onPress={handleCheckGalleryPermission}>
        <Text style={styles.buttonText}>Check Gallery Permission</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={handleRequestGalleryPermission}>
        <Text style={styles.buttonText}>Request Gallery Permission</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={handleRequestBothPermissions}>
        <Text style={styles.buttonText}>Request Camera & Gallery Permissions</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.primaryButton} onPress={handleOpenImagePicker}>
        <Text style={styles.primaryButtonText}>Open Image Picker</Text>
      </TouchableOpacity>

      {imageUri ? (
        <Text style={styles.imageInfo}>Selected Image: {imageUri}</Text>
      ) : (
        <Text style={styles.imageInfo}>No image selected</Text>
      )}

      <ImagePickerModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onChange={handleImageChange}
        label="Example Image"
        hasImage={!!imageUri}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
  },
  button: {
    backgroundColor: '#f0f0f0',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 16,
    color: '#333',
  },
  primaryButton: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
    alignItems: 'center',
  },
  primaryButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold',
  },
  imageInfo: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 10,
  },
});

export default GalleryPermissionsExample;
