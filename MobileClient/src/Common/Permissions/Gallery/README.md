# Gallery Permissions Module

A comprehensive React Native module for handling gallery/photo library permissions across iOS and Android platforms, with support for Android 13+ granular media permissions.

## Features

- **Cross-platform support**: iOS and Android
- **Android 13+ compatibility**: Handles new granular media permissions (READ_MEDIA_IMAGES, READ_MEDIA_VIDEO)
- **Backward compatibility**: Supports older Android versions with READ_EXTERNAL_STORAGE
- **User-friendly alerts**: Provides clear permission request dialogs and settings navigation
- **Comprehensive error handling**: Proper error logging and fallback mechanisms

## Installation

The module uses `react-native-permissions` which should already be installed in your project.

## Usage

### Basic Gallery Access

```typescript
import galleryAccess, { checkGalleryPermission } from '../Common/Permissions/Gallery';

// Request gallery permission
const handleGalleryAccess = async () => {
  const hasPermission = await galleryAccess();
  if (hasPermission) {
    console.log('Gallery access granted');
    // Proceed with gallery operations
  } else {
    console.log('Gallery access denied');
  }
};

// Check if permission is already granted
const checkPermission = async () => {
  const hasPermission = await checkGalleryPermission();
  console.log('Gallery permission status:', hasPermission);
};
```

### Advanced Media Permissions (Android 13+)

```typescript
import { 
  checkAllMediaPermissions, 
  requestAllMediaPermissions 
} from '../Common/Permissions/Gallery';

// Check all media permissions
const checkMediaPermissions = async () => {
  const permissions = await checkAllMediaPermissions();
  console.log('Images permission:', permissions.images);
  console.log('Videos permission:', permissions.videos);
};

// Request all media permissions
const requestMediaPermissions = async () => {
  const permissions = await requestAllMediaPermissions();
  if (permissions.images && permissions.videos) {
    console.log('All media permissions granted');
  }
};
```

### Combined Camera and Gallery Permissions

```typescript
import { 
  requestCameraAndGalleryPermissions,
  checkCameraAndGalleryPermissions 
} from '../Common/Permissions/MediaPermissions';

// Request both camera and gallery permissions
const requestBothPermissions = async () => {
  const permissions = await requestCameraAndGalleryPermissions();
  console.log('Camera permission:', permissions.camera);
  console.log('Gallery permission:', permissions.gallery);
  
  if (permissions.camera && permissions.gallery) {
    console.log('Both permissions granted - can use ImagePickerModal');
  }
};

// Check both permissions
const checkBothPermissions = async () => {
  const permissions = await checkCameraAndGalleryPermissions();
  return permissions.camera && permissions.gallery;
};
```

### Complete Example with ImagePickerModal

```typescript
import React, { useState } from 'react';
import { View, TouchableOpacity, Text, Alert } from 'react-native';
import { ImagePickerModal } from '../Components/ImagePickerModal';
import { requestCameraAndGalleryPermissions } from '../Common/Permissions/MediaPermissions';

const MyImagePickerScreen = () => {
  const [imageUri, setImageUri] = useState<string>('');
  const [modalVisible, setModalVisible] = useState(false);

  const handleOpenImagePicker = async () => {
    // Check permissions before opening modal
    const permissions = await requestCameraAndGalleryPermissions();
    
    if (!permissions.camera || !permissions.gallery) {
      Alert.alert(
        'Permissions Required',
        'Camera and gallery permissions are required to select images.',
        [{ text: 'OK' }]
      );
      return;
    }

    setModalVisible(true);
  };

  const handleImageChange = (uri: string) => {
    setImageUri(uri);
    console.log('Selected image:', uri);
  };

  return (
    <View>
      <TouchableOpacity onPress={handleOpenImagePicker}>
        <Text>Select Image</Text>
      </TouchableOpacity>

      <ImagePickerModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onChange={handleImageChange}
        label="Profile Picture"
        hasImage={!!imageUri}
      />
    </View>
  );
};
```

## API Reference

### `galleryAccess(): Promise<boolean>`
Requests gallery permission and returns whether it was granted.

### `checkGalleryPermission(): Promise<boolean>`
Checks if gallery permission is already granted without requesting.

### `checkAllMediaPermissions(): Promise<{images: boolean, videos: boolean}>`
Checks granular media permissions (Android 13+) or general gallery permission (older versions/iOS).

### `requestAllMediaPermissions(): Promise<{images: boolean, videos: boolean}>`
Requests all media permissions and returns the status.

## Platform-Specific Behavior

### Android
- **API 33+ (Android 13+)**: Uses `READ_MEDIA_IMAGES` and `READ_MEDIA_VIDEO`
- **API < 33**: Uses `READ_EXTERNAL_STORAGE`
- Provides clear permission dialogs with retry options
- Handles "Never ask again" scenario with settings navigation

### iOS
- Uses `PHOTO_LIBRARY` permission
- Handles blocked permissions with settings navigation
- Provides user-friendly permission request dialogs

## Error Handling

The module includes comprehensive error handling:
- Permission denied scenarios
- Permanently denied permissions
- Platform-specific error cases
- Network and system errors

All errors are logged to console for debugging purposes.
