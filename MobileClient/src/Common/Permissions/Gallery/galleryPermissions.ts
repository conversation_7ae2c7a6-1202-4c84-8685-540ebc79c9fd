import { Platform, PermissionsAndroid, Alert, Linking } from 'react-native';
import {
  PERMISSIONS,
  check,
  request,
  RESULTS,
  openSettings,
} from 'react-native-permissions';

const galleryAccess = async (): Promise<boolean> => {
  if (Platform.OS === 'android') {
    try {
      const androidVersion = parseInt(Platform.Version.toString());
      
      if (androidVersion >= 33) {
        // Android 13+ - Use READ_MEDIA_IMAGES
        const hasPermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES
        );

        if (hasPermission) {
          console.log('Gallery permission already granted');
          return true;
        }

        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES,
          {
            title: 'Gallery Permission',
            message: 'This app needs access to your gallery to select photos.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          }
        );

        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          console.log('Gallery permission granted');
          return true;
        } else if (granted === PermissionsAndroid.RESULTS.DENIED) {
          Alert.alert('Permission Denied', 'You need to grant gallery access to use this feature.', [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Try Again', onPress: () => galleryAccess() },
          ]);
          return false;
        } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
          Alert.alert(
            'Permission Permanently Denied',
            'Gallery access is permanently denied. Please enable it manually in your device settings.',
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Open Settings', onPress: () => Linking.openSettings() },
            ]
          );
          return false;
        }
        return false;
      } else {
        // Android 12 and below - Use READ_EXTERNAL_STORAGE
        const hasPermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE
        );

        if (hasPermission) {
          console.log('Gallery permission already granted');
          return true;
        }

        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
          {
            title: 'Gallery Permission',
            message: 'This app needs access to your gallery to select photos.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          }
        );

        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          console.log('Gallery permission granted');
          return true;
        } else if (granted === PermissionsAndroid.RESULTS.DENIED) {
          Alert.alert('Permission Denied', 'You need to grant gallery access to use this feature.', [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Try Again', onPress: () => galleryAccess() },
          ]);
          return false;
        } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
          Alert.alert(
            'Permission Permanently Denied',
            'Gallery access is permanently denied. Please enable it manually in your device settings.',
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Open Settings', onPress: () => Linking.openSettings() },
            ]
          );
          return false;
        }
        return false;
      }
    } catch (error) {
      console.log('Error requesting Android gallery permission:', error);
      return false;
    }
  } else if (Platform.OS === 'ios') {
    try {
      const status = await check(PERMISSIONS.IOS.PHOTO_LIBRARY);

      if (status === RESULTS.GRANTED) {
        console.log('iOS gallery permission already granted');
        return true;
      }

      if (status === RESULTS.BLOCKED) {
        Alert.alert(
          'Gallery Permission Blocked',
          'Gallery access is blocked. Please enable it in your device settings.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => openSettings() },
          ]
        );
        return false;
      }

      const requestStatus = await request(PERMISSIONS.IOS.PHOTO_LIBRARY);

      if (requestStatus === RESULTS.GRANTED) {
        console.log('iOS gallery permission granted');
        return true;
      } else if (requestStatus === RESULTS.DENIED) {
        Alert.alert('Permission Denied', 'You need to grant gallery access to use this feature.', [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Try Again', onPress: () => galleryAccess() },
        ]);
        return false;
      } else if (requestStatus === RESULTS.BLOCKED) {
        Alert.alert(
          'Permission Blocked',
          'Gallery access is blocked. Please enable it in your device settings.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => openSettings() },
          ]
        );
        return false;
      }
      return false;
    } catch (error) {
      console.log('Error requesting iOS gallery permission:', error);
      return false;
    }
  }

  console.log('Unsupported platform for gallery permissions');
  return false;
};

// Check gallery permission without requesting
export const checkGalleryPermission = async (): Promise<boolean> => {
  if (Platform.OS === 'android') {
    try {
      const androidVersion = parseInt(Platform.Version.toString());
      
      if (androidVersion >= 33) {
        return await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES);
      } else {
        return await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE);
      }
    } catch (error) {
      console.log('Error checking Android gallery permission:', error);
      return false;
    }
  } else if (Platform.OS === 'ios') {
    try {
      const status = await check(PERMISSIONS.IOS.PHOTO_LIBRARY);
      return status === RESULTS.GRANTED;
    } catch (error) {
      console.log('Error checking iOS gallery permission:', error);
      return false;
    }
  }

  return false;
};

export default galleryAccess;
