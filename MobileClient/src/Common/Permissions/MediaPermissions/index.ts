import cameraAccess, { checkCameraPermission } from '../Camera/cameraAccess';
import { galleryAccess, checkGalleryPermission } from '../Gallery';



/**
 * Check basic media permissions (camera and gallery)
 */
export const checkBasicMediaPermissions = async (): Promise<{
  camera: boolean;
  gallery: boolean;
}> => {
  try {
    const [cameraPermission, galleryPermission] = await Promise.all([
      checkCameraPermission(),
      checkGalleryPermission(),
    ]);

    return {
      camera: cameraPermission,
      gallery: galleryPermission,
    };
  } catch (error) {
    console.log('Error checking basic media permissions:', error);
    return {
      camera: false,
      gallery: false,
    };
  }
};

/**
 * Request only camera and gallery permissions (most common use case)
 */
export const requestCameraAndGalleryPermissions = async (): Promise<{
  camera: boolean;
  gallery: boolean;
}> => {
  try {
    const [cameraPermission, galleryPermission] = await Promise.all([
      cameraAccess(),
      galleryAccess(),
    ]);

    return {
      camera: cameraPermission,
      gallery: galleryPermission,
    };
  } catch (error) {
    console.log('Error requesting camera and gallery permissions:', error);
    return {
      camera: false,
      gallery: false,
    };
  }
};

/**
 * Check only camera and gallery permissions (most common use case)
 */
export const checkCameraAndGalleryPermissions = async (): Promise<{
  camera: boolean;
  gallery: boolean;
}> => {
  try {
    const [cameraPermission, galleryPermission] = await Promise.all([
      checkCameraPermission(),
      checkGalleryPermission(),
    ]);

    return {
      camera: cameraPermission,
      gallery: galleryPermission,
    };
  } catch (error) {
    console.log('Error checking camera and gallery permissions:', error);
    return {
      camera: false,
      gallery: false,
    };
  }
};

// Re-export individual permission functions
export { cameraAccess, checkCameraPermission };
export { galleryAccess, checkGalleryPermission };
